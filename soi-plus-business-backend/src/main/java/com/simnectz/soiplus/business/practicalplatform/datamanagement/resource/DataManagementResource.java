package com.simnectz.soiplus.business.practicalplatform.datamanagement.resource;

import com.simnectz.soiplus.business.practicalplatform.apimarket.domain.model.MakeSwagView;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.service.DataManagementService;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.util.Map;

/**
 * sql数据管理
 */
@RestController
@RequestMapping("v1/practical/data-management")
@RequiredArgsConstructor
public class DataManagementResource {

    private final DataManagementService dataManagementService;

    /**
     * 该用户下是否存在数据库
     * 何处调用 —— 可视化模块导入数据、数据导入模块中导入数据、数据导入模块中更新数据
     *
     * @param userId
     * @return
     */
    @GetMapping("/existdb")
    public ResponseEntity<Response<?>> getUserDb(@RequestParam String userId) {
        return ResponseEntity.ok(dataManagementService.getUserDb(userId));
    }

    /**
     * 更新数据库表信息
     * 何处调用 —— 可视化模块导入数据上传模版提交、数据导入模块中导入数据上传模版提交、数据导入模块中更新数据导入数据文件上传模版提交
     *
     * @param file
     * @param userId
     * @return
     */
    @PostMapping("/uploadtable")
    public ResponseEntity<Response<?>> fileUpload(MultipartFile file, String userId, String type) {
        return ResponseEntity.ok(dataManagementService.uploadExcelFileAndCreateTable(file, userId, type));
    }

    /**
     * 查看数据表列表
     * 何处调用 —— 进入数据导入模块展示
     *
     * @param userId
     * @return
     */
    @GetMapping("/tablelist")
    public ResponseEntity<Response<?>> tableList(@RequestParam String userId) {
        return ResponseEntity.ok(dataManagementService.getTableListByUserId(userId));
    }

    @GetMapping("/table-template")
    public void getTableTemplate(@RequestParam String userId, String tableName, HttpServletResponse response) {
        dataManagementService.getTableTemplate(response, userId, tableName);
    }

    /**
     * 上传数据表信息
     * 何处调用 —— 数据导入模块中导入数据描述上传提交、数据导入模块中更新数据描述导入数据描述上传提交
     *
     * @param file
     * @param userId
     * @return
     */
    @PostMapping("/uploadintro")
    public ResponseEntity<Response<?>> uploadTableInfo(MultipartFile file, String userId, String type) {
        return ResponseEntity.ok(dataManagementService.uploadTableInfoFileAndSave(file, userId, type));
    }

    /**
     * 查看数据库表的数据
     * 何处调用 —— 数据导入模块中查看数据
     *
     * @param userId
     * @param tableName
     * @param publicSource
     * @return
     */
    @GetMapping("/gettabledata")
    public ResponseEntity<Response<?>> getTableData(@RequestParam String userId, @RequestParam String tableName, @RequestParam String publicSource) {
        return ResponseEntity.ok(dataManagementService.getDataByUserIdAndTableName(userId, tableName, publicSource));
    }

    /**
     * 删除数据表
     * 何处调用 —— 数据导入模块中删除
     *
     * @param userId
     * @param tableName
     * @return
     */
    @DeleteMapping("/deletetable")
    public ResponseEntity<Response<?>> deleteTable(@RequestParam String userId, @RequestParam String tableName) {
        return ResponseEntity.ok(dataManagementService.deleteTblByTableName(userId, tableName));
    }

    /**
     * 导出数据表
     * 何处调用 —— 数据导入模块中导出
     *
     * @param response
     * @param userID
     * @param tableName
     * @return
     * @throws Exception
     */
    @GetMapping("/export")
    public ResponseEntity<Response<?>> export(HttpServletResponse response, String userID, String tableName) {
        return ResponseEntity.ok(dataManagementService.export(response, userID, tableName));
    }

    /**
     * 创建数据库信息
     * 何处调用 —— 数据导入模块
     *
     * @param userId
     * @param dbName
     * @return
     */
    @PostMapping("/createdb")
    public ResponseEntity<Response<?>> createdb(@RequestParam String userId, @RequestParam String dbName) {
        return ResponseEntity.ok(dataManagementService.createdb(userId, dbName));
    }

    /**
     * 何处调用 —— 生成API查询
     *
     * @param userId
     * @param tableName
     * @return
     */
    @GetMapping("/paramslist")
    public ResponseEntity<Response<?>> getParamsList(@RequestParam String userId, @RequestParam String tableName) {
        return ResponseEntity.ok(dataManagementService.getParamsListByUserId(userId, tableName));
    }

    /**
     * 何处调用 —— 生成API查询，使用自定义数据源搜索
     * 根据用户查询数据库信息
     *
     * @param userId
     * @return
     */
    @GetMapping("/userdbname")
    public ResponseEntity<Response<?>> getUserDbName(@RequestParam String userId) {
        return ResponseEntity.ok(dataManagementService.getUserDbName(userId));
    }

    /**
     * 何处调用 —— 生成API查询，运行
     *
     * @param genericApiModel
     * @param db_name
     * @param table_name
     * @return
     * @throws SQLException
     */
    @GetMapping("/{db_name}/{table_name}/execute")
    public ResponseEntity<Response<?>> getDataExecuteByParams(@RequestParam Map<String, String> genericApiModel,
                                                              @PathVariable String db_name,
                                                              @PathVariable String table_name) throws SQLException {
        return ResponseEntity.ok(dataManagementService.getDataExecuteByParams(genericApiModel, db_name, table_name));
    }

    /**
     * 何处调用 —— 生成API查询，发布API
     *
     * @param swagView
     * @return
     */
    @PostMapping("/generateswagger")
    public ResponseEntity<Response<?>> makeSwagger(@RequestBody MakeSwagView swagView) {
        return ResponseEntity.ok(dataManagementService.makeSwagger(swagView));
    }
}
