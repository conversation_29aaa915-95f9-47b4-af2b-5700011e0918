package com.simnectz.soiplus.core.authorize.domain.dto;

import com.simnectz.soiplus.core.system.annotation.validation.ValidationEmail;
import com.simnectz.soiplus.core.system.constant.RegexpConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ForgotPasswordDto implements Serializable {

    private static final long serialVersionUID = 6522472793272928219L;

    @NotBlank(message = "{EMAIL_IS_REQUIRED}")
    @ValidationEmail
    private String email;

    @NotBlank(message = "{NEW_PASSWORD_IS_REQUIRED}")
    @Pattern(regexp = RegexpConstant.PASSWORD_REGEXP, message = "{INVALID_PASSWORD_FORMAT}")
    private String newPassword;

}
