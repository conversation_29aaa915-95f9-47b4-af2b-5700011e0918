package com.simnectz.soiplus.core.authorize.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Refresh token request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RefreshTokenDto implements Serializable {

    private static final long serialVersionUID = 8950292237561331041L;

    /**
     * Access token
     * Used to access APIs that require authorization
     */
    @NotBlank(message = "{ACCESS_TOKEN_IS_REQUIRED}")
    private String accessToken;

    /**
     * Refresh token
     * Used to refresh access token when they expire
     */
    @NotBlank(message = "{REFRESH_TOKEN_IS_REQUIRED}")
    private String refreshToken;

}
