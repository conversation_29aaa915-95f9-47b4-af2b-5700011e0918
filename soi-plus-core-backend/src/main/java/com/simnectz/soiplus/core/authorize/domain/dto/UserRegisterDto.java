package com.simnectz.soiplus.core.authorize.domain.dto;

import com.simnectz.soiplus.core.system.annotation.validation.*;
import com.simnectz.soiplus.core.system.constant.FieldLength;
import com.simnectz.soiplus.core.system.constant.RegexpConstant;
import com.simnectz.soiplus.core.system.enums.CardType;
import com.simnectz.soiplus.core.system.enums.Gender;
import com.simnectz.soiplus.core.users.domian.dto.Card;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * Register user request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ValidationConfirmPassword
@EqualsAndHashCode
@ValidationCardId
public class UserRegisterDto implements Serializable, Card {

    private static final long serialVersionUID = -9175856218408202553L;

    /**
     * 昵称
     */
    @NotBlank(message = "{NICKNAME_IS_REQUIRED}")
    @ValidationNickname
    @Length(min = FieldLength.NICKNAME_MIN, max = FieldLength.NICKNAME_MAX, message = "{INVALID_NICKNAME_LENGTH}")
    private String nickname;
    /**
     * 用户姓名
     */
    @NotBlank(message = "{USERNAME_IS_REQUIRED}")
    @ValidationUsername
    @Length(min = FieldLength.USERNAME_MIN, max = FieldLength.USERNAME_MAX, message = "{USERNAME_NICKNAME_LENGTH}")
    private String username;
    /**
     * 用户邮箱
     */
    @NotBlank(message = "{EMAIL_IS_REQUIRED}")
    @ValidationEmail
    @Length(max = FieldLength.EMAIL_MAX, message = "{INVALID_EMAIL_LENGTH}")
    private String email;
    /**
     * 密码
     */
    @NotBlank(message = "{PASSWORD_IS_REQUIRED}")
    @Pattern(regexp = RegexpConstant.PASSWORD_REGEXP, message = "{INVALID_PASSWORD_FORMAT}")
    @Length(min = FieldLength.PASSWORD_MIN, max = FieldLength.PASSWORD_MAX, message = "{INVALID_PASSWORD_LENGTH}")
    private String password;
    /**
     * 确认密码
     */
    @NotBlank(message = "{CONFIRM_PASSWORD_IS_REQUIRED}")
    private String confirmPassword;
    /**
     * 用户照片
     */
    private String image;
    /**
     * 出生年份
     */
    private Date birthday;
    /**
     * 性别：1：男，2：女
     */
    @ValidationEnum(enumClass = Gender.class, message = "{INVALID_GENDER}")
    private String sex;
    /**
     * 手机号码
     */
    @NotBlank(message = "{PHONE_NUMBER_IS_REQUIRED}")
    @ValidationPhoneNumber
    private String phoneNumber;
    /**
     * 证件类型
     * 1: 中国大陆身份证(15/18位)
     * 2: 香港身份证(10位)
     * 3: 台湾身份证(10位)
     * 4: 澳门身份证(10位)
     */
    @NotBlank(message = "{CARD_TYPE_IS_REQUIRED}")
    @ValidationEnum(enumClass = CardType.class, message = "{UNSUPPORTED_CARD_TYPE}")
    private String cardType;
    /**
     * 证件号码
     */
    @NotBlank(message = "{CARD_ID_IS_REQUIRED}")
    private String cardId;
    /**
     * 用户级别
     */
    private String level;
    /**
     * 毕业学校
     */
    private String graduateSchool;
    /**
     * 所属组织的名称
     */
    private String organizationId;
    /**
     * 工号/学号
     */
    private String studentOrStaffNumber;
    /**
     * 部门
     */
    private String department;
    /**
     * 职务
     */
    private String jobTitle;
    /**
     * 工作年限
     */
    private Integer experienceYear;
    /**
     * 登录次数
     */
    private Long loginCount = 0L;
    /**
     * 用户状态，0：未激活；1：激活
     */
    private Integer status = 0;
    /**
     * 是否在黑名单，1：在黑名单，0：不在黑名单
     */
    private Integer blackList = 0;
    /**
     * 批量导入状态，0：自己注册，1：批量导入
     */
    private Integer batchStatus = 0;
    /**
     * 用户逻辑删除状态，0：未删除；1：已逻辑删除
     */
    private Integer isDeleted = 0;


}
