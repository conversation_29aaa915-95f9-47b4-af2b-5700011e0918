package com.simnectz.soiplus.core.authorize.resource;

import com.simnectz.soiplus.core.authorize.domain.dto.*;
import com.simnectz.soiplus.core.authorize.domain.vo.TokenVo;
import com.simnectz.soiplus.core.authorize.service.AuthorizeService;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * Authorize controller
 */
@RestController
@RequestMapping("v1/authorize")
@RequiredArgsConstructor
public class AuthorizeResource {

    private final AuthorizeService authorizeService;

    /**
     * User register
     *
     * @param userRegisterDto User information
     */
    @PostMapping("register")
    public ResponseEntity<Response<?>> register(@RequestBody @Validated UserRegisterDto userRegisterDto) {
        return ResponseEntity.ok(authorizeService.register(userRegisterDto));
    }

    /**
     * User login
     *
     * @param userLoginDto User information
     */
    @PostMapping("login")
    public ResponseEntity<Response<?>> login(@RequestBody @Validated UserLoginDto userLoginDto) {
        return ResponseEntity.ok(authorizeService.login(userLoginDto));
    }

    /**
     * User auto login
     *
     * @param autoLoginDto Auto login key
     */
    @PostMapping("auto-login")
    public ResponseEntity<Response<?>> autoLogin(@RequestBody @Validated AutoLoginDto autoLoginDto) {
        return ResponseEntity.ok(authorizeService.autoLogin(autoLoginDto));
    }

    /**
     * Refresh token
     *
     * @param refreshTokenDto Refresh token info
     * @return Token information
     */
    @PutMapping("refresh-token")
    public ResponseEntity<Response<TokenVo>> refreshToken(@RequestBody @Validated RefreshTokenDto refreshTokenDto) {
        return ResponseEntity.ok(authorizeService.refreshToken(refreshTokenDto));
    }


    /**
     * Get captcha
     *
     * @param captchaKey Front end random generation
     */
    @GetMapping("captcha/{captchaKey}")
    public void getCaptcha(@PathVariable("captchaKey") String captchaKey, HttpServletResponse response) {
        authorizeService.getCaptcha(captchaKey, response);
    }

    /**
     * Active user
     */
    @GetMapping("active-user")
    public void activeUser(@RequestParam("email") String email, @RequestParam("code") String code, HttpServletResponse response) {
        authorizeService.activeUser(email, code, response);
    }

    /**
     * Resend active email
     */
    @PostMapping("resend-active-email")
    public ResponseEntity<Response<?>> resendActiveEmail(@RequestBody ResendActiveEmailDto resendActiveEmailDto) {
        return ResponseEntity.ok(authorizeService.resendActiveEmail(resendActiveEmailDto.getEmail()));
    }

    /**
     * Forgot password
     */
    @PostMapping("forgot-password")
    public ResponseEntity<Response<?>> forgotPassword(@RequestBody ForgotPasswordDto forgotPasswordDto) {
        return ResponseEntity.ok(authorizeService.forgotPassword(forgotPasswordDto));
    }

    /**
     * Reset password
     */
    @GetMapping("reset-password")
    public void resetPassword(@RequestParam("code") String code, HttpServletResponse response) {
        authorizeService.resetPassword(code, response);
    }

}
