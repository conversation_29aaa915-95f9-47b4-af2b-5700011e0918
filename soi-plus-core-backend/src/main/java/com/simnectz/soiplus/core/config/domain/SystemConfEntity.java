package com.simnectz.soiplus.core.config.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "sysconf")
public class SystemConfEntity implements Serializable {

    private static final long serialVersionUID = 1304833896326256083L;

    @Id(keyType = KeyType.Auto)
    private String id;

    private String name;

    private String content;

    private String tag;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

}
