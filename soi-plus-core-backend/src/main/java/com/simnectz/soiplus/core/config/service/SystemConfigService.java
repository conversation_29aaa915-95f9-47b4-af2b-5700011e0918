package com.simnectz.soiplus.core.config.service;

import com.simnectz.soiplus.core.config.domain.SystemConfEntity;
import com.simnectz.soiplus.core.system.domain.vo.Response;

import java.util.List;

public interface SystemConfigService {
    Response<?> selectEmailSuffix();

    Response<?> queryConfig();

    Response<?> addEmailSuffix(String[] suffixList);

    Response<?> saveConfig(SystemConfEntity systemConf);

    Response<?> deleteConfig(List<Integer> ids);
}
