package com.simnectz.soiplus.core.config.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.simnectz.soiplus.core.config.domain.EmailSuffixEntity;
import com.simnectz.soiplus.core.config.domain.SystemConfEntity;
import com.simnectz.soiplus.core.config.mapper.EmailSuffixMapper;
import com.simnectz.soiplus.core.config.mapper.SystemConfMapper;
import com.simnectz.soiplus.core.config.service.SystemConfigService;
import com.simnectz.soiplus.core.system.constant.ResponseConstant;
import com.simnectz.soiplus.core.system.constant.SystemConstant;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.system.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.simnectz.soiplus.core.config.domain.table.SystemConfTableDef.system_conf;


@Service
@RequiredArgsConstructor
public class SystemConfigServiceImpl implements SystemConfigService {

    private final EmailSuffixMapper emailSuffixMapper;
    private final SystemConfMapper systemConfMapper;

    @Override
    public Response<?> selectEmailSuffix() {
        List<EmailSuffixEntity> list = emailSuffixMapper.selectAll();
        return Response.success(list);
    }

    @Override
    public Response<?> queryConfig() {
        List<SystemConfEntity> all = systemConfMapper.selectAll();
        return Response.success(all);
    }

    @Override
    @Transactional
    public Response<?> addEmailSuffix(String[] emailSuffix) {
        if (emailSuffix == null || emailSuffix.length == 0)
            return Response.failed(500, MessageUtils.message(ResponseConstant.Message.MAIL_SUFFIX_NOT_EMPTY));

        List<EmailSuffixEntity> emailSuffixEntityList = emailSuffixMapper.selectAll();
        List<Integer> ids = emailSuffixEntityList.stream().map(EmailSuffixEntity::getId).collect(Collectors.toList());
        emailSuffixMapper.deleteBatchByIds(ids);

        for (String s : emailSuffix) {
            EmailSuffixEntity emailSuffixEntity = new EmailSuffixEntity();
            emailSuffixEntity.setSuffix(s.trim());
            emailSuffixMapper.insert(emailSuffixEntity);
        }
        return Response.success();
    }

    @Override
    public Response<?> saveConfig(SystemConfEntity systemConf) {
        if (StrUtil.isBlank(systemConf.getId())) {
            systemConf.setCreateDate(new Date());
            SystemConfEntity byName = systemConfMapper.selectOneByCondition(system_conf.name.eq(systemConf.getName()));
            if (ObjUtil.isNotNull(byName)) {
                return Response.failed(-99, MessageUtils.message(ResponseConstant.Message.NAME_ALREADY_EXISTS));
            }
        }
        if (SystemConstant.CONFIG_PASSWORD.equals(systemConf.getName()) && Integer.parseInt(systemConf.getContent()) > 9) {
            return Response.failed(-99, MessageUtils.message(ResponseConstant.Message.PASSWORD_UPDATE_FAILED));
        }
        if (SystemConstant.CONFIG_USER.equals(systemConf.getName()) && Integer.parseInt(systemConf.getContent()) > 99) {
            return Response.failed(-99, MessageUtils.message(ResponseConstant.Message.INVALID_ACCOUNT_RETENTION_PERIOD));
        }
        systemConf.setUpdateDate(new Date());
        systemConfMapper.insertOrUpdate(systemConf);
        return Response.success();
    }

    @Override
    public Response<?> deleteConfig(List<Integer> ids) {
        for (int i = 0; i < ids.size(); i++) {
            systemConfMapper.deleteById(ids.get(i));
        }
        return Response.success();
    }
}
