package com.simnectz.soiplus.core.organizations.domain.dto;

import com.simnectz.soiplus.core.system.domain.dto.PaginateEnquiry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EnquiryOrganizationDto extends PaginateEnquiry implements Serializable {

    private static final long serialVersionUID = -240826707209513357L;

    private String keywords;

}
