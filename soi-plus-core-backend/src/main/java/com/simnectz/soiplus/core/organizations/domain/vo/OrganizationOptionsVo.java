package com.simnectz.soiplus.core.organizations.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class OrganizationOptionsVo implements Serializable {

    private static final long serialVersionUID = 6148222974660333255L;

    private String organizationId;
    private String organizationName;

}
