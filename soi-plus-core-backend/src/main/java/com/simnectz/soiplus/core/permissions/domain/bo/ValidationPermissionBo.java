package com.simnectz.soiplus.core.permissions.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Permission information that needs to be verified
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ValidationPermissionBo implements Serializable {

    private static final long serialVersionUID = -91359506775188765L;

    private String id;
    private String permissionName;

}
