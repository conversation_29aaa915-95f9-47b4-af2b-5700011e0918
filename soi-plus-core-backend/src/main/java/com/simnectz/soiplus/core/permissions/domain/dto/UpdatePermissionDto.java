package com.simnectz.soiplus.core.permissions.domain.dto;

import com.simnectz.soiplus.core.system.annotation.validation.ValidationRange;
import com.simnectz.soiplus.core.system.constant.FieldLength;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UpdatePermissionDto implements Serializable {

    private static final long serialVersionUID = 6200061049037990856L;

    /**
     * ID
     */
    @NotBlank(message = "{PERMISSION_ID_IS_REQUIRED}")
    private String id;

    /**
     * 上级权限ID
     */
    @NotBlank(message = "{PARENT_ID_IS_REQUIRED}")
    private String parentId;

    /**
     * 权限名称
     */
    @NotBlank(message = "{PERMISSION_NAME_IS_REQUIRED}")
    @Length(max = FieldLength.PERMISSION_NAME_MAX, message = "{INVALID_PERMISSION_NAME_LENGTH}")
    private String permissionName;

    /**
     * 权限标识，用于Security控制权限
     */
    @NotBlank(message = "{ACCREDIT_IS_REQUIRED}")
    @Length(max = FieldLength.ACCREDIT_MAX, message = "{INVALID_ACCREDIT_LENGTH}")
    private String accredit;

    /**
     * 序号
     */
    @NotNull(message = "{SORT_NUMBER_IS_REQUIRED}")
    @ValidationRange(min = FieldLength.SORT_NUMBER_MIN, max = FieldLength.SORT_NUMBER_MAX, message = "{INVALID_SORT_NUMBER_SIZE}")
    private Integer sortNumber;

}
