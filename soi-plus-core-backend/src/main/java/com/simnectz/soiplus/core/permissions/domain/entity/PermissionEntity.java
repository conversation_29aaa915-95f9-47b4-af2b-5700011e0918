package com.simnectz.soiplus.core.permissions.domain.entity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.core.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

/**
 * Permission entity
 */
@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "system_permission")
@EqualsAndHashCode(callSuper = true)
public class PermissionEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = 791581813062280564L;

    /**
     * 上级权限ID
     */
    private String parentId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限标识，用于Security控制权限
     */
    private String accredit;

    /**
     * 树路径，用于操作树
     */
    private String treePath;

    /**
     * 序号
     */
    private Integer sortNumber;

}
