package com.simnectz.soiplus.core.permissions.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Permission details response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class PermissionDetailsVo implements Serializable {

    private static final long serialVersionUID = -5927955428406069099L;

    /**
     * ID
     */
    private String id;

    /**
     * 上级权限ID
     */
    private String parentId;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限标识，用于Security控制权限
     */
    private String accredit;

    /**
     * 序号
     */
    private Integer sortNumber;

}
