package com.simnectz.soiplus.core.permissions.resource;

import com.simnectz.soiplus.core.permissions.domain.dto.CreatePermissionDto;
import com.simnectz.soiplus.core.permissions.domain.dto.EnquiryPermissionDto;
import com.simnectz.soiplus.core.permissions.domain.dto.UpdatePermissionDto;
import com.simnectz.soiplus.core.permissions.domain.vo.PermissionDetailsVo;
import com.simnectz.soiplus.core.permissions.domain.vo.PermissionOptionsVo;
import com.simnectz.soiplus.core.permissions.domain.vo.PermissionVo;
import com.simnectz.soiplus.core.permissions.service.PermissionService;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * Permission controller
 */
@RestController
@RequestMapping("v1/system/permissions")
@RequiredArgsConstructor
public class PermissionResource {

    private final PermissionService permissionService;

    /**
     * Validation permission name is unique
     *
     * @param permissionId   Permission id
     * @param permissionName Permission name
     * @return true: repeat, false: not repeat
     */
    @GetMapping("permission-name/unique")
    public ResponseEntity<Response<Boolean>> validationPermissionNameUnique(
            @RequestParam(value = "permissionId", required = false) String permissionId,
            @RequestParam("permissionName") String permissionName
    ) {
        return ResponseEntity.ok(permissionService.validationPermissionNameUnique(permissionId, permissionName));
    }

    /**
     * Enquiry permission details
     *
     * @param permissionId Permission id
     * @return Permission details
     */
    @GetMapping("{permissionId}")
    public ResponseEntity<Response<PermissionDetailsVo>> enquiryPermissionDetails(@PathVariable String permissionId) {
        return ResponseEntity.ok(permissionService.enquiryPermissionDetails(permissionId));
    }

    /**
     * Create permission
     *
     * @param createPermissionDto Permission information
     */
    @PostMapping
    public ResponseEntity<Response<?>> createPermission(@RequestBody @Validated CreatePermissionDto createPermissionDto) {
        return ResponseEntity.ok(permissionService.createPermission(createPermissionDto));
    }

    /**
     * Batch delete permissions
     *
     * @param permissionIds Permission ids
     */
    @DeleteMapping
    public ResponseEntity<Response<?>> batchDeletePermissions(@RequestParam("permissionIds") Set<String> permissionIds) {
        return ResponseEntity.ok(permissionService.batchDeletePermissions(permissionIds));
    }

    /**
     * Paginate enquiry permissions
     *
     * @param enquiryPermissionDto Criteria for query of permissions
     * @return Permissions
     */
    @GetMapping
    public ResponseEntity<Response<List<PermissionVo>>> enquiryPermissions(EnquiryPermissionDto enquiryPermissionDto) {
        return ResponseEntity.ok(permissionService.enquiryPermissions(enquiryPermissionDto.getKeywords()));
    }

    /**
     * Update permission
     *
     * @param updatePermissionDto Permission information
     */
    @PutMapping
    public ResponseEntity<Response<?>> updatePermission(@RequestBody @Validated UpdatePermissionDto updatePermissionDto) {
        return ResponseEntity.ok(permissionService.updatePermission(updatePermissionDto));
    }

    /**
     * Enquiry permission options
     *
     * @return Permission options
     */
    @GetMapping("options")
    public ResponseEntity<Response<List<PermissionOptionsVo>>> enquiryPermissionOptions() {
        return ResponseEntity.ok(permissionService.enquiryPermissionOptions());
    }

}
