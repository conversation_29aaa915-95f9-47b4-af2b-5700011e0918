package com.simnectz.soiplus.core.permissions.service;

import com.mybatisflex.core.service.IService;
import com.simnectz.soiplus.core.permissions.domain.dto.CreatePermissionDto;
import com.simnectz.soiplus.core.permissions.domain.dto.UpdatePermissionDto;
import com.simnectz.soiplus.core.permissions.domain.entity.PermissionEntity;
import com.simnectz.soiplus.core.permissions.domain.vo.PermissionDetailsVo;
import com.simnectz.soiplus.core.permissions.domain.vo.PermissionOptionsVo;
import com.simnectz.soiplus.core.permissions.domain.vo.PermissionVo;
import com.simnectz.soiplus.core.system.domain.vo.Response;

import java.util.List;
import java.util.Set;

public interface PermissionService extends IService<PermissionEntity> {

    /**
     * Validation permission name is unique
     *
     * @param permissionId   Permission id
     * @param permissionName Permission name
     * @return true: repeat, false: not repeat
     */
    Response<Boolean> validationPermissionNameUnique(String permissionId, String permissionName);

    /**
     * Enquiry permission options
     *
     * @return Permission options
     */
    Response<List<PermissionOptionsVo>> enquiryPermissionOptions();

    /**
     * Enquiry permission details
     *
     * @param permissionId Permission id
     * @return Permission details
     */
    Response<PermissionDetailsVo> enquiryPermissionDetails(String permissionId);

    /**
     * Create permission
     *
     * @param createPermissionDto Permission information
     */
    Response<?> createPermission(CreatePermissionDto createPermissionDto);

    /**
     * Batch delete permissions
     *
     * @param permissionIds Permission ids
     */
    Response<?> batchDeletePermissions(Set<String> permissionIds);

    /**
     * Enquiry permissions
     *
     * @param keywords Permission name keywords
     * @return List of eligible permissions
     */
    Response<List<PermissionVo>> enquiryPermissions(String keywords);

    /**
     * Update permission
     *
     * @param updatePermissionDto Permission information
     */
    Response<?> updatePermission(UpdatePermissionDto updatePermissionDto);

}
