package com.simnectz.soiplus.core.roles.domain.dto;

import com.simnectz.soiplus.core.system.constant.FieldLength;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Set;

/**
 * Update role request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UpdateRoleDto implements Serializable {

    private static final long serialVersionUID = 3285852756315386201L;

    @NotBlank(message = "{ROLE_ID_IS_REQUIRED}")
    private String id;

    @NotBlank(message = "{ROLE_NAME_IS_REQUIRED}")
    @Length(max = FieldLength.ROLE_NAME_MAX, message = "{INVALID_ROLE_NAME_LENGTH}")
    private String roleName;

    @Length(max = FieldLength.REMARK_MAX, message = "{INVALID_REMARK_LENGTH}")
    private String remark;

    @NotEmpty(message = "{PERMISSION_IS_REQUIRED}")
    private Set<String> permissionIds;

}
