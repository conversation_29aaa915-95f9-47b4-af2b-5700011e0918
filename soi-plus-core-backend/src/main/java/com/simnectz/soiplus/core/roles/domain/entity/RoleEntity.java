package com.simnectz.soiplus.core.roles.domain.entity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.core.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

/**
 * Role entity
 */
@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "system_role")
@EqualsAndHashCode(callSuper = true)
public class RoleEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = -6421932713556877735L;

    private String roleName;
    private String remark;

}
