package com.simnectz.soiplus.core.roles.domain.entity;

import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;

/**
 * Role permission entity
 */
@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "system_role_permission")
@EqualsAndHashCode
public class RolePermissionEntity implements Serializable {

    private static final long serialVersionUID = 4225462366786129763L;

    private String roleId;
    private String permissionId;

}
