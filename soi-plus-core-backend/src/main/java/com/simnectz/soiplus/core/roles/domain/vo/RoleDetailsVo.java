package com.simnectz.soiplus.core.roles.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * Role details response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RoleDetailsVo implements Serializable {

    private static final long serialVersionUID = 5050072179049801383L;

    private String id;
    private String roleName;
    private String remark;
    private Set<String> permissionIds;

}
