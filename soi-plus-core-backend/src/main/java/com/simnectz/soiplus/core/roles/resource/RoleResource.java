package com.simnectz.soiplus.core.roles.resource;

import com.simnectz.soiplus.core.roles.domain.dto.CreateRoleDto;
import com.simnectz.soiplus.core.roles.domain.dto.EnquiryRoleDto;
import com.simnectz.soiplus.core.roles.domain.dto.UpdateRoleDto;
import com.simnectz.soiplus.core.roles.domain.vo.RoleDetailsVo;
import com.simnectz.soiplus.core.roles.domain.vo.RoleOptionsVo;
import com.simnectz.soiplus.core.roles.domain.vo.RoleVo;
import com.simnectz.soiplus.core.roles.service.RoleService;
import com.simnectz.soiplus.core.system.domain.vo.Paginate;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * Role controller
 */
@RestController
@RequestMapping("v1/system/roles")
@RequiredArgsConstructor
public class RoleResource {

    private final RoleService roleService;

    /**
     * Validation role name is unique
     *
     * @param roleId   Role id
     * @param roleName Role name
     * @return true: repeat, false: not repeat
     */
    @GetMapping("role-name/unique")
    public ResponseEntity<Response<Boolean>> validationRoleNameUnique(
            @RequestParam(value = "roleId", required = false) String roleId,
            @RequestParam("roleName") String roleName
    ) {
        return ResponseEntity.ok(roleService.validationRoleNameUnique(roleId, roleName));
    }

    /**
     * Create role
     *
     * @param createRoleDto Role information
     */
    @PostMapping
    public ResponseEntity<Response<?>> createRole(@RequestBody @Validated CreateRoleDto createRoleDto) {
        return ResponseEntity.ok(roleService.createRole(createRoleDto));
    }

    /**
     * Batch delete roles
     *
     * @param roleIds Role ids
     */
    @DeleteMapping
    public ResponseEntity<Response<?>> batchDeleteRoles(@RequestParam("roleIds") Set<String> roleIds) {
        return ResponseEntity.ok(roleService.batchDeleteRoles(roleIds));
    }

    /**
     * Paginate enquiry roles
     *
     * @param rolePaginate Criteria for pagination query of roles
     * @return Roles
     */
    @GetMapping
    public ResponseEntity<Response<Paginate<RoleVo>>> paginateEnquiryRoles(EnquiryRoleDto rolePaginate) {
        return ResponseEntity.ok(roleService.paginateEnquiryRoles(
                rolePaginate.getKeywords(),
                rolePaginate.getCurrentPage(),
                rolePaginate.getPageSize(),
                rolePaginate.getOrderBy(),
                rolePaginate.getIsAsc(),
                rolePaginate.getStartTime(),
                rolePaginate.getEndTime()
        ));
    }

    /**
     * Enquiry role details
     *
     * @param roleId Role id
     * @return Role details
     */
    @GetMapping("{roleId}")
    public ResponseEntity<Response<RoleDetailsVo>> enquiryRoleDetails(@PathVariable String roleId) {
        return ResponseEntity.ok(roleService.enquiryRoleDetails(roleId));
    }

    /**
     * Enquiry role options
     *
     * @return Role options
     */
    @GetMapping("options")
    public ResponseEntity<Response<List<RoleOptionsVo>>> enquiryRoleOptions() {
        return ResponseEntity.ok(roleService.enquiryRoleOptions());
    }

    /**
     * Update role
     *
     * @param roleInfo Role information
     */
    @PutMapping
    public ResponseEntity<Response<?>> updateRole(@RequestBody @Validated UpdateRoleDto roleInfo) {
        return ResponseEntity.ok(roleService.updateRole(roleInfo));
    }

}
