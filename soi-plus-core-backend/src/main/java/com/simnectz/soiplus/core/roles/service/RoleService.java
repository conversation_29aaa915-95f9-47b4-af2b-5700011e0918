package com.simnectz.soiplus.core.roles.service;

import com.mybatisflex.core.service.IService;
import com.simnectz.soiplus.core.roles.domain.dto.CreateRoleDto;
import com.simnectz.soiplus.core.roles.domain.dto.UpdateRoleDto;
import com.simnectz.soiplus.core.roles.domain.entity.RoleEntity;
import com.simnectz.soiplus.core.roles.domain.vo.RoleDetailsVo;
import com.simnectz.soiplus.core.roles.domain.vo.RoleOptionsVo;
import com.simnectz.soiplus.core.roles.domain.vo.RoleVo;
import com.simnectz.soiplus.core.system.domain.vo.Paginate;
import com.simnectz.soiplus.core.system.domain.vo.Response;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Role service
 */
public interface RoleService extends IService<RoleEntity> {

    /**
     * Validation role name is unique
     *
     * @param roleId   Role id
     * @param roleName Role name
     * @return true: repeat, false: not repeat
     */
    Response<Boolean> validationRoleNameUnique(String roleId, String roleName);

    /**
     * Create role
     *
     * @param createRoleDto Role information
     */
    Response<?> createRole(CreateRoleDto createRoleDto);

    /**
     * Batch delete roles
     *
     * @param roleIds Role ids
     */
    Response<?> batchDeleteRoles(Set<String> roleIds);

    /**
     * Paginate enquiry roles
     *
     * @param keywords    Role name keywords
     * @param currentPage Current page
     * @param pageSize    Paginate size
     * @param orderBy     Order field
     * @param isAsc       true: ASC, false: DESC
     * @param startTime   Timestamp of the end time
     * @param endTime     Timestamp of the start time
     * @return List of eligible roles
     */
    Response<Paginate<RoleVo>> paginateEnquiryRoles(
            String keywords,
            Integer currentPage,
            Integer pageSize,
            String orderBy,
            Boolean isAsc,
            Date startTime,
            Date endTime
    );


    /**
     * Enquiry role details
     *
     * @param roleId Role id
     * @return Role details
     */
    Response<RoleDetailsVo> enquiryRoleDetails(String roleId);

    /**
     * Update role
     *
     * @param updateRoleDto Role information
     */
    Response<?> updateRole(UpdateRoleDto updateRoleDto);

    /**
     * Enquiry role options
     *
     * @return Role options
     */
    Response<List<RoleOptionsVo>> enquiryRoleOptions();

}
