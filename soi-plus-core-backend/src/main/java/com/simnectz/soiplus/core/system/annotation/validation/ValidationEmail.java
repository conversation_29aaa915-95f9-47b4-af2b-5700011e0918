package com.simnectz.soiplus.core.system.annotation.validation;

import com.simnectz.soiplus.core.system.validation.EmailValidation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({TYPE, FIELD, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = EmailValidation.class)
@Documented
public @interface ValidationEmail {

    String message() default "{INVALID_EMAIL_FORMAT}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
