package com.simnectz.soiplus.core.system.annotation.validation;

import com.simnectz.soiplus.core.system.validation.PhoneNumberValidation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({TYPE, FIELD, ANNOTATION_TYPE})
@Retention(RUNTIME)
@Constraint(validatedBy = PhoneNumberValidation.class)
@Documented
public @interface ValidationPhoneNumber {

    String message() default "{INVALID_PHONE_NUMBER_FORMAT}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
