package com.simnectz.soiplus.core.system.async;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.simnectz.soiplus.core.log.login.domain.entity.LoginLogEntity;
import com.simnectz.soiplus.core.log.login.service.LoginLogService;
import com.simnectz.soiplus.core.system.config.SystemProperties;
import com.simnectz.soiplus.core.system.utils.AddressUtils;
import com.simnectz.soiplus.core.system.utils.IpUtils;
import com.simnectz.soiplus.core.system.utils.ServletUtils;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.extern.slf4j.Slf4j;

import java.util.TimerTask;

@Slf4j
public class AsyncFactory {

	private static final SystemProperties systemProperties = SpringUtil.getBean(SystemProperties.class);

	/**
	 * 记录登录日志
	 */
	public static TimerTask loginRecord(String email, Integer status, String message, Object... args) {
		UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
		String clientIp = IpUtils.getIpAddr();
		return new TimerTask() {
			@Override
			public void run() {
				String logBuilder = "[" + email + " " + message + " " + clientIp + "]";
				log.info(logBuilder, args);
				if (systemProperties.getLoginLogEnable()) {
					LoginLogEntity loginLog = LoginLogEntity.builder()
							.email(email)
							.status(status)
							.loginTime(DateUtil.now())
							.browser(userAgent.getBrowser().getName())
							.ipAddr(clientIp)
							.loginLocation(AddressUtils.getRealAddressByIP(clientIp))
							.message(message)
							.os(userAgent.getOperatingSystem().getName())
							.build();
					SpringUtil.getBean(LoginLogService.class).saveLoginLog(loginLog);
				}
			}
		};
	}
}
