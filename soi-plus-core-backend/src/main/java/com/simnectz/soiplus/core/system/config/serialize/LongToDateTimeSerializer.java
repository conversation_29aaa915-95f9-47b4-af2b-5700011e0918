package com.simnectz.soiplus.core.system.config.serialize;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

public class LongToDateTimeSerializer extends StdSerializer<Long> {

    private static final long serialVersionUID = -2110190434532111406L;

    public LongToDateTimeSerializer() {
        super(Long.class);
    }

    @Override
    public void serialize(Long value, JsonGenerator generator, SerializerProvider provider) throws IOException {
        generator.writeString(DateUtil.now());
    }

}
