package com.simnectz.soiplus.core.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 注册平台枚举类
 */
@Getter
@AllArgsConstructor
public enum CardType implements EnumValidate {

    /**
     * 中国大陆身份证(15/18位)
     */
    CHINESE_MAINLAND_ID("1"),

    /**
     * 香港身份证(10位)
     */
    HONG_KONG_ID("2"),

    /**
     * 台湾身份证(10位)
     */
    TAIWAN_ID("3"),

    /**
     * 澳门身份证(10位)
     */
    MACAO_ID("4"),

    ;

    private final String value;

}
