package com.simnectz.soiplus.core.system.utils;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import com.simnectz.soiplus.core.system.constant.FieldLength;
import com.simnectz.soiplus.core.system.constant.RegexpConstant;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValidatorUtils {

    public static boolean validatePhoneNumber(String phoneNumber) {
        // 中国大陆手机号码
        Pattern chineseMainlandPattern = Pattern.compile(RegexpConstant.CHINESE_MAINLAND_PHONE_NUMBER_REGEXP);
        Matcher chineseMainlandMatcher = chineseMainlandPattern.matcher(phoneNumber);

        // 香港手机号码
        Pattern hongKongPattern = Pattern.compile(RegexpConstant.HONG_KONG_PHONE_NUMBER_REGEXP);
        Matcher hongKongMatcher = hongKongPattern.matcher(phoneNumber);

        // 台湾手机号码
        Pattern taiwanPattern = Pattern.compile(RegexpConstant.TAIWAN_PHONE_NUMBER_REGEXP);
        Matcher taiwanMatcher = taiwanPattern.matcher(phoneNumber);

        // 澳门手机号码
        Pattern macaoPattern = Pattern.compile(RegexpConstant.MACAO_PHONE_NUMBER_REGEXP);
        Matcher macaoMatcher = macaoPattern.matcher(phoneNumber);

        return chineseMainlandMatcher.matches()
                || hongKongMatcher.matches()
                || taiwanMatcher.matches()
                || macaoMatcher.matches()
                ;
    }

    public static boolean validateCardId(String cardType, String cardId) {
        if (StrUtil.isBlank(cardType) || StrUtil.isBlank(cardId)) {
            return false;
        }

        switch (cardType) {
            // 中国大陆身份证(15/18位)
            case "1":
                // 香港身份证(10位)
            case "2":
                // 台湾身份证(10位)
            case "3":
                // 澳门身份证(10位)
            case "4":
                return IdcardUtil.isValidCard(cardId);
            default:
                return false;
        }
    }

    public static boolean validateEmail(String email) {
        if (StrUtil.isBlank(email) || email.length() > FieldLength.EMAIL_MAX) {
            return false;
        }

        Pattern pattern = Pattern.compile(RegexpConstant.EMAIL_REGEXP);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }

    public static boolean validateUsername(String username) {
        if (StrUtil.isBlank(username) || username.length() < FieldLength.USERNAME_MIN || username.length() > FieldLength.USERNAME_MAX) {
            return false;
        }
        Pattern pattern = Pattern.compile(RegexpConstant.USERNAME_REGEXP);
        Matcher matcher = pattern.matcher(username);
        return matcher.matches();
    }

}
