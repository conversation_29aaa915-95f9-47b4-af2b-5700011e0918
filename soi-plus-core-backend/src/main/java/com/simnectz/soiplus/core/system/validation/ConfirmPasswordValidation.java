package com.simnectz.soiplus.core.system.validation;

import cn.hutool.core.util.StrUtil;
import com.simnectz.soiplus.core.authorize.domain.dto.UserRegisterDto;
import com.simnectz.soiplus.core.system.annotation.validation.ValidationConfirmPassword;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;


/**
 * Confirm password validator
 */
public class ConfirmPasswordValidation implements ConstraintValidator<ValidationConfirmPassword, UserRegisterDto> {

    @Override
    public void initialize(ValidationConfirmPassword constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(UserRegisterDto userInfo, ConstraintValidatorContext constraintValidatorContext) {
        String password = userInfo.getPassword();
        String confirmPassword = userInfo.getConfirmPassword();
        if (StrUtil.isBlank(password) || StrUtil.isBlank(confirmPassword)) {
            return true;
        }
        return password.equals(confirmPassword);
    }

}
