package com.simnectz.soiplus.core.system.validation;

import cn.hutool.core.util.StrUtil;
import com.simnectz.soiplus.core.system.annotation.validation.ValidationNickname;
import com.simnectz.soiplus.core.system.constant.RegexpConstant;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Username validator
 */
public class NicknameValidation implements ConstraintValidator<ValidationNickname, String> {


    @Override
    public void initialize(ValidationNickname constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(String nickname, ConstraintValidatorContext constraintValidatorContext) {
        if (StrUtil.isBlank(nickname)) {
            return true;
        }
        Pattern pattern = Pattern.compile(RegexpConstant.NICKNAME_REGEXP);
        Matcher matcher = pattern.matcher(nickname);
        return matcher.matches();
    }

}
