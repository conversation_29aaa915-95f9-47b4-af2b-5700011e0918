package com.simnectz.soiplus.core.template.resource;

import com.simnectz.soiplus.core.template.service.TemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


/**
 * Template controller
 */
@RestController
@RequestMapping("v1/system/template")
@RequiredArgsConstructor
public class TemplateResource {
    private final TemplateService templateService;

    /**
     * 下载模板文件
     */
    @GetMapping("/download")
    public void excelDownLoad(String fileName, HttpServletResponse response) {
        templateService.excelDownLoad(fileName, response);
    }

}
