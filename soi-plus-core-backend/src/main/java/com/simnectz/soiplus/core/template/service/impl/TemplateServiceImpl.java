package com.simnectz.soiplus.core.template.service.impl;

import com.simnectz.soiplus.core.system.config.SystemProperties;
import com.simnectz.soiplus.core.template.service.TemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

@Service
@RequiredArgsConstructor
public class TemplateServiceImpl implements TemplateService {

    private final SystemProperties systemProperties;

    @Override
    public void excelDownLoad(String fileName, HttpServletResponse response) {
        ResourceLoader resourceLoader = new DefaultResourceLoader();
        Resource resource = resourceLoader.getResource("classpath:" + systemProperties.getTemplatePath() + "/" + fileName);

        try (BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());
             InputStream inputStream = resource.getInputStream();
             BufferedInputStream bis = new BufferedInputStream(inputStream);
        ) {
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(resource.getFilename(), "UTF-8"));
            response.addHeader(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            bos.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
