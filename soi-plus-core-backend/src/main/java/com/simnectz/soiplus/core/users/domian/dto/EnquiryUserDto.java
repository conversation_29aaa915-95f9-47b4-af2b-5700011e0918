package com.simnectz.soiplus.core.users.domian.dto;

import com.simnectz.soiplus.core.system.domain.dto.PaginateEnquiry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * Paginate enquiry users request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EnquiryUserDto extends PaginateEnquiry implements Serializable {

    private static final long serialVersionUID = 7595000146182012344L;

    private String keywords;
    private Set<String> roleIds;
    private Integer status;
    private Integer blackList;

}
