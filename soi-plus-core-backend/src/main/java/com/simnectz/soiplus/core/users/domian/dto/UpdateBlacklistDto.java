package com.simnectz.soiplus.core.users.domian.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UpdateBlacklistDto implements Serializable {

    private static final long serialVersionUID = -3087353563417040168L;

    @NotBlank(message = "{USER_ID_IS_REQUIRED}")
    private String id;

    @NotNull(message = "{BLACKLIST_STATUS_IS_REQUIRED}")
    @Max(value = 1, message = "{INVALID_BLACKLIST_STATUS}")
    @Min(value = 0, message = "INVALID_BLACKLIST_STATUS")
    private Integer blackList;

}
