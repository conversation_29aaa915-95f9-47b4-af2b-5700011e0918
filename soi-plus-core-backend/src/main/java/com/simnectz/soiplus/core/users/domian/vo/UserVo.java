package com.simnectz.soiplus.core.users.domian.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.RelationManyToMany;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * User information response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UserVo implements Serializable {

    private static final long serialVersionUID = 1004771269003733548L;

    private String id;
    private String username;
    private String nickname;
    private String email;
    private Integer status;
    private Integer blackList;
    @RelationManyToMany(
            selfField = "id",
            targetTable = "system_role",
            targetField = "id",
            valueField = "roleName",
            joinTable = "system_user_role",
            joinSelfColumn = "user_id",
            joinTargetColumn = "role_id"
    )
    private Set<String> roles;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
