package com.simnectz.soiplus.license.info;

import com.simnectz.soiplus.license.utils.IpAddressUtils;

import java.net.InetAddress;
import java.util.List;
import java.util.stream.Collectors;

public abstract class AbstractServerInfo {

	/**
	 * 获取服务器参数
	 */
	public LicenseCheckInfo getServerInfo() {
		LicenseCheckInfo checkInfo = new LicenseCheckInfo();
		checkInfo.setIpAddress(this.getIpAddress());
		checkInfo.setMacAddress(this.getMacAddress());
		checkInfo.setCpuSerial(this.getCpuSerial());
		checkInfo.setMainBoardSerial(this.getMainBoardSerial());
		return checkInfo;
	}

	/**
	 * 获取CPU序列号
	 *
	 * @return CPU序列号
	 */
	protected abstract String getCpuSerial();


	/**
	 * 获取主板序列号
	 *
	 * @return 主板序列号
	 */
	protected abstract String getMainBoardSerial();

	/**
	 * 获取服务器Ip地址
	 * @return Ip地址
	 */
	private List<String> getIpAddress() {
		List<String> result = null;
		List<InetAddress> inetAddresses = IpAddressUtils.getLocalAllInetAddress();
		if (!inetAddresses.isEmpty()) {
			result = inetAddresses.stream()
					.map(InetAddress::getHostAddress)
					.distinct()
					.map(String::toLowerCase)
					.collect(Collectors.toList());
		}
		return result;
	}

	/**
	 * 获取服务器Mac地址
	 * @return Mac地址
	 */
	private List<String> getMacAddress() {
		List<String> result = null;
		List<InetAddress> inetAddresses = IpAddressUtils.getLocalAllInetAddress();
		if (!inetAddresses.isEmpty()) {
			result = inetAddresses.stream()
					.map(IpAddressUtils::getMacByInetAddress)
					.distinct()
					.map(String::toLowerCase)
					.collect(Collectors.toList());
		}
		return result;
	}
}

