package com.simnectz.soiplus.license.info;

import cn.hutool.core.util.StrUtil;
import com.simnectz.soiplus.license.utils.ShellUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

public class LinuxServerInfo extends AbstractServerInfo{
	@Override
	protected String getCpuSerial() {
		// 使用dmidecode命令获取CPU序列号
		String commend = "sudo dmidecode -t processor | grep 'ID' | awk -F ':' '{print $2}' | head -n 1";
		return getSerialNumber(commend);
	}

	@Override
	protected String getMainBoardSerial() {
		// 使用dmidecode命令获取主板序列号
		String commend = "sudo dmidecode | grep 'Serial Number' | awk -F ':' '{print $2}' | head -n 1";
		return getSerialNumber(commend);
	}

	private String getSerialNumber(String commend) {
		String serialNumber = "";
		Process process = ShellUtils.runLinuxShell(commend);
		try {
			BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
			String line = reader.readLine().trim();
			if (StrUtil.isNotBlank(line)) {
				serialNumber = line;
			}
			reader.close();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		return serialNumber;
	}
}
