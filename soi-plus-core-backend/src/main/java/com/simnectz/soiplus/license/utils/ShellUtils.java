package com.simnectz.soiplus.license.utils;

import java.io.IOException;

public class ShellUtils {
  public static Process runLinuxShell(String command) {
    String[] commandArray = new String[]{"/bin/sh", "-c", command};
    return runShell(commandArray);
  }

  public static Process runWindowsShell(String command) {
    String[] commandArray = new String[]{"cmd", "/c", command};
    return runShell(commandArray);
  }

  /**
   * 执行Shell
   *
   * @param commandArray shell脚本
   * @return 执行后返回值
   */
  public static Process runShell(String[] commandArray) {
    try {
      Process process = Runtime.getRuntime().exec(commandArray);
      process.waitFor();
      return process;
    } catch (IOException | InterruptedException e) {
      throw new RuntimeException(e);
    }
  }
}
