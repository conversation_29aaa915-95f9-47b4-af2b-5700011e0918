package com.simnectz.soiplus.license.verify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.simnectz.soiplus.core.system.exception.SystemException;
import com.simnectz.soiplus.core.system.utils.MessageUtils;
import com.simnectz.soiplus.license.constant.LicenseConstant;
import com.simnectz.soiplus.license.constant.LicenseRuleEnum;
import com.simnectz.soiplus.license.info.AbstractServerInfo;
import com.simnectz.soiplus.license.info.LicenseCheckInfo;
import com.simnectz.soiplus.license.info.LinuxServerInfo;
import com.simnectz.soiplus.license.info.WindowsServerInfo;
import com.simnectz.soiplus.license.properties.LicenseProperties;

import java.util.Date;
import java.util.List;

public class LicenseVerify {

	private static final LicenseProperties licenseProperties = SpringUtil.getBean(LicenseProperties.class);

	/**
	 * 校验是否合规
	 */
	public static boolean verify() {
		LicenseCheckInfo serverCheckInfo = getServerInfo();
		if (ObjectUtil.isEmpty(serverCheckInfo)) {
			// 不能获取服务器硬件信息
			throw new SystemException(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, LicenseConstant.Message.INVALID_SERVER_HARDWARE_INFORMATION);
		}
		Date now = DateUtil.date();
		if (StrUtil.isBlank(licenseProperties.getIssuedTime()) || StrUtil.isBlank(licenseProperties.getExpiryTime())) {
			// 授权时间异常
			throw new SystemException(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, MessageUtils.message(LicenseConstant.Message.INVALID_AUTHORIZATION_TIME));
		}
		// 校验时间
		if (DateUtil.compare(now, DateUtil.parse(licenseProperties.getIssuedTime())) < 0 || DateUtil.compare(now, DateUtil.parse(licenseProperties.getExpiryTime())) > 0) {
			// 当前时间不在授权范围内
			throw new SystemException(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, MessageUtils.message(LicenseConstant.Message.INVALID_CURRENT_TIME));
		}
		if (StrUtil.equals(licenseProperties.getRule(), LicenseRuleEnum.strict.name())) {
			// 严格模式：校验时间、ipAddress、cpuSerial、mainBoardSerial
			// 校验IP地址
			if (checkAddress(licenseProperties.getIpAddress(), serverCheckInfo.getIpAddress())) {
				// 当前服务器的IP没在授权范围内
				throw new SystemException(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, MessageUtils.message(LicenseConstant.Message.INVALID_CURRENT_IP));
			}
			// 校验Mac地址
			if (checkAddress(licenseProperties.getMacAddress(), serverCheckInfo.getMacAddress())) {
				// 当前服务器的Mac地址没在授权范围内
				throw new SystemException(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, MessageUtils.message(LicenseConstant.Message.INVALID_CURRENT_MAC_ADDRESS));
			}
			// 校验主板序列号
			if (!StrUtil.equals(licenseProperties.getMainBoardSerial(), serverCheckInfo.getMainBoardSerial())) {
				// 当前服务器的主板序列号没在授权范围内
				throw new SystemException(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, MessageUtils.message(LicenseConstant.Message.INVALID_CURRENT_MOTHERBOARD_SERIAL_NUMBER));
			}
			// 校验CPU序列号
			if (!StrUtil.equals(licenseProperties.getCpuSerial(), serverCheckInfo.getCpuSerial())) {
				// 当前服务器的CPU序列号没在授权范围内
				throw new SystemException(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, MessageUtils.message(LicenseConstant.Message.INVALID_CURRENT_CPU_SERIAL_NUMBER));
			}
		}
		return true;
	}

	/**
	 * 获取当前服务器需要额外校验的License参数
	 *
	 * @return LicenseCheckInfo
	 */
	private static LicenseCheckInfo getServerInfo() {
		// 操作系统类型
		String osName = System.getProperty("os.name").toLowerCase();
		AbstractServerInfo abstractServerInfo;
		// 根据不同操作系统类型选择不同的数据获取方法
		if (osName.startsWith("windows")) {
			abstractServerInfo = new WindowsServerInfo();
		} else if (osName.startsWith("linux")) {
			abstractServerInfo = new LinuxServerInfo();
		} else {
			//其他服务器类型
			abstractServerInfo = new LinuxServerInfo();
		}
		return abstractServerInfo.getServerInfo();
	}

	/**
	 * 校验当前服务器的IP/Mac地址是否在可被允许的IP范围内
	 */
	private static boolean checkAddress(List<String> expectedList, List<String> serverList) {
		if (CollUtil.isEmpty(expectedList)) {
			return false;
		}
		if (CollUtil.isEmpty(serverList)) {
			return true;
		}
		for (String expected : expectedList) {
			if (serverList.contains(expected.trim().toLowerCase())) {
				return false;
			}
		}
		return true;
	}
}
