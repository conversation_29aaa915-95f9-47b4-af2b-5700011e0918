import { del, get, post, put } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getPathList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/path', params)
}

export function getCourseDetails(courseCode) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/course/code-details/${courseCode}`)
}

export function batchDeletePath(ids) {
  return del(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/path', { ids })
}

export function getSubjectRecommend() {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/path/subject-recommend`)
}

export function subscribeCourse(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/path/subscription', data)
}

export function firstStudySubject(data) {
  return put(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/path/first-study', data)
}

export function finishedStudySubject(data) {
  return put(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/path/finished-study', data)
}
