import { get, post } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getCategory() {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/category')
}

export function getSubcategory(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/subcategory', params)
}

export function getQuestionTitles(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/titles', params)
}

export function getQuestionItem(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/item', params)
}

export function getQuestionAnswer(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/answer', params)
}

export function runCode(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/compiler', data)
}

export function saveAnswer(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/save-answer', data)
}

export function getQuestionAnswers(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/answers', params)
}

export function getQuestionByPage(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/page', params)
}

export function createQuestion(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/create', data)
}

export function updateQuestion(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/update', data)
}

export function deleteQuestion(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/coding-exercise/delete', data)
}
