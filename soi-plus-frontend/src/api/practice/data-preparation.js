import { get, post } from '@/api/utils/http'
import { DATA_PREPARATION_API_URL } from '@/contains'

export function oneKeyStart(data) {
  return post(DATA_PREPARATION_API_URL + '/dp/auto-process/', data)
}

export function label(data) {
  return post(DATA_PREPARATION_API_URL + '/dp/one-hot/', data)
}

export function cleansing(data) {
  return post(DATA_PREPARATION_API_URL + '/dp/clean/', data)
}

export function dimensionalityReduction(data) {
  return post(DATA_PREPARATION_API_URL + '/dp/dimensionality-reduction/', data)
}

export function pcaAnalysis(data) {
  return post(DATA_PREPARATION_API_URL + '/dp/pca/', data)
}

export function balancedDataSet(data) {
  return post(DATA_PREPARATION_API_URL + '/dp/sample-balance/', data)
}

export function getDirty(params) {
  return get(DATA_PREPARATION_API_URL + '/dp/dirty/', params)
}

export function getView(params) {
  return get(DATA_PREPARATION_API_URL + '/dp/view/', params)
}
