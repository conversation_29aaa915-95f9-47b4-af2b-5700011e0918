import { del, get, post, put } from '@/api/utils/http'
import { DATA_VISUALIZATION_API_URL, SOI_PLUS_BUSINESS_API_URL } from '@/contains'

// data source
export function getDataSourceList(params) {
  return get(DATA_VISUALIZATION_API_URL + '/api/v1/source', params)
}

export function testDataSourceCollect(data) {
  return post(DATA_VISUALIZATION_API_URL + '/api/v1/source/test', data)
}

export function addDataSource(data) {
  return post(DATA_VISUALIZATION_API_URL + '/api/v1/source', data)
}

export function updateDataSource(data) {
  return put(DATA_VISUALIZATION_API_URL + '/api/v1/source', data)
}

export function checkDataSourceName(data) {
  return post(DATA_VISUALIZATION_API_URL + '/api/v1/check/name', data)
}

export function deleteDataSource(dataSourceId, userId) {
  return del(DATA_VISUALIZATION_API_URL + `/api/v1/source/${dataSourceId}/${userId}`)
}

export function getDataSourceInfo(dataSourceId, userId) {
  return get(DATA_VISUALIZATION_API_URL + `/api/v1/source/getSourceInfo/${dataSourceId}/${userId}`)
}

// data table
export function getDataTableList(params) {
  return get(DATA_VISUALIZATION_API_URL + '/api/v1/view', params)
}

export function deleteDataTable(dataTableId, userId) {
  return del(DATA_VISUALIZATION_API_URL + `/api/v1/view/${dataTableId}/${userId}`)
}

export function getDataTableInfo(dataTableId, userId) {
  return get(DATA_VISUALIZATION_API_URL + `/api/v1/view/getViewInfo/${dataTableId}/${userId}`)
}

export function getDatabase(dataSourceName) {
  return get(DATA_VISUALIZATION_API_URL + `/api/v1/view/getdatabases/${dataSourceName}`)
}

export function getTables(dataSourceName, databaseName) {
  return get(DATA_VISUALIZATION_API_URL + `/api/v1/view/${dataSourceName}/${databaseName}`)
}

export function previewData(data) {
  return post(DATA_VISUALIZATION_API_URL + '/api/v1/view/previewData', data)
}

export function createDataTable(data) {
  return post(DATA_VISUALIZATION_API_URL + '/api/v1/view/new', data)
}

export function getFieldGroup(data) {
  return post(DATA_VISUALIZATION_API_URL + '/api/v1/view/getFieldGroup', data)
}

export function getData(dataTableId, data) {
  return post(DATA_VISUALIZATION_API_URL + `/api/v1/view/getdata/${dataTableId}`, data)
}

export function getColumns(dataTableId, userId) {
  return get(DATA_VISUALIZATION_API_URL + `/api/v1/view/getViewInfo/${dataTableId}/${userId}`)
}

export function getPreviewData(data) {
  return post(DATA_VISUALIZATION_API_URL + '/api/v1/view/getPreviewData', data)
}

export function getEnum(data) {
  return post(DATA_VISUALIZATION_API_URL + `/api/v1/view/enum`, data)
}

export function getBucketingField(data) {
  return post(DATA_VISUALIZATION_API_URL + `/api/v1/view/bucketingField`, data)
}

// my charts
export function getChartsList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-analysis/chart/history', params)
}

export function deleteChartsById(chartId) {
  return del(SOI_PLUS_BUSINESS_API_URL + `/v1/practical/data-analysis/chart/delete/${chartId}`)
}

export function getChartInfo(chartId) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/practical/data-analysis/chart/${chartId}`)
}

export function saveChart(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-analysis/chart', data)
}

export function updateChart(chartId, userId, data) {
  return post(SOI_PLUS_BUSINESS_API_URL + `/v1/practical/data-analysis/chart/update/${chartId}/${userId}`, data)
}
