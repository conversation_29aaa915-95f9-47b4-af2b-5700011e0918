import { DATA_CREATION_API_URL } from '@/contains'

export function exportCustomerList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/customer/export?${queryString}`)
}

export function exportTransferList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/deposit/export?${queryString}`)
}

export function exportPaymentList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/payment/export?${queryString}`)
}

export function exportCreditCardList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/creditcard/export?${queryString}`)
}

export function exportTermDepositList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/termdeposit/export?${queryString}`)
}

export function exportForeignExchangeList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/foreignexchange/export?${queryString}`)
}

export function exportStockTradingList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/stock/export?${queryString}`)
}

export function exportAvailableStockCode(hasData) {
  window.open(`${DATA_CREATION_API_URL}/stock/exportAvailableStockCode?hasData=${hasData}`)
}

export function exportFundTradingList(params) {
  const queryString = filterParams(params)
  window.open(`${DATA_CREATION_API_URL}/fund/export?${queryString}`)
}

export function exportAvailableFundCode(hasData) {
  window.open(`${DATA_CREATION_API_URL}/fund/exportAvailableFundCode?hasData=${hasData}`)
}

function filterParams(params) {
  // Filter out fields with null or empty values
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([key, value]) => value !== null && value !== '')
  )
  // Convert filtered params object to query string
  return new URLSearchParams(filteredParams).toString()
}
