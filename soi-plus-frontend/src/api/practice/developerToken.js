import { get, post } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getUserDeveloperToken(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/oauth2/client/token', params)
}

export function clientCredentials(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/oauth2/client_credentials', params)
}

export function registerClient(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/oauth2/register_client', data)
}

export function archiveClient(clientId) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/oauth2/archive_client/${clientId}`)
}
