import { del, get } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getQuestionBankList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/question-bank', params)
}

export function batchDeleteQuestions(ids) {
  return del(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/question-bank', { ids })
}

export function getDetailByQuestionId(id) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/question-bank/question-id/${id}`)
}

