import { del, get, post, put } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getSubjectOptions() {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/subject/options')
}

export function getQuestionList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/question-bank', params)
}

export function createQuestion(data) {
  console.log(data)
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/question-bank', data)
}

export function updateQuestion(data) {
  return put(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/question-bank', data)
}

export function getQuestionDetails(subjectCode) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/question-bank/subject-code/${subjectCode}`)
}

export function batchDeleteQuestion(ids) {
  return del(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/question-bank', { ids })
}
