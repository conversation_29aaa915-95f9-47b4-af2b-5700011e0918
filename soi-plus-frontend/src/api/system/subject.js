import { del, get, post, put } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getCourseOptions() {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course/options')
}

export function getSubjectList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/subject', params)
}

export function createSubject(data) {
  console.log(data)
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/subject', data)
}

export function updateSubject(data) {
  return put(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/subject', data)
}

export function getSubjectDetails(subjectCode) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/subject/subject-code/${subjectCode}`)
}

export function batchDeleteSubject(ids) {
  return del(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/subject', { ids })
}

export function getSubjectByCourseIds(courseIds) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/subject/course-ids/${courseIds}`)
}
