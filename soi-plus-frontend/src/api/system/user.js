import { get, post, del, put } from '@/api/utils/http'

export function getInfo() {
  return get('/v1/system/users/me')
}

export function getUserList(params) {
  return get('/v1/system/users', params)
}

export function batchDeleteUser(userIds) {
  return del('/v1/system/users', { userIds })
}

export function addBlacklist(data) {
  return put('/v1/system/users/blacklist', data)
}

export function createUser(data) {
  return post('/v1/system/users', data)
}

export function updateUser(data) {
  return put('/v1/system/users', data)
}

export function getUserDetails(id) {
  return get(`/v1/system/users/${id}`)
}

export function importUser(data) {
  return post('/v1/system/users/import', data, { headers: { 'Content-Type': 'multipart/form-data' }})
}
