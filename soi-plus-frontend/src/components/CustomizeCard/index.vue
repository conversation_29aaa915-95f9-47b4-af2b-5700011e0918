<template>
  <div class="app-container customize-card-container">
    <el-card shadow="never" :body-style="bodyStyle">
      <div slot="header" class="clearfix">
        <div class="header-content">
          <span>{{ title }}</span>

          <div class="header-actions">
            <el-button v-if="$route.meta.document" size="mini" @click="openDocument($route.meta.document)">{{ $t('soi.common.helpDocument') }}</el-button>
            <slot name="header-actions" />
            <el-button v-if="showBackBtn" size="mini" @click="$router.go(-1)">{{ $t('soi.common.back') }}</el-button>
          </div>
        </div>
      </div>
      <slot />
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'CustomizeCard',
  props: {
    title: {
      type: String,
      required: true
    },
    bodyStyle: {
      type: Object,
      required: false,
      default: () => {}
    },
    showBackBtn: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  computed: {
    ...mapGetters(['language'])
  },
  methods: {
    openDocument(document) {
      window.open(document[this.language], '_blank')
    }
  }
}
</script>
<style lang="scss">
.customize-card-container {
  .el-card__header {
    background: #109eae;
    color: #ffffff;
    padding: 12px 20px;
    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-actions {
        .el-button {
          color: #707070 !important;
          background-color: #fff !important;
          border-color: #fff !important;
        }
      }
    }
  }
}
</style>
