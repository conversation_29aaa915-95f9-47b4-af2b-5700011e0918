// 数据可视化API地址
const DOMAIN = process.env.VUE_APP_DOMAIN
const SERVER_IP = process.env.VUE_APP_SERVER_IP
export const DATA_VISUALIZATION_API_URL = DOMAIN + '/visualization'
// 机器学习API地址
export const MACHINE_LEARNING_API_URL = DOMAIN + '/ml'
// export const MACHINE_LEARNING_API_URL = 'http://localhost:8954'
// JUPYTER 部署地址
export const JUPYTER_URL = 'http://' + SERVER_IP + ':8999'
// 官网地址
export const OFFICIAL_WEBSITE_URL = DOMAIN
// 考试平台地址自动登录地址
export const EXAM_PLATFORM_AUTO_LOGIN_URL = DOMAIN + '/exam-platform/autologin'
// 实践平台和学习空间API地址
export const SOI_PLUS_BUSINESS_API_URL = DOMAIN + '/soi-plus-business'
// export const SOI_PLUS_BUSINESS_API_URL = 'http://127.0.0.1:8300'
export const SOI_PLUS_CORE_API_URL = DOMAIN + '/soi-plus-core'
// export const SOI_PLUS_CORE_API_URL = 'http://127.0.0.1:8200'
export const WORKFLOW_API_URL = DOMAIN + '/workflow'
// 三方保险系统登录页面
export const VIS_URL = DOMAIN + '/visfrontend/GoToLogin/index'
// FDC自动登录
export const FDC_URL = DOMAIN + '/fdcfrontend/auto-login'
// 虚拟银行系统登录页面
export const HK_VBS_URL = DOMAIN + '/vbsfrontend/lbslogin'
export const CN_VBS_URL = DOMAIN + '/vbsfrontend/cn/lbs/login'
// mBridge 登录页面
export const CN_M_BRIDGE_URL = DOMAIN + '/virtual-bank-system-cn/login'
export const HK_M_BRIDGE_URL = DOMAIN + '/virtual-bank-system-hk/login'
export const TH_M_BRIDGE_URL = DOMAIN + '/virtual-bank-system-th/login'
export const UAE_M_BRIDGE_URL = DOMAIN + '/virtual-bank-system-uae/login'
// 默认的LBS Token
export const DEFAULT_LBS_TOKEN = 'eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA'
// 数据生成工具API地址
export const DATA_CREATION_API_URL = DOMAIN + '/datagenerate'
// 数据处理工具API地址
// export const DATA_PREPARATION_API_URL = DOMAIN + '/dataprocessing'
export const DATA_PREPARATION_API_URL = 'http://**************:8000'
// 供应链金融登录页面
export const SUPPLY_CHAIN_FINANCE_URL = DOMAIN + '/scf-ui/'
// 虚拟信用卡系统登录页面
export const VIRTUAL_CREDIT_CARD_SYSTEM_URL = DOMAIN + '/virtual-credit-card-system/authorize/login'
// 文档地址
export const DOCUMENT_URL = DOMAIN + '/document/soi-plus'
// language server websocket url
export const LANGUAGE_SERVER_WS_URL = process.env.VUE_APP_LANGUAGE_SERVER_WS_URL
