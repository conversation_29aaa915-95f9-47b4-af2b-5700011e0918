{"soi": {"apiAccessGuide": {"description1": "對於開發人員集成或從給定API獲取資源，需要授予他們存取權限。 各種API提供者對其API發起各種控制，這意味著只有具有有效憑據的人才能訪問這些資源。 囙此，API訪問是確保只有具有身份驗證憑據的用戶才能訪問API並使用其資源的過程。", "description10": "一旦獲得批准，開發人員將能够訪問帳戶資訊，如帳號、餘額、交易等。", "description11": "作為此過程的一部分，您必須從每個用戶獲得權限。 如果銀行給你API存取權限，並不意味著你可以立即訪問和利用他們的所有數據。 客戶必須同意為此目的使用其數據。", "description12": "對於注册到SOI+API門戶平臺的所有開發人員，您將自動從SOI+虛擬金融系統分配10個類比客戶帳戶。 您將能够通過API市場上發佈的API訪問這些類比客戶數據。", "description13": "在SOI+API門戶/市場上注册的所有API採用了相同的方法在API訪問過程中。 標準的OAuth2.0用作客戶允許開發者訪問其帳戶的授權方法。", "description14": "要訪問虛擬金融系統中的類比客戶數據，您需要獲得以下令牌：", "description15": "開發者令牌（訪問令牌）", "description16": "客戶令牌", "description2": "API訪問通過API管理實現。 API管理用於允許或拒絕准入的覈心功能之一是API閘道。 閘道用於使API調用能够被適當地接收和處理。", "description3": "開發人員需要首先在API提供者建立的API門戶/閘道上注册自己。 使用有效憑據，您現在可以開始訪問API。", "description4": "為了獲得對API的訪問權，您需要輸入一個API金鑰來證明您的身份。 API金鑰基本上是一個真實的字母和數位字串。 開發人員需要向API提供程式注册以獲取API金鑰。", "description5": "對於開放銀行API，所有API都受到保護並高度安全。 銀行和TSP可以應用行業標準的OAuth2.0框架作為客戶的授權方法，以允許TSP訪問其帳戶。", "description6": "OAuth 2.0是一個行業標準授權框架，使銀行能够授予銀行開放API的數據存取權限。", "description7": "訪問令牌用於基於令牌的身份驗證，以使授權的TSP能够基於OAuth 2.0和Open ID Connect框架安全地訪問銀行業開放API。", "description8": "在API市場注册的所有API均採用OAuth2.0作為標準授權框架。", "description9": "開放銀行API通過允許授權用戶（例如開發人員）訪問有關銀行客戶帳戶的特定資訊來工作。 為了做到這一點，開發商必須首先注册，然後獲得銀行的準予。", "subTitle1": "OAuth2.0 認證過程", "subTitle2": "開放銀行API訪問", "subTitle3": "開放銀行API是如何工作的？", "subTitle4": "在SOI+平臺中的API門戶/市場中訪問API"}, "apiArchitectureDesign": {"connectivityDescriptionKey": "訪問源數據：", "connectivityDescriptionValue": "從系統、存儲庫或外部服務", "connectivityTitle": "系統層API", "description1": "API引導的連接是指使用可重用和設計良好的API來連結數據和應用程序的科技。", "description2": "API引導的集成，允許企業構建可組合的構建塊，這些構建塊可以被公開並且應用於創新業務能力的創建。", "description3": "這種方法使開發人員能够分解資料倉庫，並通過自助服務和重用實現增强交付。 開發人員可以實踐API管理，以優化開放內容的使用。", "description4": "在以API為主導的連接中，創建了不同的API層級，以照顧各種系統、流程和最終用戶體驗。", "interfaceDescriptionKey": "數據呈現：", "interfaceDescriptionValue": "安全和治理", "interfaceTitle": "體驗層API", "orchestrationDescriptionKey": "邏輯的應用：", "orchestrationDescriptionValue": "豐富與轉換", "orchestrationTitle": "業務層API", "primaryTitle": "以 API 主導的構建塊"}, "apiList": {"addApi": "新增 API", "addApiTip": "此頁面用於添加API，然後點擊創建API。", "addEndpoint": "新增終結點", "apiIntroduction": "API 介紹", "apiName": "API 名稱", "apiNameEn": "API 英文名稱", "apiNameEnIsRequired": "API 英文名稱不能為空", "apiNameExist": "API 名稱已存在", "apiNameIsRequired": "API 中文名稱不能為空", "apiNameZh": "API 中文名稱", "apiSettings": "API 設定", "className": "分類名稱", "classNameIsRequired": "分類名稱不能為空", "clickUpload": "點擊上傳", "confirmDeleteTip": "確認删除嗎？", "createApi": "創建API", "createSuccess": "創建成功", "deleteSuccess": "删除成功", "description": "描述", "descriptionEn": "API 英文描述", "descriptionZh": "API 中文描述", "editApi": "編輯API", "editSuccess": "編輯成功", "endpoint": "終結點", "endpointDesigner": "定義", "image": "圖片", "importApi": "導入 API", "jsonErrorTip": "不是有效的JSON檔案，請檢查並再次上傳。", "method": "請求方式", "subclassName": "子分類名稱", "subclassNameIsRequired": "子分類名稱不能為空", "swaggerJsonTipEn": "請將您的英文版swagger json完全粘貼到下麵的框中。", "swaggerJsonTipZh": "請將您的中文版swagger json完全粘貼到下麵的框中。", "swaggerSettings": "Swagger設定", "targetUrl": "目標地址", "targetUrlIsRequired": "目標地址不能為空", "targetUrlTip": "以 http:// 或 https:// 開頭", "tryoutApi": "試用 API", "uploadFailed": "上傳失敗", "uploadSuccess": "上傳成功", "urlOrFile": "API用戶指南的URL或者上傳文件", "userGuide": "用戶指南", "valueIsRequired": "請輸入swagger json"}, "apiMarketPlace": {"apiIntroduction": "API 介紹", "apiName": "API 名稱", "apiProvider": "API 供應商", "apiStatus": "API 狀態", "catalogName": "目錄名稱", "customApiFromOthers": "來自其他用戶的API", "customApiSelf": "您的API", "deleteApiTip": "您確定要删除該API嗎？", "deleteSuccess": "删除API成功", "downloadSwagger": "下載 Swagger", "generateToken": "生成令牌", "sandboxToken": "沙箱令牌", "tryoutApi": "試用 API", "userGuide": "用戶指南", "username": "用戶名"}, "apis": {"category": "分類", "categoryIsRequired": "分類不能為空", "className": "分類中文名稱", "classNameEn": "分類英文名稱", "classNameEnIsRequired": "分類英文名稱不能為空", "classNameIsRequired": "分類中文名稱不能為空", "classSubName": "子分類中文名稱", "classSubNameEn": "子分類英文名稱", "classSubNameEnIsRequired": "子分類英文名稱不能為空", "classSubNameIsRequired": "子分類中文名稱不能為空", "createApiCategory": "創建API分類", "createApiSubcategory": "創建API子分類", "deleteApiClassTip": "此操作將删除分類，是否繼續？", "description": "中文描述", "descriptionEn": "英文描述", "descriptionEnIsRequired": "英文描述不能為空", "descriptionIsRequired": "中文描述不能為空", "image": "圖片", "updateApiCategory": "編輯API分類", "updateApiSubcategory": "編輯API子分類"}, "applyCustomerDataToken": {"applyFailedTip": "申請失敗", "applyNewToken": "申請新的令牌", "applySandboxTokenFailedTip": "上一個沙箱令牌應用程序失敗，請重新提交請求。", "applySubmitSuccessTip": "您的請求已提交，請按一下“檢查任務狀態”按鈕查看任務的進度。", "applySuccessTip": "申請成功", "checkStatus": "檢查任務狀態", "correctNumberApplications": "請輸入正確的申請數量", "correctNumberRange": "該數量在1-{max}之間", "customerNumber": "客戶編號", "loginName": "登入名稱", "loginPassword": "登入密碼", "maxSandboxNumber": "剩餘可申請Sandbox Data額度為：{max}", "number": "數量", "waitingCreationCompleteTip": "正在創建沙箱數據，請等待完成。"}, "authorize": {"activateTip": "請登入電子郵箱並點擊連結啟動", "agreeEndUserPrivacyPolicy": "同意最終用戶隱私策略", "agreePrivacyPolicyTip": "請先閱讀並檢查隱私條款", "birthday": "出生日期", "birthdayPlaceholder": "請選擇你的出生日期", "captcha": "驗證碼", "captchaIsRequired": "驗證碼不能為空", "cardId": "證件號碼", "cardIdIsRequired": "證件號碼不能為空", "cardIdPlaceholder": "請輸入你的證件號碼", "cardType": "證件類型", "cardTypeIsRequired": "證件類型不能為空", "cardTypePlaceholder": "請選擇你的證件類型", "confirmPassword": "確認密碼", "confirmPasswordIsRequired": "請再次輸入你的登入密碼", "confirmPasswordPlaceholder": "請再次輸入你的登入密碼", "courseManagement": "課程管理", "department": "部門", "departmentPlaceholder": "請輸入你的部門", "email": "電子郵箱", "emailIsRequired": "電子郵箱不能為空", "emailPlaceholder": "請輸入你的電子郵箱", "emailSendSuccess": "電子郵件發送成功", "experienceYear": "工作年限", "experienceYearPlaceholder": "請輸入你的工作年限", "forgotPassword": "忘記密碼", "graduateSchool": "畢業學校", "graduateSchoolPlaceholder": "請輸入你的畢業學校", "invalidCardIdFormat": "無效的證件號碼格式", "invalidConfirmPasswordFormat": "兩次輸入的密碼不一致", "invalidEmailFormat": "無效的電子郵箱格式", "invalidNicknameFormat": "無效的昵稱格式，長度為2-64個字元", "invalidPasswordFormat": "至少要包含大小寫字母和數位，且長度為8-16個字元", "invalidPhoneNumberFormat": "無效的手機號碼格式", "invalidUsernameFormat": "無效的用戶姓名格式，長度為2-64個字元", "jobTitle": "職務", "jobTitlePlaceholder": "請輸入你的職務", "learningSpace": "學習空間", "level": "用戶級別", "levelIsRequired": "用戶等級不能為空", "levelPlaceholder": "請輸入級別", "login": "登入", "loginNow": "現在登入", "logout": "登出", "newAccountPleaseFirstRegister": "新用戶請先註冊", "newPassword": "新密碼", "nickname": "昵稱", "nicknameIsRequired": "昵稱不能為空", "nicknamePlaceholder": "請輸入你的昵稱", "organization": "組織名稱", "organizationPlaceholder": "請輸入你的組織名稱", "password": "密碼", "passwordIsRequired": "密碼不能為空", "passwordPlaceholder": "請輸入你的登入密碼", "phoneNumber": "手機號碼", "phoneNumberIsRequired": "手機號碼不能為空", "phoneNumberPlaceholder": "請輸入你的手機號碼", "practicePlatform": "實踐平臺", "proficiencyTest": "能力測試", "register": "注册", "registerSuccess": "注册成功", "resetPassword": "重置密碼", "resetPasswordTip": "請轉到電子郵箱並按一下連結進行身份驗證，連結有效期為5分鐘", "sendEmail": "發送电子郵件", "setNewPassword": "設定新密碼", "sex": "性別", "sexPlaceholder": "請選擇你的性別", "simnectzOfficialWebsite": "開連智慧官網", "soiPlatform": "數位金融學創平臺", "studentOrStaffNumber": "學/工號", "studentOrStaffNumberPlaceholder": "請輸入你的學/工號", "systemManagement": "系統管理", "username": "用戶姓名", "usernameIsRequired": "用戶姓名不能為空", "usernamePlaceholder": "請輸入你的姓名"}, "autoMachineLearning": {"accuracy": "精度", "adaboostTip": "adaboost是集成學習的一種，可用於分類和回歸。 其覈心思想是針對同一個訓練集訓練不同的分類器（弱分類器），然後把這些弱分類器集合起來，構成一個更强的最終分類器（强分類器）。", "addModel": "添加模型", "addSubTable": "添加子錶", "addSubTableLabel": "可選，從原始數據中抽取的子錶，可以有多個。 如果需要對當前數據生成更多的特徵，可從主錶中抽取一部分列，生成子錶，與主錶建立關聯，featuretools可以自動生成更多特徵。", "addTransposeSubTable": "添加反轉子錶", "addTransposeSubTableContent": "必選，需要列轉行的字段", "addTransposeSubTableLabel": "從原始數據中通過列轉行抽取的子錶，可以有多個，可選。 可從原始數據中選擇一部分列，通過列轉行操作生成子錶，再與主錶建立關聯，featuretools可以自動生成更多特徵。 現時該類子錶都是通過“索引列”與主錶關聯，主錶未指定“索引列”時，會生成一個“索引列”。", "additionalVariables": "其他字段", "ardRegressionTip": "是線性回歸方法的一種。 用於處理回歸問題。", "autoSklearnParams": "Auto Sklearn 參數", "autoSklearnPlatform": "AutoSklearn 平臺", "batchPredict": "批量預測", "batchPredictTips": "根據您上傳的數據量大小，預測所需時間大致為：3000條數據預測耗時約5分鐘； 8000條數據預測耗時約30分鐘； 20000條數據預測耗時約90分鐘。 提交文件之後，預測過程不可中斷，當前頁面不可關閉。", "build": "構建", "buildSuccess": "構建成功", "clickUpload": "點擊上傳", "columnPlaceholder": "請選擇", "createTableTip": "<p>根據您上傳的資料字典，系統將建立一個名為 <i><b>'prediction_result'</b></i> 的資料表。您可以選擇該資料表以進行預測結果的分析。</p>", "customize": "自定義", "dRFTip": "一個隨機森林算灋，可用於分類和回歸。 是對數據樣本及特徵隨機抽取，進行多個決策樹訓練，防止過擬合，提高泛化能力。", "dataDictFile": "數據字典文件", "dataFile": "數據文件", "dataManagement": "數據管理", "dataStatisticsCharts": "數據統計圖", "dataStatisticsTable": "數據統計表", "decisionTreeTip": "decision_tree是一種基本的分類與回歸方法，分類速度快。 學習時，利用訓練數據，根據損失函數最小化原則建立決策樹模型。", "deeplearningTip": "引入了一種基於隨機網格的深度神經網路算灋。", "default": "默認", "deleteSuccessTip": "删除成功", "deleteTestFileTip": "您確定要删除此測試文件嗎？", "deleteTip": "您確定要删除這些數據嗎？", "descriptionPlaceholder": "請輸入此文件的描述", "dragFileHere": "將文件拖到此處，或", "earlyStoppingRounds": "在訓練的模型數量不會再新增的時候，用於停止訓練新的模型。", "editModel": "編輯模型", "ensembleNbest": "通過 ensemble_nbest 來選擇出集成的模型。", "ensembleSize": "從模型庫中選擇出模型的集成數量。", "excludeEstimators": "需要排除的算灋。", "excludeTheseAlgorithms": "不用於模型構建的算灋。", "extraTreesTip": "ET或Extra-Trees極端隨機樹，可用於分類和回歸。 算灋與隨機森林算灋十分相似，都是由許多決策樹構成。", "fileDetails": "文件詳情", "fillType": "處理缺失數據，補0或者删除該行。", "framework": "框架", "gBMTip": "gradient_boosting梯度提升樹算灋，是集成學習的一種，相比較adaboost模型，其抗譟音的能力更强，但是實現較慢。", "gLMTip": "廣義線性模型，是一類算灋的統稱，可以用來處理分類和回歸問題。 其中最常用的有邏輯回歸和泊松回歸。", "gaussianNbTip": "基於貝葉斯定理與特徵條件獨立假設的分類方法。", "gaussianProcessTip": "高斯過程。 一般是為數據的回歸值建立聯合分佈，來處理回歸問題。", "gradientBoostingTip": "梯度提升樹算灋，是集成學習的一種，相比較adaboost模型，其抗譟音的能力更强，但是實現較慢。", "h2OParams": "H2O 參數", "h2OPlatform": "H2O 平臺", "h2oBuildResult": "H2O 構建結果", "ignoreColumn": "忽略列", "index": "索引", "indexCanNotBeEmpty": "索引列不能為空", "indexColumn": "索引列", "indexLabel": "必選，子錶索引列，通過該列與主錶建立關聯", "info": "訊息", "initailConfigurationsViaMetalearning": "用配寘項來初始化超參數算灋。", "kNearestNeighborsTip": "k近鄰算灋（KNN），是一類可以被應用於分類或者回歸的科技。 它的適用面很廣，並且在樣本量足够大的情况下準確度很高。", "liblinearSvrTip": "線性支持向量回歸。 適合大量的資料處理，用於處理回歸問題。", "libsvmSvcTip": "基於支持向量機的分類方法。", "libsvmSvrTip": "支持向量回歸，處理回歸問題。", "maxModelsToBuild": "用於指定除了Stacked Ensemble模型之外的，構建模型的數量。", "maxRuntimeSecs": "在訓練Stacked Ensemble模型之前，訓練模型使用的時間。", "modelDetails": "模型詳情", "modelManagement": "模型管理", "modelName": "模型名稱", "modelPerformanceCharts": "模型效能圖", "modelPlaceholder": "請輸入此模型的描述", "modelStatisticsTable": "模型統計表", "modelType": "模型類型", "name": "名稱", "nfolds": "k-fold 交叉驗證的折疊次數。", "noPerformanceChart": "暫無效能圖", "numericalClassification": "數值分類", "numericalPrediction": "數值回歸", "onlyXlsxFiles": "只能上傳xlsx / xls文件", "other": "可選，子錶其餘字段", "perRunTimeLimit": "單次調用機器學習模型的時間限制（以秒為組織）。 如果機器學習算灋運行超過時間限制，模型擬合將終止。 將此值設定得足够高，以便典型的機器學習算灋可以適合訓練數據。", "pleasUploadFileFirst": "請先上傳文件", "pleaseSelectAdataType": "請選擇數據類型", "pleaseWaitTip": "請等待文件上傳完成並獲取列訊息", "predict": "預測", "predictSuccess": "預測成功", "randomForestTip": "隨機森林算灋，可用於分類和回歸。 是對數據樣本及特徵隨機抽取，進行多個決策樹訓練，防止過擬合，提高泛化能力。", "ratios": "訓練數據的切分率（訓練/測試）。", "resamplingStrategy": "防止過度擬合的時候，需要用到的參數，holdout是切分數據的管道，指訓練數據和測試數據的切分比例。", "responseColumn": "預測列", "resultFile": "預測結果文件", "ridgeRegressionTip": "嶺回歸是線性回歸的一種，用於處理回歸問題，可以用來解决標準線性回歸的過擬合問題。", "rulesTip1": "請輸入正整數", "rulesTip2": "請輸入大於零小於1的小數", "rulesTip3": "請輸入一個大於30正整數", "saveSuccessTip": "保存成功", "saveTopnModels": "選擇最後需要保存的top n個模型。", "seed": "SMAC 的種子，將决定輸文件名。", "selectResponseColumnTip": "請選擇預測列", "selectTextColumnTip": "請選擇文件列", "setColumnRoles": "設定欄位角色", "settingColumnsType": "設定列類型", "sgdTip": "隨機梯度下降，每次的權重更新只利用數據集中的一個樣本來完成，也即一個epoch過程只有一次反覆運算和一個更新數據。 對雜訊比較敏感。", "sortMetric": "top模型的排序規則。 默認為AUTO，指的是在二分類時選擇ACU，多分類的時候選擇mean_per_class_error，回歸的時候選擇deviance。", "stackedEnsembleTip": "利用集成學習的方法，將所有模型利用stack方法進行集合。", "status": "狀態", "testFile": "測試文件", "textClassification": "文本分類", "textColumn": "文本列", "thePredictionIs": "預測結果", "timeLeftForThisTask": "用於蒐索適當模型的時間（以秒為組織）。 通過新增這個值，有更高的機會找到更好的模型。", "trainSize": "resampling_strategy 的參數，指訓練數據的切分比例。", "transformType": "數據預處理的方式。", "type": "類型", "updateModelSuccess": "更新成功", "updateModelTip": "您確定要更新此模型嗎？", "uploadAcceptFileType": "只能上傳.csv或者.txt文件", "uploadData": "上傳數據", "uploadDataDict": "上傳數據字典", "uploadFailTip": "上傳文件失敗", "uploadFailed": "上傳失敗，請重試", "uploadFileFirstTip": "請先上傳文件", "uploadFirstTip": "將文件拖到此處，或者", "uploadLastTip": "點擊上傳", "uploadSuccess": "上傳成功", "uploadSuccessTip": "上傳文件成功", "uploadTestFile": "上傳測試文件", "uploadTime": "上傳時間", "xgradientBoostingTip": "Xgboost算灋，同樣可以應用於分類與回歸問題。 XGBoost的特點就是計算速度快，模型表現好。"}, "codingPractice": {"addCodingQuestion": "新建編碼題", "addQuestionTip": "您確認要創建編碼題嗎？", "apiInfo": "API 信息", "apiInformation": "API信息（中文）", "apiInformationEn": "API信息（英文）", "apiInformationTip": "請輸入API信息，包含API的名稱、URL、請求頭信息、請求參數和響應信息。（中文）", "apiInformationTipEn": "請輸入API信息，包含API的名稱、URL、請求頭信息、請求參數和響應信息。（英文）", "codeDescription": "代碼描述（中文）", "codeDescriptionEn": "代碼描述（英文）", "codeDescriptionTip": "請輸入代碼的描述信息（中文）", "codeDescriptionTipEn": "請輸入代碼的描述信息（英文）", "compareAnswers": "對比答案", "coreCodePattern": "覈心代碼模式", "coreCodePatternTip": "程式碼框中預設程式碼已經指定好類名、方法名、參數名，請勿修改或重新命名，直接返回值即可", "creator": "創建人", "databaseInformation": "數據庫信息（中文）", "databaseInformationCoding": "數據庫信息", "databaseInformationEn": "數據庫信息（英文）", "databaseInformationTip": "請輸入數據庫信息（中文）", "databaseInformationTipEn": "請輸入數據庫信息（英文）", "deleteQuestionTip": "您確認要删除編碼題嗎？", "description": "描述", "downloadCodeTip": "不能下載空文件", "editCodingQuestion": "編輯編碼題", "fontSize": "字體大小", "history": "練習歷史", "initCodeDescription": "初始代碼描述", "initialCode": "初始代碼", "initialCodeTip": "請輸入初始代碼", "modelAnswer": "模型答案", "modelAnswerTip": "請輸入模型答案", "practiceQuestionsCategory": "練習題分類", "printOutputTip": "沒有輸出語句，請嘗試列印結果。", "programmingLanguage": "編程語言", "questionCategory": "分類", "questionCategoryTip": "請選擇或輸入問題一級分類（中文）", "questionCategoryTipEn": "請選擇或輸入問題一級分類（英文）", "questionDescription": "題幹（中文）", "questionDescriptionEn": "題幹（英文）", "questionDescriptionTable": "題幹", "questionDescriptionTip": "請輸入題幹（中文）", "questionDescriptionTipEn": "請輸入題幹（英文）", "questionPrimaryCategory": "一級分類（中文）", "questionPrimaryCategoryEn": "一級分類（英文）", "questionPrimaryCategoryTable": "一級分類", "questionStatus": "狀態", "questionSubcategory": "二級分類（中文）", "questionSubcategoryEn": "二級分類（英文）", "questionSubcategoryTable": "二級分類", "questionSubcategoryTip": "請選擇或輸入問題二級分類（中文）", "questionSubcategoryTipEn": "請選擇或輸入問題二級分類（英文）", "questionTitle": "題目（中文）", "questionTitleEn": "題目（英文）", "questionTitleSearch": "題目", "questionTitleTable": "题目", "questionTitleTip": "請輸入問題標題（中文）", "questionTitleTipEn": "請輸入問題標題（英文）", "questionTitleTipSearch": "請輸入問題標題", "questionsManagement": "練習題管理", "resetCode": "此操作可能不會保存您的程式碼，是否要繼續？", "run": "運行", "runTip": "點擊運行，這裡會顯示運行結果。", "serialNumber": "序號", "startPracticing": "開始練習", "supportedLanguages": "支持的語言", "tabSize": "<PERSON>b <PERSON>", "target": "目標（中文）", "targetCoding": "目標", "targetEn": "目標（英文）", "targetTip": "請輸入目標（中文）", "targetTipEn": "請輸入目標（英文）", "theme": "主題", "trainingPurpose": "訓練目的（中文）", "trainingPurposeCoding": "訓練目的", "trainingPurposeEn": "訓練目的（英文）", "trainingPurposeTip": "請輸入訓練目的（中文）", "trainingPurposeTipEn": "請輸入訓練目的（英文）", "updateQuestionTip": "您確認要更新編碼題嗎？", "viewModelAnswers": "查看模型答案", "yourAnswers": "你的答案"}, "common": {"404Tip": "請檢查您輸入的URL是否正確，或者按一下下麵的按鈕返回主頁。", "Next": "下一個", "Previous": "上一個", "active": "已啟動", "add": "添加", "back": "返回", "backHome": "返回首頁", "cancel": "取消", "chinese": "中文", "chineseMainlandId": "中國大陸身份證", "clear": "清空", "close": "關閉", "confirm": "確定", "copyright": "由 SIMNECTZ® 授權", "create": "創建", "createDate": "創建日期", "createTime": "創建時間", "creator": "創建人", "customerRoleHelpDocument": "客戶角色幫助文檔", "default": "默認", "defaultTitle": "SOI+ 平臺", "delete": "删除", "description": "描述", "details": "詳情", "download": "下載", "edit": "編輯", "endDate": "結束日期", "english": "英文", "error": "錯誤", "export": "匯出", "exportLevel": "專家（10+年）", "false": "否", "female": "女", "goAnalyze": "去分析", "helpDocument": "幫助文檔", "hongKongId": "香港身份證", "import": "導入", "invalidNumber": "無效的數字", "junior": "初級（1-3年）", "keywords": "關鍵字", "macaoId": "澳門身份證", "male": "男", "middle": "中級（3-6年）", "more": "更多", "noData": "沒有數據", "normal": "正常", "notActive": "未啟動", "oops": "哎呀!", "operate": "操作", "pageNotFound": "頁面未找到，不能進入該頁面...", "platformName": "數位金融學創平臺", "practicePlatform": "實踐平臺", "reset": "重置", "save": "保存", "search": "蒐索", "senior": "高級（6-10年）", "start": "開始", "startDate": "開始日期", "submit": "提交", "success": "成功", "tableOfContents": "目錄", "taiwanId": "臺灣身份證", "tip": "提示", "to": "至", "token": "令牌", "true": "是", "update": "更新", "updateBy": "更新人", "updateTime": "更新時間", "upload": "上傳", "view": "查看", "warning": "警告", "welcome": "歡迎！"}, "course": {"courseCategoryIsRequired": "課程大分類為必填項", "courseCategoryPlaceholder": "請選擇課程大分類", "courseCode": "課程代碼", "courseCodeIsRequired": "課程代碼為必填項", "courseCodePlaceholder": "請輸入課程代碼", "courseDescription": "課程描述", "courseDescriptionIsRequired": "課程描述為必填項", "courseDescriptionPlaceholder": "請輸入課程描述", "courseDetail": "課程詳情", "courseName": "課程名稱", "courseNameIsRequired": "課程名稱為必填項", "courseNamePlaceholder": "請輸入課程名稱", "coursePicture": "課程圖片", "coursePictureIsRequired": "課程圖片為必填項", "createCourse": "建立課程", "creator": "創建者", "deleteCoursePrompt": "此操作將永久刪除課程。是否要繼續？", "deleteError": "課程下存在有關課題，請先刪除對應課程下的課題", "editCourse": "編輯課程", "keywordsPlaceholder": "課程名稱", "updateBy": "更新者"}, "courseCategory": {"courseCategory": "課程大分類", "courseCategoryIsRequired": "課程大分類為必填項", "courseCategoryPicture": "課程大分類圖片", "courseCategoryPictureIsRequired": "課程大分類圖片為必填項", "courseCategoryPlaceholder": "請輸入課程大分類", "createCourseCategory": "建立課程大分類", "deleteCourseCategoryPrompt": "此操作將永久刪除課程大分類。是否繼續？", "deleteError": "課程大分類下有有關課程，請先刪除對應課程大分類下的課程", "editCourseCategory": "編輯課程大分類", "keywordsPlaceholder": "課程大分類"}, "courseLearning": {"businessMarkdown": "業務檔案", "completeStatus": "完成狀態", "contentDescription": "內容描述", "contentDetails": "內容詳情", "courseLearningSpace": "SOI+ 課程學習空間", "deletePathPrompt": "此操作將永久刪除路徑。是否繼續？", "documentDirectory": "檔案目錄", "firstStudyTime": "首次學習時間", "job": "工作", "myPath": "學習路徑", "name": "姓名", "noData": "暫無數據", "pathMaintenance": "路徑維護", "role": "角色", "studentOrStaffNumber": "員工/學生 ID", "subTitle": "SOI+ 課程學習空間致力於為您打造一個涵蓋廣泛、內容豐富的課程體系，以滿足您在金融科技領域的學習需求。無論您是初學者還是專業人士，SOI+都能為您提供量身訂製的學習方案，助您在未來發展的道路上飛得更高。", "subscribe": "訂閱", "technologyMarkdown": "技術文檔", "video": "影片"}, "creditCardCreation": {"beforeRemoveMessage": "確認刪除 {fileName}？", "creditCardNumber": "信用卡號", "creditCardNumberPlaceholder": "請輸入信​​用卡號", "dataSource": "資料來源", "dataSourcePlaceholder": "請輸入資料來源", "downloadCreditCardNumberTemplate": "下載信用卡號模板", "frequency": "頻率", "frequencyPlaceholder": "請輸入頻率", "frequencyRate": "頻次", "frequencyRatePlaceholder": "請輸入頻率", "handleExceedMessage": "目前限制選擇1個文件，本次選擇{thisTimeChoose}個文件，全部選擇{totallyChoose}個文件", "industry": "產業", "linkToDatatable": "批量上傳帳戶", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "tableName": "表格名稱", "tableNamePlaceholder": "請輸入表格名稱", "totalTransaction": "最大交易次數", "totalTransactionPlaceholder": "請輸入最大交易次數", "transactionAmountFrom": "交易金額起", "transactionAmountFromPlaceholder": "請輸入交易金額起", "transactionAmountTo": "至", "transactionAmountToPlaceholder": "請輸入交易金額至", "transactionBy": "交易者", "transactionByFrom": "起", "transactionByFromPlaceholder": "請輸入交易起", "transactionByTo": "至", "transactionByToPlaceholder": "請輸入交易至", "transactionByType": "帳戶", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "請選擇交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "請選擇交易日期至", "transactionTo": "交易到", "upload": "上傳", "uploadFileRule": "只上傳 csv/xls/xlsx 文件，不超過 5MB"}, "creditCardEnquiry": {"bookingAmount": "帳面金額", "bookingCcy": "帳面貨幣", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "createDateRange": "創建時間範圍", "creditCardNumber": "信用卡號", "creditCardNumberPlaceholder": "請輸入信用卡號", "creditCardType": "信用卡類型", "dataDetails": "數據詳情", "dealNumber": "交易編號", "displayName": "顯示名稱", "fromCreateDate": "起始創建時間", "fromTransactionAmount": "交易金額從", "fromTransactionAmountPlaceholder": "請輸入交易金額從", "merchantName": "商家名稱", "merchantNumber": "商家編號", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "tableName": "數據表名稱", "tableNamePlaceholder": "请选择數據表名稱", "toCreateDate": "結束創建時間", "toTransactionAmount": "交易金額至", "toTransactionAmountPlaceholder": "請輸入交易金額至", "transactionAmount": "交易金額", "transactionCcy": "貨幣類型", "transactionTime": "交易時間", "transactionType": "交易類型"}, "creditCardUpdate": {"authorizationNumber": "授權碼", "authorizationNumberPlaceholder": "請輸入授權碼", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "creditCardNumber": "信用卡號", "creditCardNumberPlaceholder": "請輸入信用卡號", "creditCardType": "信用卡類型", "creditCardTypePlaceholder": "請輸入信用卡類型", "dealNumber": "交易號碼", "dealNumberPlaceholder": "請輸入交易號碼", "merchantBalance": "商家餘額", "merchantBalancePlaceholder": "請輸入商家餘額", "merchantName": "商家名稱", "merchantNamePlaceholder": "請輸入商家名稱", "merchantNumber": "商家編號", "merchantNumberPlaceholder": "請輸入商家編號", "title": "編輯信用卡資料", "transactionAmount": "交易金額", "transactionAmountPlaceholder": "請輸入交易金額", "transactionCcy": "貨幣類型", "transactionCcyPlaceholder": "請輸入貨幣類型", "transactionTime": "交易時間", "transactionTimePlaceholder": "請選擇交易時間", "transactionType": "交易類型", "transactionTypePlaceholder": "請輸入交易類型"}, "creditCardView": {"title": "查看信用卡數據"}, "customerBulkCreation": {"accommodation": "住宿", "accommodationPercentagePlaceholder": "請輸入住宿百分比", "accommodationPlaceholder": "請選擇住宿", "accountDataCreation": "帳戶資料建立", "accountDataCreationPlaceholder": "請選擇帳戶資料建立", "accountType": "帳戶類型", "accountTypePlaceholder": "請選擇帳戶類型", "ageGroup": "年齡組", "ageGroupPercentagePlaceholder": "請輸入年齡組百分比", "ageGroupPlaceholder": "請選擇年齡組", "branchCode": "分行代碼", "branchCodePlaceholder": "請選擇分行代碼", "branchNumberFrom": "起", "branchNumberFromPlaceholder": "請輸入分行編號起", "branchNumberTo": "至", "branchNumberToPlaceholder": "請輸入分行編號至", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "contactPreferredMethod": "首選聯繫方式", "contactPreferredMethodPercentagePlaceholder": "請輸入首選聯繫方式的百分比", "contactPreferredMethodPlaceholder": "請選擇首選聯繫方式", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "countryOfBirth": "出生國家", "countryOfBirthPlaceholder": "請選擇出生國家", "createDate": "建立日期", "createDatePlaceholder": "請選擇建立日期", "currencyCode": "貨幣代號", "currencyCodePlaceholder": "請選擇貨幣代碼", "customerStatus": "客戶狀態", "customerStatusPlaceholder": "請選擇客戶狀態", "education": "教育", "educationPercentagePlaceholder": "請輸入教育百分比", "educationPlaceholder": "請選擇教育", "employerIndustry": "雇主行業", "employerIndustryPlaceholder": "請選擇雇主行業", "employmentStatus": "就業狀況", "employmentStatusPercentagePlaceholder": "請輸入就業狀況百分比", "employmentStatusPlaceholder": "請選擇就業狀況", "fexAccountCurrencyCode": "FEX 帳戶貨幣代碼", "fexAccountCurrencyCodePlaceholder": "請選擇 fex 帳戶貨幣代碼", "fromCreateDate": "從建立日期", "gender": "性別", "genderPercentagePlaceholder": "請輸入性別百分比", "genderPlaceholder": "請選性別", "householdIncome": "家庭收入", "householdIncomeAmountFrom": "起", "householdIncomeAmountFromPlaceholder": "請輸入家庭收入金額起", "householdIncomeAmountTo": "至", "householdIncomeAmountToPlaceholder": "請輸入家庭收入金額至", "householdIncomePercentagePlaceholder": "請輸入家庭收入百分比", "householdIncomePlaceholder": "請選擇家庭收入", "maritalStatus": "婚姻狀況", "maritalStatusPercentagePlaceholder": "請輸入婚姻狀況百分比", "maritalStatusPlaceholder": "請選擇婚姻狀況", "maximumNumber": "最大添加數量為{number}", "monthlyIncome": "每月收入", "monthlyIncomeFrom": "起", "monthlyIncomeFromPlaceholder": "請輸入月收入起", "monthlyIncomePercentagePlaceholder": "請輸入每月收入的百分比", "monthlyIncomePlaceholder": "請選擇月收入", "monthlyIncomeTo": "至", "monthlyIncomeToPlaceholder": "請輸入月收入至", "nationality": "國籍", "nationalityPercentagePlaceholder": "請輸入國籍百分比", "nationalityPlaceholder": "請選國籍", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "occupation": "職業", "occupationPlaceholder": "請選擇職業", "otherMonthlyIncome": "其他月收入", "otherMonthlyIncomeFrom": "起", "otherMonthlyIncomeFromPlaceholder": "請輸入其他月收入起", "otherMonthlyIncomePlaceholder": "請選擇其他月收入", "otherMonthlyIncomeTo": "至", "otherMonthlyIncomeToPlaceholder": "請輸入其他月收入至", "permanentResidenceStatus": "永久居住身分", "permanentResidenceStatusPercentagePlaceholder": "請輸入永久居住身分百分比", "permanentResidenceStatusPlaceholder": "請選擇永久居住身分", "preLanguage": "首選聯繫語言", "preLanguagePercentagePlaceholder": "請輸入首選聯繫語言的百分比", "preLanguagePlaceholder": "請選擇首選聯繫語言", "region": "地區", "regionPercentagePlaceholder": "請輸入地區百分比", "regionPlaceholder": "請選擇地區", "sensitiveStatus": "敏感狀態", "sensitiveStatusPercentagePlaceholder": "請輸入敏感狀態的百分比", "sensitiveStatusPlaceholder": "請選擇敏感狀態", "sumOfPercentages": "{title}的百分比總和必須為 100", "tableName": "表名稱", "tableNamePlaceholder": "請選擇表格名稱", "toCreateDate": "建立日期日期", "totalCustomers": "客戶總數", "totalCustomersPlaceholder": "請輸入客戶總數"}, "customerCreation": {"bulkCreation": "批次創建", "creationType": "創建類型", "singleCreation": "單次創建"}, "customerEnquiry": {"accommodation": "住宿", "accommodationPlaceholder": "請選擇住宿", "ageGroup": "年齡組", "ageGroupPlaceholder": "請選擇年齡組", "branchCode": "分行代碼", "clearingCode": "銀行代碼", "contactPreferredMethod": "首選聯繫方式", "contactPreferredMethodPlaceholder": "請選擇首選聯繫方式", "countryCode": "國家代碼", "createDate": "建立日期", "customerId": "客戶 ID", "customerIdType": "客戶 ID 類型", "customerNumber": "客戶編碼", "education": "教育", "educationPlaceholder": "請選擇教育", "employmentStatus": "就業狀況", "employmentStatusPlaceholder": "請選擇就業狀況", "firstName": "名字", "fromCreateDate": "建立起始日期", "gender": "性別", "genderPlaceholder": "請選性別", "lastName": "姓氏", "maritalStatus": "婚姻狀況", "maritalStatusPlaceholder": "請選擇婚姻狀況", "nationality": "國籍", "nationalityPlaceholder": "請選國籍", "permanentResidenceStatus": "永久居住狀態", "permanentResidenceStatusPlaceholder": "請選擇永久居住狀態", "preLanguage": "首選聯繫語言", "preLanguagePlaceholder": "請選擇首選聯繫語言", "residentialRegionName": "居住地區名稱", "residentialRegionNamePlaceholder": "請選擇居住地區名稱", "sensitiveStatus": "敏感狀態", "sensitiveStatusPlaceholder": "請選擇敏感狀態", "toCreateDate": "建立截至日期"}, "customerSingleCreation": {"accommodation": "住宿", "accommodationPlaceholder": "請選擇住宿", "accountDataCreation": "帳戶資料建立", "accountDataCreationPlaceholder": "請選擇帳戶資料建立", "accountType": "帳戶類型", "accountTypePlaceholder": "請選擇帳戶類型", "ageGroup": "年齡組", "ageGroupPlaceholder": "請選擇年齡組", "branchCode": "分行代碼", "branchCodePlaceholder": "請選擇分行代碼", "branchNumberFrom": "起", "branchNumberFromPlaceholder": "請輸入分行編號起", "branchNumberTo": "至", "branchNumberToPlaceholder": "請輸入分行編號至", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "contactPreferredMethod": "聯絡人", "contactPreferredMethodPlaceholder": "請選擇聯絡人", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "countryOfBirth": "出生國家", "countryOfBirthPlaceholder": "請選擇出生國家", "createDate": "建立日期", "createDatePlaceholder": "請選擇建立日期", "currencyCode": "貨幣代號", "currencyCodePlaceholder": "請選擇貨幣代碼", "customerStatus": "客戶狀態", "customerStatusPlaceholder": "請選擇客戶狀態", "education": "教育程度", "educationPlaceholder": "請選擇教育程度", "employerIndustry": "雇主行業", "employerIndustryPlaceholder": "請選擇雇主行業", "employmentStatus": "就業狀況", "employmentStatusPlaceholder": "請選擇就業狀況", "fexAccountCurrencyCode": "FEX 帳戶貨幣代碼", "fexAccountCurrencyCodePlaceholder": "請選擇 FEX 帳號貨幣代碼", "fromCreateDate": "從建立日期", "gender": "性別", "genderPlaceholder": "請選性別", "householdIncome": "家庭收入", "householdIncomeAmountFrom": "起", "householdIncomeAmountFromPlaceholder": "請輸入家庭收入金額起", "householdIncomeAmountTo": "至", "householdIncomeAmountToPlaceholder": "請輸入家庭收入金額至", "householdIncomePlaceholder": "請選擇家庭收入", "maritalStatus": "婚姻狀況", "maritalStatusPlaceholder": "請選擇婚姻狀況", "monthlyIncome": "每月收入", "monthlyIncomeFrom": "起", "monthlyIncomeFromPlaceholder": "請輸入其他月收入起", "monthlyIncomePlaceholder": "請選擇月收入", "monthlyIncomeTo": "至", "monthlyIncomeToPlaceholder": "請輸入每月收入至", "nationality": "國籍", "nationalityPlaceholder": "請選國籍", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "occupation": "職業", "occupationPlaceholder": "請選擇職業", "otherMonthlyIncome": "其他月收入", "otherMonthlyIncomeFrom": "起", "otherMonthlyIncomeFromPlaceholder": "請輸入其他月收入起", "otherMonthlyIncomePlaceholder": "請選擇其他月收入", "otherMonthlyIncomeTo": "至", "otherMonthlyIncomeToPlaceholder": "請輸入其他月收入至", "permanentResidenceStatus": "永久居留身分", "permanentResidenceStatusPlaceholder": "請選擇永久居留身份", "preLanguage": "首選聯繫語言", "preLanguagePlaceholder": "請選擇首選聯繫語言", "region": "地區", "regionPlaceholder": "請選擇地區", "sensitiveStatus": "敏感狀態", "sensitiveStatusPlaceholder": "請選擇敏感狀態", "tableName": "表格名稱", "tableNamePlaceholder": "請選擇表格名稱", "toCreateDate": "到建立日期"}, "customerUpdate": {"accommodation": "住宿", "accommodationPlaceholder": "請選擇住宿", "age": "年齡", "ageGroup": "年齡組", "ageGroupPlaceholder": "請選擇年齡組", "agePlaceholder": "請輸入年齡", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "chineseName": "中文姓名", "chineseNamePlaceholder": "請輸入中文姓名", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "companyAddressLine1": "公司地址行1", "companyAddressLine1Placeholder": "請輸入公司地址行1", "companyAddressLine2": "公司地址行2", "companyAddressLine2Placeholder": "請輸入公司地址行2", "companyAddressLine3": "公司地址行3", "companyAddressLine3Placeholder": "請輸入公司地址行3", "companyAddressLine4": "公司地址行4", "companyAddressLine4Placeholder": "請輸入公司地址行4", "contactPreferredMethod": "首選聯繫方式", "contactPreferredMethodPlaceholder": "請選擇首選聯繫方式", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "countryOfBirth": "出生國家", "countryOfBirthPlaceholder": "請選擇出生國家", "countryOfResidence": "居住國家", "countryOfResidencePlaceholder": "請輸入居住國家", "createDate": "建立日期", "createDatePlaceholder": "請選擇建立日期", "cusPrestEmploymentSinceDate": "客戶目前就業開始日期", "cusPrestEmploymentSinceDatePlaceholder": "請選擇客戶目前就業開始日期", "custPresentAddPeriod": "客戶目前地址年限", "custPresentAddPeriodPlaceholder": "請輸入客戶目前地址年限", "custPresentAddUpdateDate": "客戶目前地址更新日期", "custPresentAddUpdateDatePlaceholder": "請選擇客戶目前地址更新日期", "custPresentEmploymentPeriod": "客戶目前就業年限", "custPresentEmploymentPeriodPlaceholder": "請輸入客戶目前就業年限", "custPreviousAddPeriod": "客戶歷史居住年限", "custPreviousAddPeriodPlaceholder": "請輸入客戶歷史居住年限", "custPreviousAddUpdateDate": "客戶歷史地址更新日期", "custPreviousAddUpdateDatePlaceholder": "請選擇客戶歷史地址更新日期", "custPreviousEmploymntDate": "客戶歷史的僱用日期", "custPreviousEmploymntDatePlaceholder": "請選擇客戶歷史的僱用日期", "custPreviousEmploymntPeriod": "客戶歷史就業年限", "custPreviousEmploymntPeriodPlaceholder": "請輸入客戶歷史就業年限", "custRelationMgrCode": "客戶關係經理代碼", "custRelationMgrCodePlaceholder": "請輸入客戶關係經理代碼", "customerID1": "客戶ID1", "customerID1Placeholder": "請輸入客戶ID1", "customerID2": "客戶ID2", "customerID2Placeholder": "請輸入客戶ID2", "customerIDType1": "客戶ID類型1", "customerIDType1Placeholder": "請輸入客戶ID類型1", "customerIDType2": "客戶ID類型2", "customerIDType2Placeholder": "請輸入客戶ID型2", "customerNumber": "客戶編碼", "customerNumberPlaceholder": "請輸入客戶編碼", "customerStatus": "客戶狀態", "customerStatusPlaceholder": "請選擇客戶狀態", "education": "教育程度", "educationPlaceholder": "請選擇教育程度", "emailAddress1": "電子郵件地址1", "emailAddress1Placeholder": "請輸入電子郵件地址1", "employerCompanyName": "雇主公司名稱", "employerCompanyNamePlaceholder": "請輸入雇主公司名稱", "employerIndustry": "雇主行業", "employerIndustryPlaceholder": "請選擇雇主所在行業", "employmentStatus": "就業狀況", "employmentStatusPlaceholder": "請選擇就業狀況", "firstName": "名字", "firstNamePlaceholder": "請輸入名字", "gender": "性別", "genderPlaceholder": "請選性別", "hkidfirstIssue": "香港身分證首次發行", "hkidfirstIssuePlaceholder": "請輸入香港身分證首次發行", "hkidissueDate": "香港身分證發行日期", "hkidissueDatePlaceholder": "請選擇香港身分證發行日期", "householdIncome": "家庭收入", "householdIncomePlaceholder": "請輸入家庭收入", "issueCountry1": "發行國家1", "issueCountry1Placeholder": "請輸入發行國家1", "issueCountry2": "發行國家2", "issueCountry2Placeholder": "請輸入發行國家2", "issueDate1": "ID1發行日期", "issueDate1Placeholder": "請輸入ID1發行日期", "issueDate2": "ID2發行日期", "issueDate2Placeholder": "請輸入ID2發行日期", "lastName": "姓氏", "lastNamePlaceholder": "請輸入姓氏", "maritalDate": "結婚日期", "maritalDatePlaceholder": "請選擇結婚日期", "maritalStatus": "婚姻狀況", "maritalStatusPlaceholder": "請選擇婚姻狀況", "minorInd": "是否未成年人", "minorIndPlaceholder": "請輸入是否未成年人", "mobilePhoneNumber1": "手機號碼1", "mobilePhoneNumber1Placeholder": "請輸入手機號碼1", "monthlySalary": "月薪", "monthlySalaryPlaceholder": "請輸入月薪", "nationality1": "國籍1", "nationality1Placeholder": "請選國籍1", "occupation": "職業", "occupationPlaceholder": "請選擇職業", "otherMonthlyIncome": "其他月收入", "otherMonthlyIncomePlaceholder": "請輸入其他月收入", "permanentResidenceStatus": "永久居留身分", "permanentResidenceStatusPlaceholder": "請選擇永久居留身份", "personalInfoUpdateDate": "個人資料更新日期", "personalInfoUpdateDatePlaceholder": "請選擇個人資料更新日期", "position": "職位", "positionPlaceholder": "請輸入職位", "preLanguage1": "首選聯系語言", "preLanguage1Placeholder": "請選擇首選聯系語言", "preTimeFrom1": "客戶服務時間開始1", "preTimeFrom1Placeholder": "請輸入客戶服務時間開始1", "preTimeTo1": "客戶服務時間結束1", "preTimeTo1Placeholder": "請輸入客戶服務時間結束1", "residentAddressMaintBranch": "客戶居住地的分行編號", "residentAddressMaintBranchPlaceholder": "請輸入客戶居住地的分行編號", "residentialAddressLine1": "住宅地址行 1", "residentialAddressLine1Placeholder": "請輸入住宅地址行 1", "residentialAddressLine2": "住宅地址行 2", "residentialAddressLine2Placeholder": "請輸入住宅地址行 2", "residentialAddressLine3": "住宅地址行 3", "residentialAddressLine3Placeholder": "請輸入住宅地址行 3", "residentialAddressLine4": "住宅地址行 4", "residentialAddressLine4Placeholder": "請輸入住宅地址行 4", "residentialDistrictName": "居住小區名稱", "residentialDistrictNamePlaceholder": "請輸入居住小區名稱", "residentialRegionName": "居住區域名稱", "residentialRegionNamePlaceholder": "請選擇居住區域名稱", "seniorInd": "是否老年人", "seniorIndPlaceholder": "請請輸入是否老年人", "sensitiveStatus": "敏感狀態", "sensitiveStatusPlaceholder": "請選擇敏感狀態", "spouseDateOfBirth": "配偶出生日期", "spouseDateOfBirthPlaceholder": "請選擇配偶出生日期", "spouseID": "配偶 ID", "spouseIDPlaceholder": "請輸入配偶 ID", "spouseIDType": "配偶證件類型", "spouseIDTypePlaceholder": "請輸入配偶 ID 類型", "spouseName": "配偶姓名", "spouseNamePlaceholder": "請輸入配偶姓名", "tableName": "表名稱", "tableNamePlaceholder": "請輸入表格名稱", "title": "編輯客戶數據", "weChatID": "微信ID", "weChatIDPlaceholder": "請輸入微信id"}, "customerView": {"title": "查看客戶數據"}, "dataAnalysisUseCase": {"dataAnalysisBank": "銀行", "dataAnalysisInsurance": "保險", "dataAnalysisStock": "證券", "document": "文檔", "name": "名稱", "video": "視頻"}, "dataDictionary": {"advancedTransactionDataBasedOnKnowledgeGraphData": "基於知識圖譜的進階版交易數據", "creditCardLossPredict": "信用卡流失預測交易數據", "genericTransactionDataWithAMLModuleIncluded": "通用交易數據（包括洗錢可疑交易行為模型）", "knowledgeGraphData": "知識圖譜數據", "selfGeneratedData": "自身生成數據", "useCaseData": "綜合案例數據"}, "dataEnquiryDetails": {"CustRelationMgrCode": "客戶關係經理代碼", "accommodation": "住房狀態", "accountNumber": "帳號", "actualBalAmt": "實際餘額", "age": "年齡", "ageGroup": "年齡組", "authorizationNumber": "授權編號", "bookingAmount": "帳面金額", "bookingCcy": "帳面編號", "branchCode": "分行代碼", "ccy": "貨幣", "channel": "渠道", "channelID": "渠道ID", "chineseName": "中文姓名", "clearingCode": "銀行代號", "companyAddressLine1": "公司地址行1", "companyAddressLine2": "公司地址行2", "companyAddressLine3": "公司地址行3", "companyAddressLine4": "公司地址行4", "contactPreferredMethod": "首選聯繫方式", "countryCode": "國家代碼", "countryOfBirth": "出生國家", "countryOfResidence": "居住國家", "crDrMaintInd": "借貸標誌", "createDate": "建立日期", "creditCardNumber": "信用卡號", "creditCardType": "信用卡類型", "cusPrestEmploymentSinceDate": "客戶目前就業開始日期", "custPresentAddPeriod": "客戶目前地址年限", "custPresentAddUpdateDate": "客戶目前地址更新日期", "custPresentEmploymentPeriod": "客戶目前就業年限", "custPreviousAddPeriod": "客戶歷史居住年限", "custPreviousAddUpdateDate": "客戶歷史地址更新日期", "custPreviousEmploymntDate": "客戶歷史的僱用日期", "custPreviousEmploymntPeriod": "客戶歷史就業年限", "custRelationMgrCode": "客戶關係經理代碼", "custodyCharges": "託管費用 (HKD)", "customerAccountNumber": "帳戶號碼", "customerAccountType": "帳戶類型", "customerID1": "客戶ID1", "customerID2": "客戶ID2", "customerIDType1": "客戶ID類型1", "customerIDType2": "客戶ID類型2", "customerNumber": "客戶代碼", "customerStatus": "客戶狀態", "dateOfBirth": "出生日期", "dealNumber": "交易號碼", "depositAmount": "存款金額", "depositNumber": "存款編號", "displayName": "顯示名稱", "education": "教育", "emailAddress1": "電子郵件地址1", "employerCompanyName": "雇主公司名稱", "employerIndustry": "雇主行業", "employmentStatus": "就業狀況", "exchangeAmoutInForeignCurrency": "外幣兌換金額", "exchangeAmoutInLocalCurrency": "本地貨幣兌換金額", "exchangeRate": "匯率", "firstName": "客戶名稱", "foreignCurrency": "外幣", "fundCcy": "基金貨幣類型", "fundCode": "基金代號", "fundPrice": "基金價格", "gender": "性別", "hKIDFirstIssue": "香港身分證首次簽發", "hKIDIssueDate": "香港身分證簽發日期", "hkidfirstIssue": "香港身份證首次簽發", "hkidissueDate": "香港身分簽發日期", "householdIncome": "家庭收入", "id": "ID", "issueCountry1": "發行國家1", "issueCountry2": "發行國家2", "issueDate1": "發行日期1", "issueDate2": "發行日期2", "lastName": "客戶姓", "lastUpdateDate": "最後更新日期", "lastUpdatedDate": "上次更新日期", "localCurrency": "本地貨幣", "lotSize": "每手股數", "maritalDate": "結婚日期", "maritalStatus": "婚姻狀況", "maturityAmount": "到期金額", "maturityDate": "到期日", "maturityInterest": "到期利息", "maturityStatus": "到期狀態", "merchantBalance": "商家餘額", "merchantName": "商家名稱", "merchantNumber": "商家編號", "minorInd": "未成年人", "mobilePhoneNumber1": "手機號碼1", "monthlySalary": "月薪", "nationality1": "國籍1", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "occupation": "職業", "otherMonthlyIncome": "其他月收入", "payeeCategory": "收款人類別", "payeeCategoryID": "收款人類別ID", "payeeID": "收款人ID", "payeeNumber": "收款人編號", "paymentEffectiveDay": "付款生效日", "permanentResidenceStatus": "永久居留身分", "personalInfoUpdateDate": "個人資料更新日期", "position": "職位", "postBalInForeignCurrencyAccount": "外幣帳戶過帳餘額", "preLanguage1": "首選聯系語言", "preTimeFrom1": "預時間從1", "preTimeTo1": "預時間至1", "prevBalInForeignCurrencyAccount": "外幣帳戶上一筆餘額", "previousBalAmt": "初期餘額", "refAccountNumber": "對方帳號", "reference": "參考", "remarks": "備註", "residentAddressMaintBranch": "客戶居住地的分行編號", "residentialAddressLine1": "住宅地址行 1", "residentialAddressLine2": "住宅地址行 2", "residentialAddressLine3": "住宅地址行 3", "residentialAddressLine4": "住宅地址行 4", "residentialDistrictName": "住宅區名稱", "residentialRegionName": "住宅區域名稱", "riskRating": "風險評等", "sandBoxId": "沙箱ID", "seniorInd": "老年人", "sensitiveStatus": "敏感狀態", "sharingNo": "交易份額", "spouseDateOfBirth": "配偶出生日期", "spouseID": "配偶身分證", "spouseIDType": "配偶身分證類型", "spouseName": "配偶姓名", "status": "狀態", "stockNumber": "股票編號", "stockPrice": "股票價格 (HKD)", "stockTrdingAmount": "股票交易額 (HKD)", "stockTrdingCommission": "股票交易佣金 (HKD)", "systemDate": "系統日期", "termInterestRate": "存款利率", "termPeriod": "存款期限", "tfrSeqNo": "對方交易流水號", "tradingAmount": "基金交易額", "tradingOption": "交易選項", "tranAmt": "交易金額", "tranDate": "交易日期", "tranDesc": "交易描述", "tranSeq": "交易流水號", "tranType": "交易類型", "transactionAmount": "交易總金額 (HKD)", "transactionCcy": "貨幣類型", "transactionCurrency": "交易貨幣", "transactionDate": "交易日期", "transactionDealNumber": "交易編號", "transactionDesc": "交易描述", "transactionTime": "交易時間", "transactionType": "交易類型", "trdingCommission": "交易佣金", "weChatID": "微信ID"}, "dataImport": {"clickUploadTip": "點擊上傳", "createDatabaseSuccess": "創建數據庫成功", "createDatabaseTip": "在上傳數據之前，請創建自己的數據庫。", "customDataSource": "自定義數據源", "dataTableName": "數據錶名稱", "databaseName": "數據庫名稱", "databaseNameFormatError": "只允許使用小寫字母、數位和底線", "databaseNameLengthError": "長度為5到30個字元", "databaseTable": "數據庫錶", "databaseTableBackupFile": "數據庫錶備份文件。", "deleteDataTableTip": "確定要删除此數據錶嗎?", "downloadTemplateFile": "下载模板文件", "fileSizeTooLargeTip": "文件大小不能超過50MB！", "fromYour": "來自您的", "importData": "導入數據", "importDataDescription": "導入數據描述", "importSuccess": "導入成功", "mysql": "MySQL", "only50": "僅展示50條數據預覽", "onlySelectOneFileTip": "一次只能上傳一個文件。 請先上傳所選文件。", "onlySupported": "僅支持", "period": "。", "pleaseSelectFile": "請選擇文件。", "pleaseUpload": "請上傳", "previewData": "預覽數據", "publicDataSource": "公共數據源", "supportedFileTypes": "*.xlsx 或 *.xls 文件，", "updateData": "更新數據", "updateDataDescription": "更新數據描述", "uploadFailed": "上傳失敗", "uploadSuccess": "上傳成功", "uploadTip": "只上傳*.xlsx或*.xls文檔，文件大小不能超過50MB。"}, "dataVisualization": {"addMysqlConnect": "添加MySQL連接", "aggregate": "聚合", "aliasNameError": "只允許大小寫字母、數位和底線，長度在1-10之間。", "ascending": "昇序", "avg": "平均值", "backgroundColor": "背景顏色", "barDirection": "柱狀圖方向", "bucketByMonth": "按月分桶", "bucketByQuarterly": "按季度分桶", "bucketByYear": "按年分桶", "bucketing": "分桶", "bucketingDate": "該字段為日期類型，可選擇範圍：", "bucketingEnd": "分桶的結束位置", "bucketingNumber": "該字段為數位類型，可選擇範圍：", "bucketingStart": "分桶的起始位置", "bucketingType": "該字段為字串類型，數據總量：", "categoryColor": "分類顏色", "chartBottomMargin": "圖表下邊距", "chartLeftMargin": "圖表左邊距", "chartMargins": "圖標邊距", "chartNameError": "只允許大小寫字母、數位、破折號和底線，長度在1-100之間。", "chartNameTip": "請輸入圖表名稱", "chartRightMargin": "圖表右邊距", "chartTopMargin": "圖表上邊距", "charts": "圖表", "childTypeCount": "類型計數", "clearStyleTip": "更改維度，量度，顏色，過濾，聚合和排序時，將重新生成圖表。 這時，上一個圖表的樣式將被清除。 您確定要執行嗎？", "color": "按顏色分類", "colorTip": "請將分類字段拖到此處", "columnAliasName": "列別名", "columnAliasNameMessage": "請輸入列別名", "columnAliasNameP": "數據類型轉換新生成列的名稱", "columnName": "參數名", "columnNameMessage": "請輸入列名", "connectionSymbol": "連接符號", "converted": "轉換後的數據無法再次轉換", "count": "計數", "createChart": "創建圖錶", "createDataSource": "創建數據源", "createDataTable": "創建數據錶", "createDatabase": "創建數據庫", "createTip": "如果沒有歷史數據表，請點擊此按鈕去創建。", "currentDataSource": "當前數據源", "currentDataType": "當前數據類型", "currentDatabase": "當前數據庫", "currentDateFormat": "當前日期格式", "customDataSource": "自定義數據源", "dataSource": "數據源", "dataSourceDescription": "數據源描述", "dataSourceHost": "數據源地址", "dataSourceNameSame": "名稱已存在，請換一個試試。", "dataSourceNameTip": "請輸入數據源名稱", "dataSourcePassword": "數據源密碼", "dataSourcePasswordIsRequired": "數據源密碼不能為空", "dataSourcePort": "數據源端口號", "dataSourcePortIsRequired": "數據源端口號不能為空", "dataSourceSourceHostIsRequired": "數據源地址不能為空", "dataSourceType": "數據源類型", "dataSourceTypeIsRequired": "數據源類型不能為空", "dataSourceUsername": "數據源用戶名", "dataSourceUsernameIsRequired": "數據源用戶名不能為空", "dataTable": "數據錶", "dataTableName": "數據錶名稱", "dataTableNameTip": "請輸入數據表名稱", "dataTooLarge": "數据集太大，請過濾", "databaseDate": "數據庫日期類型", "dateCalculation": "計算年齡", "dateFormat": "轉換後日期格式", "dateFormatMessage": "請選擇轉換的日期格式。", "decimalLength": "數位最大長度", "deleteDataSourceTip": "删除該數據源，會將關聯該數據源的數據錶和圖錶一起删除！ 確定删除嗎？", "deleteMyChartTip": "您確定要删除嗎？", "deleteTableTip": "删除該數據錶，會將關聯該數據錶的圖錶一起删除！ 確定删除嗎？", "descending": "降序", "dimensionsTip": "請將維度字段拖到此處", "editDataSource": "編輯數據源", "endColor": "結束顏色", "enumTypeFormat": "枚舉類型轉換", "excessiveColor": "過度顏色", "executeFail": "執行失敗", "figure": "圖形", "filter": "條件過濾", "filterConditionNotNull": "過濾條件不能為空", "getModelFail": "獲取數據模型詳情失敗", "hasNoDataSourceTip": "您沒有初始化同步數據庫，是否現在開始創建？", "importCsvFile": "導入xls、xlsx文件", "joinCondition": "關聯條件", "joinTable": "請選擇要關聯的錶", "joinTableTitle": "关联錶", "joinType": "請選擇關聯類型", "keepDecimalPlaces": "保留小數位數", "labelFont": "標籤字體", "labelFontColor": "標籤字體顏色", "labelFontSize": "標籤字體大小", "labelPosition": "標籤位置", "left": "距離左邊", "legend": "圖例", "legendBottomMargins": "圖例下邊距", "legendDirection": "圖例方向", "legendFont": "圖例字體", "legendFontColor": "圖例字體顏色", "legendFontSize": "圖例字體大小", "legendLeftMargins": "圖例左邊距", "legendOrient": "圖例方向", "legendPosition": "圖例位置", "lineStyle": "折線樣式", "lineType": "折線圖類型", "loadingText": "拼命加載中...", "measuresTip": "請將量度字段拖到此處", "millisecond": "毫秒", "name": "名稱", "nameSame": "別名不能和列名相同", "noDataSourceTip": "沒有數據源", "noDataTableTip": "沒有數據表", "noDatabaseTip": "沒有數據庫", "noSorting": "不排序", "numberType": "數位類型", "numberTypeFormat": "數位類型轉換", "numberTypeMessage": "請選擇數位類型。", "only50": "僅展示50條數據預覽", "order": "排序", "perfectTip": "請完善規則後重試", "pieType": "餅圖類型", "pleaseExecute": "保存失敗，請先執行", "previewData": "數據預覽", "publicDataSource": "公共數據源", "queryFailed": "査詢失敗", "quickGroup": "快捷分組", "quickGroupTip": "請輸入分組大小", "reimport": "重新導入", "rotate": "X軸標籤旋轉角度", "saveChart": "保存圖表", "searchField": "搜索字段", "second": "秒", "selectDataSourceType": "選擇數據源類型", "selectModel": "選擇數據表", "setChartsTitle": "設定圖表標題", "setTimeFormat": "數據轉換", "showAllLabelsOnXAxis": "顯示X軸所有標籤", "showLabel": "是否顯示標籤", "showLegend": "是否顯示圖例", "showSplitLine": "是否顯示分割線", "showXAxis": "是否顯示X軸", "showYAxis": "是否顯示Y軸", "showZoom": "是否顯示縮放圖", "stackTotal": "是否堆疊總數", "startColor": "開始顏色", "string": "字串", "style": "樣式", "sum": "求和", "syncDataLoading": "正在同步數據，請耐心等待。", "synchronizeSandboxData": "同步沙箱數據", "tables": "數據庫錶", "testConnect": "測試連結", "timeStampType": "時間戳類型", "timeStampTypeMessage": "請選擇時間戳的類型。", "timeTypeFormat": "時間類型轉換", "timeTypeMessage": "請選擇時間的類型。", "timestamp": "時間戳", "title": "標題", "titleFont": "標題字體", "titleFontColor": "標題字體顏色", "titleFontSize": "標題字體大小", "titlePosition": "標題位置", "tooManyTypes": "枚舉類型過多，暫不支持轉換。", "top": "距離上邊", "typeCount": "分類計數", "typeMismatch": "類型不匹配", "unableToDelete": "無法删除，請先删除其子節點。", "updateChart": "更新圖表", "updateSuccess": "更新成功", "xAxisColor": "X軸顏色", "yAxisColor": "Y軸顏色", "zoomFontColor": "縮放圖字體顏色"}, "documentManagement": {"changeFileTip": "請先上傳已選擇的文檔，或删除之後重新選擇。", "classIsRequired": "分類不能為空", "className": "分類中文名稱", "classNameEn": "分類英文名稱", "classNameEnIsRequired": "分類中文名稱不能為空", "classNameEnPlaceholder": "請填寫分類英文名稱", "classNameIsRequired": "分類中文名稱不能為空", "classNamePlaceholder": "請填寫分類中文名稱", "clickUploadTip": "點擊上傳", "cnDocUrl": "中文文檔", "cnVideoUrl": "中文視頻", "createCategory": "創建分類", "deleteCategoryTip": "此操作將永久删除類別，是否繼續？", "deleteTip": "你確定要删除嗎？", "docName": "文檔中文名稱", "docNameEn": "文檔英文名稱", "docNameEnIsRequired": "文檔英文名稱不能為空", "docNameIsRequired": "文檔中文名稱不能為空", "docType": "文檔類型", "docUploadTip": "支持以下文檔格式：*.txt，*.doc，*.docx，*.pdf，*.ppt，*.pptx，*.xlsx，*.xls。 文檔中如果包含中文字元，請先手動轉換成PDF格式再上傳。", "editCategory": "編輯分類", "enDocUrl": "英文文檔", "enVideoUrl": "英文視頻", "groupName": "分組中文名稱", "groupNameEn": "分組英文名稱", "groupNameEnIsRequired": "分組英文名稱不能為空", "groupNameEnPlaceholder": "請填寫分組英文名稱", "groupNameIsRequired": "分組中文名稱不能為空", "groupNamePlaceholder": "請填寫分組中文名稱", "icon": "圖標", "noteTip": "注意：分組名稱和分類名稱為系統參數，不可更改！！！", "selectFile": "選擇文檔", "selectFileTip": "請選擇要上傳的文件", "uploadFailedTip": "上傳失敗，請稍後重試", "uploadSuccessTip": "上傳成功", "videoUploadTip": "支持以下文檔格式：*.mp3，*.mp4"}, "domainPracticals": {"businessUseCase": "業務場景用例", "code": "代碼", "coreLogic": "覈心邏輯", "document": "文檔", "name": "名稱", "technologyUseCase": "技術場景用例", "video": "視頻"}, "downloadCenter": {"dockerInstall": "<h1 style=\"border: 0px; margin: 0px 0px 10px; padding: 0px; font-size: 2.1em; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> Windows Docker 安裝 </h1> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker 並非是一個通用的容器工具，它依賴於已存在並運行的Linux內核環境。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker 實質上是在已經運行的 Linux 下製造了一個隔離的文檔環境，囙此它執行的效率幾乎等同於所部署的 Linux 主機。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 囙此，Docker必須部署在Linux內核的系統上。 如果其他系統想部署Docker就必須安裝一個虛擬Linux環境。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img width=\"50%\" src=\"https://www.runoob.com/wp-content/uploads/2016/05/CV09QJMI2fb7L2k0.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 在Windows上部署Docker的方法都是先安裝一個虛擬機，並在安裝Linux系統的的虛擬機中運行Docker。 </p> <h2 style=\"border: 0px; margin: 2px 0px; padding: 0px; font-size: 1.8em; line-height: 1.8em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> Win10 系統 </h2> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker Desktop是Docker在Windows 10和macOS作業系統上的官方安裝方式，這個方法依然屬於先在虛擬機中安裝Linux然後再安裝Docker的方法。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker Desktop 官方下載地址：&nbsp;<a href=\"https://hub.docker.com/editions/community/docker-ce-desktop-windows\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">https://hub.docker.com/editions/community/docker-ce-desktop-windows</a> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <strong style=\"border: 0px; margin: 0px; padding: 0px;\">注意：</strong>此方法僅適用於Windows 10作業系統專業版、企業版、教育版和部分家庭版！ </p> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 安裝 Hyper-V </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Hyper-V是微軟開發的虛擬機，類似於VMWare或VirtualBox，僅適用於Windows 10。 這是Docker Desktop for Windows所使用的虛擬機。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 但是，這個虛擬機一旦啟用，QEMU、VirtualBox或VMWare Workstation 15及以下版本將無法使用！ 如果你必須在電腦上使用其他虛擬機（例如開發Android應用必須使用的模擬器），請不要使用Hyper-V！ </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <br/> </p> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 開啟 Hyper-V </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-4363-20171206211136409-1609350099.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 程序和功能 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-4368-20171206211345066-1430601107.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 啟用或關閉Windows功能 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-9748-20171206211435534-1499766232.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> <span style=\"color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; font-size: 12px; background-color: rgb(255, 255, 255);\">選中Hyper-V</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-6433-20171206211858191-1177002365.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 也可以通過命令來啟用Hyper-V，請右鍵開始菜單並以管理員身份運行PowerShell，執行以下命令： </p> <pre class=\"prettyprint prettyprinted\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All</pre> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 安裝 Docker Desktop for Windows </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 點擊&nbsp;<a href=\"https://hub.docker.com/?overlay=onboarding\" rel=\"noopener noreferrer\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">Get started with Docker Desktop</a>，並下載Windows的版本，如果你還沒有登錄，會要求注册登入： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/5AEB69DA-6912-4B08-BE79-293FBE659894.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 運行安裝文件 </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 雙擊下載的Docker for Windows Installer安裝文件，一路Next，點擊Finish完成安裝。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513669129-6146-20171206214940331-1428569749.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668903-9668-20171206220321613-1349447293.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 安裝完成後，Docker會自動啟動。 通知欄上會出現個小鯨魚的圖標<img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513582421-4552-whale-x-win.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/>，這表示Docker正在運行。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 桌邊也會出現三個圖標，如下圖所示： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 我們可以在命令列執行docker version來查看版本號，docker run hello-world 來載入測試鏡像測試。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 如果沒啟動，你可以在Windows蒐索Docker來啟動： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513585082-6751-docker-app-search.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 啟動後，也可以在通知欄上看到小鯨魚圖標： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513585123-3777-whale-taskbar-circle.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <blockquote style=\"border: 0px; margin: 10px; padding: 10px; background-color: rgb(243, 247, 240); font-size: 13px; line-height: 2em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal;\"> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 4px; line-height: 1.5em; overflow-wrap: break-word; word-break: break-all; font-size: 14px; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; font-style: italic;\"> 如果啟動中遇到因WSL 2導致地錯誤，請安裝&nbsp;<a href=\"https://docs.microsoft.com/zh-cn/windows/wsl/install-win10\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none; font-size: 13px;\">WSL 2</a>。 </p> </blockquote> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 安裝之後，可以打開 PowerShell 並運行以下命令檢測是否運行成功： </p> <pre class=\"prettyprint prettyprinted\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">docker run hello-world</pre> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 在成功運行之後應該會出現以下訊息： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/EmkOezweLQVIwA1T__original.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <hr style=\"background-color: rgb(212, 212, 212); color: rgb(212, 212, 212); height: 1px; border: 0px; clear: both; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; font-size: 12px; white-space: normal;\"/> <h2 style=\"border: 0px; margin: 2px 0px; padding: 0px; font-size: 1.8em; line-height: 1.8em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> win7、win8 系統 </h2> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> win7、win8 等需要利用 docker toolbox 來安裝，國內可以使用阿裡雲的鏡像來下載，下載地址：<a href=\"http://mirrors.aliyun.com/docker-toolbox/windows/docker-toolbox/\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">http://mirrors.aliyun.com/docker-toolbox/windows/docker-toolbox/</a> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 安裝比較簡單，雙擊運行，點下一步即可，可以勾選自己需要的組件： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/691999-20180512142142130-1831870973.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> docker toolbox 是一個工具集，它主要包含以下一些內容： </p> <ul style=\"list-style-type: none;\" class=\" list-paddingleft-2\"> <li> <p> Docker CLI - 客戶端，用來運行docker引擎創建鏡像和容器。 </p> </li> <li> <p> Docker Machine - 可以讓你在 Windows 的命令列中運行 docker 引擎命令。 </p> </li> <li> <p> Docker Compose - 用來運行 docker-compose 命令。 </p> </li> <li> <p> Kitematic - 這是 Docker 的 GUI 版本。 </p> </li> <li> <p> Docker QuickStart shell - 這是一個已經配寘好Docker的命令列環境。 </p> </li> <li> <p> Oracle VM Virtualbox - 虛擬機。 </p> </li> </ul> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 下載完成之後直接點擊安裝，安裝成功後，桌邊會出現三個圖標，如下圖所示： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/icon-set.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 點擊 Docker QuickStart 圖標來啟動 Docker Toolbox 終端。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 如果系統顯示 User Account Control 視窗來運行 VirtualBox 修改你的電腦，選擇Yes。 </p>", "dockerInstallMacos": "<p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <font face=\"arial, helvetica, sans-serif\"><span style=\"font-size: 24px;\">MacOS Docker安裝</span></font> </p><p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 請點擊以下連結下載&nbsp;<a href=\"https://download.docker.com/mac/stable/Docker.dmg\" target=\"_blank\" rel=\"noopener\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">Stable</a>&nbsp;或&nbsp;<a href=\"https://download.docker.com/mac/edge/Docker.dmg\" target=\"_blank\" rel=\"noopener\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">Edge</a>&nbsp;版本的 Docker for Mac。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 如同macOS其它軟體一樣，安裝也非常簡單，按兩下下載的.dmg文檔，然後將鯨魚圖標拖拽到Application資料夾即可。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1E045CE6-D504-4E7D-8C57-EEFB8AC83BF1.jpg\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> <span style=\"color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; font-size: 12px; background-color: rgb(255, 255, 255);\">從應用中找到Docker圖標並點擊運行。 可能會詢問macOS的登入密碼，輸入即可。</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480613-7638-docker-app-in-apps.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 點擊頂部狀態列中的鯨魚圖標會彈出操作選單。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480612-6026-whale-in-menu-bar.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480613-8590-menu.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 第一次點擊圖標，可能會看到這個安裝成功的介面，點擊\"Got it!\"可以關閉這個視窗。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480614-7648-install-success-docker-cloud.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 啟動終端後，通過命令可以檢查安裝後的Docker版本。 </p> <pre class=\"prettyprint\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">$ docker --version <br/>Docker version 17.09.1-ce, build 19e2cf6</pre> <p> <br/> </p>", "document": "文檔", "downloadService": "下載服務", "downloadServiceContent": "<p> 在Powershell依次輸入下麵的命令，等待鏡像下載完成。 </p> <p> docker pull theiaide/theia-full </p> <p> <br/> </p>", "downloadServiceContentMacos": "<p> 在終端工具依次輸入下麵的命令，等待鏡像下載完成。 </p> <p> docker pull theiaide/theia-full</p> <p> <br/> </p>", "downloadServiceContentMacosMl": "<p> 在終端工具依次輸入下麵的命令，等待鏡像下載完成。 </p> <p> docker pull linkerwang/amlserver:v1 </p> <p> docker pull linkerwang/amltool:v1 </p> <p> <br/> </p>", "downloadServiceContentMl": "<p> 在Powershell依次輸入下麵的命令，等待鏡像下載完成。 </p> <p> docker pull linkerwang/amlserver:v1 </p> <p> docker pull linkerwang/amltool:v1 </p> <p> <br/> </p>", "installDocker": "Docker安裝", "installIde": "下載工作區", "installMl": "下載機器學習", "macOSInstall": "MacOS", "name": "名稱", "runAndUse": "運行並使用", "runAndUseContent": "<p> 在PowerShell輸入下麵的命令啟動鏡像 </p> <p> docker run -itd --init -p 30000:3000 -v \"/home/<USER>/home/<USER>\" -v \"/home/<USER>/.ssh:/home/<USER>/.ssh\" -v \"/home/<USER>/.ssh:/root/.ssh\" theiaide/theia-full </p> <p> chown -R 1000 /home/<USER>/p><p> <br/> </p><p>在瀏覽器輸入http://127.0.0.1:30000打開工作區工具</p>", "runAndUseContentMacos": "<p> 在終端工具輸入下麵的命令啟動鏡像 </p> <p> docker run -itd --init -p 30000:3000 -v \"/home/<USER>/home/<USER>\" -v \"/home/<USER>/.ssh:/home/<USER>/.ssh\" -v \"/home/<USER>/.ssh:/root/.ssh\" theiaide/theia-full </p><p> chown -R 1000 /home/<USER>/p> <p> <br/> </p><p>在瀏覽器輸入http://127.0.0.1:30000打開工作區工具</p>", "runAndUseContentMacosMl": "<p> 在終端工具輸入下麵的命令啟動鏡像 </p> <p> docker run -d -p 8084:8084 --name amlserver linkerwang/amlserver:v1 </p> <p> docker run -d -p 9000:80 --name amltool linkerwang/amltool:v1 </p> <p> <br/> </p><p>在瀏覽器輸入http://127.0.0.1:9000打開機器學習工具</p>", "runAndUseContentMl": "<p> 在PowerShell輸入下麵的命令啟動鏡像 </p> <p> docker run -d -p 8084:8084 --name amlserver linkerwang/amlserver:v1 </p> <p> docker run -d -p 9000:80 --name amltool linkerwang/amltool:v1 </p> <p> <br/> </p><p>在瀏覽器輸入http://127.0.0.1:9000打開機器學習工具</p>", "subtitle": "根據您電腦的作業系統選擇下方對應的安裝手冊進行工作區本地服務的安裝。", "subtitleMl": "根據您電腦的作業系統選擇下方對應的安裝手冊進行機器學習本地服務的安裝。", "title": "將工作區服務安裝到您的本地電腦，體驗更快更穩定的軟體發展功能", "titleMl": "將機器學習服務安裝到您的本地電腦，體驗更快更穩定的機器學習功能", "verifyService": "驗證服務", "verifyServiceContent": "<p> 在Powershell中輸入命令\"docker images\"，驗證鏡像是否下載完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> theiaide/theia-full&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; latest&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; be7675928ad5&nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8.53GB </p> <p> <br/> </p>", "verifyServiceContentMacos": "<p> 在終端工具中輸入命令\"docker images\"，驗證鏡像是否下載完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> theiaide/theia-full&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; latest&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; be7675928ad5&nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8.53GB </p> <p> <br/> </p>", "verifyServiceContentMacosMl": "<p> 在終端工具中輸入命令\"docker images\"，驗證鏡像是否下載完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> linkerwang/amlserver&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9f2145f73a0a&nbsp; &nbsp; &nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.58GB </p> <p> linkerwang/amltool&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; v1&nbsp; &nbsp; &nbsp; &nbsp; <span style=\"white-space: pre;\">\t&nbsp;&nbsp;&nbsp;&nbsp;</span>23e31282671c&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp;&nbsp;&nbsp;3 minutes ago&nbsp; &nbsp;&nbsp;&nbsp; 148MB </p> <p> <br/> </p>", "verifyServiceContentMl": "<p> 在Powershell中輸入命令\"docker images\"，驗證鏡像是否下載完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> linkerwang/amlserver&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9f2145f73a0a&nbsp; &nbsp; &nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.58GB </p> <p> linkerwang/amltool&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; v1&nbsp; &nbsp; &nbsp; &nbsp; <span style=\"white-space: pre;\">\t&nbsp;&nbsp;&nbsp;&nbsp;</span>23e31282671c&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp;&nbsp;&nbsp;3 minutes ago&nbsp; &nbsp;&nbsp;&nbsp; 148MB </p> <p> <br/> </p>", "video": "視頻", "windowsInstall": "Windows"}, "financialDataCharacteristics": {"bankingServicesAndData": "銀行服務和數據", "description1": "金融業務主要為客戶提供全面、優質、安全的金融服務。客戶永遠是在金融系統和數據的中心。在提供服務給客戶之前，金融機構必須從客戶對客戶有充分的理解，盡可能收集最多的資訊。在獲取客戶資訊後，金融機構將根據客戶的需求建立不同的產品，然後客戶可以開始進行交易。所有金融數據都是按照這種層次結構來建造的，並保存在高度安全的環境中，以備將來的產品銷售和服務。", "description2": "SIMNECTZ的沙盒具有相同的資料結構，並具有客戶、產品和交易資料的層次結構。", "description3": "了解金融資料的特徵和結構至關重要。在新建立金融數據的時候，必須先建立客戶數據，然後是產品和交易數據。", "description4": "在一般商業銀行中，核心銀行是商業銀行業務的核心，它為所有外圍系統/服務（如信用卡、貸款等）提供交易，財務會計和帳本服務等。", "description5": "在先前的數位轉型過程中，客戶資料已經透過清理和重組，形成了關於客戶的「單一事實」資料資訊。這綜合客戶資料資訊用於透過標準資料存取API介面為所有外圍系統/服務提供服務。", "description6": "除了帳本和客戶資料外，所有交易資料都與各自的系統/服務一起產生和儲存。"}, "foreignExchangeCreation": {"accountNumber": "帳戶號碼", "beforeRemoveMessage": "确认删除 {fileName}？", "channel": "渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadAccountTemplate": "下載模板文件", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "頻次", "frequencyRatePlaceholder": "请输入頻次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上傳帳戶", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "settlementAccount": "結算帳號", "settlementAccountPlaceholder": "請輸入結算帳號", "tableName": "表格名称", "tableNamePlaceholder": "请输入表格名称", "totalTransaction": "最大交易次數", "totalTransactionPlaceholder": "请输入最大交易次數", "transactionAccount": "交易帳號", "transactionAccountPlaceholder": "請輸入交易帳號", "transactionAccountType": "交易帳號類型", "transactionAmountFrom": "交易金額（港幣）起", "transactionAmountFromPlaceholder": "請輸入交易金額起", "transactionAmountTo": "至", "transactionAmountToPlaceholder": "請輸入交易金額至", "transactionCcy": "貨幣類型", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transactionType": "交易類型", "upload": "上传", "uploadFileRule": "仅上传 csv/xls/xlsx 文件，不超过 5MB"}, "foreignExchangeEnquiry": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "dataDetails": "數據詳情", "exchangeAmoutInForeignCurrency": "外幣兌換金額", "exchangeAmoutInLocalCurrency": "交易金額（HKD）", "exchangeRate": "匯率", "foreignCurrency": "外幣", "fromCreateDate": "起始創建時間", "localCurrency": "本地貨幣", "postBalInForeignCurrencyAccount": "在外幣帳號中發布餘額", "prevBalInForeignCurrencyAccount": "上期外幣帳戶餘額", "tableName": "數據表名稱", "tableNamePlaceholder": "请选择數據表名稱", "toCreateDate": "結束創建時間", "transactionTime": "交易時間", "transactionType": "交易類型"}, "foreignExchangeUpdate": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "exchangeAmoutInForeignCurrency": "外幣兌換金額", "exchangeAmoutInForeignCurrencyPlaceholder": "請輸入外幣兌換金額", "exchangeAmoutInLocalCurrency": "本幣兌換金額", "exchangeAmoutInLocalCurrencyPlaceholder": "請輸入本幣兌換金額", "exchangeRate": "匯率", "exchangeRatePlaceholder": "請輸入匯率", "foreignCurrency": "外幣", "foreignCurrencyPlaceholder": "請輸入外幣", "localCurrency": "本地貨幣", "localCurrencyPlaceholder": "請輸入本地貨幣", "postBalInForeignCurrencyAccount": "外幣帳戶過帳餘額", "postBalInForeignCurrencyAccountPlaceholder": "請輸入外幣帳戶過帳餘額", "prevBalInForeignCurrencyAccount": "外幣帳戶上一筆餘額", "prevBalInForeignCurrencyAccountPlaceholder": "請輸入外幣帳戶上一筆餘額", "title": "編輯外匯資料", "transactionTime": "交易時間", "transactionTimePlaceholder": "請選擇交易時間", "transactionType": "交易類型", "transactionTypePlaceholder": "請輸入交易類型"}, "foreignExchangeView": {"title": "查看外汇數據"}, "fundCreation": {"accountNumber": "帳戶號碼", "accountNumberPlaceholder": "請輸入交易帳號", "add": "添加", "amountFrom": "起始金額", "amountFromPlaceholder": "请输入起始金额", "amountTo": "至", "amountToPlaceholder": "請輸入截至金額", "batchUploadFundCode": "批量上傳基金代碼", "beforeRemoveMessage": "确认删除 {fileName}？", "buy": "買入", "channel": "渠道", "channelPlaceholder": "請選擇渠道", "dataSource": "數據源", "dataSourcePlaceholder": "請選擇數據源", "downloadAccountTemplate": "下載帳戶模板", "fileSizeAndTypeTip": "僅支持上傳 csv/xls/xlsx 文件，不得超過 5MB。", "frequency": "頻率", "frequencyPlaceholder": "請選擇頻率", "frequencyRate": "頻次", "frequencyRatePlaceholder": "請選擇頻次", "fromDate": "開始日期", "fromDatePlaceholder": "請選擇開始日期", "fundCode": "基金代碼", "fundCodeList": "可用的基金代碼", "fundCodeTemplate": "基金代碼模板", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上傳帳戶", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "sell": "賣出", "settlementAccount": "結算帳戶", "shareFrom": "起始份額", "shareFromPlaceholder": "请输入起始份额", "shareToPlaceholder": "請輸入截至份額", "toDate": "結束日期", "toDatePlaceholder": "請選擇結束日期", "totalPlaceholder": "請輸入最大交易次數", "totalTransaction": "最大交易次數", "transactionAccount": "交易帳號", "transactionAccountType": "交易帳戶類型", "transactionDate": "交易日期", "transactionDetails": "交易詳情", "transactionType": "交易类型"}, "fundEnquiry": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "dataDetails": "數據詳情", "fromCreateDate": "起始創建時間", "fromTransactionAmount": "交易金額從", "fromTransactionAmountPlaceholder": "請輸入交易金額從", "fundCcy": "基金貨幣", "fundCode": "基金代號", "fundPrice": "基金價格", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "riskRating": "風險評等", "sharingNo": "持倉份額", "tableName": "數據表名稱", "tableNamePlaceholder": "请选择數據表名稱", "toCreateDate": "結束創建時間", "toTransactionAmount": "交易金額至", "toTransactionAmountPlaceholder": "請輸入交易金額至", "tradingAmount": "基金交易額", "tradingOption": "交易類型", "transactionAmount": "交易總金額 (HKD)", "transactionDate": "交易日期", "trdingCommission": "交易手續費"}, "fundUpdate": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "fundCcy": "基金貨幣類型", "fundCcyPlaceholder": "請輸入基金帳戶", "fundCode": "基金代號", "fundCodePlaceholder": "請輸入基金代號", "fundPrice": "基金價格", "fundPricePlaceholder": "請輸入基金價格", "riskRating": "風險評等", "riskRatingPlaceholder": "請輸入風險評等", "sharingNo": "交易份額", "sharingNoPlaceholder": "請輸入交易份額", "title": "編輯基金資料", "tradingAmount": "基金交易額", "tradingAmountPlaceholder": "請輸入基金交易額", "tradingOption": "交易選項", "tradingOptionPlaceholder": "請輸入交易選項", "transactionAmount": "交易總金額 (HKD)", "transactionAmountPlaceholder": "請輸入交易總金額", "transactionDate": "交易日期", "transactionDatePlaceholder": "請輸入交易日期", "trdingCommission": "交易佣金", "trdingCommissionPlaceholder": "請輸入交易佣金"}, "fundView": {"title": "查看基金數據"}, "homework": {"answerQuestion": "回答試題", "deleteQuestionPrompt": "該操作將永久刪除該試題。您要繼續嗎?", "editQuestion": "編輯試題", "homework": "作業", "homeworkList": "作業列表", "keywordsPlaceholder": "試題內容", "myHomework": "我的作業", "questionContent": "試題內容", "questionDetail": "試題詳情", "questionModelanswer": "試題標準答案", "studentAnswer": "學生答案", "studentAnswerIsRequired": "學生答案不能為空"}, "loginLog": {"browser": "瀏覽器", "email": "用戶郵箱", "failed": "失敗", "loginIp": "登入IP", "loginLocation": "登入地點", "loginStatus": "登入狀態", "loginTime": "登入時間", "message": "操作資訊", "os": "作業系統", "success": "成功"}, "nodeManagement": {"add": "新增", "addFlow": "新增節點", "addTip": "你確定要加入嗎？", "addWorkflow": "新增規則", "apiHeader": "api_header", "apiMethod": "api_method", "apiParams": "api_params", "apiUrl": "api_url", "canNotEmptyTip": "該欄位不能為空", "deleteTip": "你確定要刪除嗎？", "editFlow": "編輯節點", "enterApiHeaderTip": "請輸入 api_header", "enterApiMethodTip": "請輸入 api_method", "enterApiParamsTip": "請輸入 api_params", "enterApiUrlTip": "請輸入 api_url", "enterGroupTip": "請輸入節點分組", "enterNameTip": "請輸入節點名稱", "group": "分組", "json": "Json", "lastUpdateDate": "更新時間", "name": "名稱", "operatingSuccess": "操作成功", "save": "儲存", "update": "更新", "updateTip": "你確定要更新嗎？", "updateWorkflow": "更新規則", "workflowManage": "規則管理"}, "obtainDeveloperToken": {"accessTokenTip": "輸入access_token後點擊連結位址", "addClient": "添加客戶端", "addClientTip": "請先創建一個客戶端，然後使用創建的客戶端生成您自己的令牌。", "archive": "註銷", "authorizationCodeTip": "輸入每一步必要的資訊後點擊其下麵的連結位址", "authorizationCodeTip1": "從認證服務器獲取 'code'", "authorizationCodeTip2": "用 'code' 換取 'access_token'", "authorizationCodeTip3": "輸入第一步獲取的code", "authorizedGrantTypesTip": "至少勾選一項授權方式，且不能只單獨勾選refresh_token", "cancelled": "已註銷", "client": "客戶端", "clientCredentialsTip": "點擊連結位址即可測試", "clientId": "客戶端ID", "clientIdTip": "客戶端ID必須輸入，且必須唯一，長度至少5位", "createNow": "現在創建", "createSuccess": "創建成功", "developerToken": "開發者令牌", "editClient": "編輯客戶端", "email": "電子郵箱", "generateDeveloperTokenSuccess": "生成開發者令牌成功", "generateToken": "生成令牌", "grantTypes": "授權方式", "inEffect": "生效中", "password": "密碼", "passwordTip": "輸入平臺的email，password後點擊連結位址", "redirectUri": "回檔地址", "redirectUriIsRequired": "請輸入回檔地址", "refreshTokenTip": "輸入refresh_token後點擊連結位址", "secret": "客戶端密碼", "secretTip": "客戶端密碼必須輸入，且長度至少8位", "status": "狀態", "test": "測試客戶端", "testTip": "針對不同的grant_type提供不同的測試URL", "trusted": "可信的", "trustedTip": "只適用於授權方式包括authorization_code的情况，當用戶登錄成功後，若選No，則會跳轉到讓用戶Approve的頁面讓用戶同意授權，若選Yes，則在登入後不需要再讓用戶Approve同意授權（因為是受信任的）。", "webServerRedirectUriTip": "若授權方式包括authorization_code，則必須輸入回檔地址"}, "organization": {"contactEmail": "聯系電子郵箱", "contactEmailIsRequired": "企業聯系電子郵件不能為空", "contactEmailPlaceholder": "請輸入企業聯系電子郵箱", "contactPerson": "企業連絡人", "contactPersonIsRequired": "企業連絡人不能為空", "contactPersonPlaceholder": "請輸入企業連絡人", "contactPhoneNumber": "聯系手機號碼", "contactPhoneNumberIsRequired": "企業聯繫電話不能為空", "contactPhoneNumberPlaceholder": "請輸入聯系手機號碼", "createOrganization": "創建企業", "deleteOrganizationPrompt": "此操作將永久删除企業，是否繼續？", "editOrganization": "編輯企業", "invalidContactPersonFormat": "無效的連絡人格式，長度為2-64個字元", "invalidEmailFormat": "無效的電子郵箱格式", "invalidPhoneNumberFormat": "無效的手機號碼格式", "keywordsPlaceholder": "企業名稱", "organizationAbbreviationCode": "企業簡寫代碼", "organizationAbbreviationCodeIsRequired": "企業簡寫程式碼不能為空", "organizationAbbreviationCodePlaceholder": "請輸入企業簡寫程式碼", "organizationCode": "企業代碼", "organizationCodePlaceholder": "請輸入企業代碼", "organizationEntityAddress": "企業實體地址", "organizationEntityAddressPlaceholder": "請輸入企業實體地址", "organizationId": "企業ID", "organizationName": "企業名稱", "organizationNameIsRequired": "企業名稱不能為空", "organizationNamePlaceholder": "請輸入企業名稱", "organizationNature": "經營範圍", "organizationNaturePlaceholder": "請輸入企業經營範圍", "organizationTaxCode": "企業稅務代碼", "organizationTaxCodePlaceholder": "請輸入企業稅務代碼"}, "paymentCreation": {"accountNumber": "帳戶號碼", "accountTypeFrom": "帳戶類型", "accountTypeFromPlaceholder": "請輸入帳戶類型來自", "amountFrom": "起", "amountFromPlaceholder": "請輸入轉帳金額起", "amountTo": "至", "amountToPlaceholder": "請輸入轉帳金額至", "beforeRemoveMessage": "是否確認移除{fileName}？", "channel": "渠道", "channelPlaceholder": "請輸入渠道", "dataSource": "資料來源", "dataSourcePlaceholder": "請輸入資料來源", "downloadBranchTemplate": "下載模板文件", "frequency": "頻率", "frequencyPlaceholder": "請輸入頻率", "frequencyRate": "頻次", "frequencyRatePlaceholder": "請輸入頻次", "handleExceedMessage": "目前限制選擇1個文件，本次選擇{thisTimeChoose}個文件，總共選擇{totallyChoose}個文件", "industry": "產業", "industryPlaceholder": "請選擇行業", "linkToDatatable": "批量上傳帳戶", "moveMoneyFrom": "支付帳戶", "moveMoneyFromPlaceholder": "請輸入支付帳戶", "moveMoneyFromType": "轉帳來源類型", "moveMoneyTo": "支付至", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "tableName": "表名稱", "tableNamePlaceholder": "請輸入表格名稱", "totalTransaction": "最大交易次數", "totalTransactionPlaceholder": "請輸入最大交易次數", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "請選擇交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "請選擇交易日期至", "upload": "上傳", "uploadFileRule": "僅上傳csv/xls/xlsx文件，不得超過5MB"}, "paymentEnquiry": {"createDate": "建立日期", "customerAccountNumber": "帳戶號碼", "customerAccountNumberPlaceholder": "請輸入帳戶號碼", "customerAccountType": "帳戶類型", "fromCreateDate": "建立起始日期", "fromTransactionAmount": "交易金額從", "fromTransactionAmountPlaceholder": "請輸入交易金額從", "lastUpdateDate": "最後更新日期", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "payeeCategory": "收款人類別", "payeeCategoryID": "收款人類別ID", "payeeID": "收款人ID", "remarks": "備註", "toCreateDate": "建立截至日期", "toTransactionAmount": "交易金額至", "toTransactionAmountPlaceholder": "請輸入交易金額至", "transactionAmount": "交易金額", "transactionCurrency": "交易貨幣", "transactionDealNumber": "交易編號", "transactionTime": "交易時間"}, "paymentUpdate": {"createDate": "建立日期", "createDatePlaceholder": "請選擇建立日期", "customerAccountNumber": "帳戶號碼", "customerAccountNumberPlaceholder": "請輸入帳戶號碼", "customerAccountType": "帳戶類型", "customerAccountTypePlaceholder": "請輸入帳號類型", "customerNumber": "客戶編碼", "customerNumberPlaceholder": "請輸入客戶編碼", "lastUpdateDate": "最後更新日期", "lastUpdateDatePlaceholder": "請選擇最後更新日期", "payeeCategory": "收款人類別", "payeeCategoryID": "收款人類別ID", "payeeCategoryIDPlaceholder": "請輸入收款人類別ID", "payeeCategoryPlaceholder": "請輸入收款人類別", "payeeID": "收款人ID", "payeeIDPlaceholder": "請輸入收款人ID", "payeeNumber": "收款人號碼", "payeeNumberPlaceholder": "請輸入收款人號碼", "paymentEffectiveDay": "付款生效日", "paymentEffectiveDayPlaceholder": "請輸入付款生效日", "remarks": "備註", "remarksPlaceholder": "請輸入備註", "sandBoxId": "沙盒ID", "sandBoxIdPlaceholder": "請輸入沙盒ID", "status": "狀態", "statusPlaceholder": "請輸入狀態", "title": "編輯支付資料", "transactionAmount": "交易金額", "transactionAmountPlaceholder": "請輸入交易金額", "transactionCurrency": "交易幣種", "transactionCurrencyPlaceholder": "請輸入交易幣種", "transactionDealNumber": "交易編號", "transactionDealNumberPlaceholder": "請輸入交易交易號碼", "transactionTime": "交易時間", "transactionTimePlaceholder": "請輸入交易時間"}, "paymentView": {"title": "查看支付數據"}, "permission": {"accredit": "權限標識", "accreditIsRequired": "權限標識不能為空", "accreditPlaceholder": "請輸入權限標識", "createPermission": "創建權限", "deletePermissionPrompt": "此操作將永久删除權限，是否繼續？", "editPermission": "編輯權限", "keywordsPlaceholder": "權限名稱", "parentId": "上級權限", "parentIdIsRequired": "上級權限不能為空", "parentIdPlaceholder": "請選擇上級權限", "permissionName": "權限名稱", "permissionNameIsRequired": "權限名稱不能為空", "permissionNamePlaceholder": "請輸入權限名稱", "sortNumber": "排序編號", "sortNumberIsRequired": "排序編號不能為空", "sortNumberPlaceholder": "請輸入排序編號"}, "practicalPlatformIntroduction": {"description1": "SOI+數位金融實踐平臺旨在通過實踐練習培養複合型金融科技人才。 該平臺提供了一個全面的培訓環境，涵蓋了廣泛的科技和金融業務知識和用例。", "description2": "該平臺由不同的組件設計和構建，以方便業務和科技背景用戶的使用。 他們是：", "description3": "該平臺根據不同的業務功能提供了一系列開放API。 開放API的背後是類比的虛擬銀行系統、具有眾多金融業務邏輯的保險系統。 所有原料藥均按照香港金融管理局製定的指引製定。 API中的參數基於國際標準ISO20022。", "description4": "孿生金融數據沙箱提供了超過1億套完整的金融數據（包括來自國內外銀行的交易數據）。 所有數據均由自主開發的數據生成引擎生成。 合成數據避免了數據隱私等合法性問題。", "description5": "通過不同的工具和程式設計方法提供多種數據分析功能，如數據處理、自動機器學習、數據視覺化、數據導入、數據生成和數據查詢。", "description6": "该平台为不同场景提供金融模拟系统，全面培养学生在不同金融领域的学习和实践能力。每个系统都包含大量的业务流程。这些系统可以有效地辅助大学教学，真正将理论与实践融入金融科技教学。", "subtitle1": "API 市場", "subtitle2": "孿生金融數據沙箱", "subtitle3": "數據分析", "subtitle4": "金融模拟系统", "title": "平臺介紹"}, "question": {"courseCategoryIsRequired": "課程大分類為必填項", "courseCategoryPlaceholder": "請選擇課程大分類", "courseNameIsRequired": "課程名稱為必填項", "courseNamePlaceholder": "請選擇課程名稱", "createQuestion": "建立試題", "deleteQuestionPrompt": "此操作將永久刪除試題。請是否繼續？", "editQuestion": "編輯試題", "keywordsPlaceholder": "試題內容", "modelAnswer": "標準答案", "modelAnswerIsRequired": "標準答案為必填項", "modelAnswerPlaceholder": "請輸入標準答案", "questionContent": "試題內容", "questionContentIsRequired": "試題內容為必填項", "questionContentPlaceholder": "請輸入試題內容", "subjectNameIsRequired": "課題名稱為必填項目", "subjectNamePlaceholder": "請選擇課題名稱"}, "role": {"createRole": "創建角色", "deleteRolePrompt": "此操作將永久删除角色，是否繼續？", "editRole": "編輯角色", "keywordsPlaceholder": "角色名稱", "permission": "權限", "permissionIsRequired": "權限不能為空", "permissionPlaceholder": "請選擇權限", "remark": "備註", "remarkPlaceholder": "請輸入備註資訊", "roleName": "角色名稱", "roleNameIsRequired": "角色名稱不能為空", "roleNamePlaceholder": "請輸入角色名稱"}, "router": {"404": 404, "addApi": "新增API", "api": "API", "apiAccessGuide": "API 訪問指南", "apiArchitectureDesign": "API 架構設計", "apiCatalogue": "API 目錄", "apiCategoryManagement": "API 分類管理", "apiList": "API 列錶", "apiManagement": "API 管理", "apiMarketPlace": "API 市場", "apiSubcategoryManagement": "API 子分類管理", "apiUsageGuide": "API 使用指南", "apis": "Apis", "applyCustomerDataToken": "申請客戶數據/權杖", "autoMachineLearning": "自動化機器學習", "codingPractice": "編碼練習", "codingQuestionManagement": "編碼題管理", "course": "課程", "courseBusiness": "業務", "courseBusinessCommercialBanking": "商業銀行", "courseBusinessRetailBanking": "零售銀行", "courseBusinessRetailBankingBusinessRisk": "商業風險", "courseBusinessRetailBankingCreditCard": "信用卡", "courseBusinessRetailBankingCreditRisk": "信用風險", "courseBusinessRetailBankingFex": "外匯業務", "courseBusinessRetailBankingFraudRiskManagement": "欺詐風險管理", "courseBusinessRetailBankingLoan": "貸款", "courseBusinessRetailBankingMarketRisk": "市場風險", "courseBusinessRetailBankingOperationRisk": "操作風險", "courseBusinessRetailBankingRegulatoryRisk": "監管風險", "courseBusinessRetailBankingRiskManagementPrinciple": "風險管理原則", "courseBusinessRetailBankingTermDeposit": "定期存款", "courseBusinessRiskManagement": "風險管理", "courseBusinessSupplyChainFinance": "供應鏈金融", "courseCategory": "課程分類", "courseIndustryTalk": "業界討論", "courseIntroduce": "課程介紹", "courseLearning": "課程學習", "courseLearningManagement": "課程學習管理", "coursePartipate": "參與", "courseRecommend": "推薦", "courseTechnology": "技术", "courseTechnologyBlockchain": "區塊鏈", "courseTechnologyDataAnalysis": "數據分析", "courseTechnologyProgrammingLanguage": "編程語言", "courseTransfer": "課程學習轉頁", "creditCardDataCreation": "信用卡數據生成", "creditCardDataEnquiry": "信用卡數據查詢", "crossBorderPayment": "跨境支付", "customApi": "自定義 API", "customerDataCreation": "客戶數據生成", "customerDataEnquiry": "客戶數據查詢", "dashboard": "儀錶盤", "dataAnalysis": "數據分析", "dataAnalysisUseCase": "數據分析案例", "dataAnalysisUseCaseList": "數據分析案例清單", "dataAnalysisUseCaseSubcategory": "數據分析案例分類", "dataCreation": "數據生成", "dataDictionary": "數據字典", "dataDictionaryDetails": "數據字典詳情", "dataDictionarySubcategory": "數據字典分類", "dataEnquiry": "數據查詢", "dataImport": "數據導入", "dataPreparation": "數據處理", "dataSource": "數據源", "dataTable": "數據錶", "dataVisualization": "數據可視化", "depositDataCreation": "存款數據生成", "documentCenter": "文檔中心", "documentList": "文檔列錶", "documentManagement": "文檔管理", "domainPracticals": "實踐練習", "domainPracticalsBusiness": "業務", "domainPracticalsBusinessCommercialBanking": "商業銀行", "domainPracticalsBusinessFraudRiskManagement": "欺詐風險管理", "domainPracticalsBusinessOperationalRisk": "操作風險", "domainPracticalsBusinessRegulatoryRisk": "監管風險", "domainPracticalsBusinessRetailBanking": "零售銀行", "domainPracticalsBusinessRiskManagement": "風險管理", "domainPracticalsTechnology": "技術", "domainPracticalsTechnologyBlockchain": "區塊鏈", "domainPracticalsTechnologyDataAnalysis": "數據分析", "domainPracticalsTechnologyProgrammingLanguage": "編程語言", "downloadCenter": "下載中心", "eKYCWorkflowDetails": "eKYC工作流詳情", "editApi": "編輯API", "explore": "探索", "financialDataCharacteristics": "金融數據特徵", "foreignExchangeDataCreation": "外汇數據生成", "foreignExchangeDataEnquiry": "外匯數據查詢", "fraudDetectionCenter": "銀行反欺詐系統", "fundDataEnquiry": "基金數據查詢", "fundTradingDataCreation": "基金交易數據生成", "homework": "作業", "importApi": "導入API", "insuranceClaims": "保險理賠", "insuranceWorkflowDetails": "保險理賠工作流詳情", "jupyter": "<PERSON><PERSON><PERSON>", "kyc": "KYC", "login": "登入", "loginLog": "登入日誌", "mBridge": "數位貨幣橋", "manageDocument": "管理文檔", "manageEKYCWorkflow": "管理eKYC工作流", "manageInsuranceWorkflow": "管理保險理賠工作流", "myCharts": "我的圖表", "myPath": "學習路徑", "nodeManagement": "節點管理", "obtainDeveloperToken": "獲取開發者權杖", "organizationManagement": "企業管理", "paymentDataCreation": "支付數據生成", "paymentDataEnquiry": "支付數據查詢", "permissionManagement": "權限管理", "practiceManagement": "實踐平臺管理", "practicePlatformIntroduce": "平臺介紹", "practiceTransfer": "實踐平臺中轉頁", "privacyPolicy": "隱私策略", "productDataCreation": "帳戶數據生成", "publicNodeRules": "公共Node規則", "question": "試題", "register": "注册", "resetPassword": "重置密碼", "roleManagement": "角色管理", "ruleManagement": "規則管理", "sandboxManagement": "Sandbox管理", "sandboxUsageGuide": "沙箱使用指南", "sendEmail": "發送郵件", "simulatedSystems": "模擬应用", "stockDataEnquiry": "股票數據查詢", "stockTradingDataCreation": "股票交易數據生成", "subject": "課題", "supplyChainFinance": "供應鏈金融", "systemManagement": "系統管理", "tcManagement": "T&C管理", "technicalDevelopment": "技術開發", "termDepositDataCreation": "定期存款數據生成", "termDepositDataEnquiry": "定期存款數據查詢", "tokenAccessGuide": "令牌訪問指南", "transfer": "中轉頁", "transferDataCreation": "转账數據生成", "transferDataEnquiry": "轉帳數據查詢", "tryoutApi": "試用 API", "userGuide": "用戶手冊", "userManagement": "用戶管理", "virtualBankSystem": "虛擬銀行系統", "virtualCreditCardSystem": "虛擬信用卡系統", "virtualEWallet": "虛擬電子錢包", "virtualInsuranceSystem": "虛擬保險系統", "withdrawalDataCreation": "取款數據生成", "workflowConfig": "工作流配寘", "workflowList": "工作流清單", "workspace": "工作空間"}, "ruleManagement": {"add": "新增", "addRule": "新增規則", "addTip": "你確定要加入嗎？", "canNotEmptyTip": "該欄位不能為空", "deleteTip": "你確定要刪除嗎？", "enterNameTip": "請輸入規則名稱", "name": "名稱", "operatingSuccess": "操作成功", "pleaseSelectNodeGroup": "請選擇節點分組", "ruleManagement": "請選擇節點分組", "updateDate": "更新時間", "updateRule": "編輯規則", "updateTip": "你確定要更新嗎？"}, "sandboxManagement": {"customerNumber": "客戶帳號", "deleteInside": "删除用戶內的沙箱數據", "deleteOut": "删除用戶沙箱數據", "deleteSandboxDataTip": "未選擇任何沙箱數據", "deleteTip": "你確定要删除嗎？", "deleteUserTip": "未選擇任何學生", "email": "電子郵箱", "loginName": "登入名稱", "loginPassword": "登入密碼", "sandboxId": "沙箱ID", "studentUser": "學生用戶"}, "sandboxUsageGuide": {"description1": "每個成功注册到平臺的用戶將被分配一個數據沙箱。 一組預定義的金融數據記錄，包括客戶人口統計、帳戶資訊和交易數據，將分配給每個數據沙箱。 用戶可以在平臺上的類比應用程序和FDC（欺詐檢測中心）中使用這些預先分配的數據記錄。 為了避免重疊，每個數據沙箱中的數據記錄不會跨越不同的用戶（即每個用戶的沙箱數據都是獨立的一套）。", "description2": "在初始設定中，每個數據沙箱攜帶5組預定義的客戶數據。 用戶可以通過應用客戶權杖功能請求最多10組的其他客戶數據。", "description3": "除了通過類比應用程序訪問客戶數據之外，用戶還可以編寫自己的程式碼，通過API調用訪問客戶數據。 此客戶權杖表示客戶已授權訪問其數據。 每個客戶數據記錄都有其唯一的權杖。 客戶權杖必須嵌入到每個API訪問調用中。 您可以通過“申請客戶權杖”功能申請客戶權杖。"}, "simulatedSystems": {"adminList": "管理員賬戶列錶", "bankList": "銀行賬戶列錶", "cnVbsAccount": "中國虛擬銀行帳號", "customerList": "客戶帳號列錶", "downloadEWalletTip": "點擊下載我們的電子錢包演示程序。", "hkVbsAccount": "香港虛擬銀行帳號", "hkVcsAccount": "虛擬信用卡帳號", "login": "登入", "loginName": "登錄名", "loginPassword": "登入密碼", "role": "角色", "selectSupplyChainFinanceTip": "請選擇以下分配的沙箱數據記錄以登入到供應鏈金融系統：", "selectVbsCustomerTip": "請選擇以下分配的沙箱數據記錄以登入到虛擬銀行系統：", "selectVcsCustomerTip": "請選擇以下分配的沙箱數據記錄以登入到虛擬信用卡系統：", "selectVisCustomerTip": "請選擇以下分配的沙箱數據記錄以登入到虛擬保險系統：", "staffList": "員工帳號列錶", "systemManagerList": "系統管理員帳號列表", "systemOperatorList": "系統維運帳號列表", "thVbsAccount": "泰國虛擬銀行帳號", "uaeVbsAccount": "阿聯酋虛擬銀行帳號", "username": "用戶名"}, "stockCreation": {"accountNumber": "帳戶號碼", "batchUploadStockCode": "批量上傳股票代碼", "beforeRemoveMessage": "确认删除 {fileName}？", "channel": "渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadAccountTemplate": "下載帳戶模板", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "頻次", "frequencyRatePlaceholder": "请输入頻次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上傳帳戶", "noOfLots": "手數", "noOfLotsPlaceholder": "請輸入每手股數", "orderType": "訂單類型", "settlementAccount": "結算帳號", "settlementAccountPlaceholder": "請輸入結算帳號", "stockCode": "股票代號", "stockCodeList": "可用的股票代碼", "stockCodeTemplate": "股票代碼模板", "tableName": "表格名称", "tableNamePlaceholder": "请输入表格名称", "totalTransaction": "最大交易次數", "totalTransactionPlaceholder": "请输入最大交易次數", "transactionAccount": "交易帳號", "transactionAccountPlaceholder": "請輸入交易帳號", "transactionAccountType": "交易帳號類型", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transactionDetails": "交易詳情", "transactionType": "交易類型", "upload": "上传", "uploadFileRule": "仅上传 csv/xls/xlsx 文件，不超过 5MB"}, "stockEnquiry": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "createDate": "建立日期", "custodyCharges": "託管費用 (HKD)", "fromCreateDate": "從建立日期", "fromTransactionAmount": "交易金額從", "fromTransactionAmountPlaceholder": "請輸入交易金額從", "lotSize": "每手股數", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "riskRating": "風險評等", "sharingNo": "交易份額", "stockNumber": "股票代碼", "stockPrice": "股票價格 (HKD)", "stockTrdingAmount": "股票交易額 (HKD)", "stockTrdingCommission": "股票交易佣金 (HKD)", "toCreateDate": "到建立日期", "toTransactionAmount": "交易金額至", "toTransactionAmountPlaceholder": "請輸入交易金額至", "tradingOption": "交易類型", "transactionAmount": "交易總金額 (HKD)", "transactionDate": "交易日期", "transactionDesc": "交易描述"}, "stockUpdate": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "custodyCharges": "託管費用 (HKD)", "custodyChargesPlaceholder": "請輸入託管費用", "lotSize": "每手股數", "lotSizePlaceholder": "請輸入每手股數", "riskRating": "風險評等", "riskRatingPlaceholder": "請輸入風險評等", "sharingNo": "交易份額", "sharingNoPlaceholder": "請輸入交易份額", "stockNumber": "股票編號", "stockNumberPlaceholder": "請輸入股票編號", "stockPrice": "股票價格 (HKD)", "stockPricePlaceholder": "請輸入股票價格", "stockTrdingAmount": "股票交易金額 (HKD)", "stockTrdingAmountPlaceholder": "請輸入股票交易金額", "stockTrdingCommission": "股票交易佣金 (HKD)", "stockTrdingCommissionPlaceholder": "請輸入股票交易佣金", "title": "編輯股票資料", "tradingOption": "交易選項", "tradingOptionPlaceholder": "請輸入交易選項", "transactionAmount": "交易金額 (HKD)", "transactionAmountPlaceholder": "請輸入交易金額", "transactionDate": "交易日期", "transactionDatePlaceholder": "請輸入交易日期", "transactionDesc": "交易描述", "transactionDescPlaceholder": "請輸入交易描述"}, "stockView": {"title": "查看股票數據"}, "subject": {"assignment": "作業", "assignmentUpload": "作業上傳", "basic": "基礎", "business": "業務", "category": "類型", "categoryIsRequired": "型別為必填項", "categoryPlaceholder": "請選擇類型", "courseHoursIsRequired": "課題長度為必填項", "courseName": "所屬課程", "courseNameIsRequired": "課程名稱為必填項", "courseNamePlaceholder": "請選擇課程名稱", "createSubject": "建立課題", "deleteSubjectPrompt": "此操作將永久刪除課題。是否要繼續？", "description": "描述", "descriptionIsRequired": "描述為必填項", "descriptionPlaceholder": "請輸入描述", "editSubject": "編輯課題", "high": "高", "keywordsPlaceholder": "課題名稱", "level": "等級", "levelIsRequired": "等級為必填項", "levelPlaceholder": "請選擇等級", "medium": "中", "objective": "目標", "objectiveIsRequired": "目標為必填項目", "objectivePlaceholder": "請輸入目標", "picture": "圖片", "pictureUpload": "圖片上傳", "pictureUploadIsRequired": "圖片上傳為必填項目", "presentationMaterial": "示範材料", "presentationMaterialUpload": "示範資料上傳", "subjectCode": "課題代碼", "subjectCodeIsRequired": "課題代碼為必填項", "subjectCodePlaceholder": "請輸入課題代碼", "subjectDetail": "課題詳細資料", "subjectHours": "課題時長（分鐘）", "subjectHoursPlaceholder": "請輸入課題時長", "subjectName": "課題名稱", "subjectNameIsRequired": "課題名稱為必填項目", "subjectNamePlaceholder": "請輸入課題名稱", "technical": "技術", "video": "影片", "videoLinkUpload": "影片連結上傳"}, "termDepositCreation": {"accountNumber": "帳戶號碼", "amountFrom": "定存金額起", "amountFromPlaceholder": "請輸入金額起", "amountTo": "至", "amountToPlaceholder": "請輸入金額至", "beforeRemoveMessage": "确认删除 {fileName}？", "channel": "渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadAccountTemplate": "下載模板文件", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "頻次", "frequencyRatePlaceholder": "请输入頻次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上傳帳戶", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "period": "訂存期限", "settlementAccount": "結算帳號", "settlementAccountPlaceholder": "請輸入結算帳號", "tableName": "表格名称", "tableNamePlaceholder": "请输入表格名称", "termDepositAmount": "定存金額", "totalTransaction": "最大交易次數", "totalTransactionPlaceholder": "请输入最大交易次數", "transactionAccount": "交易帳號", "transactionAccountPlaceholder": "請輸入交易帳號", "transactionAccountType": "交易帳號類型", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transactionDetails": "交易詳情", "upload": "上传", "uploadFileRule": "仅上传 csv/xls/xlsx 文件，不超过 5MB"}, "termDepositEnquiry": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "createDate": "建立日期", "depositAmount": "存款金額", "depositNumber": "存款編號", "depositNumberPlaceholder": "請輸入存款編號", "fromCreateDate": "建立起始日期", "fromTransactionAmount": "交易金額從", "fromTransactionAmountPlaceholder": "請輸入交易金額從", "maturityAmount": "到期金額", "maturityDate": "到期日", "maturityInterest": "到期利息", "maturityStatus": "到期狀態", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "systemDate": "系統日期", "termInterestRate": "存款利率", "termPeriod": "存款期限", "toCreateDate": "建立截至日期", "toTransactionAmount": "交易金額至", "toTransactionAmountPlaceholder": "請輸入交易金額至"}, "termDepositUpdate": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "createDate": "建立日期", "createDatePlaceholder": "請選擇建立日期", "depositAmount": "存款金額", "depositAmountPlaceholder": "請輸入存款金額", "depositNumber": "存款編號", "depositNumberPlaceholder": "請輸入存款編號", "lastUpdatedDate": "上次更新日期", "lastUpdatedDatePlaceholder": "請選擇上次更新日期", "maturityAmount": "到期金額", "maturityAmountPlaceholder": "請輸入到期金額", "maturityDate": "到期日", "maturityDatePlaceholder": "請輸入到期日", "maturityInterest": "到期利息", "maturityInterestPlaceholder": "請輸入到期利息", "maturityStatus": "到期狀態", "maturityStatusPlaceholder": "請輸入到期狀態", "sandBoxId": "沙箱ID", "sandBoxIdPlaceholder": "請輸入沙箱ID", "systemDate": "系統日期", "systemDatePlaceholder": "請選擇系統日期", "termInterestRate": "存款利率", "termInterestRatePlaceholder": "請輸入存款利率", "termPeriod": "存款期限", "termPeriodPlaceholder": "請輸入存款期間", "title": "編輯定期存款資料"}, "termDepositView": {"title": "查看定期存款數據"}, "tokenAccessGuide": {"description1": "開發人員需要首先在API門戶/閘道中注册。", "description10": "/deposit/account/allAccounts/{customerNumber}/{index}/{items}", "description2": "使用有效憑據，開發人員可以請求訪問令牌。", "description3": "獲取開發者令牌", "description4": "/platform/oauth2/client/token", "description5": "與預先分配的客戶帳戶一起，關聯的客戶令牌已經創建並存儲在資料庫中，並準備好檢索。", "description6": "獲取客戶令牌", "description7": "/sysadmin/login", "description8": "/sysadmin/tokenRetrieval/{loginPK}", "description9": "客戶帳戶査詢：", "subTitle1": "開發者令牌（訪問令牌）", "subTitle2": "如何獲取訪問令牌？", "subTitle3": "示例代碼-如何獲取訪問令牌？", "subTitle4": "客戶令牌", "subTitle5": "如何獲取客戶令牌？", "subTitle6": "示例代碼-如何獲取客戶令牌？", "subTitle7": "示例代碼-如何在API市場訪問API？"}, "transferCreation": {"accountNumber": "帳戶號碼", "accountTypeFrom": "帳戶類型來自", "accountTypeFromPlaceholder": "請輸入帳戶類型來自", "accountTypeTo": "帳戶類型至", "accountTypeToPlaceholder": "請輸入帳戶類型至", "amountFr": "起", "amountTo": "至", "beforeRemoveMessage": "是否確認移除{fileName}？", "channel": "渠道", "channelPlaceholder": "請輸入渠道", "dataSource": "資料來源", "dataSourcePlaceholder": "請輸入資料來源", "downloadBranchTemplate": "下載模板文件", "frequency": "頻率", "frequencyPlaceholder": "請輸入頻率", "frequencyRate": "頻次", "frequencyRatePlaceholder": "請輸入頻次", "handleExceedMessage": "目前限制選擇1個文件，本次選擇{thisTimeChoose}個文件，總共選擇{totallyChoose}個文件", "linkToDatatable": "批量上傳帳戶", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "tableName": "表名稱", "tableNamePlaceholder": "請輸入表格名稱", "totalTransaction": "最大交易次數", "totalTransactionPlaceholder": "請輸入最大交易次數", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "請選擇交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "請選擇交易日期至", "transferAmount": "轉帳金額", "transferAmountFrom": "起", "transferAmountFromPlaceholder": "請輸入轉帳金額起", "transferAmountTo": "至", "transferAmountToPlaceholder": "請輸入轉帳金額至", "transferFrom": "轉帳自", "transferFromPlaceholder": "請輸入轉出帳戶", "transferFromType": "轉出帳戶", "transferTo": "轉帳至", "transferToPlaceholder": "請輸入轉入帳戶", "transferToType": "轉入帳戶", "upload": "上傳", "uploadFileRule": "僅上傳csv/xls/xlsx文件，不得超過5MB"}, "transferEnquiry": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "ccy": "貨幣", "channel": "渠道", "channelID": "渠道ID", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "crDrMaintInd": "交易類型", "custRelationMgrCode": "客戶經理編號", "dataDetails": "數據詳情", "fromCreateDate": "起始創建時間", "fromTransactionAmount": "交易金額", "fromTransactionAmountPlaceholder": "請輸入交易金額從", "hkidfirstIssue": "香港身分證首次簽發日期", "hkidissueDate": "香港身分證簽發日期", "numberGreaterThanPlaceholder": "{to} 必須大於 {form}", "refAccountNumber": "對方帳號", "tableName": "數據表名稱", "tableNamePlaceholder": "请选择數據表名稱", "tfrSeqNo": "對方交易流水號", "toCreateDate": "結束創建時間", "toTransactionAmount": "至", "toTransactionAmountPlaceholder": "請輸入交易金額至", "tranAmt": "交易金額", "tranDate": "交易日期", "tranDesc": "交易描述", "tranDescPlaceholder": "請輸入交易描述", "tranSeq": "交易流水號", "tranType": "交易類型"}, "transferUpdate": {"accountNumber": "帳號", "accountNumberPlaceholder": "請輸入帳號", "actualBalAmt": "實際餘額金額", "actualBalAmtPlaceholder": "請輸入實際餘額金額", "branchCode": "分行代碼", "branchCodePlaceholder": "請輸入分行代碼", "ccy": "貨幣", "ccyPlaceholder": "請輸入貨幣", "channel": "渠道", "channelID": "渠道ID", "channelIDPlaceholder": "請輸入渠道ID", "channelPlaceholder": "請輸入渠道", "clearingCode": "銀行代碼", "clearingCodePlaceholder": "請輸入銀行代碼", "countryCode": "國家代碼", "countryCodePlaceholder": "請輸入國家代碼", "crDrMaintInd": "交易類型", "crDrMaintIndPlaceholder": "請輸入交易類型", "previousBalAmt": "上一個餘額金額", "previousBalAmtPlaceholder": "請輸入上一個餘額金額", "refAccountNumber": "對方帳號", "refAccountNumberPlaceholder": "請輸入對方帳號", "reference": "參考", "referencePlaceholder": "請輸入參考", "tfrSeqNo": "對方交易流水號", "tfrSeqNoPlaceholder": "請輸入對方交易流水號", "title": "編輯轉帳數據", "tranAmt": "交易金額", "tranAmtPlaceholder": "請輸入交易金額", "tranDate": "交易日期", "tranDatePlaceholder": "請選擇交易日期", "tranDesc": "交易描述", "tranDescPlaceholder": "請輸入交易描述", "tranSeq": "交易流水號", "tranSeqPlaceholder": "請輸入交易流水號", "tranType": "交易類型", "tranTypePlaceholder": "請輸入交易類型"}, "transferView": {"title": "查看轉帳數據"}, "user": {"activeStatus": "啟動狀態", "activeStatusPlaceholder": "用戶啟動狀態", "birthday": "出生日期", "birthdayPlaceholder": "請選擇出生日期", "cardId": "證件號碼", "cardIdIsRequired": "證件號碼不能為空", "cardIdPlaceholder": "請輸入證件號碼", "cardType": "證件類型", "cardTypeIsRequired": "證件類型不能為空", "cardTypePlaceholder": "請選擇證件類型", "createUser": "創建用戶", "deleteUserPrompt": "此操作將永久删除用戶，是否繼續？", "department": "部門", "departmentPlaceholder": "請輸入部門", "downloadTemplate": "下載範本", "editUser": "編輯用戶", "email": "電子郵箱", "emailIsRequired": "電子郵箱不能為空", "emailPlaceholder": "請輸入電子郵箱", "experienceYear": "工作年限", "experienceYearPlaceholder": "請輸入工作年限", "graduateSchool": "畢業學校", "graduateSchoolPlaceholder": "請輸入畢業學校", "importUser": "導入用戶", "invalidCardIdFormat": "無效的證件號碼格式", "invalidEmailFormat": "無效的電子郵箱格式", "invalidNicknameFormat": "無效的昵稱格式，長度為2-64個字元", "invalidPasswordFormat": "至少要包含大小寫字母和數位，且長度為8-16個字元", "invalidPhoneNumberFormat": "無效的手機號碼格式", "invalidUsernameFormat": "無效的用戶姓名格式，長度為2-64個字元", "isBlacklist": "是否在黑名單", "isBlacklistPlaceholder": "是否在黑名單", "jobTitle": "職務", "jobTitlePlaceholder": "請輸入職務", "keywordsPlaceholder": "請輸入電子郵箱/用戶名", "level": "用戶級別", "levelIsRequired": "用戶等級不能為空", "levelPlaceholder": "請輸入級別", "nickname": "昵稱", "nicknameIsRequired": "昵稱不能為空", "nicknamePlaceholder": "請輸入昵稱", "organization": "組織名稱", "organizationPlaceholder": "請輸入組織名稱", "password": "密碼", "passwordIsRequired": "密碼不能為空", "passwordPlaceholder": "請輸入登入密碼", "phoneNumber": "手機號碼", "phoneNumberIsRequired": "手機號碼不能為空", "phoneNumberPlaceholder": "請輸入手機號碼", "pleaseSelectUserOrganization": "請選擇用戶所屬企業", "role": "角色", "roleIdsPlaceholder": "請選擇用戶角色", "roleIsRequired": "角色不能為空", "rolePlaceholder": "用戶角色", "selectFile": "選擇文件", "sex": "性別", "sexPlaceholder": "請選擇性別", "startImport": "開始導入", "studentOrStaffNumber": "學/工號", "studentOrStaffNumberPlaceholder": "請輸入學/工號", "username": "用戶名", "usernameIsRequired": "用戶姓名不能為空", "usernamePlaceholder": "請輸入用戶姓名"}, "workflowConfig": {"accountOpeningProcessNodeList": "開戶流程節點列表", "accountOpeningProcessNodeRules": "開戶流程節點規則", "apiMethod": "API 請求方式", "apiUrl": "API 地址", "clickUploadTip": "點擊上傳", "createWorkflow": "創建工作流", "customWorkflow": "自定義工作流", "deleteTip": "你確定要删除嗎？", "eKYC": "eKYC", "eKYCTip1": "提示：使用公共節點創建您的Workflow。", "eKYCTip2": "<p> <span>請按照以下說明開始創建Workflow：</span> </p> <p> <span>1.Workflow創建工具</span> </p> <p> <span>&nbsp; &nbsp; 點擊<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow創建工具</b></a>跳轉到線上編輯工具。</span> </p> <p> <span>2.下載模板</span> </p> <p> <span>&nbsp; &nbsp; 在上一頁“工作流程”的公共工作流中點擊下載按鈕，得到公共工作流範本點擊下載。</span> </p> <p> <span>3.使用workflow模板</span> </p> <p> <span>&nbsp; &nbsp; 在打開的工具頁面中點擊open，選擇上一步下載得到的EKYC.bpmn文件。</span> </p> <p> <span>4.開始創建</span> </p> <p> <span>&nbsp; &nbsp; 在workflow模板的基礎上開始創建您的workflow。</span> </p> <p> <span>5.保存工作流</span> </p> <p> <span>&nbsp; &nbsp; 在工具頁面左下角點擊“下載”圖標，保存您創建的工作流文檔，然後點擊右側的“上傳文件”上傳您保存的工作流文件。</span> </p> <p> <br/> </p> <p> <span>Workflow創建工具的詳細操作手册，請<a target=\"_blank\" href=\"https://simnectz-portal.cefar.cuhk.edu.hk/document/soi-plus/工作流操作檔案.pdf\"><b>點擊下載</b></a>。</span> </p> <p> <span>使用公共Node創建workflow，需要遵守Node之間的順序規則，請參攷“<a target=\"_blank\" href=\"/domain-practicals/public-node-rules?type=KYC\"})\"><b>公共Node規則</b></a>”。</span> </p> <p> <br/> </p>", "file": "文件", "fileError": "請上傳文件，文件格式：.bpmn，大小在2MB以內", "fileNumberError": "一次只能上傳一個文檔。 請先上傳所選文檔。", "fileSizeError": "文件大小不能超過2MB！", "group": "分組", "insurance": "創建保險理賠", "insuranceClaimsProcessNodeList": "保險理賠流程節點列表", "insuranceTip1": "提示：使用公共節點創建您的Workflow。", "insuranceTip2": "<p> <span>請按照以下說明開始創建Workflow：</span> </p> <p> <span>1.Workflow創建工具</span> </p> <p> <span>&nbsp; &nbsp; 點擊<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow創建工具</b></a>跳轉到線上編輯工具。</span> </p> <p> <span>2.下載模板</span> </p> <p> <span>&nbsp; &nbsp; 在上一頁“工作流程”的公共工作流中點擊下載按鈕，得到公共工作流範本點擊下載。</span> </p> <p> <span>3.使用workflow模板</span> </p> <p> <span>&nbsp; &nbsp; 在打開的工具頁面中點擊open，選擇上一步下載得到的insurance_claim.bpmn文件。</span> </p> <p> <span>4.開始創建</span> </p> <p> <span>&nbsp; &nbsp; 在workflow模板的基礎上開始創建您的workflow。</span> </p> <p> <span>5.保存工作流</span> </p> <p> <span>&nbsp; &nbsp; 在工具頁面左下角點擊“下載”圖標，保存您創建的工作流文檔，然後點擊右側的“上傳文件”上傳您保存的工作流文件。</span> </p> <p> <br/> </p> <p> <span>Workflow創建工具的詳細操作手册，請<a target=\"_blank\" href=\"https://simnectz-portal.cefar.cuhk.edu.hk/document/soi-plus/工作流操作檔案.pdf\"><b>點擊下載</b></a>。</span> </p> <p> <span>使用公共Node創建workflow，需要遵守Node之間的順序規則，請參攷“<a target=\"_blank\" href=\"/domain-practicals/public-node-rules?type=insurance\"})\"><b>公共Node規則</b></a>”。</span> </p> <p> <br/> </p>", "kycAccountOpeningProcess": "KYC -開戶流程", "name": "名稱", "process": "步驟", "processGroup": "流程組", "publicWorkflow": "公共工作流", "selectProcessType": "選擇流程類型", "uploadFailed": "上傳失敗！ 請稍後再試。", "uploadSuccess": "上傳成功！", "uploadTip": "只上傳*.bpmn文檔，文件大小不能超過2MB。", "workflowNameError": "請填寫工作流名稱"}, "workspace": {"createNow": "現在創建", "createWorkspaceTip": "創建你的專屬工作空間，實現與您的團隊的線上合作與編碼工作。", "deleteWorkspaceTip": "確定要删除工作空間嗎？ 删除之後，工作空間的數據將不可恢復。", "deleting": "正在删除", "entry": "進入", "noResource": "您沒有權限創建工作空間，請聯系管理員。", "offTime": "上次關機時間", "runStatus": "運行狀態", "running": "正在運行", "shutDownWorkspaceTip": "確定要關閉服務嗎？ 請確認您的文檔都已經保存。", "shutdown": "已關閉", "shuttingDown": "正在關閉", "start": "啟動", "starting": "正在啟動", "url": "訪問地址", "workspaceName": "工作空間名稱", "workspaceNameErrorTip": "工作空間名稱僅支持輸入大小寫字母、'-'和'_'", "workspaceNameTip": "請輸入工作空間名稱"}}}