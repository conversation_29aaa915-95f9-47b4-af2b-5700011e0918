{"soi": {"apiAccessGuide": {"description1": "For a developer to integrate or obtain resources from a given API, they need to be granted access. Various API providers initiate various controls to their APIs, meaning that only those with valid credentials can be allowed to access these resources. Therefore, API access is the process of ensuring that only users with authenticated credentials can access an API and use its resources.", "description10": "Once approved, the developer will be able to access account information such as account numbers, balances, transactions, and more.", "description11": "As part of this process, you must obtain permissions from each and every user. If banks give you API access, it doesn't mean that you can immediately access and make use of all their data. The client has to consent to use their data for this purpose.", "description12": "For all developers registered onto SOI+ API Portal Platform, you will automatically assign with 10 simulated customer accounts from SOI+ Virtual Financial System. You will be able to access these simulated customer data through the APIs published in the API Market Place.", "description13": "All APIs registered in SOI+ API Portal/Market Place adopted the same approach in API access. Standard OAuth2.0 used as the authorisation method for customers to allow the client to access their account.", "description14": "To access simulated customer data in virtual financial systems, you need to obtain the followings tokens:", "description15": "Developer Token (Access Token)", "description16": "Customer <PERSON>", "description2": "API access is achieved through API Management. One of the core functions that API management uses to allow or deny admission is the API gateway. Gateways are used to enable API calls to be received and processed appropriately.", "description3": "Developers needs to first register yourself on the API portal/gateway established by the API provider. With valid credentials, you can now start to access the API.", "description4": "For you to gain access to an API, you'll need to enter an API key to prove your identity. An API key is basically an authentic string of letters and numbers. Developers need to register with the API provider for them to get an API key.", "description5": "For the case of Open Banking API, all APIs are protected and highly secured. Bank and the TSP could apply the industry- standard OAuth 2.0 framework as the delegated authorisation method for customers to allow the TSP to access their account.", "description6": "OAuth 2.0 is the industry-standard authorisation framework that enables the bank to grant data access for a Banking Open API.", "description7": "Access tokens are used in token-based authentication to enable an authorised TSP to securely access the Banking Open API based on the OAuth 2.0 and Open ID Connect framework.", "description8": "All APIs registered in the API Market Place adopted OAuth2.0 as standard authorisation framework.", "description9": "Open Banking APIs work by allowing authorized users (e.g. developers) to access specific information about bank customers' accounts. In order to do this, the developer must first register for access and then be approved by the bank.", "subTitle1": "OAuth2.0 authentication process", "subTitle2": "Open Banking API Access", "subTitle3": "How does an Open Banking API work?", "subTitle4": "Access API in SOI+ API Portal/Market Place"}, "apiArchitectureDesign": {"connectivityDescriptionKey": "Access to Source Data: ", "connectivityDescriptionValue": "From systems, repositories or external services", "connectivityTitle": "System APIs", "description1": "API-led connectivity refers to the technique of using reusable and well-designed APIs to link data and applications.", "description2": "With API-led integration, it allows enterprises to build composable building blocks that can be exposed and applied to create innovative business capabilities.", "description3": "This methodology enables developers to disintegrate data silos and achieve enhanced delivery through self-service and reuse. Developers can practice API management to make optimal use of the exposed assets.", "description4": "In API-led connectivity, different layers of APIs are created that take care of various systems, processes, and end-user experiences.", "interfaceDescriptionKey": "Presentation of Data: ", "interfaceDescriptionValue": "Security and governance", "interfaceTitle": "Experience  APIs", "orchestrationDescriptionKey": "Application of Logic: ", "orchestrationDescriptionValue": "Enrichment and transformation", "orchestrationTitle": "Process APIs", "primaryTitle": "API-led Connectivity Building Blocks"}, "apiList": {"addApi": "Add API", "addApiTip": "This page is used to add APIs, then click on Create API.", "addEndpoint": "Add Endpoint", "apiIntroduction": "API Introduction", "apiName": "API Name", "apiNameEn": "API English Name", "apiNameEnIsRequired": "API English name is required", "apiNameExist": "API name already exists", "apiNameIsRequired": "API Chinese name is required", "apiNameZh": "API Chinese Name", "apiSettings": "API Settings", "className": "Class Name", "classNameIsRequired": "Class name cannot be empty", "clickUpload": "Click Upload", "confirmDeleteTip": "Are you sure to delete it?", "createApi": "Create API", "createSuccess": "Create Success", "deleteSuccess": "Delete Success", "description": "Description", "descriptionEn": "API English Description", "descriptionZh": "API Chinese Description", "editApi": "Edit Api", "editSuccess": "Edit Success", "endpoint": "Endpoint", "endpointDesigner": "Endpoint Designer", "image": "Image", "importApi": "Import API", "jsonErrorTip": "Not a valid JSON file, please check and upload again.", "method": "Method", "subclassName": "Subclass Name", "subclassNameIsRequired": "Subclass name cannot be empty", "swaggerJsonTipEn": "Please fully paste your English version of Swagger JSON into the box below.", "swaggerJsonTipZh": "Please fully paste your Chinese version of Swagger JSON into the box below.", "swaggerSettings": "Swagger Settings", "targetUrl": "Target URL", "targetUrlIsRequired": "The target address cannot be empty", "targetUrlTip": "Starting with http:// or https://", "tryoutApi": "Tryout API", "uploadFailed": "Upload Failed", "uploadSuccess": "Upload Success", "urlOrFile": "URL or upload file for API user guide", "userGuide": "User Guide", "valueIsRequired": "Please enter swagger json"}, "apiMarketPlace": {"apiIntroduction": "API Introduction", "apiName": "API Name", "apiProvider": "API Provider", "apiStatus": "API Status", "catalogName": "Catalog Name", "customApiFromOthers": "APIs From Other Users", "customApiSelf": "Custom APIs", "deleteApiTip": "Are you sure you want to delete this API?", "deleteSuccess": "Successfully deleted API", "downloadSwagger": "Download Swagger", "generateToken": "Generate Token", "sandboxToken": "Sandbox Token", "tryoutApi": "Tryout API", "userGuide": "User Guide", "username": "Username"}, "apis": {"category": "Category", "categoryIsRequired": "Category is required", "className": "Category Chinese Name", "classNameEn": "Category English Name", "classNameEnIsRequired": "Category english name is required", "classNameIsRequired": "Category chinese name is required", "classSubName": "Subcategory Chinese Name", "classSubNameEn": "Subcategory English Name", "classSubNameEnIsRequired": "Subcategory english name is required", "classSubNameIsRequired": "Subcategory chinese name is required", "createApiCategory": "Creat API Category", "createApiSubcategory": "Creat API Subcategory", "deleteApiClassTip": "This operation will delete the category. Do you want to continue?", "description": "Chinese Description", "descriptionEn": "English Description", "descriptionEnIsRequired": "English description is required", "descriptionIsRequired": "Chinese description is required", "image": "Image", "updateApiCategory": "Update API Category", "updateApiSubcategory": "Update API Subcategory"}, "applyCustomerDataToken": {"applyFailedTip": "Apply failed", "applyNewToken": "Apply New Token", "applySandboxTokenFailedTip": "The previous sandbox token application failed, please resubmit the request.", "applySubmitSuccessTip": "Your request has been submitted. Please click the \"Check Task Status\" button to check the progress of the task.", "applySuccessTip": "Apply success", "checkStatus": "Check Status", "correctNumberApplications": "Please enter the correct number of applications", "correctNumberRange": "The quantity is between 1- {max}", "customerNumber": "Customer Number", "loginName": "Login Name", "loginPassword": "Login Password", "maxSandboxNumber": "The remaining amount of Sandbox Data that can be applied for is: {max}", "number": "Number", "waitingCreationCompleteTip": "Creating sandbox data, please wait for completion."}, "authorize": {"activateTip": "Please log in to the email and click on the link to activate", "agreeEndUserPrivacyPolicy": "Agree to End User Privacy Policy", "agreePrivacyPolicyTip": "Please read and check the privacy policy first", "birthday": "Birthday", "birthdayPlaceholder": "Please select your birthday", "captcha": "<PERSON><PERSON>", "captchaIsRequired": "Captcha is required", "cardId": "Card Number", "cardIdIsRequired": "Card number is required", "cardIdPlaceholder": "Please enter your card ID number", "cardType": "Card Type", "cardTypeIsRequired": "Card type is required", "cardTypePlaceholder": "Please select your card type", "confirmPassword": "Confirm Password", "confirmPasswordIsRequired": "Please enter your login password again", "confirmPasswordPlaceholder": "Enter your login password again", "courseManagement": "Course Management", "department": "Department", "departmentPlaceholder": "Please enter your department", "email": "Email", "emailIsRequired": "Email is required", "emailPlaceholder": "Please enter your email", "emailSendSuccess": "Email send success", "experienceYear": "Experience Year", "experienceYearPlaceholder": "Please enter your years of work experience", "forgotPassword": "Forgot Password", "graduateSchool": "Graduate School", "graduateSchoolPlaceholder": "Please enter your graduation school", "invalidCardIdFormat": "Invalid card id format", "invalidConfirmPasswordFormat": "Entered passwords differ", "invalidEmailFormat": "Invalid email format", "invalidNicknameFormat": "Invalid nickname format, with a length of 2-64 characters", "invalidPasswordFormat": "At least uppercase and lowercase letters and numbers should be included, and the length should be 8-16 characters", "invalidPhoneNumberFormat": "Invalid phone number format", "invalidUsernameFormat": "Invalid username format, with a length of 2-64 characters", "jobTitle": "Job Title", "jobTitlePlaceholder": "Please enter your job title", "learningSpace": "Learning Space", "level": "User level", "levelIsRequired": "User level is required", "levelPlaceholder": "Please enter the level", "login": "<PERSON><PERSON>", "loginNow": "Login Now", "logout": "Logout", "newAccountPleaseFirstRegister": "New account please first register", "newPassword": "New Password", "nickname": "Nickname", "nicknameIsRequired": "Nickname is required", "nicknamePlaceholder": "Please enter your nickname", "organization": "Organization Name", "organizationPlaceholder": "Please enter your organization name", "password": "Password", "passwordIsRequired": "Password is required", "passwordPlaceholder": "Please enter your login password", "phoneNumber": "Phone Number", "phoneNumberIsRequired": "Phone number is required", "phoneNumberPlaceholder": "Please enter your phone number", "practicePlatform": "Practical Platform", "proficiencyTest": "Proficiency Test", "register": "Register", "registerSuccess": "Register Success", "resetPassword": "Reset Password", "resetPasswordTip": "Please go to the email and click on the link for authentication. The link is valid for 5 minutes", "sendEmail": "Send Email", "setNewPassword": "Set New Password", "sex": "Gender", "sexPlaceholder": "Please select your gender", "simnectzOfficialWebsite": "SIMNECTZ Official Website", "soiPlatform": "Digital Finance Learning And Innovation Platform", "studentOrStaffNumber": "Student/Staff Number", "studentOrStaffNumberPlaceholder": "Please enter your student/staff number", "systemManagement": "System Management", "username": "Username", "usernameIsRequired": "Username is required", "usernamePlaceholder": "Please enter your name"}, "autoMachineLearning": {"accuracy": "Accuracy", "adaboostTip": "Adaboost is a type of ensemble learning, which can be used for classification and regression. The core idea is to train different classifiers (weak classifiers) for the same training set, and then group these weak classifiers to form a stronger final classifier (strong classifier).", "addModel": "Add Model", "addSubTable": "Add Sub Table", "addSubTableLabel": "Optionally, there can be multiple sub-tables extracted from the original data. If you need to generate more features for the current data, you can extract a part of the columns from the main table, generate a sub-table, and establish an association with the main table. Featuretools can automatically generate more features.", "addTransposeSubTable": "Add Transpose Sub Table", "addTransposeSubTableContent": "Required, the fields that need to be changed from column to row", "addTransposeSubTableLabel": "There can be multiple sub-tables extracted from the original data through column-to-row conversion. You can select a part of the columns from the original data, generate a sub-table through the column-to-row operation, and then establish an association with the main table. Featuretools can automatically generate more features. At present, this type of sub-table is associated with the main table through an \"index column\". When the main table does not specify an \"index column\", an \"index column\" is generated.", "additionalVariables": "additional_variables", "ardRegressionTip": "It is a kind of linear regression method. Used to deal with regression problems.", "autoSklearnParams": "Auto Sklearn Params", "autoSklearnPlatform": "Auto Sklearn Platform", "batchPredict": "<PERSON><PERSON>dict", "batchPredictTips": "Based on the amount of data you have uploaded, the estimated time required for prediction is approximately 5 minutes for 3000 pieces of data; Predicting 8000 data takes about 30 minutes; 20000 data predictions take approximately 90 minutes. After submitting the file, the prediction process cannot be interrupted and the current page cannot be closed.", "build": "Build", "buildSuccess": "Build Successful", "clickUpload": "click upload", "columnPlaceholder": "Please select", "createTableTip": "<p>Based on the data dictionary you uploaded, a table named <i><b>'prediction_result'</b></i> will be created. You can select this table to analyze the prediction results.</p>", "customize": "Customize", "dRFTip": "A random forest algorithm that can be used for classification and regression. It is to randomly extract data samples and features, and train multiple decision trees to prevent over-fitting and improve generalization ability.", "dataDictFile": "Data Dictionary File", "dataFile": "Data File", "dataManagement": "Data Management", "dataStatisticsCharts": "Data Statistics Charts", "dataStatisticsTable": "Data Statistics Table", "decisionTreeTip": "Decision tree is a basic classification and regression method with fast classification speed. When learning, use the training data to establish a decision tree model according to the principle of minimizing the loss function.", "deeplearningTip": "A deep neural network algorithm based on random grid is introduced.", "default": "<PERSON><PERSON><PERSON>", "deleteSuccessTip": "Delete successful", "deleteTestFileTip": "Are you sure you want to delete this test file?", "deleteTip": "Are you sure you want to delete these data?", "descriptionPlaceholder": "Please enter a description for this file", "dragFileHere": "Drag file here, or", "earlyStoppingRounds": "Used to stop training new models when the number of trained models will no longer increase.", "editModel": "Edit Model", "ensembleNbest": "Use ensemble_nbest to select the integrated model.", "ensembleSize": "Select the integrated quantity of the model from the model library.", "excludeEstimators": "Algorithms to be excluded.", "excludeTheseAlgorithms": "Algorithms not used for model building.", "extraTreesTip": "ET or Extra-Trees can be used for classification and regression. The algorithm is very similar to the random forest algorithm, and both are composed of many decision trees.", "fileDetails": "File Details", "fillType": "Deal with missing data, add 0 or delete the row.", "framework": "Framework", "gBMTip": "The gradient boosting tree algorithm is a kind of integrated learning. Compared with the adaboost model, its anti-noise ability is stronger, but its implementation is slower.", "gLMTip": "Generalized linear model is a general term for a class of algorithms, which can be used to deal with classification and regression problems. The most commonly used ones are logistic regression and Poisson regression.", "gaussianNbTip": "A classification method based on <PERSON><PERSON>' theorem and the assumption of independence of characteristic conditions.", "gaussianProcessTip": "Gaussian process. Generally, a joint distribution is established for the regression value of the data to deal with the regression problem.", "gradientBoostingTip": "The gradient boosting tree algorithm is a kind of integrated learning. Compared with the adaboost model, its anti-noise ability is stronger, but the implementation is slower.", "h2OParams": "H2O Params", "h2OPlatform": "H2O Platform", "h2oBuildResult": "H2O Build Result", "ignoreColumn": "Ignore Col<PERSON>n", "index": "Index", "indexCanNotBeEmpty": "Index Can Not Be Empty", "indexColumn": "Index Column", "indexLabel": "Required, the index column of the sub-table, through which the column is associated with the main table", "info": "Information", "initailConfigurationsViaMetalearning": "Use configuration items to initialize the hyperparameter algorithm.", "kNearestNeighborsTip": "K-nearest neighbor algorithm (KNN) is a class of techniques that can be applied to classification or regression. It has a wide range of applications and high accuracy when the sample size is large enough.", "liblinearSvrTip": "Linear support vector regression. Suitable for large amount of data processing, used to deal with regression problems.", "libsvmSvcTip": "Classification method based on support vector machine.", "libsvmSvrTip": "Support vector regression, dealing with regression problems.", "maxModelsToBuild": "Used to specify the number of models to be built in addition to the Stacked Ensemble model.", "maxRuntimeSecs": "The time used to train the model before training the Stacked Ensemble model.", "modelDetails": "Model Details", "modelManagement": "Model Management", "modelName": "Model Name", "modelPerformanceCharts": "Model Performance Charts", "modelPlaceholder": "Please input description for this model", "modelStatisticsTable": "Model Statistics Table", "modelType": "Model Type", "name": "Name", "nfolds": "k-fold The number of folds for cross-validation.", "noPerformanceChart": "No Performance Chart", "numericalClassification": "Numerical Classification", "numericalPrediction": "Numerical Regression", "onlyXlsxFiles": "Only xlsx / xls files are supported", "other": "Optional, the remaining fields of the sub-table", "perRunTimeLimit": "The time limit (in seconds) for a single call of the machine learning model. If the machine learning algorithm runs beyond the time limit, the model fitting will terminate. Set this value high enough so that typical machine learning algorithms can fit the training data.", "pleasUploadFileFirst": "Please upload a file first", "pleaseSelectAdataType": "Please select a data type", "pleaseWaitTip": "Please wait for the file to finish uploading and fetch column info", "predict": "Predict", "predictSuccess": "Successful prediction", "randomForestTip": "Random forest algorithm can be used for classification and regression. It is to randomly extract data samples and features, and train multiple decision trees to prevent over-fitting and improve generalization ability.", "ratios": "The split rate of training data (training/testing).", "resamplingStrategy": "When preventing over-fitting, the parameter that needs to be used, holdout is the way to split the data, which refers to the split ratio of training data and test data.", "responseColumn": "Predict Columns", "resultFile": "Prediction result file", "ridgeRegressionTip": "Ridge regression is a type of linear regression, used to deal with regression problems, and can be used to solve the overfitting problem of standard linear regression.", "rulesTip1": "Please enter a positive integer", "rulesTip2": "Please enter a decimal greater than zero and less than 1", "rulesTip3": "Please enter a positive integer greater than 30", "saveSuccessTip": "Successfully saved", "saveTopnModels": "Select the top n models that need to be saved last.", "seed": "The seed of SMAC will determine the output file name.", "selectResponseColumnTip": "Please select predicted column", "selectTextColumnTip": "Please select text column", "setColumnRoles": "Set Column Roles", "settingColumnsType": "Setting Columns Type", "sgdTip": "In stochastic gradient descent, each weight update is completed using only one sample in the data set, that is, an epoch process has only one iteration and one update data. Sensitive to noise.", "sortMetric": "The ordering rules of the top model. The default is AUTO, which means that ACU is selected for two-class classification, mean_per_class_error is selected for multi-classification, and deviance is selected for regression.", "stackedEnsembleTip": "Using the integrated learning method, all models are assembled using the stack method.", "status": "Status", "testFile": "Test File", "textClassification": "Text Classification", "textColumn": "Text Column", "thePredictionIs": "Predict Results", "timeLeftForThisTask": "The time (in seconds) used to search for the appropriate model. By increasing this value, there is a higher chance of finding a better model.", "trainSize": "The parameter of resampling_strategy refers to the split ratio of training data.", "transformType": "The way of data preprocessing.", "type": "Type", "updateModelSuccess": "Update success", "updateModelTip": "Are you sure you want to update this model?", "uploadAcceptFileType": "Only .csv or .txt files can be uploaded", "uploadData": "Upload Data", "uploadDataDict": "Upload Data Dictionary", "uploadFailTip": "Failed to upload file", "uploadFailed": "Upload failed, please try again", "uploadFileFirstTip": "Please upload the file first", "uploadFirstTip": "Drag the file here, or", "uploadLastTip": "click upload", "uploadSuccess": "Upload successful", "uploadSuccessTip": "Successfully uploaded file", "uploadTestFile": "Upload Test Files", "uploadTime": "Upload Time", "xgradientBoostingTip": "The Xgboost algorithm can also be applied to classification and regression problems. XGBoost is characterized by fast calculation speed and good model performance."}, "codingPractice": {"addCodingQuestion": "Add Coding Question", "addQuestionTip": "You confirm that you want to create the coding question?", "apiInfo": "API Information", "apiInformation": "API Information(Chinese)", "apiInformationEn": "API Information(English)", "apiInformationTip": "Please enter API information, including API name, URL, request header information, request parameters and response information.(Chinese)", "apiInformationTipEn": "Please enter API information, including API name, URL, request header information, request parameters and response information.(English)", "codeDescription": "Code Description(Chinese)", "codeDescriptionEn": "Code Description(English)", "codeDescriptionTip": "Please enter a description of the code(Chinese)", "codeDescriptionTipEn": "Please enter a description of the code(English)", "compareAnswers": "Compare Answers", "coreCodePattern": "Core Code Pattern", "coreCodePatternTip": "The preset code in the code box has been assigned the class name, method name, parameter name, please do not modify or rename, just return the value directly", "creator": "Creator", "databaseInformation": "Database Information(Chinese)", "databaseInformationCoding": "Database Information", "databaseInformationEn": "Database Information(English)", "databaseInformationTip": "Please enter database information(Chinese)", "databaseInformationTipEn": "Please enter database information(English)", "deleteQuestionTip": "Are you sure you want to delete the coding question?", "description": "Description", "downloadCodeTip": "Unable to download empty file", "editCodingQuestion": "Edit Coding Question", "fontSize": "Font Size", "history": "Practice History", "initCodeDescription": "Initial code description", "initialCode": "Initial Code", "initialCodeTip": "Please enter initial code", "modelAnswer": "Model Answer", "modelAnswerTip": "Please enter model answer", "practiceQuestionsCategory": "Practice Questions Category", "printOutputTip": "There is no output statement, please try to print the result.", "programmingLanguage": "Programming Language", "questionCategory": "Category", "questionCategoryTip": "Please select or input question category(Chinese)", "questionCategoryTipEn": "Please input question category(English)", "questionDescription": "<PERSON><PERSON>(Chinese)", "questionDescriptionEn": "<PERSON><PERSON>(English)", "questionDescriptionTable": "<PERSON><PERSON>", "questionDescriptionTip": "Please enter stem(Chinese)", "questionDescriptionTipEn": "Please enter stem(English)", "questionPrimaryCategory": "Primary Category(Chinese)", "questionPrimaryCategoryEn": "Primary Category(English)", "questionPrimaryCategoryTable": "Primary Category", "questionStatus": "Question Status", "questionSubcategory": "Subcategory(Chinese)", "questionSubcategoryEn": "Subcategory(English)", "questionSubcategoryTable": "Subcategory", "questionSubcategoryTip": "Please select or enter a question subcategory(Chinese)", "questionSubcategoryTipEn": "Please select or enter a question subcategory(English)", "questionTitle": "Question Title(Chinese)", "questionTitleEn": "Question Title(English)", "questionTitleSearch": "Question Title", "questionTitleTable": "Question Title", "questionTitleTip": "Please enter question title(Chinese)", "questionTitleTipEn": "Please enter question title(English)", "questionTitleTipSearch": "Please enter question title", "questionsManagement": "Practice Questions Management", "resetCode": "This operation may not save your code, do you want to continue?", "run": "Run", "runTip": "Click to run, the results of the run will be displayed here.", "serialNumber": "No.", "startPracticing": "Start Practicing", "supportedLanguages": "Supported Languages", "tabSize": "<PERSON><PERSON>", "target": "Target(Chinese)", "targetCoding": "Target", "targetEn": "Target(English)", "targetTip": "Please enter a target(Chinese)", "targetTipEn": "Please enter a target(English)", "theme": "Theme", "trainingPurpose": "Training Purpose(Chinese)", "trainingPurposeCoding": "Training Purpose", "trainingPurposeEn": "Training Purpose(English)", "trainingPurposeTip": "Please enter training purpose(Chinese)", "trainingPurposeTipEn": "Please enter training purpose(English)", "updateQuestionTip": "You confirm that you want to update the coding question?", "viewModelAnswers": "View Model Answers", "yourAnswers": "Your Answer"}, "common": {"404Tip": "Please check if the URL you entered is correct, or click the button below to return to the homepage.", "Next": "Next", "Previous": "Previous", "active": "Active", "add": "Add", "back": "Back", "backHome": "Back Home", "cancel": "Cancel", "chinese": "Chinese", "chineseMainlandId": "Chinese Mainland ID", "clear": "Clear", "close": "Close", "confirm": "Confirm", "copyright": "Empowered by SIMNECTZ®", "create": "Create", "createDate": "Create Date", "createTime": "Create Time", "creator": "Creator", "customerRoleHelpDocument": "Customer Role Help Document", "default": "<PERSON><PERSON><PERSON>", "defaultTitle": "SOI+ Platform", "delete": "Delete", "description": "Description", "details": "Details", "download": "Download", "edit": "Edit", "endDate": "End Date", "english": "English", "error": "Error", "export": "Export", "exportLevel": "Export(10+ years)", "false": "False", "female": "Female", "goAnalyze": "Go Analyze", "helpDocument": "Help Document", "hongKongId": "Hong Kong ID", "import": "Import", "invalidNumber": "Invalid Number", "junior": "Junior(1-3 years)", "keywords": "Keywords", "macaoId": "Macao ID", "male": "Male", "middle": "Middle(3-6 years)", "more": "More", "noData": "No Data", "normal": "Normal", "notActive": "Not Active", "oops": "OOPS!", "operate": "Operate", "pageNotFound": "The page was not found and cannot be accessed...", "platformName": "Digital Finance Learning And Innovation Platform", "practicePlatform": "Practical Platform", "reset": "Reset", "save": "Save", "search": "Search", "senior": "Senior(6-10 years)", "start": "Start", "startDate": "Start Date", "submit": "Submit", "success": "Success", "tableOfContents": "Table of Contents", "taiwanId": "Taiwan ID", "tip": "Tip", "to": "To", "token": "Token", "true": "True", "update": "Update", "updateBy": "Update By", "updateTime": "Update Time", "upload": "Upload", "view": "View", "warning": "Warning", "welcome": "Welcome!"}, "course": {"courseCategoryIsRequired": "Course category is required", "courseCategoryPlaceholder": "Please select course category", "courseCode": "Course Code", "courseCodeIsRequired": "Course code is required", "courseCodePlaceholder": "Please enter course code", "courseDescription": "Course Description", "courseDescriptionIsRequired": "Course description is required", "courseDescriptionPlaceholder": "Please enter course description", "courseDetail": "Course Detail", "courseName": "Course Name", "courseNameIsRequired": "Course name is required", "courseNamePlaceholder": "Please enter course name", "coursePicture": "Course Picture", "coursePictureIsRequired": "Course picture is required", "createCourse": "Create Course", "creator": "Creator", "deleteCoursePrompt": "This operation will permanently delete the course Prompt. Do you want to continue?", "deleteError": "If relevant subjects exist under the course, please delete the subjects under the corresponding course", "editCourse": "Edit Course", "keywordsPlaceholder": "Course name", "updateBy": "Update By"}, "courseCategory": {"courseCategory": "Course Category", "courseCategoryIsRequired": "Course category is required", "courseCategoryPicture": "Course Category Picture", "courseCategoryPictureIsRequired": "Course category picture is required", "courseCategoryPlaceholder": "Please enter course category", "createCourseCategory": "Create Course Category", "deleteCourseCategoryPrompt": "This operation will permanently delete the course category Prompt. Do you want to continue?", "deleteError": "If relevant courses exist under the course category, please delete the courses under the corresponding course category", "editCourseCategory": "Edit Course Category", "keywordsPlaceholder": "Course category"}, "courseLearning": {"businessMarkdown": "Business Documents", "completeStatus": "Complete Status", "contentDescription": "Content Description", "contentDetails": "Content Details", "courseLearningSpace": "SOI+ Course Learning Space", "deletePathPrompt": "This operation will permanently delete the path Prompt. Do you want to continue?", "documentDirectory": "Document Directory", "firstStudyTime": "First Study Time", "job": "Job", "myPath": "Learning Path", "name": "Name", "noData": "No Data", "pathMaintenance": "Path Maintenance", "role": "Role", "studentOrStaffNumber": "Staff/Student ID", "subTitle": "The SOI+ course learning space is committed to creating a curriculum system that covers a wide range of topics and is rich in content, in order to meet your learning needs in the Fintech field. Whether you are a beginner or a professional, SOI+ can provide you with tailored learning solutions to help you soar higher on the path of future development.", "subscribe": "subscribe", "technologyMarkdown": "Technology Documents", "video": "Video"}, "creditCardCreation": {"beforeRemoveMessage": "Confirm to remove {fileName}?", "creditCardNumber": "Credit Card Number", "creditCardNumberPlaceholder": "Please enter the credit card number", "dataSource": "Data Source", "dataSourcePlaceholder": "Please enter the data source", "downloadCreditCardNumberTemplate": "Download Credit Card Number Template", "frequency": "Frequency", "frequencyPlaceholder": "Please enter the frequency", "frequencyRate": "Frequency Rate", "frequencyRatePlaceholder": "Please enter the frequency rate", "handleExceedMessage": "Currently restricted to select 1 file, this time choose {thisTimeChoose} files，and totally choose {totallyChoose} files", "industry": "Industry", "linkToDatatable": "Batch Upload Account", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "tableName": "Table Name", "tableNamePlaceholder": "Please enter the table name", "totalTransaction": "Max Transactions", "totalTransactionPlaceholder": "Please enter the max transactions", "transactionAmountFrom": "Transaction Amount From", "transactionAmountFromPlaceholder": "Please enter the transaction amount from", "transactionAmountTo": "To", "transactionAmountToPlaceholder": "Please enter the transaction amount to", "transactionBy": "Transaction By", "transactionByFrom": "From", "transactionByFromPlaceholder": "Please enter the transaction by from", "transactionByTo": "To", "transactionByToPlaceholder": "Please enter the transaction by to", "transactionByType": "Account", "transactionDate": "Transaction Date", "transactionDateFrom": "From", "transactionDateFromPlaceholder": "Please select the transaction date from", "transactionDateTo": "To", "transactionDateToPlaceholder": "Please select the transaction date to", "transactionTo": "Transaction To", "upload": "Upload", "uploadFileRule": "Only upload csv/xls/xlsx files, not exceed 5MB"}, "creditCardEnquiry": {"bookingAmount": "Booking Amount", "bookingCcy": "Booking Ccy", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter country code", "createDateRange": "Create Date Range", "creditCardNumber": "Credit Card Number", "creditCardNumberPlaceholder": "Please enter credit card number", "creditCardType": "Credit Card Type", "dataDetails": "Data Details", "dealNumber": "Deal Number", "displayName": "Display Name", "fromCreateDate": "From Create Date", "fromTransactionAmount": "Transaction Amount From", "fromTransactionAmountPlaceholder": "Please enter transaction amount from", "merchantName": "Merchant Name", "merchantNumber": "Merchant Number", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "tableName": "Table Name", "tableNamePlaceholder": "Please select table name", "toCreateDate": "To Create Date", "toTransactionAmount": "Transaction Amount To", "toTransactionAmountPlaceholder": "Please enter transaction amount to", "transactionAmount": "Transaction Amount", "transactionCcy": "Transaction Ccy", "transactionTime": "Transaction Time", "transactionType": "Transaction Type"}, "creditCardUpdate": {"authorizationNumber": "Authorization Number", "authorizationNumberPlaceholder": "Please enter the authorization number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter the branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "creditCardNumber": "Credit Card Number", "creditCardNumberPlaceholder": "Please enter the credit card number", "creditCardType": "Credit Card Type", "creditCardTypePlaceholder": "Please enter the credit card type", "dealNumber": "Deal Number", "dealNumberPlaceholder": "Please enter the deal number", "merchantBalance": "Merchant Balance", "merchantBalancePlaceholder": "Please enter the merchant balance", "merchantName": "Merchant Name", "merchantNamePlaceholder": "Please enter the merchant name", "merchantNumber": "Merchant Number", "merchantNumberPlaceholder": "Please enter the merchant number", "title": "Edit Credit Card Data", "transactionAmount": "Transaction Amount", "transactionAmountPlaceholder": "Please enter the transaction amount", "transactionCcy": "Transaction Ccy", "transactionCcyPlaceholder": "Please enter the transaction ccy", "transactionTime": "Transaction Time", "transactionTimePlaceholder": "Please select the transaction time", "transactionType": "Transaction Type", "transactionTypePlaceholder": "Please enter the transaction type"}, "creditCardView": {"title": "View Credit Card Data"}, "customerBulkCreation": {"accommodation": "Accommodation", "accommodationPercentagePlaceholder": "Please enter the percentage of accommodation", "accommodationPlaceholder": "Please select the accommodation", "accountDataCreation": "Account Data Creation", "accountDataCreationPlaceholder": "Please select the account data creation", "accountType": "Account Type", "accountTypePlaceholder": "Please select the account type", "ageGroup": "Age Group", "ageGroupPercentagePlaceholder": "Please enter the percentage of age group", "ageGroupPlaceholder": "Please select the age group", "branchCode": "Branch Code", "branchCodePlaceholder": "Please select the branch code", "branchNumberFrom": "From", "branchNumberFromPlaceholder": "Please enter the branch number from", "branchNumberTo": "To", "branchNumberToPlaceholder": "Please enter the branch number to", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "contactPreferredMethod": "Contact Preferred Method", "contactPreferredMethodPercentagePlaceholder": "Please enter the percentage of contact preferred method", "contactPreferredMethodPlaceholder": "Please select the contact preferred method", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "countryOfBirth": "Country Of Birth", "countryOfBirthPlaceholder": "Please select the country of birth", "createDate": "Create Date", "createDatePlaceholder": "Please select the create date", "currencyCode": "Currency Code", "currencyCodePlaceholder": "Please select the currency code", "customerStatus": "Customer Status", "customerStatusPlaceholder": "Please select the customer status", "education": "Education", "educationPercentagePlaceholder": "Please enter the percentage of education", "educationPlaceholder": "Please select the education", "employerIndustry": "Employer Industry", "employerIndustryPlaceholder": "Please select the employer industry", "employmentStatus": "Employment Status", "employmentStatusPercentagePlaceholder": "Please enter the percentage of employment status", "employmentStatusPlaceholder": "Please select the employment status", "fexAccountCurrencyCode": "FEX Account Currency Code", "fexAccountCurrencyCodePlaceholder": "Please select the fex account currency code", "fromCreateDate": "From Create Date", "gender": "Gender", "genderPercentagePlaceholder": "Please enter the percentage of gender", "genderPlaceholder": "Please select the Gender", "householdIncome": "Household Income", "householdIncomeAmountFrom": "From", "householdIncomeAmountFromPlaceholder": "Please enter the household income amount from", "householdIncomeAmountTo": "To", "householdIncomeAmountToPlaceholder": "Please enter the household income amount to", "householdIncomePercentagePlaceholder": "Please enter the percentage of household income", "householdIncomePlaceholder": "Please select the household income", "maritalStatus": "Marital Status", "maritalStatusPercentagePlaceholder": "Please enter the percentage of marital status", "maritalStatusPlaceholder": "Please select the marital status", "maximumNumber": "Maximum number added is {number}", "monthlyIncome": "Monthly Income", "monthlyIncomeFrom": "From", "monthlyIncomeFromPlaceholder": "Please enter the monthly income from", "monthlyIncomePercentagePlaceholder": "Please enter the percentage of monthly income", "monthlyIncomePlaceholder": "Please select the monthly income", "monthlyIncomeTo": "To", "monthlyIncomeToPlaceholder": "Please enter the monthly income to", "nationality": "Nationality", "nationalityPercentagePlaceholder": "Please enter the percentage of nationality", "nationalityPlaceholder": "Please select the nationality", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "occupation": "Occupation", "occupationPlaceholder": "Please select the Occupation", "otherMonthlyIncome": "Other Monthly Income", "otherMonthlyIncomeFrom": "From", "otherMonthlyIncomeFromPlaceholder": "Please enter the other monthly income from", "otherMonthlyIncomePlaceholder": "Please select the other monthly income", "otherMonthlyIncomeTo": "To", "otherMonthlyIncomeToPlaceholder": "Please enter the other monthly income to", "permanentResidenceStatus": "Permanent Residence Status", "permanentResidenceStatusPercentagePlaceholder": "Please enter the percentage of permanent residence status", "permanentResidenceStatusPlaceholder": "Please select the permanent residence status", "preLanguage": "Pre Language", "preLanguagePercentagePlaceholder": "Please enter the percentage of pre language", "preLanguagePlaceholder": "Please select the pre language", "region": "Region", "regionPercentagePlaceholder": "Please enter the percentage of region", "regionPlaceholder": "Please select the region", "sensitiveStatus": "Sensitive Status", "sensitiveStatusPercentagePlaceholder": "Please enter the percentage of sensitive status", "sensitiveStatusPlaceholder": "Please select the sensitive status", "sumOfPercentages": "The percentages for the {title} must sum to 100", "tableName": "Table Name", "tableNamePlaceholder": "Please select the table name", "toCreateDate": "To Create Date", "totalCustomers": "Total Customers", "totalCustomersPlaceholder": "Please enter the total customers"}, "customerCreation": {"bulkCreation": "Bulk Creation", "creationType": "Creation Type", "singleCreation": "Single Creation"}, "customerEnquiry": {"accommodation": "Accommodation", "accommodationPlaceholder": "Please select the accommodation", "ageGroup": "Age Group", "ageGroupPlaceholder": "Please select the age group", "branchCode": "Branch Code", "clearingCode": "Clearing Code", "contactPreferredMethod": "Contact Preferred Method", "contactPreferredMethodPlaceholder": "Please select the contact preferred method", "countryCode": "Country Code", "createDate": "Create Date", "customerId": "Customer Id", "customerIdType": "Customer Id Type", "customerNumber": "Customer Number", "education": "Education", "educationPlaceholder": "Please select the education", "employmentStatus": "Employment Status", "employmentStatusPlaceholder": "Please select the employment status", "firstName": "First Name", "fromCreateDate": "From Create Date", "gender": "Gender", "genderPlaceholder": "Please select the gender", "lastName": "Last Name", "maritalStatus": "Marital Status", "maritalStatusPlaceholder": "Please select the marital status", "nationality": "Nationality", "nationalityPlaceholder": "Please select the nationality", "permanentResidenceStatus": "Permanent Residence Status", "permanentResidenceStatusPlaceholder": "Please select the permanent residence status", "preLanguage": "Pre Language", "preLanguagePlaceholder": "Please select the pre language", "residentialRegionName": "Residential Region Name", "residentialRegionNamePlaceholder": "Please select the residential region name", "sensitiveStatus": "Sensitive Status", "sensitiveStatusPlaceholder": "Please select the sensitive status", "toCreateDate": "To Create Date"}, "customerSingleCreation": {"accommodation": "Accommodation", "accommodationPlaceholder": "Please select the Accommodation", "accountDataCreation": "Account Data Creation", "accountDataCreationPlaceholder": "Please select the account data creation", "accountType": "Account Type", "accountTypePlaceholder": "Please select the account type", "ageGroup": "Age Group", "ageGroupPlaceholder": "Please select the age group", "branchCode": "Branch Code", "branchCodePlaceholder": "Please select the branch code", "branchNumberFrom": "From", "branchNumberFromPlaceholder": "Please enter the branch number from", "branchNumberTo": "To", "branchNumberToPlaceholder": "Please enter the branch number to", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "contactPreferredMethod": "Contact Preferred Method", "contactPreferredMethodPlaceholder": "Please select the contact preferred method", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "countryOfBirth": "Country Of Birth", "countryOfBirthPlaceholder": "Please select the country of birth", "createDate": "Create Date", "createDatePlaceholder": "Please select the create date", "currencyCode": "Currency Code", "currencyCodePlaceholder": "Please select the currency code", "customerStatus": "Customer Status", "customerStatusPlaceholder": "Please select the customer status", "education": "Education", "educationPlaceholder": "Please select the Education", "employerIndustry": "Employer Industry", "employerIndustryPlaceholder": "Please select the employer industry", "employmentStatus": "Employment Status", "employmentStatusPlaceholder": "Please select the employment status", "fexAccountCurrencyCode": "FEX Account Currency Code", "fexAccountCurrencyCodePlaceholder": "Please select the fex account currency code", "fromCreateDate": "From Create Date", "gender": "Gender", "genderPlaceholder": "Please select the Gender", "householdIncome": "Household Income", "householdIncomeAmountFrom": "From", "householdIncomeAmountFromPlaceholder": "Please enter the household income amount from", "householdIncomeAmountTo": "To", "householdIncomeAmountToPlaceholder": "Please enter the household income amount to", "householdIncomePlaceholder": "Please select the household_income", "maritalStatus": "Marital Status", "maritalStatusPlaceholder": "Please select the marital status", "monthlyIncome": "Monthly Income", "monthlyIncomeFrom": "From", "monthlyIncomeFromPlaceholder": "Please enter the monthly income from", "monthlyIncomePlaceholder": "Please select the monthly income", "monthlyIncomeTo": "To", "monthlyIncomeToPlaceholder": "Please enter the monthly income to", "nationality": "Nationality", "nationalityPlaceholder": "Please select the Nationality", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "occupation": "Occupation", "occupationPlaceholder": "Please select the Occupation", "otherMonthlyIncome": "Other Monthly Income", "otherMonthlyIncomeFrom": "From", "otherMonthlyIncomeFromPlaceholder": "Please enter the other monthly income from", "otherMonthlyIncomePlaceholder": "Please select the other_monthly_income", "otherMonthlyIncomeTo": "To", "otherMonthlyIncomeToPlaceholder": "Please enter the other monthly income to", "permanentResidenceStatus": "Permanent Residence Status", "permanentResidenceStatusPlaceholder": "Please select the permanent residence status", "preLanguage": "Pre Language", "preLanguagePlaceholder": "Please select the pre language", "region": "Region", "regionPlaceholder": "Please select the Region", "sensitiveStatus": "Sensitive Status", "sensitiveStatusPlaceholder": "Please select the sensitive status", "tableName": "Table Name", "tableNamePlaceholder": "Please select the table name", "toCreateDate": "To Create Date"}, "customerUpdate": {"accommodation": "Accommodation", "accommodationPlaceholder": "Please enter the accommodation", "age": "Age", "ageGroup": "Age Group", "ageGroupPlaceholder": "Please enter the age group", "agePlaceholder": "Please enter the Age", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter the branch code", "chineseName": "Chinese Name", "chineseNamePlaceholder": "Please enter the chinese name", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "companyAddressLine1": "Company Address Line1", "companyAddressLine1Placeholder": "Please enter the company address line1", "companyAddressLine2": "Company Address Line2", "companyAddressLine2Placeholder": "Please enter the company address line2", "companyAddressLine3": "Company Address Line3", "companyAddressLine3Placeholder": "Please enter the company address line3", "companyAddressLine4": "Company Address Line4", "companyAddressLine4Placeholder": "Please enter the company address line4", "contactPreferredMethod": "Contact Preferred Method", "contactPreferredMethodPlaceholder": "Please enter the contact preferred method", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "countryOfBirth": "Country Of Birth", "countryOfBirthPlaceholder": "Please enter the country of birth", "countryOfResidence": "Country Of Residence", "countryOfResidencePlaceholder": "Please enter the country of residence", "createDate": "Create Date", "createDatePlaceholder": "Please enter the create date", "cusPrestEmploymentSinceDate": "Cus Prest Employment Since Date", "cusPrestEmploymentSinceDatePlaceholder": "Please enter the cus prest employment since date", "custPresentAddPeriod": "Cust Present Add Period", "custPresentAddPeriodPlaceholder": "Please enter the cust present add period", "custPresentAddUpdateDate": "Cust Present Add Update Date", "custPresentAddUpdateDatePlaceholder": "Please enter the cust present add update date", "custPresentEmploymentPeriod": "Cust Present Employment Period", "custPresentEmploymentPeriodPlaceholder": "Please enter the cust present employment period", "custPreviousAddPeriod": "Cust Previous Add Period", "custPreviousAddPeriodPlaceholder": "Please enter the cust previous add period", "custPreviousAddUpdateDate": "Cust Previous Add Update Date", "custPreviousAddUpdateDatePlaceholder": "Please enter the cust previous add update date", "custPreviousEmploymntDate": "Cust Previous Employment Date", "custPreviousEmploymntDatePlaceholder": "Please enter the cust previous employment date", "custPreviousEmploymntPeriod": "Cust Previous Employment Period", "custPreviousEmploymntPeriodPlaceholder": "Please enter the cust previous employment period", "custRelationMgrCode": "Cust Relation Mgr Code", "custRelationMgrCodePlaceholder": "Please enter the cust relation mgr code", "customerID1": "Customer ID1", "customerID1Placeholder": "Please enter the customer ID1", "customerID2": "Customer ID2", "customerID2Placeholder": "Please enter the customer ID2", "customerIDType1": "Customer ID Type1", "customerIDType1Placeholder": "Please enter the customer ID type1", "customerIDType2": "Customer ID Type2", "customerIDType2Placeholder": "Please enter the customer ID type2", "customerNumber": "Customer Number", "customerNumberPlaceholder": "Please enter the customer number", "customerStatus": "Customer Status", "customerStatusPlaceholder": "Please enter the customer status", "education": "Education", "educationPlaceholder": "Please enter the education", "emailAddress1": "Email Address1", "emailAddress1Placeholder": "Please enter the email address1", "employerCompanyName": "Employer Company Name", "employerCompanyNamePlaceholder": "Please enter the employer company name", "employerIndustry": "Employer Industry", "employerIndustryPlaceholder": "Please enter the employer industry", "employmentStatus": "Employment Status", "employmentStatusPlaceholder": "Please enter the employment status", "firstName": "First Name", "firstNamePlaceholder": "Please enter the first name", "gender": "Gender", "genderPlaceholder": "Please enter the Gender", "hkidfirstIssue": "HK ID First Issue", "hkidfirstIssuePlaceholder": "Please enter the HK ID first issue", "hkidissueDate": "HK ID Issue Date", "hkidissueDatePlaceholder": "Please enter the HK ID issue date", "householdIncome": "Household Income", "householdIncomePlaceholder": "Please enter the household income", "issueCountry1": "Issue Country1", "issueCountry1Placeholder": "Please enter the issue country1", "issueCountry2": "Issue Country2", "issueCountry2Placeholder": "Please enter the issue country2", "issueDate1": "Issue Date1", "issueDate1Placeholder": "Please enter the issue date1", "issueDate2": "Issue Date2", "issueDate2Placeholder": "Please enter the issue date2", "lastName": "Last Name", "lastNamePlaceholder": "Please enter the last name", "maritalDate": "Marital Date", "maritalDatePlaceholder": "Please enter the marital date", "maritalStatus": "Marital Status", "maritalStatusPlaceholder": "Please enter the marital status", "minorInd": "Minor Ind", "minorIndPlaceholder": "Please enter the minor ind", "mobilePhoneNumber1": "Mobile Phone Number1", "mobilePhoneNumber1Placeholder": "Please enter the mobile phone number1", "monthlySalary": "Monthly Salary", "monthlySalaryPlaceholder": "Please enter the monthly salary", "nationality1": "Nationality1", "nationality1Placeholder": "Please enter the Nationality1", "occupation": "Occupation", "occupationPlaceholder": "Please enter the occupation", "otherMonthlyIncome": "Other Monthly Income", "otherMonthlyIncomePlaceholder": "Please enter the other monthly income", "permanentResidenceStatus": "Permanent Residence Status", "permanentResidenceStatusPlaceholder": "Please enter the permanent residence status", "personalInfoUpdateDate": "Personal Info Update Date", "personalInfoUpdateDatePlaceholder": "Please enter the personal info update date", "position": "Position", "positionPlaceholder": "Please enter the Position", "preLanguage1": "Pre Language1", "preLanguage1Placeholder": "Please enter the pre language1", "preTimeFrom1": "Pre Time From1", "preTimeFrom1Placeholder": "Please enter the pre time from1", "preTimeTo1": "Pre Time To1", "preTimeTo1Placeholder": "Please enter the pre time to1", "residentAddressMaintBranch": "Resident Address Maint Branch", "residentAddressMaintBranchPlaceholder": "Please enter the resident address maint-branch", "residentialAddressLine1": "Residential Address Line1", "residentialAddressLine1Placeholder": "Please enter the residential address line1", "residentialAddressLine2": "Residential Address Line2", "residentialAddressLine2Placeholder": "Please enter the residential address line2", "residentialAddressLine3": "Residential Address Line3", "residentialAddressLine3Placeholder": "Please enter the residential address line3", "residentialAddressLine4": "Residential Address Line4", "residentialAddressLine4Placeholder": "Please enter the residential address line4", "residentialDistrictName": "Residential District Name", "residentialDistrictNamePlaceholder": "Please enter the residential district name", "residentialRegionName": "Residential Region Name", "residentialRegionNamePlaceholder": "Please enter the residential region name", "seniorInd": "Senior Ind", "seniorIndPlaceholder": "Please enter the senior ind", "sensitiveStatus": "Sensitive Status", "sensitiveStatusPlaceholder": "Please enter the sensitive status", "spouseDateOfBirth": "Spouse Date Of Birth", "spouseDateOfBirthPlaceholder": "Please enter the spouse date of birth", "spouseID": "Spouse ID", "spouseIDPlaceholder": "Please enter the spouse id", "spouseIDType": "Spouse ID Type", "spouseIDTypePlaceholder": "Please enter the spouse id type", "spouseName": "Spouse Name", "spouseNamePlaceholder": "Please enter the spouse name", "tableName": "Table Name", "tableNamePlaceholder": "Please enter the table name", "title": "Edit Customer Data", "weChatID": "WeChat ID", "weChatIDPlaceholder": "Please enter the wechat id"}, "customerView": {"title": "View Customer Data"}, "dataAnalysisUseCase": {"dataAnalysisBank": "Bank", "dataAnalysisInsurance": "Insurance", "dataAnalysisStock": "Stock", "document": "Document", "name": "Name", "video": "Video"}, "dataDictionary": {"advancedTransactionDataBasedOnKnowledgeGraphData": "Advanced Transaction Data Based On Knowledge Graph Data", "creditCardLossPredict": "Credit Card Loss Predict", "genericTransactionDataWithAMLModuleIncluded": "Generic Transaction Data With AML Module Included", "knowledgeGraphData": "Knowledge Graph Data", "selfGeneratedData": "Self Generated Data", "useCaseData": "Use Case Data"}, "dataEnquiryDetails": {"CustRelationMgrCode": "Cust Relation Mgr Code", "accommodation": "Accommodation", "accountNumber": "Account Number", "actualBalAmt": "Actual Bal Amt", "age": "Age", "ageGroup": "Age Group", "authorizationNumber": "Authorization Number", "bookingAmount": "Booking Amount", "bookingCcy": "Booking Ccy", "branchCode": "Branch Code", "ccy": "<PERSON><PERSON><PERSON><PERSON>", "channel": "Channel", "channelID": "Channel ID", "chineseName": "Chinese Name", "clearingCode": "Clearing Code", "companyAddressLine1": "Company Address Line1", "companyAddressLine2": "Company Address Line2", "companyAddressLine3": "Company Address Line3", "companyAddressLine4": "Company Address Line4", "contactPreferredMethod": "Contact Preferred Method", "countryCode": "Country Code", "countryOfBirth": "Country Of Birth", "countryOfResidence": "Country Of Residence", "crDrMaintInd": "Tran Type", "createDate": "Create Date", "creditCardNumber": "Credit Card Number", "creditCardType": "Credit Card Type", "cusPrestEmploymentSinceDate": "Cus Prest Employment Since Date", "custPresentAddPeriod": "Cust Present Add Period", "custPresentAddUpdateDate": "Cust Present Add Update Date", "custPresentEmploymentPeriod": "Cust Present Employment Period", "custPreviousAddPeriod": "Cust Previous Add Period", "custPreviousAddUpdateDate": "Cust Previous Add Update Date", "custPreviousEmploymntDate": "Cust Previous Employment Date", "custPreviousEmploymntPeriod": "Cust Previous Employment Period", "custRelationMgrCode": "Customer Relation Mgr Code", "custodyCharges": "Custody Charges (HKD)", "customerAccountNumber": "Account Number", "customerAccountType": "Account Type", "customerID1": "Customer ID1", "customerID2": "Customer ID2", "customerIDType1": "Customer ID Type1", "customerIDType2": "Customer ID Type2", "customerNumber": "Customer Number", "customerStatus": "Customer Status", "dateOfBirth": "Date Of Birth", "dealNumber": "Deal Number", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "depositNumber": "Deposit Number", "displayName": "Display Name", "education": "Education", "emailAddress1": "Email Address1", "employerCompanyName": "Employer Company Name", "employerIndustry": "Employer Industry", "employmentStatus": "Employment Status", "exchangeAmoutInForeignCurrency": "Exchange Amount In Foreign Currency", "exchangeAmoutInLocalCurrency": "Exchange Amount In Local Currency", "exchangeRate": "Exchange Rate", "firstName": "First Name", "foreignCurrency": "Foreign Currency", "fundCcy": "Fund Ccy", "fundCode": "Fund Code", "fundPrice": "Fund Price", "gender": "Gender", "hKIDFirstIssue": "HK ID First Issue", "hKIDIssueDate": "HK ID Issue Date", "hkidfirstIssue": "Hong Kong ID First Issue", "hkidissueDate": "Hong Kong ID Issue Date", "householdIncome": "Household Income", "id": "ID", "issueCountry1": "Issue Country1", "issueCountry2": "Issue Country2", "issueDate1": "Issue Date1", "issueDate2": "Issue Date2", "lastName": "Last Name", "lastUpdateDate": "Last Update Date", "lastUpdatedDate": "Last Updated Date", "localCurrency": "Local Currency", "lotSize": "Lot Size", "maritalDate": "Marital Date", "maritalStatus": "Marital Status", "maturityAmount": "Maturity Amount", "maturityDate": "Maturity Date", "maturityInterest": "Maturity Interest", "maturityStatus": "Maturity Status", "merchantBalance": "Merchant Balance", "merchantName": "Merchant Name", "merchantNumber": "Merchant Number", "minorInd": "Minor Ind", "mobilePhoneNumber1": "Mobile Phone Number1", "monthlySalary": "Monthly Salary", "nationality1": "Nationality1", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "occupation": "Occupation", "otherMonthlyIncome": "Other Monthly Income", "payeeCategory": "Payee Category", "payeeCategoryID": "Payee Category ID", "payeeID": "Payee ID", "payeeNumber": "Payee Number", "paymentEffectiveDay": "Payment Effective Day", "permanentResidenceStatus": "Permanent Residence Status", "personalInfoUpdateDate": "Personal Info Update Date", "position": "Position", "postBalInForeignCurrencyAccount": "Post Bal In Foreign Currency Account", "preLanguage1": "Pre Language1", "preTimeFrom1": "Pre Time From1", "preTimeTo1": "Pre Time To1", "prevBalInForeignCurrencyAccount": "Prev <PERSON>l In Foreign Currency Account", "previousBalAmt": "Previous Bal Amt", "refAccountNumber": "Ref Account Number", "reference": "Reference", "remarks": "Remarks", "residentAddressMaintBranch": "Resident Address Maint Branch", "residentialAddressLine1": "Residential Address Line1", "residentialAddressLine2": "Residential Address Line2", "residentialAddressLine3": "Residential Address Line3", "residentialAddressLine4": "Residential Address Line4", "residentialDistrictName": "Residential District Name", "residentialRegionName": "Residential Region Name", "riskRating": "Risk Rating", "sandBoxId": "Sandbox ID", "seniorInd": "Senior Ind", "sensitiveStatus": "Sensitive Status", "sharingNo": "Sharing No.", "spouseDateOfBirth": "Spouse Date Of Birth", "spouseID": "Spouse ID", "spouseIDType": "Spouse ID Type", "spouseName": "Spouse Name", "status": "Status", "stockNumber": "Stock Code", "stockPrice": "Stock Price (HKD)", "stockTrdingAmount": "Stock Trading Amount (HKD)", "stockTrdingCommission": "Stock Trading Commission (HKD)", "systemDate": "System Date", "termInterestRate": "Term Interest Rate", "termPeriod": "Term Period", "tfrSeqNo": "Tfr Seq No", "tradingAmount": "Trading Amount", "tradingOption": "Trading Option", "tranAmt": "Tran Amt", "tranDate": "Tran Date", "tranDesc": "Tran Desc", "tranSeq": "Tran Seq", "tranType": "Tran Type", "transactionAmount": "Transaction Amount (HKD)", "transactionCcy": "Transaction Ccy", "transactionCurrency": "Transaction Currency", "transactionDate": "Transaction Date", "transactionDealNumber": "Transaction Deal Number", "transactionDesc": "Transaction Desc", "transactionTime": "Transaction Time", "transactionType": "Transaction Type", "trdingCommission": "Trading Commission", "weChatID": "We Chat ID"}, "dataImport": {"clickUploadTip": "Click to upload", "createDatabaseSuccess": "Database creation successful", "createDatabaseTip": "Before uploading data, please create your own database.", "customDataSource": "Custom Data Source", "dataTableName": "Data Table Name", "databaseName": "Database Name", "databaseNameFormatError": "Only lowercase letters, numbers, and underscores are allowed", "databaseNameLengthError": "Length is 5 to 30 characters", "databaseTable": "data table", "databaseTableBackupFile": "database table backup file.", "deleteDataTableTip": "Are you sure you want to delete this data table?", "downloadTemplateFile": "Download Template File", "fileSizeTooLargeTip": "The file size cannot exceed 50MB!", "fromYour": "From your", "importData": "Import Data", "importDataDescription": "Import Data Description", "importSuccess": "Import successful", "mysql": "MySQL", "only50": "Only 50 data previews are shown.", "onlySelectOneFileTip": "Only one file can be uploaded at a time. Please upload the selected file first.", "onlySupported": "Only supported ", "period": ".", "pleaseSelectFile": "Please select a file.", "pleaseUpload": "Please upload", "previewData": "Preview Data", "publicDataSource": "Public Data Source", "supportedFileTypes": "*.xlsx or *.xls file, ", "updateData": "Update Data", "updateDataDescription": "Update Data Description", "uploadFailed": "Upload failed", "uploadSuccess": "Upload successful", "uploadTip": "Only upload *. xlsx or *. xls files, and the file size cannot exceed 50MB."}, "dataVisualization": {"addMysqlConnect": "Ddd MySQL Connect", "aggregate": "Aggregate", "aliasNameError": "Only upper and lower case letters, numbers and underscores are allowed, the length is between 1-10.", "ascending": "Ascending", "avg": "Average", "backgroundColor": "Background Color", "barDirection": "Bar Direction", "bucketByMonth": "Bucket By Month", "bucketByQuarterly": "Bucket By Quarterly", "bucketByYear": "Bucket By Year", "bucketing": "Bucketing", "bucketingDate": "The field is a date type, and the range can be selected: ", "bucketingEnd": "The end position of the bucket", "bucketingNumber": "This field is of numeric type, and the range can be selected: ", "bucketingStart": "The starting position of the bucket", "bucketingType": "The field is a string type, and the amount of data: ", "categoryColor": "Category Color", "chartBottomMargin": "Chart Bottom Margin", "chartLeftMargin": "Chart Left Margin", "chartMargins": "Chart Margins", "chartNameError": "Only uppercase and lowercase letters, numbers, dashes, and underscores are allowed, with a length between 1-100.", "chartNameTip": "Please enter a chart name", "chartRightMargin": "Chart Right Margin", "chartTopMargin": "Chart Top Margin", "charts": "Charts", "childTypeCount": "Type Count", "clearStyleTip": "When dimensions, measures, color, filter, aggregate and order are changed, the chart will be regenerated. At this time, the style of the previous chart will be cleared. Are you sure to execute it?", "color": "Classify by color", "colorTip": "Please drag the classification field here", "columnAliasName": "Column Ali<PERSON> Name", "columnAliasNameMessage": "Please input column alias name.", "columnAliasNameP": "The name of the newly generated column for data type conversion", "columnName": "Column Name", "columnNameMessage": "Please input column name.", "connectionSymbol": "Connection Symbol", "converted": "The converted data cannot be converted again", "count": "Count", "createChart": "Create Chart", "createDataSource": "Create Data Source", "createDataTable": "Create Data Table", "createDatabase": "Create Database", "createTip": "If there is no historical data table, please click this button to create it.", "currentDataSource": "Current Data Source", "currentDataType": "Current Data Type", "currentDatabase": "Current Database", "currentDateFormat": "Current Date Format", "customDataSource": "Custom Data Source", "dataSource": "Data Source", "dataSourceDescription": "Data Source Description", "dataSourceHost": "Data Source Host", "dataSourceNameSame": "The name already exists, please try a different one.", "dataSourceNameTip": "Please enter data source name", "dataSourcePassword": "Data Source Password", "dataSourcePasswordIsRequired": "Data source password is required", "dataSourcePort": "Data Source Port", "dataSourcePortIsRequired": "Data source port is required", "dataSourceSourceHostIsRequired": "Data source host is required", "dataSourceType": "Data Source Type", "dataSourceTypeIsRequired": "Data source type is required", "dataSourceUsername": "Data Source Username", "dataSourceUsernameIsRequired": "Data source username is required", "dataTable": "Data Table", "dataTableName": "Data Table Name", "dataTableNameTip": "Please enter a data table name.", "dataTooLarge": "Data set is large,please filter", "databaseDate": "Database Date Type", "dateCalculation": "Calculate Age", "dateFormat": "Date Format To", "dateFormatMessage": "Please select date format type.", "decimalLength": "Number Max Length", "deleteDataSourceTip": "Deleting this data source will delete the data tables and charts associated with it! Are you sure to delete it?", "deleteMyChartTip": "Are you sure you want to delete it?", "deleteTableTip": "Deleting this data table will delete the charts associated with it! Are you sure to delete it?", "descending": "Descending", "dimensionsTip": "Please drag dimensions fields here", "editDataSource": "Edit Data Source", "endColor": "End Color", "enumTypeFormat": "Enum type conversion", "excessiveColor": "Excessive Color", "executeFail": "Execute fail!", "figure": "Figure", "filter": "Conditional filtering", "filterConditionNotNull": "Filter condition not null", "getModelFail": "Get data model fail", "hasNoDataSourceTip": "You have not initialized the synchronization database. Do you want to start creating it now?", "importCsvFile": "Import xls, xlsx file", "joinCondition": "Join condition", "joinTable": "Please select join table", "joinTableTitle": "Join Table", "joinType": "Please select type", "keepDecimalPlaces": "Keep Decimal Places", "labelFont": "Label Font", "labelFontColor": "Label Font Color", "labelFontSize": "Label Font Size", "labelPosition": "Label Position", "left": "Left", "legend": "Legend", "legendBottomMargins": "Legend <PERSON>", "legendDirection": "Legend Direction", "legendFont": "Legend Font", "legendFontColor": "Legend Font Color", "legendFontSize": "Legend Font Si<PERSON>", "legendLeftMargins": "Legend <PERSON>", "legendOrient": "Legend Orient", "legendPosition": "Legend Position", "lineStyle": "Line Style", "lineType": "Line Type", "loadingText": "Loading desperately...", "measuresTip": "Please drag measures field here", "millisecond": "Millisecond", "name": "Name", "nameSame": "The name is already taken", "noDataSourceTip": "No data source", "noDataTableTip": "No data table", "noDatabaseTip": "No database", "noSorting": "No Sorting", "numberType": "Number Type", "numberTypeFormat": "Number Type Conversion", "numberTypeMessage": "Please select number type.", "only50": "Only 50 data previews are shown.", "order": "Order", "perfectTip": "Please refine the rules and try again", "pieType": "Pie Type", "pleaseExecute": "Save fail, please execute.", "previewData": "Preview Data", "publicDataSource": "Public Data Source", "queryFailed": "Query Fail", "quickGroup": "Quick Group", "quickGroupTip": "Please enter the group size", "reimport": "Re Import", "rotate": "X axis label rotation angle", "saveChart": "Save Chart", "searchField": "Search fields", "second": "Second", "selectDataSourceType": "Please select data source type", "selectModel": "Select data table", "setChartsTitle": "Set charts title", "setTimeFormat": "Data conversion", "showAllLabelsOnXAxis": "Show all labels on x axis", "showLabel": "Show Label", "showLegend": "Show Legend", "showSplitLine": "Show Split Line", "showXAxis": "Show X Axis", "showYAxis": "Show Y Axis", "showZoom": "Show Zoom", "stackTotal": "Stack Total", "startColor": "Start Color", "string": "String", "style": "Style", "sum": "Summation", "syncDataLoading": "Syncing data, please be patient.", "synchronizeSandboxData": "Synchronize Sandbox Data", "tables": "Tables", "testConnect": "Test Connect", "timeStampType": "Time Stamp Type", "timeStampTypeMessage": "Please select time stamp type.", "timeTypeFormat": "Date type conversion", "timeTypeMessage": "Please select time type.", "timestamp": "Timestamp", "title": "Title", "titleFont": "Title Font", "titleFontColor": "Title Font Color", "titleFontSize": "Title Font Size", "titlePosition": "Title Position", "tooManyTypes": "There are too many types of this field, and conversion is not currently supported", "top": "Top", "typeCount": "Category Count", "typeMismatch": "Type Mismatch", "unableToDelete": "Unable to delete, please delete its child node first.", "updateChart": "Update Chart", "updateSuccess": "Update Success", "xAxisColor": "X Axis Color", "yAxisColor": "Y Axis Color", "zoomFontColor": "Zoom Font Color"}, "documentManagement": {"changeFileTip": "Please upload the selected document first, or delete it and select again.", "classIsRequired": "Class is required", "className": "Category Chinese Name", "classNameEn": "Category English Name", "classNameEnIsRequired": "Category english name is required", "classNameEnPlaceholder": "Please enter category english name", "classNameIsRequired": "Category chinese name is required", "classNamePlaceholder": "Please enter category chinese name", "clickUploadTip": "Click to upload", "cnDocUrl": "Chinese Document", "cnVideoUrl": "Chinese Video", "createCategory": "Create Category", "deleteCategoryTip": "This operation will permanently delete the category. Do you want to continue?", "deleteTip": "Are you sure you want to delete it?", "docName": "Document Chinese Name", "docNameEn": "Document English Name", "docNameEnIsRequired": "The English name of the document is required", "docNameIsRequired": "The Chinese name of the document is required", "docType": "Document Type", "docUploadTip": "Supports the following file formats: *. txt, *. doc, *. docx, *. pdf, *. ppt, *. pptx, *. xlsx, *. xls. If the document contains Chinese characters, please manually convert it to PDF format before uploading.", "editCategory": "Edit Category", "enDocUrl": "English document", "enVideoUrl": "English Video", "groupName": "Group Chinese Name", "groupNameEn": "Group English Name", "groupNameEnIsRequired": "Group english name is required", "groupNameEnPlaceholder": "Please enter group english name", "groupNameIsRequired": "Group chinese name is required", "groupNamePlaceholder": "Please enter group chinese name", "icon": "Icon", "noteTip": "Note: Group Name and Catalog Name are system parameters and do not change!!!", "selectFile": "Select file", "selectFileTip": "Please select the file to upload", "uploadFailedTip": "Upload failed, please try again later", "uploadSuccessTip": "Upload successful", "videoUploadTip": "Supports the following file formats: *. mp3, *. mp4"}, "domainPracticals": {"businessUseCase": "Business Use Case", "code": "Code", "coreLogic": "Core Logic", "document": "Document", "name": "Name", "technologyUseCase": "Technology Use Case", "video": "Video"}, "downloadCenter": {"dockerInstall": "<p> <span style=\"font-size: 24px;\"><strong>Windows docker installation</strong></span> </p> <p> <br/> </p> <p> Docker is not a general container tool, it depends on the existing and running Linux kernel environment. </p> <p> Docker essentially creates an isolated file environment under the running Linux, so its execution efficiency is almost equal to that of the deployed Linux host. </p> <p> Therefore, docker must be deployed on the Linux kernel system. If other systems want to deploy docker, they must install a virtual linux environment. </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img width=\"50%\" src=\"https://www.runoob.com/wp-content/uploads/2016/05/CV09QJMI2fb7L2k0.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> The way to deploy docker on windows is to install a virtual machine and run docker in the virtual machine of Linux system. </p> <p> <br/> </p> <p> <span style=\"font-size: 24px;\"><strong>Win10 system</strong></span> </p> <p> <br/> </p> <p> Docker desktop is the official installation method of docker on Windows 10 and MacOS operating systems. This method still belongs to the method of installing Linux in the virtual machine before installing docker. </p> <p> <br/> </p> <p> Official download address of docker desktop: <a href=\"https://hub.docker.com/editions/community/docker-ce-desktop-windows\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\">https://hub.docker.com/editions/community/docker-ce-desktop-windows</a> </p> <p> <br/> </p> <p> Note: this method is only applicable to windows 10 professional edition, enterprise edition, education edition and some home editions! </p> <p> <br/> </p> <p> <strong>Install hyper - V</strong> </p> <p> <br/> </p> <p> Hyper - V is a virtual machine developed by Microsoft, similar to VMware or VirtualBox, only applicable to windows 10. This is the virtual machine used by docker desktop for windows. </p> <p> <br/> </p> <p> However, once this virtual machine is enabled, QEMU, VirtualBox or VMware Workstation 15 and below will not be available! If you have to use other virtual machines on your computer (such as the emulator that you must use to develop Android Applications), don&#39;t use hyper - V！ </p> <p> <br/> </p> <p> <strong>Turn on hyper - V</strong> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-4363-20171206211136409-1609350099.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 程序和功能 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-4368-20171206211345066-1430601107.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Turn windows features on or off</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-9748-20171206211435534-1499766232.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> <span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255); font-family: arial, helvetica, sans-serif; font-size: 16px;\">Select Hyper-V</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-6433-20171206211858191-1177002365.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Hyper-V can also be enabled through the command. Right click the start menu and run PowerShell as an administrator to execute the following command:</span> </p> <pre class=\"prettyprint prettyprinted\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All</pre> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> Install Docker Desktop for Windows </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Click&nbsp;</span><a href=\"https://hub.docker.com/?overlay=onboarding\" rel=\"noopener noreferrer\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none; text-decoration: underline; font-family: arial, helvetica, sans-serif; font-size: 16px;\"><span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Get started with Docker Desktop</span></a><span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">，and download the version of windows. If you have not logged in, you will be required to register and log in:</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/5AEB69DA-6912-4B08-BE79-293FBE659894.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> <span style=\"font-size: 24px;\"><strong>Run setup file</strong></span> </p> <p> Double click the downloaded docker for Windows Installer installation file, go all the way to next, and click Finish to complete the installation. </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513669129-6146-20171206214940331-1428569749.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668903-9668-20171206220321613-1349447293.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-size: 16px; font-family: arial, helvetica, sans-serif;\">After installation, docker will start automatically. A little whale icon will appear on the notice bar</span><img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513582421-4552-whale-x-win.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/><span style=\"font-size: 16px; font-family: arial, helvetica, sans-serif;\">，This indicates that docker is running.</span> </p> <p> Three icons will also appear beside the table, as shown in the figure below: </p> <p> We can run docker version on the command line to view the version number, and docker run Hello world to load the test image test. </p> <p> If it doesn&#39;t start, you can search docker in windows to start it: </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513585082-6751-docker-app-search.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">After startup, you can also see the little whale icon on the notification bar:</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513585123-3777-whale-taskbar-circle.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <blockquote style=\"border: 0px; margin: 10px; padding: 10px; background-color: rgb(243, 247, 240); font-size: 13px; line-height: 2em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal;\"> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 4px; line-height: 1.5em; overflow-wrap: break-word; word-break: break-all; font-size: 14px; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; font-style: italic;\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">If you encounter an error caused by WSL 2 during startup, install WSL 2.</span> </p> </blockquote> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">After installation, you can open PowerShell and run the following command to check whether it runs successfully:</span> </p> <pre class=\"prettyprint prettyprinted\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">docker run hello-world</pre> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">After successful operation, the following information should appear:</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/EmkOezweLQVIwA1T__original.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <hr style=\"background-color: rgb(212, 212, 212); color: rgb(212, 212, 212); height: 1px; border: 0px; clear: both; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; font-size: 12px; white-space: normal;\"/> <p> <span style=\"font-size: 24px;\"><strong>Win7, win8 system</strong></span> </p> <p> Win7 and win8 need to be installed with docker toolbox. In China, they can be downloaded using alicloud image. Download address:<a href=\"http://mirrors.aliyun.com/docker-toolbox/windows/docker-toolbox/\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none; font-family: arial, helvetica, sans-serif; font-size: 14px;\">http://mirrors.aliyun.com/docker-toolbox/windows/docker-toolbox/</a> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Installation is relatively simple. Double click to run and click next. You can check the components you need:</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/691999-20180512142142130-1831870973.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> Docker toolbox is a tool set, which mainly includes the following contents: </p> <ul class=\" list-paddingleft-2\" style=\"list-style-type: disc;\"> <li> <p> Docker cli - client, used to run the docker engine to create images and containers. </p> </li> <li> <p> Docker machine - allows you to run docker engine commands from the windows command line. </p> </li> <li> <p> Docker compose - used to run the docker compose command. </p> </li> <li> <p> Kitematic - this is the GUI version of docker. </p> </li> <li> <p> Docker QuickStart shell - this is a command line environment with docker configured. </p> </li> <li> <p> Oracle VM VirtualBox - virtual machine. </p> </li> </ul> <p> After the download is completed, click Install directly. After the installation is successful, three icons will appear at the table, as shown in the figure below: </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/icon-set.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> Click the docker QuickStart icon to start the docker toolbox terminal. </p> <p> If the system displays the user account control window to run VirtualBox to modify your computer, select Yes. </p>", "dockerInstallMacos": "<p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family:arial, helvetica, sans-serif\"><span style=\"font-size: 24px;\">MacOS Docker Install</span></span> </p><p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Please click the link below to download&nbsp;</span><a href=\"https://download.docker.com/mac/stable/Docker.dmg\" target=\"_blank\" rel=\"noopener\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none; text-decoration: underline; font-family: arial, helvetica, sans-serif; font-size: 16px;\"><span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Stable</span></a><span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">&nbsp;or&nbsp;</span><a href=\"https://download.docker.com/mac/edge/Docker.dmg\" target=\"_blank\" rel=\"noopener\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none; text-decoration: underline; font-family: arial, helvetica, sans-serif; font-size: 16px;\"><span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Edge</span></a><span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">&nbsp;version Docker for Mac。</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Just like other MacOS software, the installation is very simple. Double click the downloaded. dmg file, and then drag the whale icon to the application folder.</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1E045CE6-D504-4E7D-8C57-EEFB8AC83BF1.jpg\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> <span style=\"color: rgb(51, 51, 51); background-color: rgb(255, 255, 255); font-family: arial, helvetica, sans-serif; font-size: 16px;\">Find the docker icon from the app and click Run. You may ask for the Mac OS login password and enter it.</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480613-7638-docker-app-in-apps.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">Click the whale icon in the top status bar to pop up the operation menu.</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480612-6026-whale-in-menu-bar.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480613-8590-menu.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">The first time you click the icon, you may see the successful installation interface. Click \"get it!\" to close the window.</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480614-7648-install-success-docker-cloud.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <span style=\"font-family: arial, helvetica, sans-serif; font-size: 16px;\">After starting the terminal, you can check the installed docker version through the command.</span> </p> <pre class=\"prettyprint\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">$ docker --version<br/>Docker version 17.09.1-ce, build 19e2cf6</pre> <p> <br/> </p>", "document": "Document", "downloadService": "Download Services", "downloadServiceContent": "<p> Enter the following commands in the PowerShell and wait for the image download to complete. </p> <p> docker pull theiaide/theia-full </p> <p> <br/> </p>", "downloadServiceContentMacos": "<p> Enter the following commands in the terminal and wait for the image download to complete. </p> <p> docker pull theiaide/theia-full </p><p> <br/> </p>", "downloadServiceContentMacosMl": "<p> Enter the following commands in the terminal and wait for the image download to complete. </p> <p> docker pull linkerwang/amlserver:v1 </p> <p> docker pull linkerwang/amltool:v1 </p> <p> <br/> </p>", "downloadServiceContentMl": "<p> Enter the following commands in the PowerShell and wait for the image download to complete. </p> <p> docker pull linkerwang/amlserver:v1 </p> <p> docker pull linkerwang/amltool:v1 </p> <p> <br/> </p>", "installDocker": "<PERSON><PERSON>", "installIde": "Download Workspace", "installMl": "Download Machine Learning", "macOSInstall": "MacOS", "name": "Name", "runAndUse": "Run And Use", "runAndUseContent": "<p> Enter the following command in PowerShell to start the image </p> <p> docker run -itd --init -p 30000:3000 -v \"/home/<USER>/home/<USER>\" -v \"/home/<USER>/.ssh:/home/<USER>/.ssh\" -v \"/home/<USER>/.ssh:/root/.ssh\" theiaide/theia-full </p> <p> chown -R 1000 /home/<USER>/p><p> <br/> </p><p> Input url http://127.0.0.1:30000 in browser to open workspace tool </p>", "runAndUseContentMacos": "<p> Enter the following command in terminal to start the image </p> <p> docker run -itd --init -p 30000:3000 -v \"/home/<USER>/home/<USER>\" -v \"/home/<USER>/.ssh:/home/<USER>/.ssh\" -v \"/home/<USER>/.ssh:/root/.ssh\" theiaide/theia-full </p> <p> chown -R 1000 /home/<USER>/p><p> <br/> </p><p> Input url http://127.0.0.1:30000 in browser to open workspace tool </p>", "runAndUseContentMacosMl": "<p> Enter the following command in terminal to start the image </p> <p> docker run -d -p 8084:8084 --name amlserver linkerwang/amlserver:v1 </p> <p> docker run -d -p 9000:80 --name amltool linkerwang/amltool:v1 </p> <p> <br/> </p><p> Input url http://127.0.0.1:9000 in browser to open machine learning tool </p>", "runAndUseContentMl": "<p> Enter the following command in PowerShell to start the image </p> <p> docker run -d -p 8084:8084 --name amlserver linkerwang/amlserver:v1 </p> <p> docker run -d -p 9000:80 --name amltool linkerwang/amltool:v1 </p> <p> <br/> </p><p> Input url http://127.0.0.1:9000 in browser to open machine learning tool </p>", "subtitle": "According to the operating system of your computer, select the corresponding installation manual below to install the workspace local service.", "subtitleMl": "According to the operating system of your computer, select the corresponding installation manual below to install the machine learning local service.", "title": "Install workspace services to your local computer to experience faster and more stable workspace functions.", "titleMl": "Install machine learning services to your local computer to experience faster and more stable machine learning functions.", "verifyService": "Verify Services", "verifyServiceContent": "<p> Enter the command \"docker images\" in the PowerShell to verify whether the image is downloaded </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> theiaide/theia-full&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; latest&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; be7675928ad5&nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8.53GB </p> <p> <br/> </p>", "verifyServiceContentMacos": "<p> Enter the command \"docker images\" in the terminal to verify whether the image is downloaded </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> theiaide/theia-full&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; latest&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; be7675928ad5&nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8.53GB </p> <p> <br/> </p>", "verifyServiceContentMacosMl": "<p> Enter the command \"docker images\" in the terminal to verify whether the image is downloaded </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> linkerwang/amlserver&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9f2145f73a0a&nbsp; &nbsp; &nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.58GB </p> <p> linkerwang/amltool&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; <span style=\"white-space: pre;\">\t&nbsp;&nbsp;&nbsp;</span>23e31282671c&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; 3 minutes ago&nbsp; &nbsp;&nbsp;&nbsp;148MB </p> <p> <br/> </p>", "verifyServiceContentMl": "<p> Enter the command \"docker images\" in the PowerShell to verify whether the image is downloaded </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> linkerwang/amlserver&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9f2145f73a0a&nbsp; &nbsp; &nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.58GB </p> <p> linkerwang/amltool&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; <span style=\"white-space: pre;\">\t&nbsp;&nbsp;&nbsp;</span>23e31282671c&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; 3 minutes ago&nbsp; &nbsp;&nbsp;&nbsp;148MB </p> <p> <br/> </p>", "video": "Video", "windowsInstall": "Windows"}, "financialDataCharacteristics": {"bankingServicesAndData": "Banking Services & Data", "description1": "Financial Business is established with the aim to provide comprehensive, excellent, and safe financial services to their customers.  Customers always appears at the kernel in any financial system and data. Before providing any services, financial institutions must collect as much information as possible from the customers. After the customer information, financial institution will establish different products in accordance to the customer's confirmation. Follow to the above, customers can now begin their transacting. All data are built with this hierarchical structure and kept in a highly secured environment for future product selling and servicing.", "description2": "SIMNECTZ's sandbox carries the same business structure, and with a hierarchy of Customer, Products and Transaction data. ", "description3": "It is crucial to understand the characteristics of financial data.  When creating financial data from scratch, it is mandatory to create the customer data,  and follow by product and transactional data. ", "description4": "In a regular commercial bank, core banking serves as the heart of a commercial banking, provides financial accounting and ledger services for all peripheral systems/services, such as credit card, loans, …..etc.", "description5": "The customer data has been cleansed and reorganized to form \"single piece of truth\" data information about customers in previous digital transformation exercise.  This consolidated customer data information are used to serve all peripheral systems/services through standard data access API interface.", "description6": "Apart from ledger and customer data, all transactional data are captured and stored together with their respective systems/services."}, "foreignExchangeCreation": {"accountNumber": "Account Number", "beforeRemoveMessage": "Confirm to remove {fileName}?", "channel": "Channel", "dataSource": "Data Source", "dataSourcePlaceholder": "Please enter the data source", "downloadAccountTemplate": "Download Template File", "frequency": "Frequency", "frequencyPlaceholder": "Please enter the frequency", "frequencyRate": "Frequency Rate", "frequencyRatePlaceholder": "Please enter the frequency rate", "handleExceedMessage": "Currently restricted to select 1 file, this time choose {thisTimeChoose} files，and totally choose {totallyChoose} files", "linkToDatatable": "Batch Upload Account", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "settlementAccount": "Settlement Account", "settlementAccountPlaceholder": "Please enter the settlement account", "tableName": "Table Name", "tableNamePlaceholder": "Please enter the table name", "totalTransaction": "Max Transactions", "totalTransactionPlaceholder": "Please enter the max transactions", "transactionAccount": "Transaction Account", "transactionAccountPlaceholder": "Please enter the transaction account", "transactionAccountType": "Transaction Account Type", "transactionAmountFrom": "Transaction Amount (HKD)", "transactionAmountFromPlaceholder": "Please enter the transaction amount from", "transactionAmountTo": "To", "transactionAmountToPlaceholder": "Please enter the transaction amount to", "transactionCcy": "Transaction Ccy", "transactionDate": "Transaction Date", "transactionDateFrom": "From", "transactionDateFromPlaceholder": "Please select the transaction date from", "transactionDateTo": "To", "transactionDateToPlaceholder": "Please select the transaction date to", "transactionType": "Transaction Type", "upload": "Upload", "uploadFileRule": "Only upload csv/xls/xlsx files, not exceed 5MB"}, "foreignExchangeEnquiry": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter account number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter country code", "dataDetails": "Data Details", "exchangeAmoutInForeignCurrency": "Exchange Amount In Foreign Currency", "exchangeAmoutInLocalCurrency": "Exchange Amount In Local Currency", "exchangeRate": "Exchange Rate", "foreignCurrency": "Foreign Currency", "fromCreateDate": "From Create Date", "localCurrency": "Local Currency", "postBalInForeignCurrencyAccount": "Post Bal In Foreign Currency Account", "prevBalInForeignCurrencyAccount": "Prev Balance In Foreign Currency Account", "tableName": "Table Name", "tableNamePlaceholder": "Please select table name", "toCreateDate": "To Create Date", "transactionTime": "Transaction Time", "transactionType": "Transaction Type"}, "foreignExchangeUpdate": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter the account number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter the branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "exchangeAmoutInForeignCurrency": "Exchange Amount In Foreign Currency", "exchangeAmoutInForeignCurrencyPlaceholder": "Please enter the exchange amount in foreign currency", "exchangeAmoutInLocalCurrency": "Exchange Amount In Local Currency", "exchangeAmoutInLocalCurrencyPlaceholder": "Please enter the exchange amount in local currency", "exchangeRate": "Exchange Rate", "exchangeRatePlaceholder": "Please enter the exchange rate", "foreignCurrency": "Foreign Currency", "foreignCurrencyPlaceholder": "Please enter the foreign currency", "localCurrency": "Local Currency", "localCurrencyPlaceholder": "Please enter the local currency", "postBalInForeignCurrencyAccount": "Post Bal In Foreign Currency Account", "postBalInForeignCurrencyAccountPlaceholder": "Please enter the post bal in foreign currency account", "prevBalInForeignCurrencyAccount": "Prev <PERSON>l In Foreign Currency Account", "prevBalInForeignCurrencyAccountPlaceholder": "Please enter the prev bal in foreign currency account", "title": "Edit Foreign Exchange Data", "transactionTime": "Transaction Time", "transactionTimePlaceholder": "Please select the transaction time", "transactionType": "Transaction Type", "transactionTypePlaceholder": "Please enter the transaction type"}, "foreignExchangeView": {"title": "Foreign Exchange Data"}, "fundCreation": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter account number", "add": "Add", "amountFrom": "Amount From", "amountFromPlaceholder": "Please enter the amount from", "amountTo": "To", "amountToPlaceholder": "Please enter the amount to", "batchUploadFundCode": "Batch Upload Fund Code", "beforeRemoveMessage": "Confirm to remove {fileName}?", "buy": "Buy", "channel": "Channel", "channelPlaceholder": "Please select channel", "dataSource": "Data Source", "dataSourcePlaceholder": "Please select data source", "downloadAccountTemplate": "Download Account Te<PERSON>late", "fileSizeAndTypeTip": "only upload csv/xls/xlsx files, not exceed 5MB.", "frequency": "Frequency", "frequencyPlaceholder": "Please select a frequency", "frequencyRate": "Frequency Rate", "frequencyRatePlaceholder": "Please enter frequency rate", "fromDate": "From Date", "fromDatePlaceholder": "Please select a start date", "fundCode": "Fund Code", "fundCodeList": "Available Fund Code", "fundCodeTemplate": "Fund Code Template", "handleExceedMessage": "Currently restricted to select 1 file, this time choose {thisTimeChoose} files，and totally choose {totallyChoose} files", "linkToDatatable": "Batch Upload Account", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "sell": "<PERSON>ll", "settlementAccount": "Settlement Account", "shareFrom": "Share No. From", "shareFromPlaceholder": "Please enter the share no from", "shareToPlaceholder": "Please enter the share no to", "toDate": "To Date", "toDatePlaceholder": "Please select a end date", "totalPlaceholder": "Please enter the max transactions", "totalTransaction": "Max Transactions", "transactionAccount": "Transaction Account", "transactionAccountType": "Transaction Account Type", "transactionDate": "Transaction Date", "transactionDetails": "Transaction Details", "transactionType": "Transaction Type"}, "fundEnquiry": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter account number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter country code", "dataDetails": "Data Details", "fromCreateDate": "From Create Date", "fromTransactionAmount": "Transaction Amount From", "fromTransactionAmountPlaceholder": "Please enter transaction amount from", "fundCcy": "Fund Ccy", "fundCode": "Fund Code", "fundPrice": "Fund Price", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "riskRating": "Risk Rating", "sharingNo": "Sharing No.", "tableName": "Table Name", "tableNamePlaceholder": "Please select table name", "toCreateDate": "To Create Date", "toTransactionAmount": "Transaction Amount To", "toTransactionAmountPlaceholder": "Please enter transaction amount to", "tradingAmount": "Trading Amount", "tradingOption": "Trading Option", "transactionAmount": "Transaction Amount (HKD)", "transactionDate": "Transaction Date", "trdingCommission": "Trading Commission"}, "fundUpdate": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter the account number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter the branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "fundCcy": "Fund Ccy", "fundCcyPlaceholder": "Please enter the fund ccy", "fundCode": "Fund Code", "fundCodePlaceholder": "Please enter the fund code", "fundPrice": "Fund Price", "fundPricePlaceholder": "Please enter the fund price", "riskRating": "Risk Rating", "riskRatingPlaceholder": "Please enter the risk rating", "sharingNo": "Sharing No.", "sharingNoPlaceholder": "Please enter the sharing no.", "title": "Edit Fund Data", "tradingAmount": "Trading Amount", "tradingAmountPlaceholder": "Please enter the trading amount", "tradingOption": "Trading Option", "tradingOptionPlaceholder": "Please enter the trading option", "transactionAmount": "Transaction Amount (HKD)", "transactionAmountPlaceholder": "Please enter the transaction amount", "transactionDate": "Transaction Date", "transactionDatePlaceholder": "Please enter the transaction date", "trdingCommission": "Trading Commission", "trdingCommissionPlaceholder": "Please enter the trading commission"}, "fundView": {"title": "View Fund Data"}, "homework": {"answerQuestion": "Answer the Question", "deleteQuestionPrompt": "This operation will permanently delete the question Prompt. Do you want to continue?", "editQuestion": "Edit Question", "homework": "Homework", "homeworkList": "Homework list", "keywordsPlaceholder": "Question Content", "myHomework": "My Homework", "questionContent": "Question Content", "questionDetail": "Question Detail", "questionModelanswer": "Model Answer", "studentAnswer": "Student Answer", "studentAnswerIsRequired": "Student answer cannot be empty"}, "loginLog": {"browser": "Browser", "email": "Email", "failed": "Failed", "loginIp": "Login IP", "loginLocation": "Login Location", "loginStatus": "Login Status", "loginTime": "Login Time", "message": "Message", "os": "OS", "success": "Success"}, "nodeManagement": {"add": "Add", "addFlow": "Add Node", "addTip": "Are you sure you want to add it?", "addWorkflow": "Add Rule", "apiHeader": "api_header", "apiMethod": "api_method", "apiParams": "api_params", "apiUrl": "api_url", "canNotEmptyTip": "This field cannot be empty", "deleteTip": "Are you sure you want to delete it?", "editFlow": "Edit a node", "enterApiHeaderTip": "Please enter api_header", "enterApiMethodTip": "Please enter api_method", "enterApiParamsTip": "Please enter api_params", "enterApiUrlTip": "Please enter api_url", "enterGroupTip": "Please enter a node group", "enterNameTip": "Please enter the node name", "group": "Group", "json": "Json", "lastUpdateDate": "Update Time", "name": "Name", "operatingSuccess": "Operation successful", "save": "Save", "update": "Update", "updateTip": "Are you sure you want to update?", "updateWorkflow": "Update Rules", "workflowManage": "Rule Management"}, "obtainDeveloperToken": {"accessTokenTip": "Enter access_token and click on the link address", "addClient": "Add Client", "addClientTip": "Please create a client first, and then use the created client to generate your own token.", "archive": "Cancel", "authorizationCodeTip": "Enter the necessary information for each step and click on the link address below it", "authorizationCodeTip1": "Obtain 'code' from authentication server", "authorizationCodeTip2": "Exchange 'code' for 'access_token'", "authorizationCodeTip3": "Enter the code obtained in the first step", "authorizedGrantTypesTip": "Check at least one authorization method, and cannot just check refresh_token separately", "cancelled": "Cancelled", "client": "Client", "clientCredentialsTip": "Click on the link address to test", "clientId": "Client ID", "clientIdTip": "The client ID must be entered and must be unique, with a length of at least 5 digits", "createNow": "Create Now", "createSuccess": "Create Success", "developerToken": "Developer Token", "editClient": "Edit Client", "email": "Email", "generateDeveloperTokenSuccess": "Successfully generated developer token", "generateToken": "Generate Token", "grantTypes": "Grant Types", "inEffect": "In Effect", "password": "Password", "passwordTip": "Enter the platform's email, password, and click on the link address", "redirectUri": "Redirect URI", "redirectUriIsRequired": "Please enter the redirect URI", "refreshTokenTip": "Enter refresh_token and click on the link address", "secret": "Secret", "secretTip": "The client password must be entered and must be at least 8 digits in length", "status": "Status", "test": "Test", "testTip": "Provide different testing URLs for different grant types", "trusted": "Trusted", "trustedTip": "This only applies to situations where the authorization method includes authorization_code. After the user successfully logs in, if No is selected, it will redirect to the page asking the user to approve the authorization. If Yes is selected, there is no need to ask the user to approve the authorization after logging in (because it is trusted).", "webServerRedirectUriTip": "If the authorization method includes authorization_code, the callback address must be entered"}, "organization": {"contactEmail": "Contact Email", "contactEmailIsRequired": "Organization contact email is required", "contactEmailPlaceholder": "Please enter organization contact email", "contactPerson": "Contact Person", "contactPersonIsRequired": "Organization contact person is required", "contactPersonPlaceholder": "Please enter organization contact person", "contactPhoneNumber": "Contact Phone Number", "contactPhoneNumberIsRequired": "Organization contact phone number is required", "contactPhoneNumberPlaceholder": "Please enter organization contact phone number", "createOrganization": "Create Organization", "deleteOrganizationPrompt": "This operation will permanently delete the organization. Do you want to continue?", "editOrganization": "Edit Organization", "invalidContactPersonFormat": "Invalid contact person format, with a length of 2-64 characters", "invalidEmailFormat": "Invalid email format", "invalidPhoneNumberFormat": "Invalid phone number format", "keywordsPlaceholder": "Organization name", "organizationAbbreviationCode": "Abbreviation Code", "organizationAbbreviationCodeIsRequired": "Organization abbreviation code is required", "organizationAbbreviationCodePlaceholder": "Please enter organization abbreviation code", "organizationCode": "Organization Code", "organizationCodePlaceholder": "Please enter organization code", "organizationEntityAddress": "Organization Entity Address", "organizationEntityAddressPlaceholder": "Please enter organization entity address", "organizationId": "Organization ID", "organizationName": "Organization Name", "organizationNameIsRequired": "Organization name is required", "organizationNamePlaceholder": "Please enter organization name", "organizationNature": "Nature", "organizationNaturePlaceholder": "Please enter organization nature", "organizationTaxCode": "Organization Tax Code", "organizationTaxCodePlaceholder": "Please enter organization tax code"}, "paymentCreation": {"accountNumber": "Account Number", "accountTypeFrom": "Account Type", "accountTypeFromPlaceholder": "Please enter the account type from", "amountFrom": "Amount From", "amountFromPlaceholder": "Please enter the amount from", "amountTo": "To", "amountToPlaceholder": "Please enter the amount to", "beforeRemoveMessage": "Confirm to remove {fileName}?", "channel": "Channel", "channelPlaceholder": "Please enter the channel", "dataSource": "Data Source", "dataSourcePlaceholder": "Please enter the data source", "downloadBranchTemplate": "Download Template File", "frequency": "Frequency", "frequencyPlaceholder": "Please enter the frequency", "frequencyRate": "Frequency Rate", "frequencyRatePlaceholder": "Please enter the frequency rate", "handleExceedMessage": "Currently restricted to select 1 file, this time choose {thisTimeChoose} files，and totally choose {totallyChoose} files", "industry": "Industry", "industryPlaceholder": "Please select the industry", "linkToDatatable": "Batch Upload Account", "moveMoneyFrom": "Payment From", "moveMoneyFromPlaceholder": "Please enter the move money from", "moveMoneyFromType": "Move Money From Type", "moveMoneyTo": "Payment To", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "tableName": "Table Name", "tableNamePlaceholder": "Please enter the table name", "totalTransaction": "Max Transactions", "totalTransactionPlaceholder": "Please enter the max transactions", "transactionDate": "Transaction Date", "transactionDateFrom": "From", "transactionDateFromPlaceholder": "Please select the transaction date from", "transactionDateTo": "To", "transactionDateToPlaceholder": "Please select the transaction date to", "upload": "Upload", "uploadFileRule": "Only upload csv/xls/xlsx files, not exceed 5MB"}, "paymentEnquiry": {"createDate": "Create Date", "customerAccountNumber": "Account Number", "customerAccountNumberPlaceholder": "Please enter the account number", "customerAccountType": "Account Type", "fromCreateDate": "From Create Date", "fromTransactionAmount": "Transaction Amount From", "fromTransactionAmountPlaceholder": "Please enter the transaction amount from", "lastUpdateDate": "Last Update Date", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "payeeCategory": "Payee Category", "payeeCategoryID": "Payee Category I D", "payeeID": "Payee ID", "remarks": "Remarks", "toCreateDate": "To Create Date", "toTransactionAmount": "Transaction Amount To", "toTransactionAmountPlaceholder": "Please enter the transaction amount to", "transactionAmount": "Transaction Amount", "transactionCurrency": "Transaction Currency", "transactionDealNumber": "Transaction Deal Number", "transactionTime": "Transaction Time"}, "paymentUpdate": {"createDate": "Create Date", "createDatePlaceholder": "Please select the create date", "customerAccountNumber": "Account Number", "customerAccountNumberPlaceholder": "Please enter the account number", "customerAccountType": "Account Type", "customerAccountTypePlaceholder": "Please enter the account type", "customerNumber": "Customer Number", "customerNumberPlaceholder": "Please enter the customer number", "lastUpdateDate": "Last Update Date", "lastUpdateDatePlaceholder": "Please select the last update date", "payeeCategory": "Payee Category", "payeeCategoryID": "Payee Category ID", "payeeCategoryIDPlaceholder": "Please enter the payee category ID", "payeeCategoryPlaceholder": "Please enter the payee category", "payeeID": "Payee ID", "payeeIDPlaceholder": "Please enter the payee ID", "payeeNumber": "Payee Number", "payeeNumberPlaceholder": "Please enter the payee number", "paymentEffectiveDay": "Payment Effective Day", "paymentEffectiveDayPlaceholder": "Please enter the payment effective day", "remarks": "remarks", "remarksPlaceholder": "Please enter the remarks", "sandBoxId": "Sand Box Id", "sandBoxIdPlaceholder": "Please enter the sand box id", "status": "status", "statusPlaceholder": "Please enter the status", "title": "Edit Payment Data", "transactionAmount": "Transaction Amount", "transactionAmountPlaceholder": "Please enter the transaction amount", "transactionCurrency": "Transaction Currency", "transactionCurrencyPlaceholder": "Please enter the transaction currency", "transactionDealNumber": "Transaction Deal Number", "transactionDealNumberPlaceholder": "Please enter the transaction deal number", "transactionTime": "Transaction Time", "transactionTimePlaceholder": "Please enter the transaction time"}, "paymentView": {"title": "View Payment Data"}, "permission": {"accredit": "Accredit", "accreditIsRequired": "Accredit is required", "accreditPlaceholder": "Please enter accredit", "createPermission": "Create Permission", "deletePermissionPrompt": "This operation will permanently delete the permission. Do you want to continue?", "editPermission": "Edit Permission", "keywordsPlaceholder": "Permission name", "parentId": "Parent Permission", "parentIdIsRequired": "Parent permission is required", "parentIdPlaceholder": "Please select parent permission", "permissionName": "Permission Name", "permissionNameIsRequired": "Permission name is required", "permissionNamePlaceholder": "Please enter permission name", "sortNumber": "Sort Number", "sortNumberIsRequired": "Sort number is required", "sortNumberPlaceholder": "Please enter sort number"}, "practicalPlatformIntroduction": {"description1": "SOI+ Digital Finance Practical Platform is built with the aim to develop composite FinTech talents through a hands-on and practical model.  The platform provides a comprehensive training environment covers a wide spectrum of both technology and financial business knowledge and use cases.", "description2": "The platform is designed and constructed with different components to facilitate the use of  both business and technical background users.  They are:", "description3": "The platform provides a series of open APIs in accordance to different business functions. Behind the open APIs are the simulated virtual banking system, insurance system with numerous financial business logic.  All APIs are developed in accordance with the guidelines formulated by the Hong Kong Monetary Authority. The parameters in the API are based on the international standard ISO20022.", "description4": "The synthetic financial data sandbox provides more than 100 million complete sets of financial data (including transaction data from domestic and foreign banks). All data is generated by a home-grown data generation engine. Synthetic data avoids legitimacy issues such as data privacy.", "description5": "Multiple data analysis capabilities are provided, such as Data preparation, Auto Machine Learning, Data Visualization, Data Importing, Data Generation and Data Enquiry through different toolings and programming methodologies. ", "description6": "The platform provides financial simulation systems for different scenarios, comprehensively cultivating students' learning and practice in different financial fields. Each system contains a large number of business processes. These systems can effectively assist university teaching and truly integrate theory and practice in fintech teaching.", "subtitle1": "API Market Place", "subtitle2": "Synthetic Financial Data Sandbox", "subtitle3": "Data Analysis", "subtitle4": "Financial Simulation System", "title": "Platform Introduction"}, "question": {"courseCategoryIsRequired": "Course category is required", "courseCategoryPlaceholder": "Please select course category", "courseNameIsRequired": "Course name is required", "courseNamePlaceholder": "Please select course name", "createQuestion": "Create Question", "deleteQuestionPrompt": "This operation will permanently delete the question Prompt. Do you want to continue?", "editQuestion": "Edit Question", "keywordsPlaceholder": "Question content", "modelAnswer": "Model Answer", "modelAnswerIsRequired": "Model answer is required", "modelAnswerPlaceholder": "Please enter  model answer", "questionContent": "Question Content", "questionContentIsRequired": "Question content is required", "questionContentPlaceholder": "Please enter question content", "subjectNameIsRequired": "Subject name is required", "subjectNamePlaceholder": "Please select subject name"}, "role": {"createRole": "Create Role", "deleteRolePrompt": "This operation will permanently delete the role. Do you want to continue?", "editRole": "Edit Role", "keywordsPlaceholder": "Role name", "permission": "Permission", "permissionIsRequired": "Permission is required", "permissionPlaceholder": "Please select permissions", "remark": "Remark", "remarkPlaceholder": "Please enter remark information", "roleName": "Role Name", "roleNameIsRequired": "Role name is required", "roleNamePlaceholder": "Please enter role name"}, "router": {"404": 404, "addApi": "Add API", "api": "API", "apiAccessGuide": "API Access Guide", "apiArchitectureDesign": "API Architecture Design", "apiCatalogue": "API Catalogue", "apiCategoryManagement": "API Category Management", "apiList": "API List", "apiManagement": "API Management", "apiMarketPlace": "API Market Place", "apiSubcategoryManagement": "API Subcategory Management", "apiUsageGuide": "API Usage Guide", "apis": "Apis", "applyCustomerDataToken": "Apply Customer Data/Token", "autoMachineLearning": "Auto Machine Learning", "codingPractice": "Coding Practice", "codingQuestionManagement": "Coding Question Management", "course": "Course", "courseBusiness": "Business", "courseBusinessCommercialBanking": "Commercial Banking", "courseBusinessRetailBanking": "Retail Banking", "courseBusinessRetailBankingBusinessRisk": "Business Risk", "courseBusinessRetailBankingCreditCard": "Credit Card", "courseBusinessRetailBankingCreditRisk": "Credit Risk", "courseBusinessRetailBankingFex": "FEX", "courseBusinessRetailBankingFraudRiskManagement": "Fraud Risk Management", "courseBusinessRetailBankingLoan": "Loan", "courseBusinessRetailBankingMarketRisk": "Market Risk", "courseBusinessRetailBankingOperationRisk": "Operation Risk", "courseBusinessRetailBankingRegulatoryRisk": "Regulatory Risk", "courseBusinessRetailBankingRiskManagementPrinciple": "Risk Management Principle", "courseBusinessRetailBankingTermDeposit": "Term Deposit", "courseBusinessRiskManagement": "Risk Management", "courseBusinessSupplyChainFinance": "Supply Chain Finance", "courseCategory": "Course Category", "courseIndustryTalk": "Industry Talk", "courseIntroduce": "Introduction", "courseLearning": "Course Learning", "courseLearningManagement": "Course Learning Management", "coursePartipate": "Partipate", "courseRecommend": "Recommend", "courseTechnology": "Technology", "courseTechnologyBlockchain": "Blockchain", "courseTechnologyDataAnalysis": "Data Analysis", "courseTechnologyProgrammingLanguage": "Programming Language", "courseTransfer": "Course Learning Transfer", "creditCardDataCreation": "Credit Card Data Creation", "creditCardDataEnquiry": "Credit Card Data Enquiry", "crossBorderPayment": "Cross Border Payment", "customApi": "Custom API", "customerDataCreation": "Customer Data Creation", "customerDataEnquiry": "Customer Data Enquiry", "dashboard": "Dashboard", "dataAnalysis": "Data Analysis", "dataAnalysisUseCase": "Data Analysis Use Case", "dataAnalysisUseCaseList": "Data Analysis Use Case List", "dataAnalysisUseCaseSubcategory": "Data Analysis Use Case Subcategory", "dataCreation": "Data Creation", "dataDictionary": "Data Dictionary", "dataDictionaryDetails": "Data Dictionary Details", "dataDictionarySubcategory": "Data Dictionary Category", "dataEnquiry": "Data Enquiry", "dataImport": "Data Import", "dataPreparation": "Data Preparation", "dataSource": "Data Source", "dataTable": "Data Table", "dataVisualization": "Data Visualization", "depositDataCreation": "Deposit Data Creation", "documentCenter": "Document Center", "documentList": "Document List", "documentManagement": "Document Management", "domainPracticals": "Domain & Practicals", "domainPracticalsBusiness": "Business", "domainPracticalsBusinessCommercialBanking": "Commercial Banking", "domainPracticalsBusinessFraudRiskManagement": "Fraud Risk Management", "domainPracticalsBusinessOperationalRisk": "Operational Risk", "domainPracticalsBusinessRegulatoryRisk": "Regulatory Risk", "domainPracticalsBusinessRetailBanking": "Retail Banking", "domainPracticalsBusinessRiskManagement": "Risk Management", "domainPracticalsTechnology": "Technology", "domainPracticalsTechnologyBlockchain": "Blockchain", "domainPracticalsTechnologyDataAnalysis": "Data Analysis", "domainPracticalsTechnologyProgrammingLanguage": "Programming Language", "downloadCenter": "Download Center", "eKYCWorkflowDetails": "eKYC Workflow Details", "editApi": "Edit API", "explore": "Explore", "financialDataCharacteristics": "Financial Data Characteristics", "foreignExchangeDataCreation": "ForeignExchangeDataCreation", "foreignExchangeDataEnquiry": "Foreign Exchange Data Enquiry", "fraudDetectionCenter": "Fraud Detection Center", "fundDataEnquiry": "Fund Data Enquiry", "fundTradingDataCreation": "Fund Trading Data Creation", "homework": "Homework", "importApi": "Import API", "insuranceClaims": "Insurance Claims", "insuranceWorkflowDetails": "Insurance Claim Workflow Details", "jupyter": "<PERSON><PERSON><PERSON>", "kyc": "KYC", "login": "<PERSON><PERSON>", "loginLog": "<PERSON><PERSON>g", "mBridge": "mBridge", "manageDocument": "Manage Document", "manageEKYCWorkflow": "Manage eKYC Workflow", "manageInsuranceWorkflow": "manage Insurance Claim Workflow", "myCharts": "My Charts", "myPath": "Learning Path", "nodeManagement": "Node Management", "obtainDeveloperToken": "<PERSON><PERSON><PERSON> Dev<PERSON>", "organizationManagement": "Organization Management", "paymentDataCreation": "Payment Data Creation", "paymentDataEnquiry": "Payment Data Enquiry", "permissionManagement": "Permission Management", "practiceManagement": "Practice Platform Management", "practicePlatformIntroduce": "Platform Introduction", "practiceTransfer": "Practice Platform Transfer", "privacyPolicy": "Privacy Policy", "productDataCreation": "Account Data Creation", "publicNodeRules": "Public Node Rules", "question": "Question", "register": "Register", "resetPassword": "Reset Password", "roleManagement": "Role Management", "ruleManagement": "Rule Management", "sandboxManagement": "Sandbox Management", "sandboxUsageGuide": "Sandbox Usage Guide", "sendEmail": "Send Email", "simulatedSystems": "Simulated Systems", "stockDataEnquiry": "Stock Data Enquiry", "stockTradingDataCreation": "Stock Trading Data Creation", "subject": "Subject", "supplyChainFinance": "Supply Chain Finance", "systemManagement": "System Management", "tcManagement": "T&C Management", "technicalDevelopment": "Technical Development", "termDepositDataCreation": "Term Deposit Data Creation", "termDepositDataEnquiry": "Term Deposit Data Enquiry", "tokenAccessGuide": "Token Access Guide", "transfer": "Transfer", "transferDataCreation": "Transfer Data Creation", "transferDataEnquiry": "Transfer Data Enquiry", "tryoutApi": "Tryout API", "userGuide": "User Guide", "userManagement": "User Management", "virtualBankSystem": "Virtual Bank System", "virtualCreditCardSystem": "Virtual Credit Card System", "virtualEWallet": "Virtual e-Wallet", "virtualInsuranceSystem": "Virtual Insurance System", "withdrawalDataCreation": "Withdrawal Data Creation", "workflowConfig": "Workflow Config", "workflowList": "Workflow List", "workspace": "Workspace"}, "ruleManagement": {"add": "Add", "addRule": "Add Rule", "addTip": "Are you sure you want to add?", "canNotEmptyTip": "This field cannot be empty", "deleteTip": "Are you sure you want to delete?", "enterNameTip": "Please enter the rule name", "name": "Name", "operatingSuccess": "Operation successful", "pleaseSelectNodeGroup": "Please select node group", "ruleManagement": "Please select node group", "updateDate": "Update Time", "updateRule": "Edit Rule", "updateTip": "Are you sure you want to update?"}, "sandboxManagement": {"customerNumber": "Customer Number", "deleteInside": "Delete sandbox data within user", "deleteOut": "Delete user sandbox data", "deleteSandboxDataTip": "No sandbox data selected", "deleteTip": "Are you sure you want to delete it?", "deleteUserTip": "No student selected", "email": "Email", "loginName": "Login Name", "loginPassword": "Login Password", "sandboxId": "Sandbox ID", "studentUser": "Student User"}, "sandboxUsageGuide": {"description1": "Each user successfully registered to the platform will be assigned with a data sandbox. A set of pre-defined financial data records, includes customer demographics, account information and transaction data will be allocated to each data sandbox. User can use these pre-allocated data records in the Simulated Applications and the Fraud Detection Center on the platform.  To avoid overlapping, data records in each data sandbox will not cross over different users.", "description2": "In the initial set up, each data sandbox carries 5 sets of pre-defined customer data. Additional customer data, up to a total of 10 sets, can be requested by the user through the Apply Customer Token function.", "description3": "Apart from access customer data through simulated applications, user can also write their own code to access customer data through the API call. This customer token indicates authorization has been granted by the customer in accessing their data. Each customer data record will have its unique token. Customer token must be embedded in every API access call. Customer token can be applied through the Apply Customer Token function."}, "simulatedSystems": {"adminList": "Admin Account List", "bankList": "Bank Account List", "cnVbsAccount": "CN VBS Accounts", "customerList": "Customer Account List", "downloadEWalletTip": "Click to download our e-wallet demo.", "hkVbsAccount": "HK VBS Accounts", "hkVcsAccount": "VCS Accounts", "login": "<PERSON><PERSON>", "loginName": "Login Name", "loginPassword": "Login Password", "role": "Role", "selectSupplyChainFinanceTip": "Please select the following assigned sandbox data record to log into the Supply Chain Finance：", "selectVbsCustomerTip": "Please select the following assigned sandbox data record to log into the VBS: ", "selectVcsCustomerTip": "Please select the following assigned sandbox data record to log into the VCS: ", "selectVisCustomerTip": "Please select the following assigned sandbox data record to log into the VIS: ", "staffList": "Staff Account List", "systemManagerList": "System Manager List", "systemOperatorList": "System Operator List", "thVbsAccount": "TH VBS Accounts", "uaeVbsAccount": "UAE VBS Accounts", "username": "Username"}, "stockCreation": {"accountNumber": "Account Number", "batchUploadStockCode": "Batch Upload Stock Code", "beforeRemoveMessage": "Confirm to remove {fileName}?", "channel": "Channel", "dataSource": "Data Source", "dataSourcePlaceholder": "Please enter the data source", "downloadAccountTemplate": "Download Account Te<PERSON>late", "frequency": "Frequency", "frequencyPlaceholder": "Please enter the frequency", "frequencyRate": "Frequency Rate", "frequencyRatePlaceholder": "Please enter the frequency rate", "handleExceedMessage": "Currently restricted to select 1 file, this time choose {thisTimeChoose} files，and totally choose {totallyChoose} files", "linkToDatatable": "Batch Upload Account", "noOfLots": "Lot", "noOfLotsPlaceholder": "Please enter the no. of lots", "orderType": "Order Type", "settlementAccount": "Settlement Account", "settlementAccountPlaceholder": "Please enter settlement account", "stockCode": "Stock Code", "stockCodeList": "Available Stock Code", "stockCodeTemplate": "Stock Code Template", "tableName": "Table Name", "tableNamePlaceholder": "Please enter the table name", "totalTransaction": "Max Transactions", "totalTransactionPlaceholder": "Please enter the max transactions", "transactionAccount": "Transaction Account", "transactionAccountPlaceholder": "Please enter the transaction account", "transactionAccountType": "Transaction Account Type", "transactionDate": "Transaction Date", "transactionDateFrom": "From", "transactionDateFromPlaceholder": "Please select the transaction date from", "transactionDateTo": "To", "transactionDateToPlaceholder": "Please select the transaction date to", "transactionDetails": "Transaction Details", "transactionType": "Transaction Type", "upload": "Upload", "uploadFileRule": "Only upload csv/xls/xlsx files, not exceed 5MB"}, "stockEnquiry": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter the account number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter the branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "createDate": "Create Date", "custodyCharges": "Custody Charges (HKD)", "fromCreateDate": "From Create Date", "fromTransactionAmount": "Transaction Amount From", "fromTransactionAmountPlaceholder": "Please enter the transaction amount from", "lotSize": "Lot Size", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "riskRating": "Risk Rating", "sharingNo": "Sharing No.", "stockNumber": "Stock Code", "stockPrice": "Stock Price (HKD)", "stockTrdingAmount": "Stock Trading Amount (HKD)", "stockTrdingCommission": "Stock Trading Commission (HKD)", "toCreateDate": "To Create Date", "toTransactionAmount": "Transaction Amount To", "toTransactionAmountPlaceholder": "Please enter the transaction amount to", "tradingOption": "Trading Option", "transactionAmount": "Transaction Amount (HKD)", "transactionDate": "Transaction Date", "transactionDesc": "Transaction Desc"}, "stockUpdate": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter the account number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter the branch code", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "custodyCharges": "Custody Charges (HKD)", "custodyChargesPlaceholder": "Please enter the custody charges", "lotSize": "Lot Size", "lotSizePlaceholder": "Please enter the lot size", "riskRating": "Risk Rating", "riskRatingPlaceholder": "Please enter the risk rating", "sharingNo": "Sharing No.", "sharingNoPlaceholder": "Please enter the sharing no.", "stockNumber": "Stock Code", "stockNumberPlaceholder": "Please enter the stock code", "stockPrice": "Stock Price (HKD)", "stockPricePlaceholder": "Please enter the stock price", "stockTrdingAmount": "Stock Trading Amount (HKD)", "stockTrdingAmountPlaceholder": "Please enter the stock trading amount", "stockTrdingCommission": "Stock Trading Commission (HKD)", "stockTrdingCommissionPlaceholder": "Please enter the stock trading commission", "title": "Edit Stock Data", "tradingOption": "Trading Option", "tradingOptionPlaceholder": "Please enter the trading option", "transactionAmount": "Transaction Amount (HKD)", "transactionAmountPlaceholder": "Please enter the transaction amount", "transactionDate": "Transaction Date", "transactionDatePlaceholder": "Please enter the transaction date", "transactionDesc": "Transaction Desc", "transactionDescPlaceholder": "Please enter the transaction desc"}, "stockView": {"title": "View Stock Data"}, "subject": {"assignment": "Assignment", "assignmentUpload": "Assignment Upload", "basic": "Basic", "business": "Business", "category": "Category", "categoryIsRequired": "Category is required", "categoryPlaceholder": "Please select category", "courseHoursIsRequired": "Subject hours is required", "courseName": "Course Name", "courseNameIsRequired": "Course name is required", "courseNamePlaceholder": "Please select course name", "createSubject": "Create Subject", "deleteSubjectPrompt": "This operation will permanently delete the subject Prompt. Do you want to continue?", "description": "Description", "descriptionIsRequired": "Description is required", "descriptionPlaceholder": "Please enter description", "editSubject": "Edit Subject", "high": "High", "keywordsPlaceholder": "Subject name", "level": "Level", "levelIsRequired": "Level is required", "levelPlaceholder": "Please select level", "medium": "Medium", "objective": "Objective", "objectiveIsRequired": "Objective is required", "objectivePlaceholder": "Please enter objective", "picture": "Picture", "pictureUpload": "Picture Upload", "pictureUploadIsRequired": "Picture upload is required", "presentationMaterial": "Presentation Material", "presentationMaterialUpload": "Presentation Material Upload", "subjectCode": "Subject Code", "subjectCodeIsRequired": "Subject code is required", "subjectCodePlaceholder": "Please enter subject code", "subjectDetail": "Subject Detail", "subjectHours": "Subject Duration(mins)", "subjectHoursPlaceholder": "Please enter subject hours", "subjectName": "Subject Name", "subjectNameIsRequired": "Subject name is required", "subjectNamePlaceholder": "Please enter subject name", "technical": "Technical", "video": "Video", "videoLinkUpload": "Video Link Upload"}, "termDepositCreation": {"accountNumber": "Account Number", "amountFrom": "Term Deposit Amount From", "amountFromPlaceholder": "Please enter the amount from", "amountTo": "To", "amountToPlaceholder": "Please enter the amount to", "beforeRemoveMessage": "Confirm to remove {fileName}?", "channel": "Channel", "dataSource": "Data Source", "dataSourcePlaceholder": "Please enter the data source", "downloadAccountTemplate": "Download Template File", "frequency": "Frequency", "frequencyPlaceholder": "Please enter the frequency", "frequencyRate": "Frequency Rate", "frequencyRatePlaceholder": "Please enter the frequency rate", "handleExceedMessage": "Currently restricted to select 1 file, this time choose {thisTimeChoose} files，and totally choose {totallyChoose} files", "linkToDatatable": "Batch Upload Account", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "period": "Term Deposit Period", "settlementAccount": "Settlement Account", "settlementAccountPlaceholder": "Please enter the settlement account", "tableName": "Table Name", "tableNamePlaceholder": "Please enter the table name", "termDepositAmount": "Term Deposit Amount", "totalTransaction": "Max Transactions", "totalTransactionPlaceholder": "Please enter the max transactions", "transactionAccount": "Transaction Account", "transactionAccountPlaceholder": "Please enter the transaction account", "transactionAccountType": "Transaction Account Type", "transactionDate": "Transaction Date", "transactionDateFrom": "From", "transactionDateFromPlaceholder": "Please select the transaction date from", "transactionDateTo": "To", "transactionDateToPlaceholder": "Please select the transaction date to", "transactionDetails": "Transaction Details", "upload": "Upload", "uploadFileRule": "Only upload csv/xls/xlsx files, not exceed 5MB"}, "termDepositEnquiry": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter the account number", "createDate": "Create Date", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "depositNumber": "Deposit Number", "depositNumberPlaceholder": "Please enter the deposit number", "fromCreateDate": "From Create Date", "fromTransactionAmount": "Transaction Amount From", "fromTransactionAmountPlaceholder": "Please enter the transaction amount from", "maturityAmount": "Maturity Amount", "maturityDate": "Maturity Date", "maturityInterest": "Maturity Interest", "maturityStatus": "Maturity Status", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "systemDate": "System Date", "termInterestRate": "Term Interest Rate", "termPeriod": "Term Period", "toCreateDate": "To Create Date", "toTransactionAmount": "Transaction Amount To", "toTransactionAmountPlaceholder": "Please enter the transaction amount to"}, "termDepositUpdate": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter the account number", "createDate": "Create Date", "createDatePlaceholder": "Please select the create date", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "depositAmountPlaceholder": "Please enter the deposit amount", "depositNumber": "Deposit Number", "depositNumberPlaceholder": "Please enter the deposit number", "lastUpdatedDate": "Last Updated Date", "lastUpdatedDatePlaceholder": "Please select the last updated date", "maturityAmount": "Maturity Amount", "maturityAmountPlaceholder": "Please enter the maturity amount", "maturityDate": "Maturity Date", "maturityDatePlaceholder": "Please enter the maturity date", "maturityInterest": "Maturity Interest", "maturityInterestPlaceholder": "Please enter the maturity interest", "maturityStatus": "Maturity Status", "maturityStatusPlaceholder": "Please enter the maturity status", "sandBoxId": "Sand Box Id", "sandBoxIdPlaceholder": "Please enter the sand box id", "systemDate": "System Date", "systemDatePlaceholder": "Please select the system date", "termInterestRate": "Term Interest Rate", "termInterestRatePlaceholder": "Please enter the term interest rate", "termPeriod": "Term Period", "termPeriodPlaceholder": "Please enter the term period", "title": "Edit Term Deposit Data"}, "termDepositView": {"title": "View Term Deposit Data"}, "tokenAccessGuide": {"description1": "Developers needs first to register in the API portal/gateway.", "description10": "/deposit/account/allAccounts/{customerNumber}/{index}/{items}", "description2": "With valid credentials, developers can request for the access token.", "description3": "Get Developer Token", "description4": "/platform/oauth2/client/token", "description5": "In conjunction with the pre-assigned customer accounts, the associated customer token have already been created and stored in the database, and ready for retrieval.", "description6": "Retrieve customer tokens", "description7": "/sysadmin/login", "description8": "/sysadmin/tokenRetrieval/{loginPK}", "description9": "Customer account enquiry: ", "subTitle1": "Developer Token (Access Token)", "subTitle2": "How to get the Access Tokens?", "subTitle3": "Sample Code - How to get the Access Tokens?", "subTitle4": "Customer <PERSON>", "subTitle5": "How to retrieve the Customer <PERSON>?", "subTitle6": "Sample Code - How to retrieve customer Tokens?", "subTitle7": "Sample Code - How to retrieve customer Tokens?"}, "transferCreation": {"accountNumber": "Account Number", "accountTypeFrom": "Account Type From", "accountTypeFromPlaceholder": "Please enter the account type from", "accountTypeTo": "Account Type To", "accountTypeToPlaceholder": "Please enter the account type to", "amountFr": "From", "amountTo": "To", "beforeRemoveMessage": "Confirm to remove {fileName}?", "channel": "Channel", "channelPlaceholder": "Please enter the channel", "dataSource": "Data Source", "dataSourcePlaceholder": "Please enter the data source", "downloadBranchTemplate": "Download Template File", "frequency": "Frequency", "frequencyPlaceholder": "Please enter the frequency", "frequencyRate": "Frequency Rate", "frequencyRatePlaceholder": "Please enter the frequency rate", "handleExceedMessage": "Currently restricted to select 1 file, this time choose {thisTimeChoose} files，and totally choose {totallyChoose} files", "linkToDatatable": "Batch Upload Account", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "tableName": "Table Name", "tableNamePlaceholder": "Please enter the table name", "totalTransaction": "Max Transactions", "totalTransactionPlaceholder": "Please enter the max transactions", "transactionDate": "Transaction Date", "transactionDateFrom": "From", "transactionDateFromPlaceholder": "Please select the transaction date from", "transactionDateTo": "To", "transactionDateToPlaceholder": "Please select the transaction date to", "transferAmount": "Transfer Amount", "transferAmountFrom": "From", "transferAmountFromPlaceholder": "Please enter the transfer amount from", "transferAmountTo": "To", "transferAmountToPlaceholder": "Please enter the transfer amount to", "transferFrom": "Transfer From", "transferFromPlaceholder": "Please enter the transfer from account", "transferFromType": "Transfer From", "transferTo": "Transfer To", "transferToPlaceholder": "Please enter the transfer to account", "transferToType": "Transfer To", "upload": "Upload", "uploadFileRule": "Only upload csv/xls/xlsx files, not exceed 5MB"}, "transferEnquiry": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter account number", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter branch code", "ccy": "<PERSON><PERSON><PERSON><PERSON>", "channel": "Channel", "channelID": "Channel ID", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter country code", "crDrMaintInd": "Tran Type", "custRelationMgrCode": "Account Manager Number", "dataDetails": "Data Details", "fromCreateDate": "From Create Date", "fromTransactionAmount": "Transaction Amount From", "fromTransactionAmountPlaceholder": "Please enter transaction amount from", "hkidfirstIssue": "Date of first issuance of Hong Kong identity card", "hkidissueDate": "Hong Kong Identity Card Issue Date", "numberGreaterThanPlaceholder": "{to} must be greater than {form}", "refAccountNumber": "Ref Account Number", "tableName": "Table Name", "tableNamePlaceholder": "Please select table name", "tfrSeqNo": "Transfer Seq No", "toCreateDate": "To Create Date", "toTransactionAmount": "Transaction Amount To", "toTransactionAmountPlaceholder": "Please enter transaction amount to", "tranAmt": "Tran Amt", "tranDate": "Tran Date", "tranDesc": "Tran Desc", "tranDescPlaceholder": "Please enter tran desc", "tranSeq": "Tran Seq", "tranType": "Tran Type"}, "transferUpdate": {"accountNumber": "Account Number", "accountNumberPlaceholder": "Please enter the account number", "actualBalAmt": "Actual Bal Amt", "actualBalAmtPlaceholder": "Please enter the actual bal amt", "branchCode": "Branch Code", "branchCodePlaceholder": "Please enter the branch code", "ccy": "Ccy", "ccyPlaceholder": "Please enter the Ccy", "channel": "Channel", "channelID": "Channel ID", "channelIDPlaceholder": "Please enter the channel id", "channelPlaceholder": "Please enter the Channel", "clearingCode": "Clearing Code", "clearingCodePlaceholder": "Please enter the clearing code", "countryCode": "Country Code", "countryCodePlaceholder": "Please enter the country code", "crDrMaintInd": "<PERSON><PERSON> <PERSON> Ind", "crDrMaintIndPlaceholder": "Please enter the cr dr maint ind", "previousBalAmt": "Previous Bal Amt", "previousBalAmtPlaceholder": "Please enter the previous bal amt", "refAccountNumber": "Ref Account Number", "refAccountNumberPlaceholder": "Please enter the ref account number", "reference": "Reference", "referencePlaceholder": "Please enter the Reference", "tfrSeqNo": "Tfr Seq No", "tfrSeqNoPlaceholder": "Please enter the tfr seq no", "title": "Edit Transfer Data", "tranAmt": "Tran Amt", "tranAmtPlaceholder": "Please enter the tran amt", "tranDate": "Tran Date", "tranDatePlaceholder": "Please select the tran date", "tranDesc": "Tran Desc", "tranDescPlaceholder": "Please enter the tran desc", "tranSeq": "Tran Seq", "tranSeqPlaceholder": "Please enter the tran seq", "tranType": "Tran Type", "tranTypePlaceholder": "Please enter the tran type"}, "transferView": {"title": "View Transfer Data"}, "user": {"activeStatus": "Active Status", "activeStatusPlaceholder": "User active status", "birthday": "Birthday", "birthdayPlaceholder": "Please select your birthday", "cardId": "Card Number", "cardIdIsRequired": "Card number is required", "cardIdPlaceholder": "Please enter your card ID number", "cardType": "Card Type", "cardTypeIsRequired": "Card type is required", "cardTypePlaceholder": "Please select your card type", "createUser": "Create User", "deleteUserPrompt": "This operation will permanently delete the user. Do you want to continue?", "department": "Department", "departmentPlaceholder": "Please enter your department", "downloadTemplate": "Download Template", "editUser": "Edit User", "email": "Email", "emailIsRequired": "Email is required", "emailPlaceholder": "Please enter your email", "experienceYear": "Experience Year", "experienceYearPlaceholder": "Please enter your years of work experience", "graduateSchool": "Graduate School", "graduateSchoolPlaceholder": "Please enter your graduation school", "importUser": "Import User", "invalidCardIdFormat": "Invalid card id format", "invalidEmailFormat": "Invalid email format", "invalidNicknameFormat": "Invalid nickname format, with a length of 2-64 characters", "invalidPasswordFormat": "At least uppercase and lowercase letters and numbers should be included, and the length should be 8-16 characters", "invalidPhoneNumberFormat": "Invalid phone number format", "invalidUsernameFormat": "Invalid username format, with a length of 2-64 characters", "isBlacklist": "<PERSON> Blacklist", "isBlacklistPlaceholder": "<PERSON> Blacklist", "jobTitle": "Job Title", "jobTitlePlaceholder": "Please enter your job title", "keywordsPlaceholder": "Please enter your email/username", "level": "User level", "levelIsRequired": "User level is required", "levelPlaceholder": "Please enter the level", "nickname": "Nickname", "nicknameIsRequired": "Nickname is required", "nicknamePlaceholder": "Please enter your nickname", "organization": "Organization Name", "organizationPlaceholder": "Please enter your organization name", "password": "Password", "passwordIsRequired": "Password is required", "passwordPlaceholder": "Please enter your login password", "phoneNumber": "Phone Number", "phoneNumberIsRequired": "Phone number is required", "phoneNumberPlaceholder": "Please enter your phone number", "pleaseSelectUserOrganization": "Please select organization to which the user belongs", "role": "Role", "roleIdsPlaceholder": "Please select user role", "roleIsRequired": "Role is required", "rolePlaceholder": "User role", "selectFile": "Select File", "sex": "Gender", "sexPlaceholder": "Please select your gender", "startImport": "Start Import", "studentOrStaffNumber": "Student/Staff Number", "studentOrStaffNumberPlaceholder": "Please enter your student/staff number", "username": "Username", "usernameIsRequired": "Username is required", "usernamePlaceholder": "Please enter your name"}, "workflowConfig": {"accountOpeningProcessNodeList": "Account Opening Process Node List", "accountOpeningProcessNodeRules": "Account Opening Process Node Rules", "apiMethod": "API Method", "apiUrl": "API URL", "clickUploadTip": "Click to upload", "createWorkflow": "Create Workflow", "customWorkflow": "Custom Workflow", "deleteTip": "Are you sure you want to delete it?", "eKYC": "Create eKYC", "eKYCTip1": "Tip: Use public nodes to create your workflow.", "eKYCTip2": "<p> <span>Follow these instructions to start creating a workflow: </span> </p> <p> <span>1.Workflow creation tool</span> </p> <p> <span>&nbsp; &nbsp; Click <a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow creation tool</b></a> jump to the online editing tool.</span> </p> <p> <span>2.Download template</span> </p> <p> <span>&nbsp; &nbsp; Click the download button in the public workflow of \"workflow\" on the previous page to get the public workflow template and click download.</span> </p> <p> <span>3.Using workflow template</span> </p> <p> <span>&nbsp; &nbsp; In the open tool page, click open and select the ekyc.bpmn file downloaded from the previous step.</span> </p> <p> <span>4.Start creating</span> </p> <p> <span>&nbsp; &nbsp; Start creating your workflow based on the workflow template.</span> </p> <p> <span>5.Save workflow</span> </p> <p> <span>&nbsp; &nbsp; In the lower left corner of the tool page, click the \"download\" icon to save the workflow file you created, and then click the \"upload file\" on the right to upload the workflow file you saved.</span> </p> <p> <br/> </p> <p> <span>Detailed operation manual of workflow creation tool, Please <a target=\"_blank\" href=\"https://simnectz-portal.cefar.cuhk.edu.hk/document/soi-plus/Workflow Operations Doucment.pdf\"><b>Click to download</b></a>。</span> </p> <p> <span>To create a workflow using public nodes, you need to follow the order rules between nodes. Please refer to \"<a target=\"_blank\" href=\"/domain-practicals/public-node-rules?type=KYC\"})\"><b>Public Node Rules</b></a>\"。</span> </p> <p> <br/> </p>", "file": "File", "fileError": "Please upload the file in. bpmn format, with a size of less than 2MB", "fileNumberError": "Only one file can be uploaded at a time. Please upload the selected file first.", "fileSizeError": "The file size cannot exceed 2MB!", "group": "Group", "insurance": "Create Insurance Claim", "insuranceClaimsProcessNodeList": "Insurance Claims Process Node List", "insuranceTip1": "Tip: use public nodes to create your workflow.", "insuranceTip2": "<p> <span>Follow these instructions to start creating a workflow: </span> </p> <p> <span>1.Workflow creation tool</span> </p> <p> <span>&nbsp; &nbsp; Click <a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow creation tool</b></a> jump to the online editing tool.</span> </p> <p> <span>2.Download template</span> </p> <p> <span>&nbsp; &nbsp; Click the download button in the public workflow of \"workflow\" on the previous page to get the public workflow template and click download.</span> </p> <p> <span>3.Using workflow template</span> </p> <p> <span>&nbsp; &nbsp; In the open tool page, click open and select the insurance_claim.bpmn file downloaded from the previous step.</span> </p> <p> <span>4.Start creating</span> </p> <p> <span>&nbsp; &nbsp; Start creating your workflow based on the workflow template.</span> </p> <p> <span>5.Save workflow</span> </p> <p> <span>&nbsp; &nbsp; In the lower left corner of the tool page, click the \"download\" icon to save the workflow file you created, and then click the \"upload file\" on the right to upload the workflow file you saved.</span> </p> <p> <br/> </p> <p> <span>Detailed operation manual of workflow creation tool, Please <a target=\"_blank\" href=\"https://simnectz-portal.cefar.cuhk.edu.hk/document/soi-plus/Workflow Operations Doucment.pdf\"><b>Click to download</b></a>。</span> </p> <p> <span>To create a workflow using public nodes, you need to follow the order rules between nodes. Please refer to \"<a target=\"_blank\" href=\"/practice-management/public-node-rules?type=insurance\"})\"><b>Public Node Rules</b></a>\"。</span> </p> <p> <br/> </p>", "kycAccountOpeningProcess": "KYC - Account Opening Process", "name": "Name", "process": "Process", "processGroup": "Process Group", "publicWorkflow": "Public Workflow", "selectProcessType": "Select Process Type", "uploadFailed": "Upload failed! Please try again later.", "uploadSuccess": "Upload successful!", "uploadTip": "Only upload *. bpmn files, file size cannot exceed 2MB.", "workflowNameError": "Please fill in the workflow name"}, "workspace": {"createNow": "Create Now", "createWorkspaceTip": "Create your exclusive workspace to enable online collaboration and coding work with your team.", "deleteWorkspaceTip": "Are you sure you want to delete the workspace? After deletion, the data in the workspace will not be recoverable.", "deleting": "Deleting", "entry": "Entry", "noResource": "You do not have permission to create a workspace. Please contact the administrator.", "offTime": "Last Shutdown Time", "runStatus": "Run Status", "running": "Running", "shutDownWorkspaceTip": "Are you sure you want to shut down the service? Please confirm that all your files have been saved.", "shutdown": "Shutdown", "shuttingDown": "Shutting Down", "start": "Start", "starting": "Starting", "url": "URL", "workspaceName": "Workspace Name", "workspaceNameErrorTip": "The workspace name only supports entering uppercase and lowercase letters, '-', and '_'", "workspaceNameTip": "Please enter the workspace name"}}}