{"soi": {"apiAccessGuide": {"description1": "对于开发人员集成或从给定API获取资源，需要授予他们访问权限。各种API提供者对其API发起各种控制，这意味着只有具有有效凭据的人才能访问这些资源。因此，API访问是确保只有具有身份验证凭据的用户才能访问API并使用其资源的过程。", "description10": "一旦获得批准，开发人员将能够访问账户信息，如账号、余额、交易等。", "description11": "作为此过程的一部分，您必须从每个用户获得权限。如果银行给你API访问权限，并不意味着你可以立即访问和利用他们的所有数据。客户必须同意为此目的使用其数据。", "description12": "对于注册到SOI+API门户平台的所有开发人员，您将自动从SOI+虚拟金融系统分配10个模拟客户账户。您将能够通过API市场上发布的API访问这些模拟客户数据。", "description13": "在SOI+API门户/市场上注册的所有API采用了相同的方法在API访问过程中。标准的OAuth2.0用作客户允许开发者访问其账户的授权方法。", "description14": "要访问虚拟金融系统中的模拟客户数据，您需要获得以下令牌：", "description15": "开发者令牌（访问令牌）", "description16": "客户令牌", "description2": "API访问通过API管理实现。API管理用于允许或拒绝准入的核心功能之一是API网关。网关用于使API调用能够被适当地接收和处理。", "description3": "开发人员需要首先在API提供者建立的API门户/网关上注册自己。使用有效凭据，您现在可以开始访问API。", "description4": "为了获得对API的访问权，您需要输入一个API密钥来证明您的身份。API密钥基本上是一个真实的字母和数字字符串。开发人员需要向API提供程序注册以获取API密钥。", "description5": "对于开放银行API，所有API都受到保护并高度安全。银行和TSP可以应用行业标准的OAuth2.0框架作为客户的授权方法，以允许TSP访问其账户。", "description6": "OAuth 2.0是一个行业标准授权框架，使银行能够授予银行开放API的数据访问权限。", "description7": "访问令牌用于基于令牌的身份验证，以使授权的TSP能够基于OAuth 2.0和Open ID Connect框架安全地访问银行业开放API。", "description8": "在API市场注册的所有API均采用OAuth2.0作为标准授权框架。", "description9": "开放银行API通过允许授权用户（例如开发人员）访问有关银行客户账户的特定信息来工作。为了做到这一点，开发商必须首先注册，然后获得银行的批准。", "subTitle1": "OAuth2.0 认证过程", "subTitle2": "开放银行API访问", "subTitle3": "开放银行API是如何工作的？", "subTitle4": "在SOI+平台中的API门户/市场中访问API"}, "apiArchitectureDesign": {"connectivityDescriptionKey": "访问源数据：", "connectivityDescriptionValue": "从系统、存储库或外部服务", "connectivityTitle": "系统层API", "description1": "API引导的连接是指使用可重用和设计良好的API来链接数据和应用程序的技术。", "description2": "API引导的集成，允许企业构建可组合的构建块，这些构建块可以被公开并且应用于创新业务能力的创建。", "description3": "这种方法使开发人员能够分解数据仓库，并通过自助服务和重用实现增强交付。开发人员可以实践API管理，以优化开放内容的使用。", "description4": "在以API为主导的连接中，创建了不同的API层级，以照顾各种系统、流程和最终用户体验。", "interfaceDescriptionKey": "数据呈现：", "interfaceDescriptionValue": "安全和治理", "interfaceTitle": "体验层API", "orchestrationDescriptionKey": "逻辑的应用：", "orchestrationDescriptionValue": "丰富与转换", "orchestrationTitle": "业务层API", "primaryTitle": "以 API 主导的构建块"}, "apiList": {"addApi": "新增API", "addApiTip": "此页面用于添加API，然后点击创建API。", "addEndpoint": "新增终结点", "apiIntroduction": "API 介绍", "apiName": "API 名称", "apiNameEn": "API 英文名称", "apiNameEnIsRequired": "API 英文名称不能为空", "apiNameExist": "API 名称已存在", "apiNameIsRequired": "API 中文名称不能为空", "apiNameZh": "API 中文名称", "apiSettings": "API设置", "className": "分类名称", "classNameIsRequired": "分类名称不能为空", "clickUpload": "点击上传", "confirmDeleteTip": "确认删除吗？", "createApi": "创建API", "createSuccess": "创建成功", "deleteSuccess": "删除成功", "description": "描述", "descriptionEn": "API 英文描述", "descriptionZh": "API 中文描述", "editApi": "编辑API", "editSuccess": "编辑成功", "endpoint": "终结点", "endpointDesigner": "定义", "image": "图片", "importApi": "导入API", "jsonErrorTip": "不是有效的JSON文件，请检查并再次上传。", "method": "请求方式", "subclassName": "子分类名称", "subclassNameIsRequired": "子分类名称不能为空", "swaggerJsonTipEn": "请将您的英文版swagger json完全粘贴到下面的框中。", "swaggerJsonTipZh": "请将您的中文版swagger json完全粘贴到下面的框中。", "swaggerSettings": "Swagger设置", "targetUrl": "目标地址", "targetUrlIsRequired": "目标地址不能为空", "targetUrlTip": "以 http:// 或 https:// 开头", "tryoutApi": "试用 API", "uploadFailed": "上传失败", "uploadSuccess": "上传成功", "urlOrFile": "API用户指南的URL或者上传文件", "userGuide": "用户指南", "valueIsRequired": "请输入swagger json"}, "apiMarketPlace": {"apiIntroduction": "API 介绍", "apiName": "API 名称", "apiProvider": "API 供应商", "apiStatus": "API 状态", "catalogName": "目录名称", "customApiFromOthers": "来自其他用户的API", "customApiSelf": "您的API", "deleteApiTip": "您确定要删除该API吗？", "deleteSuccess": "删除API成功", "downloadSwagger": "下载 Swagger", "generateToken": "生成令牌", "sandboxToken": "沙箱令牌", "tryoutApi": "试用 API", "userGuide": "用户指南", "username": "用户名"}, "apis": {"category": "分类", "categoryIsRequired": "分类不能为空", "className": "分类中文名称", "classNameEn": "分类英文名称", "classNameEnIsRequired": "分类英文名称不能为空", "classNameIsRequired": "分类中文名称不能为空", "classSubName": "子分类中文名称", "classSubNameEn": "子分类英文名称", "classSubNameEnIsRequired": "子分类英文名称不能为空", "classSubNameIsRequired": "子分类中文名称不能为空", "createApiCategory": "创建API分类", "createApiSubcategory": "创建API子分类", "deleteApiClassTip": "此操作将删除分类，是否继续？", "description": "中文描述", "descriptionEn": "英文描述", "descriptionEnIsRequired": "英文描述不能为空", "descriptionIsRequired": "中文描述不能为空", "image": "图片", "updateApiCategory": "编辑API分类", "updateApiSubcategory": "编辑API子分类"}, "applyCustomerDataToken": {"applyFailedTip": "申请失败", "applyNewToken": "申请新的令牌", "applySandboxTokenFailedTip": "上一个沙盒令牌应用程序失败，请重新提交请求。", "applySubmitSuccessTip": "您的请求已提交，请单击“检查任务状态”按钮查看任务的进度。", "applySuccessTip": "申请成功", "checkStatus": "检查任务状态", "correctNumberApplications": "请输入正确的申请数量", "correctNumberRange": "该数量在1-{max}之间", "customerNumber": "客户代码", "loginName": "登录名称", "loginPassword": "登录密码", "maxSandboxNumber": "剩余可申请Sandbox Data额度为：{max}", "number": "数量", "waitingCreationCompleteTip": "正在创建沙盒数据，请等待完成。"}, "authorize": {"activateTip": "请登录电子邮箱并点击链接激活", "agreeEndUserPrivacyPolicy": "同意最终用户隐私策略", "agreePrivacyPolicyTip": "请先阅读并检查隐私条款", "birthday": "出生日期", "birthdayPlaceholder": "请选择你的出生日期", "captcha": "验证码", "captchaIsRequired": "验证码不能为空", "cardId": "证件号码", "cardIdIsRequired": "证件号码不能为空", "cardIdPlaceholder": "请输入你的证件号码", "cardType": "证件类型", "cardTypeIsRequired": "证件类型不能为空", "cardTypePlaceholder": "请选择你的证件类型", "confirmPassword": "确认密码", "confirmPasswordIsRequired": "请再次输入你的登录密码", "confirmPasswordPlaceholder": "再次输入你的登录密码", "courseManagement": "课程管理", "department": "部门", "departmentPlaceholder": "请输入你的部门", "email": "电子邮箱", "emailIsRequired": "电子邮箱不能为空", "emailPlaceholder": "请输入你的电子邮箱", "emailSendSuccess": "电子邮件发送成功", "experienceYear": "工作年限", "experienceYearPlaceholder": "请输入你的工作年限", "forgotPassword": "忘记密码", "graduateSchool": "毕业学校", "graduateSchoolPlaceholder": "请输入你的毕业学校", "invalidCardIdFormat": "无效的证件号码格式", "invalidConfirmPasswordFormat": "两次输入的密码不一致", "invalidEmailFormat": "无效的电子邮箱格式", "invalidNicknameFormat": "无效的昵称格式，长度为2-64个字符", "invalidPasswordFormat": "至少要包含大小写字母和数字，且长度为8-16个字符", "invalidPhoneNumberFormat": "无效的手机号码格式", "invalidUsernameFormat": "无效的用户姓名格式，长度为2-64个字符", "jobTitle": "职务", "jobTitlePlaceholder": "请输入你的职务", "learningSpace": "学习空间", "level": "用户级别", "levelIsRequired": "用户级别不能为空", "levelPlaceholder": "请输入级别", "login": "登录", "loginNow": "现在登录", "logout": "登出", "newAccountPleaseFirstRegister": "新用户请先注册", "newPassword": "新密码", "nickname": "昵称", "nicknameIsRequired": "昵称不能为空", "nicknamePlaceholder": "请输入你的昵称", "organization": "组织名称", "organizationPlaceholder": "请输入你的组织名称", "password": "密码", "passwordIsRequired": "密码不能为空", "passwordPlaceholder": "请输入你的登录密码", "phoneNumber": "手机号码", "phoneNumberIsRequired": "手机号码不能为空", "phoneNumberPlaceholder": "请输入你的手机号码", "practicePlatform": "实践平台", "proficiencyTest": "能力测试", "register": "注册", "registerSuccess": "注册成功", "resetPassword": "重置密码", "resetPasswordTip": "请转到电子邮箱并单击链接进行身份验证，链接有效期为5分钟", "sendEmail": "发送电子邮件", "setNewPassword": "设置新密码", "sex": "性别", "sexPlaceholder": "请选择你的性别", "simnectzOfficialWebsite": "开连智能官网", "soiPlatform": "数字金融学创平台", "studentOrStaffNumber": "学/工号", "studentOrStaffNumberPlaceholder": "请输入你的学/工号", "systemManagement": "系统管理", "username": "用户姓名", "usernameIsRequired": "用户姓名不能为空", "usernamePlaceholder": "请输入你的姓名"}, "autoMachineLearning": {"accuracy": "精度", "adaboostTip": "adaboost是集成学习的一种，可用于分类和回归。其核心思想是针对同一个训练集训练不同的分类器(弱分类器)，然后把这些弱分类器集合起来，构成一个更强的最终分类器（强分类器）。", "addModel": "添加模型", "addSubTable": "添加子表", "addSubTableLabel": "可选，从原始数据中抽取的子表，可以有多个。如果需要对当前数据生成更多的特征，可从主表中抽取一部分列，生成子表，与主表建立关联，featuretools 可以自动生成更多特征。", "addTransposeSubTable": "添加反转子表", "addTransposeSubTableContent": "必选，需要列转行的字段", "addTransposeSubTableLabel": "从原始数据中通过列转行抽取的子表，可以有多个，可选。可从原始数据中选择一部分列，通过列转行操作生成子表，再与主表建立关联，featuretools 可以自动生成更多特征。目前该类子表都是通过“索引列”与主表关联，主表未指定“索引列”时，会生成一个“索引列”。", "additionalVariables": "其他字段", "ardRegressionTip": "是线性回归方法的一种。用于处理回归问题。", "autoSklearnParams": "Auto Sklearn 参数", "autoSklearnPlatform": "AutoSklearn 平台", "batchPredict": "批量预测", "batchPredictTips": "根据您上传的数据量大小，预测所需时间大致为：3000条数据预测耗时约5分钟；8000条数据预测耗时约30分钟；20000条数据预测耗时约90分钟。提交文件之后，预测过程不可中断，当前页面不可关闭。", "build": "构建", "buildSuccess": "构建成功", "clickUpload": "点击上传", "columnPlaceholder": "请选择", "createTableTip": "<p>根据您上传的数据字典会创建名为 <i><b>'prediction_result'</b></i> 的数据表，您可以选择该表然后进行预测结果分析。</p>", "customize": "自定义", "dRFTip": "一个随机森林算法，可用于分类和回归。是对数据样本及特征随机抽取，进行多个决策树训练，防止过拟合，提高泛化能力。", "dataDictFile": "数据字典文件", "dataFile": "数据文件", "dataManagement": "数据管理", "dataStatisticsCharts": "数据统计图", "dataStatisticsTable": "数据统计表", "decisionTreeTip": "decision_tree是一种基本的分类与回归方法，分类速度快。学习时，利用训练数据，根据损失函数最小化原则建立决策树模型。", "deeplearningTip": "引入了一种基于随机网格的深度神经网络算法。", "default": "默认", "deleteSuccessTip": "删除成功", "deleteTestFileTip": "您确定要删除此测试文件吗？", "deleteTip": "您确定要删除这些数据吗？", "descriptionPlaceholder": "请输入此文件的描述", "dragFileHere": "将文件拖到此处，或", "earlyStoppingRounds": "在训练的模型数量不会再增加的时候，用于停止训练新的模型。", "editModel": "编辑模型", "ensembleNbest": "通过 ensemble_nbest 来选择出集成的模型。", "ensembleSize": "从模型库中选择出模型的集成数量。", "excludeEstimators": "需要排除的算法。", "excludeTheseAlgorithms": "不用于模型构建的算法。", "extraTreesTip": "ET或Extra-Trees极端随机树，可用于分类和回归。算法与随机森林算法十分相似，都是由许多决策树构成。", "fileDetails": "文件详情", "fillType": "处理缺失数据，补 0 或者删除该行。", "framework": "框架", "gBMTip": "gradient_boosting梯度提升树算法，是集成学习的一种，相比较adaboost模型，其抗噪音的能力更强，但是实现较慢。", "gLMTip": "广义线性模型，是一类算法的统称，可以用来处理分类和回归问题。其中最常用的有逻辑回归和泊松回归。", "gaussianNbTip": "基于贝叶斯定理与特征条件独立假设的分类方法。", "gaussianProcessTip": "高斯过程。一般是为数据的回归值建立联合分布，来处理回归问题。", "gradientBoostingTip": "梯度提升树算法，是集成学习的一种，相比较adaboost模型，其抗噪音的能力更强，但是实现较慢。", "h2OParams": "H2O 参数", "h2OPlatform": "H2O 平台", "h2oBuildResult": "H2O 构建结果", "ignoreColumn": "忽略列", "index": "索引", "indexCanNotBeEmpty": "索引列不能为空", "indexColumn": "索引列", "indexLabel": "必选，子表索引列，通过该列与主表建立关联", "info": "信息", "initailConfigurationsViaMetalearning": "用配置项来初始化超参数算法。", "kNearestNeighborsTip": "k近邻算法（KNN），是一类可以被应用于分类或者回归的技术。它的适用面很广，并且在样本量足够大的情况下准确度很高。", "liblinearSvrTip": "线性支持向量回归。适合大量的数据处理，用于处理回归问题。", "libsvmSvcTip": "基于支持向量机的分类方法。", "libsvmSvrTip": "支持向量回归，处理回归问题。", "maxModelsToBuild": "用于指定除了 Stacked Ensemble 模型之外的，构建模型的数量。", "maxRuntimeSecs": "在训练 Stacked Ensemble 模型之前，训练模型使用的时间。", "modelDetails": "模型详情", "modelManagement": "模型管理", "modelName": "模型名称", "modelPerformanceCharts": "模型性能图", "modelPlaceholder": "请输入此模型的描述", "modelStatisticsTable": "模型统计表", "modelType": "模型类型", "name": "名称", "nfolds": "k-fold 交叉验证的折叠次数。", "noPerformanceChart": "暂无性能图", "numericalClassification": "数值分类", "numericalPrediction": "数值回归", "onlyXlsxFiles": "只能上传xlsx / xls文件", "other": "可选，子表其余字段", "perRunTimeLimit": "单次调用机器学习模型的时间限制（以秒为单位）。如果机器学习算法运行超过时间限制，模型拟合将终止。将此值设置得足够高，以便典型的机器学习算法可以适合训练数据。", "predict": "预测", "predictSuccess": "预测成功", "randomForestTip": "随机森林算法，可用于分类和回归。是对数据样本及特征随机抽取，进行多个决策树训练，防止过拟合，提高泛化能力。", "ratios": "训练数据的切分率（训练/测试）。", "resamplingStrategy": "防止过度拟合的时候，需要用到的参数，holdout 是切分数据的方式，指训练数据和测试数据的切分比例。", "responseColumn": "预测列", "resultFile": "预测结果文件", "ridgeRegressionTip": "岭回归是线性回归的一种，用于处理回归问题，可以用来解决标准线性回归的过拟合问题。", "rulesTip1": "请输入正整数", "rulesTip2": "请输入大于零小于1的小数", "rulesTip3": "请输入一个大于30正整数", "saveSuccessTip": "保存成功", "saveTopnModels": "选择最后需要保存的 top n 个模型。", "seed": "SMAC 的种子，将决定输出文件名。", "selectResponseColumnTip": "请选择预测列", "selectTextColumnTip": "请选择文本列", "settingColumnsType": "设置列类型", "sgdTip": "随机梯度下降，每次的权重更新只利用数据集中的一个样本来完成，也即一个epoch过程只有一次迭代和一个更新数据。对噪声比较敏感。", "sortMetric": "top 模型的排序规则。默认为 AUTO，指的是在二分类时选择ACU,多分类的时候选择 mean_per_class_error，回归的时候 选择 deviance。", "stackedEnsembleTip": "利用集成学习的方法，将所有模型利用stack方法进行集合。", "status": "状态", "testFile": "测试文件", "textClassification": "文本分类", "textColumn": "文本列", "thePredictionIs": "预测结果", "timeLeftForThisTask": "用于搜索适当模型的时间（以秒为单位）。通过增加这个值，有更高的机会找到更好的模型。", "trainSize": "resampling_strategy 的参数，指训练数据的切分比例。", "transformType": "数据预处理的方式。", "type": "类型", "updateModelSuccess": "更新成功", "updateModelTip": "您确定要更新此模型吗？", "uploadAcceptFileType": "只能上传 .csv 或者 .txt 文件", "uploadData": "上传数据", "uploadDataDict": "上传数据字典", "uploadFailTip": "上传文件失败", "uploadFailed": "上传失败，请重试", "uploadFileFirstTip": "请先上传文件", "uploadFirstTip": "将文件拖到此处，或者", "uploadLastTip": "点击上传", "uploadSuccess": "上传成功", "uploadSuccessTip": "上传文件成功", "uploadTestFile": "上传测试文件", "uploadTime": "上传时间", "xgradientBoostingTip": "Xgboost算法，同样可以应用于分类与回归问题。XGBoost 的特点就是计算速度快，模型表现好。"}, "codingPractice": {"addCodingQuestion": "新建编码题", "addQuestionTip": "您确认要创建编码题吗？", "apiInfo": "API 信息", "apiInformation": "API信息（中文）", "apiInformationEn": "API信息（英文）", "apiInformationTip": "请输入 API 信息，包含 API 的名称、URL、请求头信息、请求参数和响应信息。（中文）", "apiInformationTipEn": "请输入 API 信息，包含 API 的名称、URL、请求头信息、请求参数和响应信息。（英文）", "codeDescription": "代码描述（中文）", "codeDescriptionEn": "代码描述（英文）", "codeDescriptionTip": "请输入代码的描述信息（中文）", "codeDescriptionTipEn": "请输入代码的描述信息（英文）", "compareAnswers": "对比答案", "coreCodePattern": "核心代码模式", "coreCodePatternTip": "代码框中预设代码已经指定好类名、方法名、参数名，请勿修改或重新命名，直接返回值即可", "creator": "创建人", "databaseInformation": "数据库信息（中文）", "databaseInformationCoding": "数据库信息", "databaseInformationEn": "数据库信息（英文）", "databaseInformationTip": "请输入数据库信息（中文）", "databaseInformationTipEn": "请输入数据库信息（英文）", "deleteQuestionTip": "您确认要删除编码题吗？", "description": "描述", "downloadCodeTip": "不能下载空文件", "editCodingQuestion": "编辑编码题", "fontSize": "字体大小", "history": "练习历史", "initCodeDescription": "初始代码描述", "initialCode": "初始代码", "initialCodeTip": "请输入初始代码", "modelAnswer": "模型答案", "modelAnswerTip": "请输入模型答案", "practiceQuestionsCategory": "练习题分类", "printOutputTip": "没有输出语句，请尝试打印结果。", "programmingLanguage": "编程语言", "questionCategory": "分类", "questionCategoryTip": "请选择或输入问题一级分类（中文）", "questionCategoryTipEn": "请选择或输入问题一级分类（英文）", "questionDescription": "题干（中文）", "questionDescriptionEn": "题干（英文）", "questionDescriptionTable": "题干", "questionDescriptionTip": "请输入题干（中文）", "questionDescriptionTipEn": "请输入题干（英文）", "questionPrimaryCategory": "一级分类（中文）", "questionPrimaryCategoryEn": "一级分类（英文）", "questionPrimaryCategoryTable": "一级分类", "questionStatus": "状态", "questionSubcategory": "二级分类（中文）", "questionSubcategoryEn": "二级分类（英文）", "questionSubcategoryTable": "二级分类", "questionSubcategoryTip": "请选择或输入问题二级分类（中文）", "questionSubcategoryTipEn": "请选择或输入问题二级分类（英文）", "questionTitle": "题目（中文）", "questionTitleEn": "题目（英文）", "questionTitleSearch": "题目", "questionTitleTable": "题目", "questionTitleTip": "请输入问题标题（中文）", "questionTitleTipEn": "请输入问题标题（英文）", "questionTitleTipSearch": "请输入问题标题", "questionsManagement": "练习题管理", "resetCode": "此操作可能不会保存您的代码，是否要继续？", "run": "运行", "runTip": "点击运行，这里会显示运行结果。", "serialNumber": "序号", "startPracticing": "开始练习", "supportedLanguages": "支持的语言", "tabSize": "<PERSON>b <PERSON>", "target": "目标（中文）", "targetCoding": "目标", "targetEn": "目标（英文）", "targetTip": "请输入目标（中文）", "targetTipEn": "请输入目标（英文）", "theme": "主题", "trainingPurpose": "训练目的（中文）", "trainingPurposeCoding": "训练目的", "trainingPurposeEn": "训练目的（英文）", "trainingPurposeTip": "请输入训练目的（中文）", "trainingPurposeTipEn": "请输入训练目的（英文）", "updateQuestionTip": "您确认要更新编码题吗？", "viewModelAnswers": "查看模型答案", "yourAnswers": "你的答案"}, "common": {"404Tip": "请检查您输入的URL是否正确，或者单击下面的按钮返回主页。", "Next": "下一个", "Previous": "上一个", "active": "已激活", "add": "添加", "back": "返回", "backHome": "返回首页", "cancel": "取消", "chinese": "中文", "chineseMainlandId": "中国大陆身份证", "clear": "清空", "close": "关闭", "confirm": "确定", "copyright": "由 SIMNECTZ® 授权", "create": "创建", "createDate": "创建日期", "createTime": "创建时间", "creator": "创建人", "customerRoleHelpDocument": "客户角色帮助文档", "default": "默认", "defaultTitle": "SOI+ 平台", "delete": "删除", "description": "描述", "details": "详情", "download": "下载", "edit": "编辑", "endDate": "结束日期", "english": "英文", "error": "错误", "export": "导出", "exportLevel": "专家（10+年）", "false": "否", "female": "女", "goAnalyze": "去分析", "helpDocument": "帮助文档", "hongKongId": "香港身份证", "import": "导入", "invalidNumber": "无效的数字", "junior": "初级（1-3年）", "keywords": "关键词", "macaoId": "澳门身份证", "male": "男", "middle": "中级（3-6年）", "more": "更多", "noData": "没有数据", "normal": "正常", "notActive": "未激活", "oops": "哎呀!", "operate": "操作", "pageNotFound": "页面未找到，不能进入该页面...", "platformName": "数字金融学创平台", "practicePlatform": "实践平台", "reset": "重置", "save": "保存", "search": "搜索", "senior": "高级（6-10年）", "start": "开始", "startDate": "开始日期", "submit": "提交", "success": "成功", "tableOfContents": "目录", "taiwanId": "台湾身份证", "tip": "提示", "to": "至", "token": "令牌", "true": "是", "update": "更新", "updateBy": "更新人", "updateTime": "更新时间", "upload": "上传", "view": "查看", "warning": "警告", "welcome": "欢迎！"}, "course": {"courseCategoryIsRequired": "课程大分类为必填项", "courseCategoryPlaceholder": "请选择课程大分类", "courseCode": "课程代码", "courseCodeIsRequired": "课程代码为必填项", "courseCodePlaceholder": "请输入课程代码", "courseDescription": "课程描述", "courseDescriptionIsRequired": "课程描述为必填项", "courseDescriptionPlaceholder": "请输入课程描述", "courseDetail": "课程详情", "courseName": "课程名称", "courseNameIsRequired": "课程名称为必填项", "courseNamePlaceholder": "请输入课程名称", "coursePicture": "课程图片", "coursePictureIsRequired": "课程图片为必填项", "createCourse": "创建课程", "creator": "创建者", "deleteCoursePrompt": "此操作将永久删除课程。是否要继续？", "deleteError": "课程下存在有关课题，请先删除对应课程下的课题", "editCourse": "编辑课程", "keywordsPlaceholder": "课程名称", "updateBy": "更新者"}, "courseCategory": {"courseCategory": "课程大分类", "courseCategoryIsRequired": "课程大分类为必填项", "courseCategoryPicture": "课程大分类图片", "courseCategoryPictureIsRequired": "课程大分类图片为必填项", "courseCategoryPlaceholder": "请输入课程大分类", "createCourseCategory": "创建课程大分类", "deleteCourseCategoryPrompt": "此操作将永久删除课程大分类。是否继续？", "deleteError": "课程大分类下存在有关课程，请先删除对应课程大分类下的课程", "editCourseCategory": "编辑课程大分类", "keywordsPlaceholder": "课程大分类"}, "courseLearning": {"businessMarkdown": "业务文档", "completeStatus": "完成状态", "contentDescription": "内容描述", "contentDetails": "内容详情", "courseLearningSpace": "SOI+ 课程学习空间", "deletePathPrompt": "此操作将永久删除路径。是否继续？", "documentDirectory": "文档目录", "firstStudyTime": "首次学习时间", "job": "工作", "myPath": "学习路径", "name": "姓名", "noData": "暂无数据", "pathMaintenance": "路径维护", "role": "角色", "studentOrStaffNumber": "员工/学生 ID", "subTitle": "SOI+ 课程学习空间致力于为您打造一个涵盖广泛、内容丰富的课程体系，以满足您在金融科技领域的学习需求。无论您是初学者还是专业人士，SOI+都能为您提供量身定制的学习方案，助您在未来发展的道路上飞得更高。", "subscribe": "订阅", "technologyMarkdown": "技术文档", "video": "视频"}, "creditCardCreation": {"beforeRemoveMessage": "确认删除 {fileName}？", "creditCardNumber": "信用卡号", "creditCardNumberPlaceholder": "请输入信用卡号", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadCreditCardNumberTemplate": "下载信用卡号模板", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "频次", "frequencyRatePlaceholder": "请输入频次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "industry": "行业", "linkToDatatable": "批量上传账户", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "tableName": "表格名称", "tableNamePlaceholder": "请输入表格名称", "totalTransaction": "最大交易次数", "totalTransactionPlaceholder": "请输入最大交易次数", "transactionAmountFrom": "交易金额起", "transactionAmountFromPlaceholder": "请输入交易金额起", "transactionAmountTo": "至", "transactionAmountToPlaceholder": "请输入交易金额至", "transactionBy": "交易者", "transactionByFrom": "起", "transactionByFromPlaceholder": "请输入交易起", "transactionByTo": "至", "transactionByToPlaceholder": "请输入交易至", "transactionByType": "账户", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transactionTo": "交易到", "upload": "上传", "uploadFileRule": "仅上传 csv/xls/xlsx 文件，不超过 5MB"}, "creditCardEnquiry": {"bookingAmount": "帐面金额", "bookingCcy": "帐面货币", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "createDateRange": "创建时间范围", "creditCardNumber": "信用卡号", "creditCardNumberPlaceholder": "请输入信用卡号", "creditCardType": "信用卡类型", "dataDetails": "数据详情", "dealNumber": "交易编号", "displayName": "显示名称", "fromCreateDate": "起始创建时间", "fromTransactionAmount": "交易金额", "fromTransactionAmountPlaceholder": "请输入交易金额", "merchantName": "商户名称", "merchantNumber": "商户编号", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "tableName": "数据表名称", "tableNamePlaceholder": "请选择数据表名称", "toCreateDate": "结束创建时间", "toTransactionAmount": "至", "toTransactionAmountPlaceholder": "请输入交易金额", "transactionAmount": "交易金额", "transactionCcy": "货币类型", "transactionTime": "交易时间", "transactionType": "交易类型"}, "creditCardUpdate": {"authorizationNumber": "授权码", "authorizationNumberPlaceholder": "请输入授权码", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "creditCardNumber": "信用卡号", "creditCardNumberPlaceholder": "请输入信用卡号", "creditCardType": "信用卡类型", "creditCardTypePlaceholder": "请输入信用卡类型", "dealNumber": "交易号", "dealNumberPlaceholder": "请输入交易号", "merchantBalance": "商户余额", "merchantBalancePlaceholder": "请输入商户余额", "merchantName": "商户名称", "merchantNamePlaceholder": "请输入商户名称", "merchantNumber": "商户编号", "merchantNumberPlaceholder": "请输入商户编号", "title": "编辑信用卡数据", "transactionAmount": "交易金额", "transactionAmountPlaceholder": "请输入交易金额", "transactionCcy": "货币类型", "transactionCcyPlaceholder": "请输入货币类型", "transactionTime": "交易时间", "transactionTimePlaceholder": "请选择交易时间", "transactionType": "交易类型", "transactionTypePlaceholder": "请输入交易类型"}, "creditCardView": {"title": "查看信用卡数据"}, "customerBulkCreation": {"accommodation": "住宿", "accommodationPercentagePlaceholder": "请输入住宿百分比", "accommodationPlaceholder": "请选择住宿", "accountDataCreation": "账户数据创建", "accountDataCreationPlaceholder": "请选择账户数据创建", "accountType": "账户类型", "accountTypePlaceholder": "请选择账户类型", "ageGroup": "年龄组", "ageGroupPercentagePlaceholder": "请输入年龄组百分比", "ageGroupPlaceholder": "请选择年龄组", "branchCode": "分行代码", "branchCodePlaceholder": "请选择分行代码", "branchNumberFrom": "起", "branchNumberFromPlaceholder": "请输入分行编号起", "branchNumberTo": "至", "branchNumberToPlaceholder": "请输入分行编号至", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "contactPreferredMethod": "首选联系方式", "contactPreferredMethodPercentagePlaceholder": "请输入首选联系方式的百分比", "contactPreferredMethodPlaceholder": "请选择首选联系方式", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "countryOfBirth": "出生国家", "countryOfBirthPlaceholder": "请选择出生国家", "createDate": "创建日期", "createDatePlaceholder": "请选择创建日期", "currencyCode": "货币代码", "currencyCodePlaceholder": "请选择货币代码", "customerStatus": "客户状态", "customerStatusPlaceholder": "请选择客户状态", "education": "教育", "educationPercentagePlaceholder": "请输入教育百分比", "educationPlaceholder": "请选择教育", "employerIndustry": "雇主行业", "employerIndustryPlaceholder": "请选择雇主行业", "employmentStatus": "就业状况", "employmentStatusPercentagePlaceholder": "请输入就业状况百分比", "employmentStatusPlaceholder": "请选择就业状况", "fexAccountCurrencyCode": "FEX 账户货币代码", "fexAccountCurrencyCodePlaceholder": "请选择 fex 账户货币代码", "fromCreateDate": "从创建日期", "gender": "性别", "genderPercentagePlaceholder": "请输入性别百分比", "genderPlaceholder": "请选择性别", "householdIncome": "家庭收入", "householdIncomeAmountFrom": "起", "householdIncomeAmountFromPlaceholder": "请输入家庭收入金额起", "householdIncomeAmountTo": "至", "householdIncomeAmountToPlaceholder": "请输入家庭收入金额至", "householdIncomePercentagePlaceholder": "请输入家庭收入百分比", "householdIncomePlaceholder": "请选择家庭收入", "maritalStatus": "婚姻状况", "maritalStatusPercentagePlaceholder": "请输入婚姻状况百分比", "maritalStatusPlaceholder": "请选择婚姻状况", "maximumNumber": "最大添加数量为{number}", "monthlyIncome": "月收入", "monthlyIncomeFrom": "起", "monthlyIncomeFromPlaceholder": "请输入月收入起", "monthlyIncomePercentagePlaceholder": "请输入每月收入的百分比", "monthlyIncomePlaceholder": "请选择月收入", "monthlyIncomeTo": "至", "monthlyIncomeToPlaceholder": "请输入月收入至", "nationality": "国籍", "nationalityPercentagePlaceholder": "请输入国籍百分比", "nationalityPlaceholder": "请选择国籍", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "occupation": "职业", "occupationPlaceholder": "请选择职业", "otherMonthlyIncome": "其他月收入", "otherMonthlyIncomeFrom": "起", "otherMonthlyIncomeFromPlaceholder": "请输入其他月收入起", "otherMonthlyIncomePlaceholder": "请选择其他月收入", "otherMonthlyIncomeTo": "至", "otherMonthlyIncomeToPlaceholder": "请输入其他月收入至", "permanentResidenceStatus": "永久居住身份", "permanentResidenceStatusPercentagePlaceholder": "请输入永久居住身份百分比", "permanentResidenceStatusPlaceholder": "请选择永久居住身份", "preLanguage": "首选联系语言", "preLanguagePercentagePlaceholder": "请输入首选联系语言的百分比", "preLanguagePlaceholder": "请选择首选联系语言", "region": "地区", "regionPercentagePlaceholder": "请输入地区百分比", "regionPlaceholder": "请选择地区", "sensitiveStatus": "敏感状态", "sensitiveStatusPercentagePlaceholder": "请输入敏感状态的百分比", "sensitiveStatusPlaceholder": "请选择敏感状态", "sumOfPercentages": "{title}的百分比总和必须为 100", "tableName": "表名称", "tableNamePlaceholder": "请选择表名称", "toCreateDate": "创建日期日期", "totalCustomers": "客户总数", "totalCustomersPlaceholder": "请输入客户总数"}, "customerCreation": {"bulkCreation": "批量创建", "creationType": "创建类型", "singleCreation": "单次创建"}, "customerEnquiry": {"accommodation": "住宿", "accommodationPlaceholder": "请选择住宿", "ageGroup": "年龄组", "ageGroupPlaceholder": "请选择年龄组", "branchCode": "分行代码", "clearingCode": "银行代码", "contactPreferredMethod": "首选联系方式", "contactPreferredMethodPlaceholder": "请选择首选联系方式", "countryCode": "国家代码", "createDate": "创建日期", "customerId": "客户 ID", "customerIdType": "客户 ID 类型", "customerNumber": "客户编码", "education": "教育", "educationPlaceholder": "请选择教育", "employmentStatus": "就业状况", "employmentStatusPlaceholder": "请选择就业状况", "firstName": "名字", "fromCreateDate": "创建起始日期", "gender": "性别", "genderPlaceholder": "请选择性别", "lastName": "姓氏", "maritalStatus": "婚姻状况", "maritalStatusPlaceholder": "请选择婚姻状况", "nationality": "国籍", "nationalityPlaceholder": "请选择国籍", "permanentResidenceStatus": "永久居住状态", "permanentResidenceStatusPlaceholder": "请选择永久居住状态", "preLanguage": "首选联系语言", "preLanguagePlaceholder": "请选择首选联系语言", "residentialRegionName": "居住地区名称", "residentialRegionNamePlaceholder": "请选择居住地区名称", "sensitiveStatus": "敏感状态", "sensitiveStatusPlaceholder": "请选择敏感状态", "toCreateDate": "创建截至日期"}, "customerSingleCreation": {"accommodation": "住宿", "accommodationPlaceholder": "请选择住宿", "accountDataCreation": "账户数据创建", "accountDataCreationPlaceholder": "请选择账户数据创建", "accountType": "账户类型", "accountTypePlaceholder": "请选择账户类型", "ageGroup": "年龄组", "ageGroupPlaceholder": "请选择年龄组", "branchCode": "分行代码", "branchCodePlaceholder": "请选择分行代码", "branchNumberFrom": "起", "branchNumberFromPlaceholder": "请输入分行编号起", "branchNumberTo": "至", "branchNumberToPlaceholder": "请输入分行编号到", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "contactPreferredMethod": "联系方式", "contactPreferredMethodPlaceholder": "请选择联系方式", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "countryOfBirth": "出生国家", "countryOfBirthPlaceholder": "请选择出生国家", "createDate": "创建日期", "createDatePlaceholder": "请选择创建日期", "currencyCode": "货币代码", "currencyCodePlaceholder": "请选择货币代码", "customerStatus": "客户状态", "customerStatusPlaceholder": "请选择客户状态", "education": "教育程度", "educationPlaceholder": "请选择教育程度", "employerIndustry": "雇主行业", "employerIndustryPlaceholder": "请选择雇主行业", "employmentStatus": "就业状况", "employmentStatusPlaceholder": "请选择就业状况", "fexAccountCurrencyCode": "FEX账户货币代码", "fexAccountCurrencyCodePlaceholder": "请选择FEX账户货币代码", "fromCreateDate": "从创建日期", "gender": "性别", "genderPlaceholder": "请选择性别", "householdIncome": "家庭收入", "householdIncomeAmountFrom": "起", "householdIncomeAmountFromPlaceholder": "请输入家庭收入金额起", "householdIncomeAmountTo": "至", "householdIncomeAmountToPlaceholder": "请输入家庭收入金额至", "householdIncomePlaceholder": "请选择家庭收入", "maritalStatus": "婚姻状况", "maritalStatusPlaceholder": "请选择婚姻状况", "monthlyIncome": "月收入", "monthlyIncomeFrom": "起", "monthlyIncomeFromPlaceholder": "请输入每月收入起", "monthlyIncomePlaceholder": "请选择月收入", "monthlyIncomeTo": "至", "monthlyIncomeToPlaceholder": "请输入每月收入至", "nationality": "国籍", "nationalityPlaceholder": "请选择国籍", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "occupation": "职业", "occupationPlaceholder": "请选择职业", "otherMonthlyIncome": "其他月收入", "otherMonthlyIncomeFrom": "起", "otherMonthlyIncomeFromPlaceholder": "请输入其他月收入起", "otherMonthlyIncomePlaceholder": "请选择其他月收入", "otherMonthlyIncomeTo": "至", "otherMonthlyIncomeToPlaceholder": "请输入其他月收入至", "permanentResidenceStatus": "永久居留身份", "permanentResidenceStatusPlaceholder": "请选择永久居留身份", "preLanguage": "首选联系语言", "preLanguagePlaceholder": "请选择首选联系语言", "region": "地区", "regionPlaceholder": "请选择地区", "sensitiveStatus": "敏感状态", "sensitiveStatusPlaceholder": "请选择敏感状态", "tableName": "表格名称", "tableNamePlaceholder": "请选择表名称", "toCreateDate": "到创建日期"}, "customerUpdate": {"accommodation": "住宿", "accommodationPlaceholder": "请选择住宿", "age": "年龄", "ageGroup": "年龄组", "ageGroupPlaceholder": "请选择年龄组", "agePlaceholder": "请输入年龄", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "chineseName": "中文姓名", "chineseNamePlaceholder": "请输入中文姓名", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "companyAddressLine1": "公司地址行1", "companyAddressLine1Placeholder": "请输入公司地址行1", "companyAddressLine2": "公司地址行2", "companyAddressLine2Placeholder": "请输入公司地址行2", "companyAddressLine3": "公司地址行3", "companyAddressLine3Placeholder": "请输入公司地址行3", "companyAddressLine4": "公司地址行4", "companyAddressLine4Placeholder": "请输入公司地址行4", "contactPreferredMethod": "首选联系方式", "contactPreferredMethodPlaceholder": "请选择首选联系方式", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "countryOfBirth": "出生国家", "countryOfBirthPlaceholder": "请选择出生国家", "countryOfResidence": "居住国家", "countryOfResidencePlaceholder": "请输入居住国家", "createDate": "创建日期", "createDatePlaceholder": "请选择创建日期", "cusPrestEmploymentSinceDate": "客户目前就业开始日期", "cusPrestEmploymentSinceDatePlaceholder": "请选择客户目前就业开始日期", "custPresentAddPeriod": "客户当前地址年限", "custPresentAddPeriodPlaceholder": "请输入客户当前地址年限", "custPresentAddUpdateDate": "客户当前地址更新日期", "custPresentAddUpdateDatePlaceholder": "请选择客户当前地址更新日期", "custPresentEmploymentPeriod": "客户目前就业年限", "custPresentEmploymentPeriodPlaceholder": "请输入客户目前就业年限", "custPreviousAddPeriod": "客户历史居住年限", "custPreviousAddPeriodPlaceholder": "请输入客户历史居住年限", "custPreviousAddUpdateDate": "客户历史地址更新日期", "custPreviousAddUpdateDatePlaceholder": "请选择客户历史地址更新日期", "custPreviousEmploymntDate": "客户历史的雇佣日期", "custPreviousEmploymntDatePlaceholder": "请选择客户历史的雇佣日期", "custPreviousEmploymntPeriod": "客户历史就业年限", "custPreviousEmploymntPeriodPlaceholder": "请输入客户历史就业年限", "custRelationMgrCode": "客户关系经理代码", "custRelationMgrCodePlaceholder": "请输入客户关系经理代码", "customerID1": "客户ID1", "customerID1Placeholder": "请输入客户ID1", "customerID2": "客户ID2", "customerID2Placeholder": "请输入客户ID2", "customerIDType1": "客户ID类型1", "customerIDType1Placeholder": "请输入客户ID类型1", "customerIDType2": "客户ID类型2", "customerIDType2Placeholder": "请输入客户ID类型2", "customerNumber": "客户编码", "customerNumberPlaceholder": "请输入客户编码", "customerStatus": "客户状态", "customerStatusPlaceholder": "请选择客户状态", "education": "教育程度", "educationPlaceholder": "请选择教育程度", "emailAddress1": "电子邮件地址1", "emailAddress1Placeholder": "请输入电子邮件地址1", "employerCompanyName": "雇主公司名称", "employerCompanyNamePlaceholder": "请输入雇主公司名称", "employerIndustry": "雇主行业", "employerIndustryPlaceholder": "请选择雇主所在行业", "employmentStatus": "就业状况", "employmentStatusPlaceholder": "请选择就业状况", "firstName": "名字", "firstNamePlaceholder": "请输入名字", "gender": "性别", "genderPlaceholder": "请选择性别", "hkidfirstIssue": "香港身份证首次发行", "hkidfirstIssuePlaceholder": "请输入香港身份证首次发行", "hkidissueDate": "香港身份证发行日期", "hkidissueDatePlaceholder": "请选择香港身份证发行日期", "householdIncome": "家庭收入", "householdIncomePlaceholder": "请输入家庭收入", "issueCountry1": "发行国家1", "issueCountry1Placeholder": "请输入发行国家1", "issueCountry2": "发行国家2", "issueCountry2Placeholder": "请输入发行国家2", "issueDate1": "ID1发行日期", "issueDate1Placeholder": "请输入ID1发行日期", "issueDate2": "ID2发行日期", "issueDate2Placeholder": "请输入ID2发行日期", "lastName": "姓氏", "lastNamePlaceholder": "请输入姓氏", "maritalDate": "结婚日期", "maritalDatePlaceholder": "请选择结婚日期", "maritalStatus": "婚姻状况", "maritalStatusPlaceholder": "请选择婚姻状况", "minorInd": "是否未成年人", "minorIndPlaceholder": "请输入是否未成年人", "mobilePhoneNumber1": "手机号码1", "mobilePhoneNumber1Placeholder": "请输入手机号码1", "monthlySalary": "月薪", "monthlySalaryPlaceholder": "请输入月薪", "nationality1": "国籍1", "nationality1Placeholder": "请选择国籍1", "occupation": "职业", "occupationPlaceholder": "请选择职业", "otherMonthlyIncome": "其他月收入", "otherMonthlyIncomePlaceholder": "请输入其他月收入", "permanentResidenceStatus": "永久居留身份", "permanentResidenceStatusPlaceholder": "请选择永久居留身份", "personalInfoUpdateDate": "个人信息更新日期", "personalInfoUpdateDatePlaceholder": "请选择个人信息更新日期", "position": "职位", "positionPlaceholder": "请输入职位", "preLanguage1": "首选联系语言", "preLanguage1Placeholder": "请选择首选联系语言", "preTimeFrom1": "客户服务时间开始1", "preTimeFrom1Placeholder": "请输入客户服务时间开始1", "preTimeTo1": "客户服务时间结束1", "preTimeTo1Placeholder": "请输入客户服务时间结束1", "residentAddressMaintBranch": "客户居住地的分行编号", "residentAddressMaintBranchPlaceholder": "请输入客户居住地的分行编号", "residentialAddressLine1": "住宅地址行 1", "residentialAddressLine1Placeholder": "请输入住宅地址行 1", "residentialAddressLine2": "住宅地址行 2", "residentialAddressLine2Placeholder": "请输入住宅地址行 2", "residentialAddressLine3": "住宅地址行 3", "residentialAddressLine3Placeholder": "请输入住宅地址行 3", "residentialAddressLine4": "住宅地址行 4", "residentialAddressLine4Placeholder": "请输入住宅地址行 4", "residentialDistrictName": "居住小区名称", "residentialDistrictNamePlaceholder": "请输入居住小区名称", "residentialRegionName": "居住区域名称", "residentialRegionNamePlaceholder": "请选择居住区域名称", "seniorInd": "是否老年人", "seniorIndPlaceholder": "请请输入是否老年人", "sensitiveStatus": "敏感状态", "sensitiveStatusPlaceholder": "请选择敏感状态", "spouseDateOfBirth": "配偶出生日期", "spouseDateOfBirthPlaceholder": "请选择配偶出生日期", "spouseID": "配偶 ID", "spouseIDPlaceholder": "请输入配偶 ID", "spouseIDType": "配偶证件类型", "spouseIDTypePlaceholder": "请输入配偶 ID 类型", "spouseName": "配偶姓名", "spouseNamePlaceholder": "请输入配偶姓名", "tableName": "表名称", "tableNamePlaceholder": "请输入表名称", "title": "编辑客户数据", "weChatID": "微信ID", "weChatIDPlaceholder": "请输入微信id"}, "customerView": {"title": "查看客户数据"}, "dataAnalysisUseCase": {"dataAnalysisBank": "银行", "dataAnalysisInsurance": "保险", "dataAnalysisStock": "证券", "document": "文档", "name": "名称", "video": "视频"}, "dataDictionary": {"advancedTransactionDataBasedOnKnowledgeGraphData": "基于知识图谱的进阶版交易数据", "creditCardLossPredict": "信用卡流失预测交易数据", "genericTransactionDataWithAMLModuleIncluded": "通用交易数据（包括洗钱可疑交易行为模型）", "knowledgeGraphData": "知识图谱数据", "selfGeneratedData": "自身生成数据", "useCaseData": "综合案例数据"}, "dataEnquiryDetails": {"CustRelationMgrCode": "客户关系经理代码", "accommodation": "住房状态", "accountNumber": "账号", "actualBalAmt": "实际余额", "age": "年龄", "ageGroup": "年龄组", "authorizationNumber": "授权编号", "bookingAmount": "帐面金额", "bookingCcy": "帐面编号", "branchCode": "分行代码", "ccy": "货币", "channel": "渠道", "channelID": "渠道ID", "chineseName": "中文姓名", "clearingCode": "银行代码", "companyAddressLine1": "公司地址行1", "companyAddressLine2": "公司地址行2", "companyAddressLine3": "公司地址行3", "companyAddressLine4": "公司地址行4", "contactPreferredMethod": "首选联系方式", "countryCode": "国家代码", "countryOfBirth": "出生国家", "countryOfResidence": "居住国家", "crDrMaintInd": "借贷标志", "createDate": "创建日期", "creditCardNumber": "信用卡号", "creditCardType": "信用卡类型", "cusPrestEmploymentSinceDate": "客户目前就业开始日期", "custPresentAddPeriod": "客户当前地址年限", "custPresentAddUpdateDate": "客户当前地址更新日期", "custPresentEmploymentPeriod": "客户目前就业年限", "custPreviousAddPeriod": "客户历史居住年限", "custPreviousAddUpdateDate": "客户历史地址更新日期", "custPreviousEmploymntDate": "客户历史的雇佣日期", "custPreviousEmploymntPeriod": "客户历史就业年限", "custRelationMgrCode": "客户关系经理代码", "custodyCharges": "托管费用 (HKD)", "customerAccountNumber": "账户号码", "customerAccountType": "账户类型", "customerID1": "客户ID1", "customerID2": "客户ID2", "customerIDType1": "客户ID类型1", "customerIDType2": "客户ID类型2", "customerNumber": "客户编码", "customerStatus": "客户状态", "dateOfBirth": "出生日期", "dealNumber": "交易号", "depositAmount": "存款金额", "depositNumber": "存款编号", "displayName": "显示名称", "education": "教育", "emailAddress1": "电子邮件地址1", "employerCompanyName": "雇主公司名称", "employerIndustry": "雇主行业", "employmentStatus": "就业状况", "exchangeAmoutInForeignCurrency": "外币兑换金额", "exchangeAmoutInLocalCurrency": "本地货币兑换金额", "exchangeRate": "汇率", "firstName": "客户名", "foreignCurrency": "外币", "fundCcy": "基金货币类型", "fundCode": "基金代码", "fundPrice": "基金价格", "gender": "性别", "hKIDFirstIssue": "香港身份证首次签发", "hKIDIssueDate": "香港身份证签发日期", "hkidfirstIssue": "香港身分证首次签发", "hkidissueDate": "香港身分证签发日期", "householdIncome": "家庭收入", "id": "ID", "issueCountry1": "发行国家1", "issueCountry2": "发行国家2", "issueDate1": "发行日期1", "issueDate2": "发行日期2", "lastName": "客户姓", "lastUpdateDate": "最后更新日期", "lastUpdatedDate": "上次更新日期", "localCurrency": "本地货币", "lotSize": "每手股数", "maritalDate": "结婚日期", "maritalStatus": "婚姻状况", "maturityAmount": "到期金额", "maturityDate": "到期日", "maturityInterest": "到期利息", "maturityStatus": "到期状态", "merchantBalance": "商户余额", "merchantName": "商户名称", "merchantNumber": "商户编号", "minorInd": "未成年人", "mobilePhoneNumber1": "手机号码1", "monthlySalary": "月薪", "nationality1": "国籍1", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "occupation": "职业", "otherMonthlyIncome": "其他月收入", "payeeCategory": "收款人类别", "payeeCategoryID": "收款人类别ID", "payeeID": "收款人ID", "payeeNumber": "收款人编号", "paymentEffectiveDay": "付款生效日", "permanentResidenceStatus": "永久居留身份", "personalInfoUpdateDate": "个人信息更新日期", "position": "职位", "postBalInForeignCurrencyAccount": "外币账户过账余额", "preLanguage1": "首选联系语言", "preTimeFrom1": "预时间从1", "preTimeTo1": "预时间至1", "prevBalInForeignCurrencyAccount": "外币账户上一笔余额", "previousBalAmt": "初期余额", "refAccountNumber": "对方账号", "reference": "参考", "remarks": "备注", "residentAddressMaintBranch": "客户居住地的分行编号", "residentialAddressLine1": "住宅地址行 1", "residentialAddressLine2": "住宅地址行 2", "residentialAddressLine3": "住宅地址行 3", "residentialAddressLine4": "住宅地址行 4", "residentialDistrictName": "住宅区名称", "residentialRegionName": "住宅区域名称", "riskRating": "风险评级", "sandBoxId": "沙箱ID", "seniorInd": "老年人", "sensitiveStatus": "敏感状态", "sharingNo": "交易份额", "spouseDateOfBirth": "配偶出生日期", "spouseID": "配偶身份证", "spouseIDType": "配偶身份证类型", "spouseName": "配偶姓名", "status": "状态", "stockNumber": "股票编号", "stockPrice": "股票价格 (HKD)", "stockTrdingAmount": "股票交易额 (HKD)", "stockTrdingCommission": "股票交易佣金 (HKD)", "systemDate": "系统日期", "termInterestRate": "存款利率", "termPeriod": "存款期限", "tfrSeqNo": "对方交易流水号", "tradingAmount": "基金交易额", "tradingOption": "交易选项", "tranAmt": "交易金额", "tranDate": "交易日期", "tranDesc": "交易描述", "tranSeq": "交易流水号", "tranType": "交易类型", "transactionAmount": "交易总金额 (HKD)", "transactionCcy": "货币类型", "transactionCurrency": "交易货币", "transactionDate": "交易日期", "transactionDealNumber": "交易编号", "transactionDesc": "交易描述", "transactionTime": "交易时间", "transactionType": "交易类型", "trdingCommission": "交易佣金", "weChatID": "微信ID"}, "dataImport": {"clickUploadTip": "点击上传", "createDatabaseSuccess": "创建数据库成功", "createDatabaseTip": "在上传数据之前，请创建自己的数据库。", "customDataSource": "自定义数据源", "dataTableName": "数据表名称", "databaseName": "数据库名称", "databaseNameFormatError": "只允许使用小写字母、数字和下划线", "databaseNameLengthError": "长度为5到30个字符", "databaseTable": "数据库表", "databaseTableBackupFile": "数据库表备份文件。", "deleteDataTableTip": "确定要删除此数据表吗？", "downloadTemplateFile": "下载模板文件", "fileSizeTooLargeTip": "文件大小不能超过50MB！", "fromYour": "来自您的", "importData": "导入数据", "importDataDescription": "导入数据描述", "importSuccess": "导入成功", "mysql": "MySQL", "only50": "仅展示50条数据预览", "onlySelectOneFileTip": "一次只能上传一个文件。请先上传所选文件。", "onlySupported": "仅支持", "period": "。", "pleaseSelectFile": "请选择文件。", "pleaseUpload": "请上传", "previewData": "预览数据", "publicDataSource": "公共数据源", "supportedFileTypes": "*.xlsx 或 *.xls 文件，", "updateData": "更新数据", "updateDataDescription": "更新数据描述", "uploadFailed": "上传失败", "uploadSuccess": "上传成功", "uploadTip": "只上传*.xlsx 或 *.xls文件，文件大小不能超过50MB。"}, "dataVisualization": {"addMysqlConnect": "添加MySQL连接", "aggregate": "聚合", "aliasNameError": "只允许大小写字母、数字和下划线，长度在1-10之间。", "ascending": "升序", "avg": "平均值", "backgroundColor": "背景颜色", "barDirection": "柱状图方向", "bucketByMonth": "按月分桶", "bucketByQuarterly": "按季度分桶", "bucketByYear": "按年分桶", "bucketing": "分桶", "bucketingDate": "该字段为日期类型，可选择范围：", "bucketingEnd": "分桶的结束位置", "bucketingNumber": "该字段为数字类型，可选择范围：", "bucketingStart": "分桶的起始位置", "bucketingType": "该字段为字符串类型，数据总量: ", "categoryColor": "分类颜色", "chartBottomMargin": "图表下边距", "chartLeftMargin": "图表左边距", "chartMargins": "图标边距", "chartNameError": "只允许大小写字母、数字、破折号和下划线，长度在1-100之间。", "chartNameTip": "请输入图表名称", "chartRightMargin": "图表右边距", "chartTopMargin": "图表上边距", "charts": "图表", "childTypeCount": "类型计数", "clearStyleTip": "更改维度，量度，颜色，过滤，聚合和排序时，将重新生成图表。这时，上一个图表的样式将被清除。您确定要执行吗？", "color": "按颜色分类", "colorTip": "请将分类字段拖到此处", "columnAliasName": "列别名", "columnAliasNameMessage": "请输入列别名", "columnAliasNameP": "数据类型转换新生成列的名称", "columnName": "参数名", "columnNameMessage": "请输入列名", "connectionSymbol": "连接符号", "converted": "转换后的数据无法再次转换", "count": "计数", "createChart": "创建图表", "createDataSource": "创建数据源", "createDataTable": "创建数据表", "createDatabase": "创建数据库", "createTip": "如果没有历史数据表，请点击此按钮去创建。", "currentDataSource": "当前数据源", "currentDataType": "当前数据类型", "currentDatabase": "当前数据库", "currentDateFormat": "当前日期格式", "customDataSource": "自定义数据源", "dataSource": "数据源", "dataSourceDescription": "数据源描述", "dataSourceHost": "数据源地址", "dataSourceNameSame": "名称已存在,请换一个试试。", "dataSourceNameTip": "请输入数据源名称", "dataSourcePassword": "数据源密码", "dataSourcePasswordIsRequired": "数据源密码不能为空", "dataSourcePort": "数据源端口号", "dataSourcePortIsRequired": "数据源端口号不能为空", "dataSourceSourceHostIsRequired": "数据源地址不能为空", "dataSourceType": "数据源类型", "dataSourceTypeIsRequired": "数据源类型不能为空", "dataSourceUsername": "数据源用户名", "dataSourceUsernameIsRequired": "数据源用户名不能为空", "dataTable": "数据表", "dataTableName": "数据表名称", "dataTableNameTip": "请输入数据表名称", "dataTooLarge": "数据集太大，请过滤", "databaseDate": "数据库日期类型", "dateCalculation": "计算年龄", "dateFormat": "转换后日期格式", "dateFormatMessage": "请选择转换的日期格式。", "decimalLength": "数字最大长度", "deleteDataSourceTip": "删除该数据源，会将关联该数据源的数据表和图表一起删除！确定删除吗？", "deleteMyChartTip": "您确定要删除吗？", "deleteTableTip": "删除该数据表，会将关联该数据表的图表一起删除！确定删除吗？", "descending": "降序", "dimensionsTip": "请将维度字段拖到此处", "editDataSource": "编辑数据源", "endColor": "结束颜色", "enumTypeFormat": "枚举类型转换", "excessiveColor": "过度颜色", "executeFail": "执行失败", "figure": "图形", "filter": "条件过滤", "filterConditionNotNull": "过滤条件不能为空", "getModelFail": "获取数据模型详情失败", "hasNoDataSourceTip": "您没有初始化同步数据库，是否现在开始创建？", "importCsvFile": "导入xls、xlsx文件", "joinCondition": "关联条件", "joinTable": "请选择要关联的表", "joinTableTitle": "关联表", "joinType": "请选择关联类型", "keepDecimalPlaces": "保留小数位数", "labelFont": "标签字体", "labelFontColor": "标签字体颜色", "labelFontSize": "标签字体大小", "labelPosition": "标签位置", "left": "距离左边", "legend": "图例", "legendBottomMargins": "图例下边距", "legendDirection": "图例方向", "legendFont": "图例字体", "legendFontColor": "图例字体颜色", "legendFontSize": "图例字体大小", "legendLeftMargins": "图例左边距", "legendOrient": "图例方向", "legendPosition": "图例位置", "lineStyle": "折线样式", "lineType": "折线图类型", "loadingText": "拼命加载中...", "measuresTip": "请将量度字段拖到此处", "millisecond": "毫秒", "name": "名称", "nameSame": "别名不能和列名相同", "noDataSourceTip": "没有数据源", "noDataTableTip": "没有数据表", "noDatabaseTip": "没有数据库", "noSorting": "不排序", "numberType": "数字类型", "numberTypeFormat": "数字类型转换", "numberTypeMessage": "请选择数字类型。", "only50": "仅展示50条数据预览", "order": "排序", "perfectTip": "请完善规则后重试", "pieType": "饼图类型", "pleaseExecute": "保存失败，请先执行", "previewData": "数据预览", "publicDataSource": "公共数据源", "queryFailed": "查询失败", "quickGroup": "快捷分组", "quickGroupTip": "请输入分组大小", "reimport": "重新导入", "rotate": "X轴标签旋转角度", "saveChart": "保存图表", "searchField": "搜索字段", "second": "秒", "selectDataSourceType": "选择数据源类型", "selectModel": "选择数据表", "setChartsTitle": "设置图表标题", "setTimeFormat": "数据转换", "showAllLabelsOnXAxis": "显示X轴所有标签", "showLabel": "是否显示标签", "showLegend": "是否显示图例", "showSplitLine": "是否显示分割线", "showXAxis": "是否显示X轴", "showYAxis": "是否显示Y轴", "showZoom": "是否显示缩放图", "stackTotal": "是否堆叠总数", "startColor": "开始颜色", "string": "字符串", "style": "样式", "sum": "求和", "syncDataLoading": "正在同步数据，请耐心等待。", "synchronizeSandboxData": "同步沙箱数据", "tables": "数据库表", "testConnect": "测试链接", "timeStampType": "时间戳类型", "timeStampTypeMessage": "请选择时间戳的类型。", "timeTypeFormat": "时间类型转换", "timeTypeMessage": "请选择时间的类型。", "timestamp": "时间戳", "title": "标题", "titleFont": "标题字体", "titleFontColor": "标题字体颜色", "titleFontSize": "标题字体大小", "titlePosition": "标题位置", "tooManyTypes": "枚举类型过多，暂不支持转换。", "top": "距离上边", "typeCount": "分类计数", "typeMismatch": "类型不匹配", "unableToDelete": "无法删除，请先删除其子节点。", "updateChart": "更新图表", "updateSuccess": "更新成功", "xAxisColor": "X轴颜色", "yAxisColor": "Y轴颜色", "zoomFontColor": "缩放图字体颜色"}, "documentManagement": {"changeFileTip": "请先上传已选择的文档，或删除之后重新选择。", "classIsRequired": "分类不能为空", "className": "分类中文名称", "classNameEn": "分类英文名称", "classNameEnIsRequired": "分类中文名称不能为空", "classNameEnPlaceholder": "请填写分类英文名称", "classNameIsRequired": "分类中文名称不能为空", "classNamePlaceholder": "请填写分类中文名称", "clickUploadTip": "点击上传", "cnDocUrl": "中文文档", "cnVideoUrl": "中文视频", "createCategory": "创建分类", "deleteCategoryTip": "此操作将永久删除类别，是否继续？", "deleteTip": "你确定要删除吗？", "docName": "文档中文名称", "docNameEn": "文档英文名称", "docNameEnIsRequired": "文档英文名称不能为空", "docNameIsRequired": "文档中文名称不能为空", "docType": "文档类型", "docUploadTip": "支持以下文件格式: *.txt, *.doc, *.docx, *.pdf, *.ppt, *.pptx, *.xlsx, *.xls。 文档中如果包含中文字符，请先手动转换成PDF格式再上传。", "editCategory": "编辑分类", "enDocUrl": "英文文档", "enVideoUrl": "英文视频", "groupName": "分组中文名称", "groupNameEn": "分组英文名称", "groupNameEnIsRequired": "分组英文名称不能为空", "groupNameEnPlaceholder": "请填写分组英文名称", "groupNameIsRequired": "分组中文名称不能为空", "groupNamePlaceholder": "请填写分组中文名称", "icon": "图标", "noteTip": "注意：分组名称和分类名称为系统参数，不可更改！！！", "selectFile": "选择文件", "selectFileTip": "请选择要上传的文件", "uploadFailedTip": "上传失败，请稍后重试", "uploadSuccessTip": "上传成功", "videoUploadTip": "支持以下文件格式: *.mp3, *.mp4"}, "domainPracticals": {"businessUseCase": "业务场景用例", "code": "代码", "coreLogic": "核心逻辑", "document": "文档", "name": "名称", "technologyUseCase": "技术场景用例", "video": "视频"}, "downloadCenter": {"dockerInstall": "<h1 style=\"border: 0px; margin: 0px 0px 10px; padding: 0px; font-size: 2.1em; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> Windows Docker 安装 </h1> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker 并非是一个通用的容器工具，它依赖于已存在并运行的 Linux 内核环境。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker 实质上是在已经运行的 Linux 下制造了一个隔离的文件环境，因此它执行的效率几乎等同于所部署的 Linux 主机。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 因此，Docker 必须部署在 Linux 内核的系统上。如果其他系统想部署 Docker 就必须安装一个虚拟 Linux 环境。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img width=\"50%\" src=\"https://www.runoob.com/wp-content/uploads/2016/05/CV09QJMI2fb7L2k0.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 在 Windows 上部署 Docker 的方法都是先安装一个虚拟机，并在安装 Linux 系统的的虚拟机中运行 Docker。 </p> <h2 style=\"border: 0px; margin: 2px 0px; padding: 0px; font-size: 1.8em; line-height: 1.8em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> Win10 系统 </h2> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker Desktop 是 Docker 在 Windows 10 和 macOS 操作系统上的官方安装方式，这个方法依然属于先在虚拟机中安装 Linux 然后再安装 Docker 的方法。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Docker Desktop 官方下载地址：&nbsp;<a href=\"https://hub.docker.com/editions/community/docker-ce-desktop-windows\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">https://hub.docker.com/editions/community/docker-ce-desktop-windows</a> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <strong style=\"border: 0px; margin: 0px; padding: 0px;\">注意：</strong>此方法仅适用于 Windows 10 操作系统专业版、企业版、教育版和部分家庭版！ </p> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 安装 Hyper-V </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> Hyper-V 是微软开发的虚拟机，类似于 VMWare 或 VirtualBox，仅适用于 Windows 10。这是 Docker Desktop for Windows 所使用的虚拟机。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 但是，这个虚拟机一旦启用，QEMU、VirtualBox 或 VMWare Workstation 15 及以下版本将无法使用！如果你必须在电脑上使用其他虚拟机（例如开发 Android 应用必须使用的模拟器），请不要使用 Hyper-V！ </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <br/> </p> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 开启 Hyper-V </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-4363-20171206211136409-1609350099.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 程序和功能 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-4368-20171206211345066-1430601107.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 启用或关闭Windows功能 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-9748-20171206211435534-1499766232.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> <span style=\"color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; font-size: 12px; background-color: rgb(255, 255, 255);\">选中Hyper-V</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668234-6433-20171206211858191-1177002365.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 也可以通过命令来启用 Hyper-V ，请右键开始菜单并以管理员身份运行 PowerShell，执行以下命令： </p> <pre class=\"prettyprint prettyprinted\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All</pre> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 安装 Docker Desktop for Windows </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 点击&nbsp;<a href=\"https://hub.docker.com/?overlay=onboarding\" rel=\"noopener noreferrer\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">Get started with Docker Desktop</a>，并下载 Windows 的版本，如果你还没有登录，会要求注册登录： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/5AEB69DA-6912-4B08-BE79-293FBE659894.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <h3 style=\"border: 0px; margin: 8px 0px; padding: 0px; font-size: 1.4em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> 运行安装文件 </h3> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 双击下载的 Docker for Windows Installer 安装文件，一路 Next，点击 Finish 完成安装。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513669129-6146-20171206214940331-1428569749.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513668903-9668-20171206220321613-1349447293.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 安装完成后，Docker 会自动启动。通知栏上会出现个小鲸鱼的图标<img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513582421-4552-whale-x-win.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/>，这表示 Docker 正在运行。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 桌边也会出现三个图标，如下图所示： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 我们可以在命令行执行 docker version 来查看版本号，docker run hello-world 来载入测试镜像测试。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 如果没启动，你可以在 Windows 搜索 Docker 来启动： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513585082-6751-docker-app-search.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 启动后，也可以在通知栏上看到小鲸鱼图标： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/1513585123-3777-whale-taskbar-circle.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <blockquote style=\"border: 0px; margin: 10px; padding: 10px; background-color: rgb(243, 247, 240); font-size: 13px; line-height: 2em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal;\"> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 4px; line-height: 1.5em; overflow-wrap: break-word; word-break: break-all; font-size: 14px; background-image: initial; background-position: initial; background-size: initial; background-repeat: initial; background-attachment: initial; background-origin: initial; background-clip: initial; font-style: italic;\"> 如果启动中遇到因 WSL 2 导致地错误，请安装&nbsp;<a href=\"https://docs.microsoft.com/zh-cn/windows/wsl/install-win10\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none; font-size: 13px;\">WSL 2</a>。 </p> </blockquote> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 安装之后，可以打开 PowerShell 并运行以下命令检测是否运行成功： </p> <pre class=\"prettyprint prettyprinted\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">docker run hello-world</pre> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 在成功运行之后应该会出现以下信息： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/EmkOezweLQVIwA1T__original.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <hr style=\"background-color: rgb(212, 212, 212); color: rgb(212, 212, 212); height: 1px; border: 0px; clear: both; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; font-size: 12px; white-space: normal;\"/> <h2 style=\"border: 0px; margin: 2px 0px; padding: 0px; font-size: 1.8em; line-height: 1.8em; color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; white-space: normal; background-color: rgb(255, 255, 255);\"> win7、win8 系统 </h2> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> win7、win8 等需要利用 docker toolbox 来安装，国内可以使用阿里云的镜像来下载，下载地址：<a href=\"http://mirrors.aliyun.com/docker-toolbox/windows/docker-toolbox/\" rel=\"noopener noreferrer\" target=\"_blank\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">http://mirrors.aliyun.com/docker-toolbox/windows/docker-toolbox/</a> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 安装比较简单，双击运行，点下一步即可，可以勾选自己需要的组件： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2016/05/691999-20180512142142130-1831870973.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> docker toolbox 是一个工具集，它主要包含以下一些内容： </p> <ul style=\"list-style-type: none;\" class=\" list-paddingleft-2\"> <li> <p> Docker CLI - 客户端，用来运行 docker 引擎创建镜像和容器。 </p> </li> <li> <p> Docker Machine - 可以让你在 Windows 的命令行中运行 docker 引擎命令。 </p> </li> <li> <p> Docker Compose - 用来运行 docker-compose 命令。 </p> </li> <li> <p> Kitematic - 这是 Docker 的 GUI 版本。 </p> </li> <li> <p> Docker QuickStart shell - 这是一个已经配置好Docker的命令行环境。 </p> </li> <li> <p> Oracle VM Virtualbox - 虚拟机。 </p> </li> </ul> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 下载完成之后直接点击安装，安装成功后，桌边会出现三个图标，如下图所示： </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2017/12/icon-set.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 点击 Docker QuickStart 图标来启动 Docker Toolbox 终端。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 如果系统显示 User Account Control 窗口来运行 VirtualBox 修改你的电脑，选择 Yes。 </p>", "dockerInstallMacos": "<p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <font face=\"arial, helvetica, sans-serif\"><span style=\"font-size: 24px;\">MacOS Docker安装</span></font> </p><p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 请点击以下链接下载&nbsp;<a href=\"https://download.docker.com/mac/stable/Docker.dmg\" target=\"_blank\" rel=\"noopener\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">Stable</a>&nbsp;或&nbsp;<a href=\"https://download.docker.com/mac/edge/Docker.dmg\" target=\"_blank\" rel=\"noopener\" style=\"border: 0px; margin: 0px; padding: 0px; color: rgb(0, 102, 0); transition-duration: 0.2s; transition-property: opacity; outline: none;\">Edge</a>&nbsp;版本的 Docker for Mac。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 如同 macOS 其它软件一样，安装也非常简单，双击下载的 .dmg 文件，然后将鲸鱼图标拖拽到 Application 文件夹即可。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1E045CE6-D504-4E7D-8C57-EEFB8AC83BF1.jpg\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p> <span style=\"color: rgb(51, 51, 51); font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; font-size: 12px; background-color: rgb(255, 255, 255);\">从应用中找到 Docker 图标并点击运行。可能会询问 macOS 的登陆密码，输入即可。</span> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480613-7638-docker-app-in-apps.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 点击顶部状态栏中的鲸鱼图标会弹出操作菜单。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480612-6026-whale-in-menu-bar.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480613-8590-menu.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 第一次点击图标，可能会看到这个安装成功的界面，点击 \"Got it!\" 可以关闭这个窗口。 </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> <img src=\"https://www.runoob.com/wp-content/uploads/2018/01/1515480614-7648-install-success-docker-cloud.png\" style=\"border: 0px; margin: 0px; padding: 0px; max-width: 100%; height: auto;\"/> </p> <p style=\"border: 0px; margin-top: 0px; margin-bottom: 0px; padding: 0px; line-height: 2em; overflow-wrap: break-word; word-break: break-all; font-size: 13px; font-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", \"Noto Sans CJK SC\", \"WenQuanYi Micro Hei\", Arial, sans-serif; color: rgb(51, 51, 51); white-space: normal; background-color: rgb(255, 255, 255);\"> 启动终端后，通过命令可以检查安装后的 Docker 版本。 </p> <pre class=\"prettyprint\" style=\"border-width: 1px 1px 1px 4px; border-style: solid; border-color: rgb(221, 221, 221); border-image: initial; margin: 15px auto; padding: 10px 15px; font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 12px; line-height: 20px; font-family: Menlo, Monaco, Consolas, \"Andale Mono\", \"lucida console\", \"Courier New\", monospace; white-space: pre-wrap; word-break: break-all; overflow-wrap: break-word; background-image: url(\"/images/codecolorer_bg.gif\"); background-position: center top; background-color: rgb(251, 251, 251); color: rgb(51, 51, 51);\">$ docker --version <br/>Docker version 17.09.1-ce, build 19e2cf6</pre> <p> <br/> </p>", "document": "文档", "downloadService": "下载服务", "downloadServiceContent": "<p> 在Powershell依次输入下面的命令，等待镜像下载完成。 </p> <p> docker pull theiaide/theia-full </p> <p> <br/> </p>", "downloadServiceContentMacos": "<p> 在终端工具依次输入下面的命令，等待镜像下载完成。 </p> <p> docker pull theiaide/theia-full</p> <p> <br/> </p>", "downloadServiceContentMacosMl": "<p> 在终端工具依次输入下面的命令，等待镜像下载完成。 </p> <p> docker pull linkerwang/amlserver:v1 </p> <p> docker pull linkerwang/amltool:v1 </p> <p> <br/> </p>", "downloadServiceContentMl": "<p> 在Powershell依次输入下面的命令，等待镜像下载完成。 </p> <p> docker pull linkerwang/amlserver:v1 </p> <p> docker pull linkerwang/amltool:v1 </p> <p> <br/> </p>", "installDocker": "Docker安装", "installIde": "下载工作区", "installMl": "下载机器学习", "macOSInstall": "MacOS", "name": "名称", "runAndUse": "运行并使用", "runAndUseContent": "<p> 在PowerShell输入下面的命令启动镜像 </p> <p> docker run -itd --init -p 30000:3000 -v \"/home/<USER>/home/<USER>\" -v \"/home/<USER>/.ssh:/home/<USER>/.ssh\" -v \"/home/<USER>/.ssh:/root/.ssh\" theiaide/theia-full </p> <p> chown -R 1000 /home/<USER>/p><p> <br/> </p><p>在浏览器输入http://127.0.0.1:30000打开工作区工具</p>", "runAndUseContentMacos": "<p> 在终端工具输入下面的命令启动镜像 </p> <p> docker run -itd --init -p 30000:3000 -v \"/home/<USER>/home/<USER>\" -v \"/home/<USER>/.ssh:/home/<USER>/.ssh\" -v \"/home/<USER>/.ssh:/root/.ssh\" theiaide/theia-full </p><p> chown -R 1000 /home/<USER>/p> <p> <br/> </p><p>在浏览器输入http://127.0.0.1:30000打开工作区工具</p>", "runAndUseContentMacosMl": "<p> 在终端工具输入下面的命令启动镜像 </p> <p> docker run -d -p 8084:8084 --name amlserver linkerwang/amlserver:v1 </p> <p> docker run -d -p 9000:80 --name amltool linkerwang/amltool:v1 </p> <p> <br/> </p><p>在浏览器输入http://127.0.0.1:9000打开机器学习工具</p>", "runAndUseContentMl": "<p> 在PowerShell输入下面的命令启动镜像 </p> <p> docker run -d -p 8084:8084 --name amlserver linkerwang/amlserver:v1 </p> <p> docker run -d -p 9000:80 --name amltool linkerwang/amltool:v1 </p> <p> <br/> </p><p>在浏览器输入http://127.0.0.1:9000打开机器学习工具</p>", "subtitle": "根据您电脑的操作系统选择下方对应的安装手册进行工作区本地服务的安装。", "subtitleMl": "根据您电脑的操作系统选择下方对应的安装手册进行机器学习本地服务的安装。", "title": "将工作区服务安装到您的本地电脑，体验更快更稳定的软件开发功能", "titleMl": "将机器学习服务安装到您的本地电脑，体验更快更稳定的机器学习功能", "verifyService": "验证服务", "verifyServiceContent": "<p> 在Powershell中输入命令\"docker images\"，验证镜像是否下载完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> theiaide/theia-full&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; latest&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; be7675928ad5&nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8.53GB </p> <p> <br/> </p>", "verifyServiceContentMacos": "<p> 在终端工具中输入命令\"docker images\"，验证镜像是否下载完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> theiaide/theia-full&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; latest&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; be7675928ad5&nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8.53GB </p> <p> <br/> </p>", "verifyServiceContentMacosMl": "<p> 在终端工具中输入命令\"docker images\"，验证镜像是否下载完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> linkerwang/amlserver&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9f2145f73a0a&nbsp; &nbsp; &nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.58GB </p> <p> linkerwang/amltool&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; v1&nbsp; &nbsp; &nbsp; &nbsp; <span style=\"white-space: pre;\">\t&nbsp;&nbsp;&nbsp;&nbsp;</span>23e31282671c&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp;&nbsp;&nbsp;3 minutes ago&nbsp; &nbsp;&nbsp;&nbsp; 148MB </p> <p> <br/> </p>", "verifyServiceContentMl": "<p> 在Powershell中输入命令\"docker images\"，验证镜像是否下载完成 </p> <p> root@ecs-b771:~# docker images </p> <p> REPOSITORY&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;TAG&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;IMAGE ID&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CREATED&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SIZE </p> <p> linkerwang/amlserver&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;v1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9f2145f73a0a&nbsp; &nbsp; &nbsp; &nbsp; 3 weeks ago&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.58GB </p> <p> linkerwang/amltool&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; v1&nbsp; &nbsp; &nbsp; &nbsp; <span style=\"white-space: pre;\">\t&nbsp;&nbsp;&nbsp;&nbsp;</span>23e31282671c&nbsp; &nbsp;<span style=\"white-space: pre;\"></span>&nbsp;&nbsp;&nbsp;3 minutes ago&nbsp; &nbsp;&nbsp;&nbsp; 148MB </p> <p> <br/> </p>", "video": "视频", "windowsInstall": "Windows"}, "financialDataCharacteristics": {"bankingServicesAndData": "银行服务和数据", "description1": "金融业务主要为客户提供全面、优质、安全的金融服务。客户永远是在金融系统和数据的中心。在提供服务给客户之前，金融机构必须从客户对客户有充分的理解，尽可能收集最多的信息。在获取客户信息后，金融机构将根据客户的需求建立不同的产品，然后客户可以开始进行交易。所有金融数据都是按照这种层次结构来建造的，并保存在高度安全的环境中，以备将来的产品销售和服务。", "description2": "SIMNECTZ的沙盒具有相同的数据结构，并具有客户、产品和交易数据的层次结构。", "description3": "了解金融数据的特征和结构至关重要。在新创建金融数据的时候，必须先创建客户数据，然后是产品和交易数据。", "description4": "在一般商业银行中，核心银行是商业银行业务的核心，它为所有外围系统/服务（如信用卡、贷款等）提供交易，财务会计和账本服务等。", "description5": "在之前的数字化转型过程中，客户数据已经通过清理和重组，形成了关于客户的“单一事实”数据信息。这综合客户数据信息用于通过标准数据访问API接口为所有外围系统/服务提供服务。", "description6": "除了账本和客户数据外，所有交易数据都与各自的系统/服务一起产生和存储。"}, "foreignExchangeCreation": {"accountNumber": "账户号码", "beforeRemoveMessage": "确认删除 {fileName}？", "channel": "渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadAccountTemplate": "下载模板文件", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "频次", "frequencyRatePlaceholder": "请输入频次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上传账户", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "settlementAccount": "结算账户", "settlementAccountPlaceholder": "请输入结算账户", "tableName": "表格名称", "tableNamePlaceholder": "请输入表格名称", "totalTransaction": "最大交易次数", "totalTransactionPlaceholder": "请输入最大交易次数", "transactionAccount": "交易账户", "transactionAccountPlaceholder": "请输入交易账户", "transactionAccountType": "交易账户类型", "transactionAmountFrom": "交易金额（港币）起", "transactionAmountFromPlaceholder": "请输入交易金额起", "transactionAmountTo": "至", "transactionAmountToPlaceholder": "请输入交易金额至", "transactionCcy": "货币类型", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transactionType": "交易类型", "upload": "上传", "uploadFileRule": "仅上传 csv/xls/xlsx 文件，不超过 5MB"}, "foreignExchangeEnquiry": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "dataDetails": "数据详情", "exchangeAmoutInForeignCurrency": "以外币兑换金额", "exchangeAmoutInLocalCurrency": "交易金额（HKD）", "exchangeRate": "汇率", "foreignCurrency": "外币", "fromCreateDate": "起始创建时间", "localCurrency": "本地货币", "postBalInForeignCurrencyAccount": "外币账户过账余额", "prevBalInForeignCurrencyAccount": "外币账户上期余额", "tableName": "数据表名称", "tableNamePlaceholder": "请选择数据表名称", "toCreateDate": "结束创建时间", "transactionTime": "交易时间", "transactionType": "交易类型"}, "foreignExchangeUpdate": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "exchangeAmoutInForeignCurrency": "外币兑换金额", "exchangeAmoutInForeignCurrencyPlaceholder": "请输入外币兑换金额", "exchangeAmoutInLocalCurrency": "本币兑换金额", "exchangeAmoutInLocalCurrencyPlaceholder": "请输入本币兑换金额", "exchangeRate": "汇率", "exchangeRatePlaceholder": "请输入汇率", "foreignCurrency": "外币", "foreignCurrencyPlaceholder": "请输入外币", "localCurrency": "本地货币", "localCurrencyPlaceholder": "请输入本地货币", "postBalInForeignCurrencyAccount": "外币账户过账余额", "postBalInForeignCurrencyAccountPlaceholder": "请输入外币账户过账余额", "prevBalInForeignCurrencyAccount": "外币账户上一笔余额", "prevBalInForeignCurrencyAccountPlaceholder": "请输入外币账户上一笔余额", "title": "编辑外汇数据", "transactionTime": "交易时间", "transactionTimePlaceholder": "请选择交易时间", "transactionType": "交易类型", "transactionTypePlaceholder": "请输入交易类型"}, "foreignExchangeView": {"title": "查看外汇数据"}, "fundCreation": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入交易账号", "add": "添加", "amountFrom": "起始金额", "amountFromPlaceholder": "请输入起始金额", "amountTo": "至", "amountToPlaceholder": "请输入截至金额", "batchUploadFundCode": "批量上传基金代码", "beforeRemoveMessage": "确认删除 {fileName}？", "buy": "买入", "channel": "渠道", "channelPlaceholder": "请选择渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请选择数据源", "downloadAccountTemplate": "下载账户模板", "fileSizeAndTypeTip": "仅支持上传 csv/xls/xlsx 文件，不超过 5MB。", "frequency": "频率", "frequencyPlaceholder": "请选择频率", "frequencyRate": "频次", "frequencyRatePlaceholder": "请选择频次", "fromDate": "开始日期", "fromDatePlaceholder": "请选择开始日期", "fundCode": "基金代码", "fundCodeList": "可用的基金代码", "fundCodeTemplate": "基金代码模板", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上传账户", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "sell": "卖出", "settlementAccount": "结算账户", "shareFrom": "起始份额", "shareFromPlaceholder": "请输入起始份额", "shareToPlaceholder": "请输入截至份额", "toDate": "结束日期", "toDatePlaceholder": "请选择结束日期", "totalPlaceholder": "请输入最大交易次数", "totalTransaction": "最大交易次数", "transactionAccount": "交易账号", "transactionAccountType": "交易账户类型", "transactionDate": "交易日期", "transactionDetails": "交易详情", "transactionType": "交易类型"}, "fundEnquiry": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "dataDetails": "数据详情", "fromCreateDate": "起始创建时间", "fromTransactionAmount": "交易金额", "fromTransactionAmountPlaceholder": "请输入交易金额", "fundCcy": "基金货币", "fundCode": "基金代码", "fundPrice": "基金价格", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "riskRating": "风险评级", "sharingNo": "持仓份额", "tableName": "数据表名称", "tableNamePlaceholder": "请选择数据表名称", "toCreateDate": "结束创建时间", "toTransactionAmount": "至", "toTransactionAmountPlaceholder": "请输入交易金额", "tradingAmount": "基金交易额", "tradingOption": "交易类型", "transactionAmount": "交易总金额 (HKD)", "transactionDate": "交易日期", "trdingCommission": "交易手续费"}, "fundUpdate": {"accountNumber": "账户号码", "accountNumberPlaceholder": "请输入账户号码", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "fundCcy": "基金货币类型", "fundCcyPlaceholder": "请输入基金账户", "fundCode": "基金代码", "fundCodePlaceholder": "请输入基金代码", "fundPrice": "基金价格", "fundPricePlaceholder": "请输入基金价格", "riskRating": "风险评级", "riskRatingPlaceholder": "请输入风险评级", "sharingNo": "交易份额", "sharingNoPlaceholder": "请输入交易份额", "title": "编辑基金数据", "tradingAmount": "基金交易额", "tradingAmountPlaceholder": "请输入基金交易额", "tradingOption": "交易选项", "tradingOptionPlaceholder": "请输入交易选项", "transactionAmount": "交易总金额 (HKD)", "transactionAmountPlaceholder": "请输入交易总金额", "transactionDate": "交易日期", "transactionDatePlaceholder": "请输入交易日期", "trdingCommission": "交易佣金", "trdingCommissionPlaceholder": "请输入交易佣金"}, "fundView": {"title": "查看基金数据"}, "homework": {"answerQuestion": "回答试题", "deleteQuestionPrompt": "该操作将永久删除该试题。您要继续吗?", "editQuestion": "编辑试题", "homework": "作业", "homeworkList": "作业列表", "keywordsPlaceholder": "试题内容", "myHomework": "我的作业", "questionContent": "试题内容", "questionDetail": "试题详情", "questionModelanswer": "试题标准答案", "studentAnswer": "学生答案", "studentAnswerIsRequired": "学生答案不能为空"}, "loginLog": {"browser": "浏览器", "email": "用户邮箱", "failed": "失败", "loginIp": "登录IP", "loginLocation": "登录地点", "loginStatus": "登录状态", "loginTime": "登录时间", "message": "操作信息", "os": "操作系统", "success": "成功"}, "nodeManagement": {"add": "添加", "addFlow": "新增节点", "addTip": "你确定要添加吗？", "addWorkflow": "添加规则", "apiHeader": "api_header", "apiMethod": "api_method", "apiParams": "api_params", "apiUrl": "api_url", "canNotEmptyTip": "该字段不能为空", "deleteTip": "你确定要删除吗？", "editFlow": "编辑节点", "enterApiHeaderTip": "请输入 api_header", "enterApiMethodTip": "请输入 api_method", "enterApiParamsTip": "请输入 api_params", "enterApiUrlTip": "请输入 api_url", "enterGroupTip": "请输入节点分组", "enterNameTip": "请输入节点名称", "group": "分组", "json": "Json", "lastUpdateDate": "更新时间", "name": "名称", "operatingSuccess": "操作成功", "save": "保存", "update": "更新", "updateTip": "你确定要更新吗？", "updateWorkflow": "更新规则", "workflowManage": "规则管理"}, "obtainDeveloperToken": {"accessTokenTip": "输入access_token后点击链接地址", "addClient": "添加客户端", "addClientTip": "请先创建一个客户端，然后使用创建的客户端生成您自己的令牌。", "archive": "注销", "authorizationCodeTip": "输入每一步必要的信息后点击其下面的链接地址", "authorizationCodeTip1": "从认证服务器获取 'code'", "authorizationCodeTip2": "用 'code' 换取 'access_token'", "authorizationCodeTip3": "输入第一步获取的code", "authorizedGrantTypesTip": "至少勾选一项授权方式，且不能只单独勾选refresh_token", "cancelled": "已注销", "client": "客户端", "clientCredentialsTip": "点击链接地址即可测试", "clientId": "客户端ID", "clientIdTip": "客户端ID必须输入，且必须唯一，长度至少5位", "createNow": "现在创建", "createSuccess": "创建成功", "developerToken": "开发者令牌", "editClient": "编辑客户端", "email": "电子邮箱", "generateDeveloperTokenSuccess": "生成开发者令牌成功", "generateToken": "生成令牌", "grantTypes": "授权方式", "inEffect": "生效中", "password": "密码", "passwordTip": "输入平台的email，password后点击链接地址", "redirectUri": "回调地址", "redirectUriIsRequired": "请输入回调地址", "refreshTokenTip": "输入refresh_token后点击链接地址", "secret": "客户端密码", "secretTip": "客户端密码必须输入,且长度至少8位", "status": "状态", "test": "测试客户端", "testTip": "针对不同的grant_type提供不同的测试URL", "trusted": "可信的", "trustedTip": "只适用于授权方式包括authorization_code的情况，当用户登录成功后，若选No，则会跳转到让用户Approve的页面让用户同意授权，若选Yes，则在登录后不需要再让用户Approve同意授权(因为是受信任的)。", "webServerRedirectUriTip": "若授权方式包括authorization_code，则必须输入回调地址"}, "organization": {"contactEmail": "联系电子邮箱", "contactEmailIsRequired": "企业联系电子邮件不能为空", "contactEmailPlaceholder": "请输入企业联系电子邮箱", "contactPerson": "企业联系人", "contactPersonIsRequired": "企业联系人不能为空", "contactPersonPlaceholder": "请输入企业联系人", "contactPhoneNumber": "联系手机号码", "contactPhoneNumberIsRequired": "企业联系电话不能为空", "contactPhoneNumberPlaceholder": "请输入联系手机号码", "createOrganization": "创建企业", "deleteOrganizationPrompt": "此操作将永久删除企业，是否继续？", "editOrganization": "编辑企业", "invalidContactPersonFormat": "无效的联系人格式，长度为2-64个字符", "invalidEmailFormat": "无效的电子邮箱格式", "invalidPhoneNumberFormat": "无效的手机号码格式", "keywordsPlaceholder": "企业名称", "organizationAbbreviationCode": "企业简写代码", "organizationAbbreviationCodeIsRequired": "企业简写代码不能为空", "organizationAbbreviationCodePlaceholder": "请输入企业简写代码", "organizationCode": "企业代码", "organizationCodePlaceholder": "请输入企业代码", "organizationEntityAddress": "企业实体地址", "organizationEntityAddressPlaceholder": "请输入企业实体地址", "organizationId": "企业ID", "organizationName": "企业名称", "organizationNameIsRequired": "企业名称不能为空", "organizationNamePlaceholder": "请输入企业名称", "organizationNature": "经营范围", "organizationNaturePlaceholder": "请输入企业经营范围", "organizationTaxCode": "企业税务代码", "organizationTaxCodePlaceholder": "请输入企业税务代码"}, "paymentCreation": {"accountNumber": "账户号码", "accountTypeFrom": "账户类型", "accountTypeFromPlaceholder": "请输入账户类型来自", "amountFrom": "起", "amountFromPlaceholder": "请输入转账金额起", "amountTo": "至", "amountToPlaceholder": "请输入转账金额至", "beforeRemoveMessage": "是否确认移除{fileName}？", "channel": "渠道", "channelPlaceholder": "请输入渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadBranchTemplate": "下载模板文件", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "频次", "frequencyRatePlaceholder": "请输入频次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，总共选择{totallyChoose}个文件", "industry": "行业", "industryPlaceholder": "请选择行业", "linkToDatatable": "批量上传账户", "moveMoneyFrom": "支付账户", "moveMoneyFromPlaceholder": "请输入支付账户", "moveMoneyFromType": "转账来源类型", "moveMoneyTo": "支付至", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "tableName": "表名称", "tableNamePlaceholder": "请输入表名称", "totalTransaction": "最大交易次数", "totalTransactionPlaceholder": "请输入最大交易次数", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "upload": "上传", "uploadFileRule": "仅上传csv/xls/xlsx文件，不得超过5MB"}, "paymentEnquiry": {"createDate": "创建日期", "customerAccountNumber": "账户号码", "customerAccountNumberPlaceholder": "请输入账号", "customerAccountType": "账户类型", "fromCreateDate": "创建起始日期", "fromTransactionAmount": "交易金额", "fromTransactionAmountPlaceholder": "请输入交易金额", "lastUpdateDate": "最后更新日期", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "payeeCategory": "收款人类别", "payeeCategoryID": "收款人类别ID", "payeeID": "收款人ID", "remarks": "备注", "toCreateDate": "创建截至日期", "toTransactionAmount": "至", "toTransactionAmountPlaceholder": "请输入交易金额", "transactionAmount": "交易金额", "transactionCurrency": "交易货币", "transactionDealNumber": "交易编号", "transactionTime": "交易时间"}, "paymentUpdate": {"createDate": "创建日期", "createDatePlaceholder": "请选择创建日期", "customerAccountNumber": "账户号码", "customerAccountNumberPlaceholder": "请输入账户号码", "customerAccountType": "账户类型", "customerAccountTypePlaceholder": "请输入账户类型", "customerNumber": "客户编码", "customerNumberPlaceholder": "请输入客户编码", "lastUpdateDate": "最后更新日期", "lastUpdateDatePlaceholder": "请选择最后更新日期", "payeeCategory": "收款人类别", "payeeCategoryID": "收款人类别ID", "payeeCategoryIDPlaceholder": "请输入收款人类别ID", "payeeCategoryPlaceholder": "请输入收款人类别", "payeeID": "收款人ID", "payeeIDPlaceholder": "请输入收款人ID", "payeeNumber": "收款人号码", "payeeNumberPlaceholder": "请输入收款人号码", "paymentEffectiveDay": "付款生效日", "paymentEffectiveDayPlaceholder": "请输入付款生效日", "remarks": "备注", "remarksPlaceholder": "请输入备注", "sandBoxId": "沙盒ID", "sandBoxIdPlaceholder": "请输入沙盒ID", "status": "状态", "statusPlaceholder": "请输入状态", "title": "编辑支付数据", "transactionAmount": "交易金额", "transactionAmountPlaceholder": "请输入交易金额", "transactionCurrency": "交易币种", "transactionCurrencyPlaceholder": "请输入交易币种", "transactionDealNumber": "交易编号", "transactionDealNumberPlaceholder": "请输入交易交易号码", "transactionTime": "交易时间", "transactionTimePlaceholder": "请输入交易时间"}, "paymentView": {"title": "查看支付数据"}, "permission": {"accredit": "权限标识", "accreditIsRequired": "权限标识不能为空", "accreditPlaceholder": "请输入权限标识", "createPermission": "创建权限", "deletePermissionPrompt": "此操作将永久删除权限，是否继续？", "editPermission": "编辑权限", "keywordsPlaceholder": "权限名称", "parentId": "上级权限", "parentIdIsRequired": "上级权限不能为空", "parentIdPlaceholder": "请选择上级权限", "permissionName": "权限名称", "permissionNameIsRequired": "权限名称不能为空", "permissionNamePlaceholder": "请输入权限名称", "sortNumber": "排序编号", "sortNumberIsRequired": "排序编号不能为空", "sortNumberPlaceholder": "请输入排序编号"}, "practicalPlatformIntroduction": {"description1": "SOI+数字金融实践平台旨在通过实践练习培养复合型金融科技人才。该平台提供了一个全面的培训环境，涵盖了广泛的技术和金融业务知识和用例。", "description2": "该平台由不同的组件设计和构建，以方便业务和技术背景用户的使用。他们是：", "description3": "该平台根据不同的业务功能提供了一系列开放API。开放API的背后是模拟的虚拟银行系统、具有众多金融业务逻辑的保险系统。所有原料药均按照香港金融管理局制定的指引制定。API中的参数基于国际标准ISO20022。", "description4": "孪生金融数据沙盒提供了超过1亿套完整的金融数据（包括来自国内外银行的交易数据）。所有数据均由自主开发的数据生成引擎生成。合成数据避免了数据隐私等合法性问题。", "description5": "通过不同的工具和编程方法提供多种数据分析功能，如数据处理、自动机器学习、数据可视化、数据导入、数据生成和数据查询。", "description6": "该平台为不同场景提供金融模拟系统，全面培养学生在不同金融领域的学习和实践能力。每个系统都包含大量的业务流程。这些系统可以有效地辅助大学教学，真正将理论与实践融入金融科技教学。", "subtitle1": "API 市场", "subtitle2": "孪生金融数据沙箱", "subtitle3": "数据分析", "subtitle4": "金融模拟系统", "title": "平台介绍"}, "question": {"courseCategoryIsRequired": "课程大分类为必填项", "courseCategoryPlaceholder": "请选择课程大分类", "courseNameIsRequired": "课程名称为必填项", "courseNamePlaceholder": "请选择课程名称", "createQuestion": "创建试题", "deleteQuestionPrompt": "此操作将永久删除试题。请是否继续？", "editQuestion": "编辑试题", "keywordsPlaceholder": "试题内容", "modelAnswer": "标准答案", "modelAnswerIsRequired": "标准答案为必填项", "modelAnswerPlaceholder": "请输入标准答案", "questionContent": "试题内容", "questionContentIsRequired": "试题内容为必填项", "questionContentPlaceholder": "请输入试题内容", "subjectNameIsRequired": "课题名称为必填项", "subjectNamePlaceholder": "请选择课题名称"}, "role": {"createRole": "创建角色", "deleteRolePrompt": "此操作将永久删除角色，是否继续？", "editRole": "编辑角色", "keywordsPlaceholder": "角色名称", "permission": "权限", "permissionIsRequired": "权限不能为空", "permissionPlaceholder": "请选择权限", "remark": "备注", "remarkPlaceholder": "请输入备注信息", "roleName": "角色名称", "roleNameIsRequired": "角色名称不能为空", "roleNamePlaceholder": "请输入角色名称"}, "router": {"404": 404, "addApi": "新增API", "api": "API", "apiAccessGuide": "API 访问指南", "apiArchitectureDesign": "API 架构设计", "apiCatalogue": "API 目录", "apiCategoryManagement": "API 分类管理", "apiList": "API 列表", "apiManagement": "API 管理", "apiMarketPlace": "API 市场", "apiSubcategoryManagement": "API 子分类管理", "apiUsageGuide": "API 使用指南", "apis": "Apis", "applyCustomerDataToken": "申请客户数据/令牌", "autoMachineLearning": "自动化机器学习", "codingPractice": "编码练习", "codingQuestionManagement": "编码题管理", "course": "课程", "courseBusiness": "业务", "courseBusinessCommercialBanking": "商业银行", "courseBusinessRetailBanking": "零售银行", "courseBusinessRetailBankingBusinessRisk": "商业风险", "courseBusinessRetailBankingCreditCard": "信用卡", "courseBusinessRetailBankingCreditRisk": "信用风险", "courseBusinessRetailBankingFex": "外汇业务", "courseBusinessRetailBankingFraudRiskManagement": "欺诈风险管理", "courseBusinessRetailBankingLoan": "贷款", "courseBusinessRetailBankingMarketRisk": "市场风险", "courseBusinessRetailBankingOperationRisk": "操作风险", "courseBusinessRetailBankingRegulatoryRisk": "监管风险", "courseBusinessRetailBankingRiskManagementPrinciple": "风险管理原则", "courseBusinessRetailBankingTermDeposit": "定期存款", "courseBusinessRiskManagement": "风险管理", "courseBusinessSupplyChainFinance": "供应链金融", "courseCategory": "课程分类", "courseIndustryTalk": "行业讨论", "courseIntroduce": "课程介绍", "courseLearning": "课程学习", "courseLearningManagement": "课程学习管理", "coursePartipate": "参与", "courseRecommend": "推荐", "courseTechnology": "技术", "courseTechnologyBlockchain": "区块链", "courseTechnologyDataAnalysis": "数据分析", "courseTechnologyProgrammingLanguage": "编程语言", "courseTransfer": "课程学习中转页", "creditCardDataCreation": "信用卡数据生成", "creditCardDataEnquiry": "信用卡数据查询", "crossBorderPayment": "跨境支付", "customApi": "自定义 API", "customerDataCreation": "客户数据生成", "customerDataEnquiry": "客户数据查询", "dashboard": "仪表盘", "dataAnalysis": "数据分析", "dataAnalysisUseCase": "数据分析案例", "dataAnalysisUseCaseList": "数据分析案例列表", "dataAnalysisUseCaseSubcategory": "数据分析案例分类", "dataCreation": "数据生成", "dataDictionary": "数据字典", "dataDictionaryDetails": "数据字典详情", "dataDictionarySubcategory": "数据字典分类", "dataEnquiry": "数据查询", "dataImport": "数据导入", "dataPreparation": "数据处理", "dataSource": "数据源", "dataTable": "数据表", "dataVisualization": "数据可视化", "depositDataCreation": "存款数据生成", "documentCenter": "文档中心", "documentList": "文档列表", "documentManagement": "文档管理", "domainPracticals": "实践练习", "domainPracticalsBusiness": "业务", "domainPracticalsBusinessCommercialBanking": "商业银行", "domainPracticalsBusinessFraudRiskManagement": "欺诈风险管理", "domainPracticalsBusinessOperationalRisk": "操作风险", "domainPracticalsBusinessRegulatoryRisk": "监管风险", "domainPracticalsBusinessRetailBanking": "零售银行", "domainPracticalsBusinessRiskManagement": "风险管理", "domainPracticalsTechnology": "技术", "domainPracticalsTechnologyBlockchain": "区块链", "domainPracticalsTechnologyDataAnalysis": "数据分析", "domainPracticalsTechnologyProgrammingLanguage": "编程语言", "downloadCenter": "下载中心", "eKYCWorkflowDetails": "eKYC工作流详情", "editApi": "编辑API", "explore": "探索", "financialDataCharacteristics": "金融数据特征", "foreignExchangeDataCreation": "外汇数据生成", "foreignExchangeDataEnquiry": "外汇数据查询", "fraudDetectionCenter": "银行反欺诈系统", "fundDataEnquiry": "基金数据查询", "fundTradingDataCreation": "基金交易数据生成", "homework": "作业", "importApi": "导入API", "insuranceClaims": "保险理赔", "insuranceWorkflowDetails": "保险理赔工作流详情", "jupyter": "<PERSON><PERSON><PERSON>", "kyc": "KYC", "login": "登录", "loginLog": "登录日志", "mBridge": "数字货币桥", "manageDocument": "管理文档", "manageEKYCWorkflow": "管理eKYC工作流", "manageInsuranceWorkflow": "管理保险理赔工作流", "myCharts": "我的图表", "myPath": "学习路径", "nodeManagement": "节点管理", "obtainDeveloperToken": "获取开发者令牌", "organizationManagement": "企业管理", "paymentDataCreation": "支付数据生成", "paymentDataEnquiry": "支付数据查询", "permissionManagement": "权限管理", "practiceManagement": "实践平台管理", "practicePlatformIntroduce": "平台介绍", "practiceTransfer": "实践平台中转页", "privacyPolicy": "隐私策略", "productDataCreation": "账户数据生成", "publicNodeRules": "公共Node规则", "question": "试题", "register": "注册", "resetPassword": "重置密码", "roleManagement": "角色管理", "ruleManagement": "规则管理", "sandboxManagement": "Sandbox管理", "sandboxUsageGuide": "沙箱使用指南", "sendEmail": "发送邮件", "simulatedSystems": "模拟应用", "stockDataEnquiry": "股票数据查询", "stockTradingDataCreation": "股票交易数据生成", "subject": "课题", "supplyChainFinance": "供应链金融", "systemManagement": "系统管理", "tcManagement": "T&C管理", "technicalDevelopment": "技术开发", "termDepositDataCreation": "定期存款数据生成", "termDepositDataEnquiry": "定期存款数据查询", "tokenAccessGuide": "令牌访问指南", "transfer": "中转页", "transferDataCreation": "转账数据生成", "transferDataEnquiry": "转账数据查询", "tryoutApi": "试用 API", "userGuide": "用户手册", "userManagement": "用户管理", "virtualBankSystem": "虚拟银行系统", "virtualCreditCardSystem": "虚拟信用卡系统", "virtualEWallet": "虚拟电子钱包", "virtualInsuranceSystem": "虚拟保险系统", "withdrawalDataCreation": "取款数据生成", "workflowConfig": "工作流配置", "workflowList": "工作流列表", "workspace": "工作空间"}, "ruleManagement": {"add": "添加", "addRule": "添加规则", "addTip": "你确定要添加吗？", "canNotEmptyTip": "该字段不能为空", "deleteTip": "你确定要删除吗？", "enterNameTip": "请输入规则名称", "name": "名称", "operatingSuccess": "操作成功", "pleaseSelectNodeGroup": "请选择节点分组", "ruleManagement": "请选择节点分组", "updateDate": "更新时间", "updateRule": "编辑规则", "updateTip": "你确定要更新吗？"}, "sandboxManagement": {"customerNumber": "客户账号", "deleteInside": "删除用户内的沙箱数据", "deleteOut": "删除用户沙箱数据", "deleteSandboxDataTip": "未选择任何沙箱数据", "deleteTip": "你确定要删除吗？", "deleteUserTip": "未选择任何学生", "email": "电子邮箱", "loginName": "登录名称", "loginPassword": "登录密码", "sandboxId": "沙箱ID", "studentUser": "学生用户"}, "sandboxUsageGuide": {"description1": "每个成功注册到平台的用户将被分配一个数据沙箱。一组预定义的金融数据记录，包括客户人口统计、账户信息和交易数据，将分配给每个数据沙箱。用户可以在平台上的模拟应用程序和FDC（欺诈检测中心）中使用这些预先分配的数据记录。为了避免重叠，每个数据沙箱中的数据记录不会跨越不同的用户（即每个用户的沙箱数据都是独立的一套）。", "description2": "在初始设置中，每个数据沙箱携带5组预定义的客户数据。用户可以通过应用客户令牌功能请求最多10组的其他客户数据。", "description3": "除了通过模拟应用程序访问客户数据之外，用户还可以编写自己的代码，通过API调用访问客户数据。此客户令牌表示客户已授权访问其数据。每个客户数据记录都有其唯一的令牌。客户令牌必须嵌入到每个API访问调用中。您可以通过“申请客户令牌” 功能申请客户令牌。"}, "simulatedSystems": {"adminList": "管理员账户列表", "bankList": "银行账户列表", "cnVbsAccount": "中国虚拟银行账号", "customerList": "客户账号列表", "downloadEWalletTip": "点击下载我们的电子钱包演示程序。", "hkVbsAccount": "香港虚拟银行账号", "hkVcsAccount": "虚拟信用卡账号", "login": "登录", "loginName": "登录名", "loginPassword": "登录密码", "role": "角色", "selectSupplyChainFinanceTip": "请选择以下分配的沙箱数据记录以登录到供应链金融系统：", "selectVbsCustomerTip": "请选择以下分配的沙箱数据记录以登录到虚拟银行系统：", "selectVcsCustomerTip": "请选择以下分配的沙箱数据记录以登录到虚拟信用卡系统：", "selectVisCustomerTip": "请选择以下分配的沙箱数据记录以登录到虚拟保险系统：", "staffList": "员工账号列表", "systemManagerList": "系统管理员账号列表", "systemOperatorList": "系统运维账号列表", "thVbsAccount": "泰国虚拟银行账号", "uaeVbsAccount": "阿联酋虚拟银行账号", "username": "用户名"}, "stockCreation": {"accountNumber": "账户号码", "batchUploadStockCode": "批量上传股票代码", "beforeRemoveMessage": "确认删除 {fileName}？", "channel": "渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadAccountTemplate": "下载账户模板", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "频次", "frequencyRatePlaceholder": "请输入频次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上传账户", "noOfLots": "手数", "noOfLotsPlaceholder": "请输入手数", "orderType": "订单类型", "settlementAccount": "结算账户", "settlementAccountPlaceholder": "请输入结算账户", "stockCode": "股票代码", "stockCodeList": "可用的股票代码", "stockCodeTemplate": "股票代码模板", "tableName": "表格名称", "tableNamePlaceholder": "请输入表格名称", "totalTransaction": "最大交易次数", "totalTransactionPlaceholder": "请输入最大交易次数", "transactionAccount": "交易账户", "transactionAccountPlaceholder": "请输入交易账户", "transactionAccountType": "交易账户类型", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transactionDetails": "交易详情", "transactionType": "交易类型", "upload": "上传", "uploadFileRule": "仅上传 csv/xls/xlsx 文件，不超过 5MB"}, "stockEnquiry": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "createDate": "创建日期", "custodyCharges": "托管费用 (HKD)", "fromCreateDate": "从创建日期", "fromTransactionAmount": "交易金额", "fromTransactionAmountPlaceholder": "请输入交易金额", "lotSize": "每手股数", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "riskRating": "风险评级", "sharingNo": "交易份额", "stockNumber": "股票代码", "stockPrice": "股票价格 (HKD)", "stockTrdingAmount": "股票交易额 (HKD)", "stockTrdingCommission": "股票交易佣金 (HKD)", "toCreateDate": "到创建日期", "toTransactionAmount": "至", "toTransactionAmountPlaceholder": "请输入交易金额", "tradingOption": "交易类型", "transactionAmount": "交易总金额 (HKD)", "transactionDate": "交易日期", "transactionDesc": "交易描述"}, "stockUpdate": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "custodyCharges": "托管费用 (HKD)", "custodyChargesPlaceholder": "请输入托管费用", "lotSize": "每手股数", "lotSizePlaceholder": "请输入每手股数", "riskRating": "风险评级", "riskRatingPlaceholder": "请输入风险评级", "sharingNo": "交易份额", "sharingNoPlaceholder": "请输入交易份额", "stockNumber": "股票编号", "stockNumberPlaceholder": "请输入股票编号", "stockPrice": "股票价格 (HKD)", "stockPricePlaceholder": "请输入股票价格", "stockTrdingAmount": "股票交易金额 (HKD)", "stockTrdingAmountPlaceholder": "请输入股票交易金额", "stockTrdingCommission": "股票交易佣金 (HKD)", "stockTrdingCommissionPlaceholder": "请输入股票交易佣金", "title": "编辑股票数据", "tradingOption": "交易选项", "tradingOptionPlaceholder": "请输入交易选项", "transactionAmount": "交易金额 (HKD)", "transactionAmountPlaceholder": "请输入交易金额", "transactionDate": "交易日期", "transactionDatePlaceholder": "请输入交易日期", "transactionDesc": "交易描述", "transactionDescPlaceholder": "请输入交易描述"}, "stockView": {"title": "查看股票数据"}, "subject": {"assignment": "作业", "assignmentUpload": "作业上传", "basic": "基础", "business": "业务", "category": "类型", "categoryIsRequired": "类型为必填项", "categoryPlaceholder": "请选择类型", "courseHoursIsRequired": "课题时长为必填项", "courseName": "所属课程", "courseNameIsRequired": "课程名称为必填项", "courseNamePlaceholder": "请选择课程名称", "createSubject": "创建课题", "deleteSubjectPrompt": "此操作将永久删除课题。是否要继续？", "description": "描述", "descriptionIsRequired": "描述为必填项", "descriptionPlaceholder": "请输入描述", "editSubject": "编辑课题", "high": "高", "keywordsPlaceholder": "课题名称", "level": "级别", "levelIsRequired": "级别为必填项", "levelPlaceholder": "请选择级别", "medium": "中", "objective": "目标", "objectiveIsRequired": "目标为必填项", "objectivePlaceholder": "请输入目标", "picture": "图片", "pictureUpload": "图片上传", "pictureUploadIsRequired": "图片上传为必填项", "presentationMaterial": "演示材料", "presentationMaterialUpload": "演示材料上传", "subjectCode": "课题代码", "subjectCodeIsRequired": "课题代码为必填项", "subjectCodePlaceholder": "请输入课题代码", "subjectDetail": "课题详细信息", "subjectHours": "课题时长（分钟）", "subjectHoursPlaceholder": "请输入课题时长", "subjectName": "课题名称", "subjectNameIsRequired": "课题名称为必填项", "subjectNamePlaceholder": "请输入课题名称", "technical": "技术", "video": "视频", "videoLinkUpload": "视频链接上传"}, "termDepositCreation": {"accountNumber": "账户号码", "amountFrom": "定存金额起", "amountFromPlaceholder": "请输入金额起", "amountTo": "至", "amountToPlaceholder": "请输入金额至", "beforeRemoveMessage": "确认删除 {fileName}？", "channel": "渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadAccountTemplate": "下载模板文件", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "频次", "frequencyRatePlaceholder": "请输入频次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，全部选择{totallyChoose}个文件", "linkToDatatable": "批量上传账户", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "period": "定存期限", "settlementAccount": "结算账户", "settlementAccountPlaceholder": "请输入结算账户", "tableName": "表格名称", "tableNamePlaceholder": "请输入表格名称", "termDepositAmount": "定存金额", "totalTransaction": "最大交易次数", "totalTransactionPlaceholder": "请输入最大交易次数", "transactionAccount": "交易账户", "transactionAccountPlaceholder": "请输入交易账户", "transactionAccountType": "交易账户类型", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transactionDetails": "交易详情", "upload": "上传", "uploadFileRule": "仅上传 csv/xls/xlsx 文件，不超过 5MB"}, "termDepositEnquiry": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "createDate": "创建日期", "depositAmount": "存款金额", "depositNumber": "存款编号", "depositNumberPlaceholder": "请输入存款编号", "fromCreateDate": "创建起始日期", "fromTransactionAmount": "交易金额", "fromTransactionAmountPlaceholder": "请输入交易金额", "maturityAmount": "到期金额", "maturityDate": "到期日", "maturityInterest": "到期利息", "maturityStatus": "到期状态", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "systemDate": "系统日期", "termInterestRate": "存款利率", "termPeriod": "存款期限", "toCreateDate": "创建截至日期", "toTransactionAmount": "至", "toTransactionAmountPlaceholder": "请输入交易金额"}, "termDepositUpdate": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "createDate": "创建日期", "createDatePlaceholder": "请选择创建日期", "depositAmount": "存款金额", "depositAmountPlaceholder": "请输入存款金额", "depositNumber": "存款编号", "depositNumberPlaceholder": "请输入存款编号", "lastUpdatedDate": "上次更新日期", "lastUpdatedDatePlaceholder": "请选择上次更新日期", "maturityAmount": "到期金额", "maturityAmountPlaceholder": "请输入到期金额", "maturityDate": "到期日", "maturityDatePlaceholder": "请输入到期日", "maturityInterest": "到期利息", "maturityInterestPlaceholder": "请输入到期利息", "maturityStatus": "到期状态", "maturityStatusPlaceholder": "请输入到期状态", "sandBoxId": "沙箱ID", "sandBoxIdPlaceholder": "请输入沙箱ID", "systemDate": "系统日期", "systemDatePlaceholder": "请选择系统日期", "termInterestRate": "存款利率", "termInterestRatePlaceholder": "请输入存款利率", "termPeriod": "存款期限", "termPeriodPlaceholder": "请输入存款期限", "title": "编辑定期存款数据"}, "termDepositView": {"title": "查看定期存款数据"}, "tokenAccessGuide": {"description1": "开发人员需要首先在API门户/网关中注册。", "description10": "/deposit/account/allAccounts/{customerNumber}/{index}/{items}", "description2": "使用有效凭据，开发人员可以请求访问令牌。", "description3": "获取开发者令牌", "description4": "/platform/oauth2/client/token", "description5": "与预先分配的客户账户一起，关联的客户令牌已经创建并存储在数据库中，并准备好检索。", "description6": "获取客户令牌", "description7": "/sysadmin/login", "description8": "/sysadmin/tokenRetrieval/{loginPK}", "description9": "客户账户查询：", "subTitle1": "开发者令牌（访问令牌）", "subTitle2": "如何获取访问令牌？", "subTitle3": "示例代码-如何获取访问令牌？", "subTitle4": "客户令牌", "subTitle5": "如何获取客户令牌？", "subTitle6": "示例代码-如何获取客户令牌？", "subTitle7": "示例代码-如何在API市场访问API？"}, "transferCreation": {"accountNumber": "账户号码", "accountTypeFrom": "账户类型来自", "accountTypeFromPlaceholder": "请输入账户类型来自", "accountTypeTo": "账户类型至", "accountTypeToPlaceholder": "请输入账户类型至", "amountFr": "起", "amountTo": "至", "beforeRemoveMessage": "是否确认移除{fileName}？", "channel": "渠道", "channelPlaceholder": "请输入渠道", "dataSource": "数据源", "dataSourcePlaceholder": "请输入数据源", "downloadBranchTemplate": "下载模板文件", "frequency": "频率", "frequencyPlaceholder": "请输入频率", "frequencyRate": "频次", "frequencyRatePlaceholder": "请输入频次", "handleExceedMessage": "当前限制选择1个文件，本次选择{thisTimeChoose}个文件，总共选择{totallyChoose}个文件", "linkToDatatable": "批量上传账户", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "tableName": "表名称", "tableNamePlaceholder": "请输入表名称", "totalTransaction": "最大交易次数", "totalTransactionPlaceholder": "请输入最大交易次数", "transactionDate": "交易日期", "transactionDateFrom": "起", "transactionDateFromPlaceholder": "请选择交易日期起", "transactionDateTo": "至", "transactionDateToPlaceholder": "请选择交易日期至", "transferAmount": "转账金额", "transferAmountFrom": "起", "transferAmountFromPlaceholder": "请输入转账金额起", "transferAmountTo": "至", "transferAmountToPlaceholder": "请输入转账金额至", "transferFrom": "转账自", "transferFromPlaceholder": "请输入转出账户", "transferFromType": "转出账户", "transferTo": "转账至", "transferToPlaceholder": "请输入转入账户", "transferToType": "转入账户", "upload": "上传", "uploadFileRule": "仅上传csv/xls/xlsx文件，不得超过5MB"}, "transferEnquiry": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "ccy": "货币", "channel": "渠道", "channelID": "渠道ID", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "crDrMaintInd": "交易类型", "custRelationMgrCode": "客户经理编号", "dataDetails": "数据详情", "fromCreateDate": "起始创建时间", "fromTransactionAmount": "交易金额", "fromTransactionAmountPlaceholder": "请输入交易金额", "hkidfirstIssue": "香港身份证首次证签发日期", "hkidissueDate": "香港身份证签发日期", "numberGreaterThanPlaceholder": "{to} 必须大于 {form}", "refAccountNumber": "对方账号", "tableName": "数据表名称", "tableNamePlaceholder": "请选择数据表名称", "tfrSeqNo": "对方交易流水号", "toCreateDate": "结束创建时间", "toTransactionAmount": "至", "toTransactionAmountPlaceholder": "请输入交易金额", "tranAmt": "交易金额", "tranDate": "交易日期", "tranDesc": "交易描述", "tranDescPlaceholder": "请输入交易描述", "tranSeq": "交易流水号", "tranType": "交易类型"}, "transferUpdate": {"accountNumber": "账号", "accountNumberPlaceholder": "请输入账号", "actualBalAmt": "实际余额金额", "actualBalAmtPlaceholder": "请输入实际余额金额", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "ccy": "货币", "ccyPlaceholder": "请输入货币", "channel": "渠道", "channelID": "渠道ID", "channelIDPlaceholder": "请输入渠道ID", "channelPlaceholder": "请输入渠道", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "crDrMaintInd": "交易类型", "crDrMaintIndPlaceholder": "请输入交易类型", "previousBalAmt": "上一个余额金额", "previousBalAmtPlaceholder": "请输入上一个余额金额", "refAccountNumber": "对方账号", "refAccountNumberPlaceholder": "请输入对方账号", "reference": "参考", "referencePlaceholder": "请输入参考", "tfrSeqNo": "对方交易流水号", "tfrSeqNoPlaceholder": "请输入对方交易流水号", "title": "编辑转账数据", "tranAmt": "交易金额", "tranAmtPlaceholder": "请输入交易金额", "tranDate": "交易日期", "tranDatePlaceholder": "请选择交易日期", "tranDesc": "交易描述", "tranDescPlaceholder": "请输入交易描述", "tranSeq": "交易流水号", "tranSeqPlaceholder": "请输入交易流水号", "tranType": "交易类型", "tranTypePlaceholder": "请输入交易类型"}, "transferView": {"title": "查看转账数据"}, "user": {"activeStatus": "激活状态", "activeStatusPlaceholder": "用户激活状态", "birthday": "出生日期", "birthdayPlaceholder": "请选择出生日期", "cardId": "证件号码", "cardIdIsRequired": "证件号码不能为空", "cardIdPlaceholder": "请输入证件号码", "cardType": "证件类型", "cardTypeIsRequired": "证件类型不能为空", "cardTypePlaceholder": "请选择证件类型", "createUser": "创建用户", "deleteUserPrompt": "此操作将永久删除用户，是否继续？", "department": "部门", "departmentPlaceholder": "请输入的部门", "downloadTemplate": "下载模板", "editUser": "编辑用户", "email": "电子邮箱", "emailIsRequired": "电子邮箱不能为空", "emailPlaceholder": "请输入电子邮箱", "experienceYear": "工作年限", "experienceYearPlaceholder": "请输入工作年限", "graduateSchool": "毕业学校", "graduateSchoolPlaceholder": "请输入毕业学校", "importUser": "导入用户", "invalidCardIdFormat": "无效的证件号码格式", "invalidEmailFormat": "无效的电子邮箱格式", "invalidNicknameFormat": "无效的昵称格式，长度为2-64个字符", "invalidPasswordFormat": "至少要包含大小写字母和数字，且长度为8-16个字符", "invalidPhoneNumberFormat": "无效的手机号码格式", "invalidUsernameFormat": "无效的用户姓名格式，长度为2-64个字符", "isBlacklist": "是否在黑名单", "isBlacklistPlaceholder": "是否在黑名单", "jobTitle": "职务", "jobTitlePlaceholder": "请输入职务", "keywordsPlaceholder": "请输入电子邮箱/用户名", "level": "用户级别", "levelIsRequired": "用户级别不能为空", "levelPlaceholder": "请输入用户级别", "nickname": "昵称", "nicknameIsRequired": "昵称不能为空", "nicknamePlaceholder": "请输入用户昵称", "organization": "组织名称", "organizationPlaceholder": "请输入组织名称", "password": "密码", "passwordIsRequired": "密码不能为空", "passwordPlaceholder": "请输入登录密码", "phoneNumber": "手机号码", "phoneNumberIsRequired": "手机号码不能为空", "phoneNumberPlaceholder": "请输入手机号码", "pleaseSelectUserOrganization": "请选择用户所属企业", "role": "角色", "roleIdsPlaceholder": "请选择用户角色", "roleIsRequired": "角色不能为空", "rolePlaceholder": "用户角色", "selectFile": "选择文件", "sex": "性别", "sexPlaceholder": "请选择性别", "startImport": "开始导入", "studentOrStaffNumber": "学/工号", "studentOrStaffNumberPlaceholder": "请输入学/工号", "username": "用户名", "usernameIsRequired": "用户姓名不能为空", "usernamePlaceholder": "请输入用户姓名"}, "workflowConfig": {"accountOpeningProcessNodeList": "开户流程节点列表", "accountOpeningProcessNodeRules": "开户流程节点规则", "apiMethod": "API 请求方式", "apiUrl": "API 地址", "clickUploadTip": "点击上传", "createWorkflow": "创建工作流", "customWorkflow": "自定义工作流", "deleteTip": "你确定要删除吗？", "eKYC": "eKYC", "eKYCTip1": "提示：使用公共节点创建您的Workflow。", "eKYCTip2": "<p> <span>请按照以下说明开始创建Workflow：</span> </p> <p> <span>1.Workflow创建工具</span> </p> <p> <span>&nbsp; &nbsp; 点击<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow创建工具</b></a>跳转到在线编辑工具。</span> </p> <p> <span>2.下载模板</span> </p> <p> <span>&nbsp; &nbsp; 在上一页“工作流程”的公共工作流中点击下载按钮，得到公共工作流模板点击下载。</span> </p> <p> <span>3.使用workflow模板</span> </p> <p> <span>&nbsp; &nbsp; 在打开的工具页面中点击open，选择上一步下载得到的EKYC.bpmn文件。</span> </p> <p> <span>4.开始创建</span> </p> <p> <span>&nbsp; &nbsp; 在workflow模板的基础上开始创建您的workflow。</span> </p> <p> <span>5.保存工作流</span> </p> <p> <span>&nbsp; &nbsp; 在工具页面左下角点击“下载”图标，保存您创建的工作流文件，然后点击右侧的“上传文件”上传您保存的工作流文件。</span> </p> <p> <br/> </p> <p> <span>Workflow创建工具的详细操作手册，请<a target=\"_blank\" href=\"https://simnectz-portal.cefar.cuhk.edu.hk/document/soi-plus/工作流操作文档.pdf\"><b>点击下载</b></a>。</span> </p> <p> <span>使用公共Node创建workflow，需要遵守Node之间的顺序规则，请参考“<a target=\"_blank\" href=\"/domain-practicals/public-node-rules?type=KYC\"})\"><b>公共Node规则</b></a>”。</span> </p> <p> <br/> </p>", "file": "文件", "fileError": "请上传文件，文件格式：.bpmn，大小在2MB以内", "fileNumberError": "一次只能上传一个文件。请先上传所选文件。", "fileSizeError": "文件大小不能超过2MB！", "group": "分组", "insurance": "创建保险理赔", "insuranceClaimsProcessNodeList": "保险理赔流程节点列表", "insuranceTip1": "提示：使用公共节点创建您的Workflow。", "insuranceTip2": "<p> <span>请按照以下说明开始创建Workflow：</span> </p> <p> <span>1.Workflow创建工具</span> </p> <p> <span>&nbsp; &nbsp; 点击<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow创建工具</b></a>跳转到在线编辑工具。</span> </p> <p> <span>2.下载模板</span> </p> <p> <span>&nbsp; &nbsp; 在上一页“工作流程”的公共工作流中点击下载按钮，得到公共工作流模板点击下载。</span> </p> <p> <span>3.使用workflow模板</span> </p> <p> <span>&nbsp; &nbsp; 在打开的工具页面中点击open，选择上一步下载得到的insurance_claim.bpmn文件。</span> </p> <p> <span>4.开始创建</span> </p> <p> <span>&nbsp; &nbsp; 在workflow模板的基础上开始创建您的workflow。</span> </p> <p> <span>5.保存工作流</span> </p> <p> <span>&nbsp; &nbsp; 在工具页面左下角点击“下载”图标，保存您创建的工作流文件，然后点击右侧的“上传文件”上传您保存的工作流文件。</span> </p> <p> <br/> </p> <p> <span>Workflow创建工具的详细操作手册，请<a target=\"_blank\" href=\"https://simnectz-portal.cefar.cuhk.edu.hk/document/soi-plus/工作流操作文档.pdf\"><b>点击下载</b></a>。</span> </p> <p> <span>使用公共Node创建workflow，需要遵守Node之间的顺序规则，请参考“<a target=\"_blank\" href=\"/domain-practicals/public-node-rules?type=insurance\"})\"><b>公共Node规则</b></a>”。</span> </p> <p> <br/> </p>", "kycAccountOpeningProcess": "KYC - 开户流程", "name": "名称", "process": "步骤", "processGroup": "流程组", "publicWorkflow": "公共工作流", "selectProcessType": "选择流程类型", "uploadFailed": "上传失败！请稍后再试。", "uploadSuccess": "上传成功！", "uploadTip": "只上传*.bpmn文件，文件大小不能超过2MB。", "workflowNameError": "请填写工作流名称"}, "workspace": {"createNow": "现在创建", "createWorkspaceTip": "创建你的专属工作空间，实现与您的团队的线上协作与编码工作。", "deleteWorkspaceTip": "确定要删除工作空间吗？删除之后，工作空间的数据将不可恢复。", "deleting": "正在删除", "entry": "进入", "noResource": "您没有权限创建工作空间，请联系管理员。", "offTime": "上次关机时间", "runStatus": "运行状态", "running": "正在运行", "shutDownWorkspaceTip": "确定要关闭服务吗？请确认您的文件都已经保存。", "shutdown": "已关闭", "shuttingDown": "正在关闭", "start": "启动", "starting": "正在启动", "url": "访问地址", "workspaceName": "工作空间名称", "workspaceNameErrorTip": "工作空间名称仅支持输入大小写字母、'-' 和 '_'", "workspaceNameTip": "请输入工作空间名称"}}}