<template>
  <div class="practice-platform-layout">
    <el-row class="practice-transfer-container" type="flex" justify="center">
      <el-col :span="14" :md="18" :xs="24" :sm="24">
        <div class="header" style="position: sticky;top: 0; background: white;z-index: 1000;transition: all 0.3s ease;; border-bottom: 1px solid rgba(0,0,0,0.05)">
          <div class="soi-logo">
            <img :src="logo" class="logo" alt="logo" @click="officialWebsite()">
          </div>
          <div class="right-actions">
            <span class="welcome-user">{{ `${$t('soi.common.welcome')} ${username}` }}</span>

            <language />

            <el-dropdown class="avatar-container" trigger="click">
              <div class="avatar-wrapper">
                <img :src="defaultAvatar" width="32px" class="user-avatar" alt="avatar">
                <i class="el-icon-caret-bottom" />
              </div>
              <el-dropdown-menu slot="dropdown" class="user-dropdown">
                <el-dropdown-item @click.native="logout">
                  <span style="display:block;">{{ $t('soi.authorize.logout') }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <transition name="fade-transform" mode="out-in">
          <router-view />
        </transition>

        <div class="by" @click="officialWebsite()">
          {{ $t('soi.common.copyright') }}
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Language from '@/components/Language/index.vue'
import { OFFICIAL_WEBSITE_URL } from '@/contains'
import { mapGetters } from 'vuex'

export default {
  name: 'PracticePlatformLayout',
  components: { Language },
  data() {
    return {
      defaultAvatar: require(`@/assets/images/avatar.png`),
      logo: require(`@/assets/images/logo-${process.env.VUE_APP_TEMPORARY_PERMISSION}.png`)
    }
  },
  computed: {
    ...mapGetters([
      'avatar',
      'username'
    ])
  },
  methods: {
    officialWebsite() {
      window.open(OFFICIAL_WEBSITE_URL, '_self')
    },
    logout() {
      this.$store.dispatch('user/logout')
      this.$router.push('/authorize/login')
    }
  }
}
</script>

<style lang="scss" scoped>
.practice-transfer-container {
  padding: 0 16px;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .official-link, .logout-link {
      font-size: 17px;
      margin-right: 40px;
    }

    .right-actions {
      display: flex;
      align-items: center;
    }
  }

  .soi-logo {
    display: inline-block;
    cursor: pointer;

    img {
      width: 200px;
    }
  }

  .platforms {
    margin: 100px auto;
  }

  .el-card {
    height: 200px;
    text-align: center;
    font-size: 22px;
    cursor: pointer;
    border-radius: 10px;

    .content {
      line-height: 30px;
      display: inline-block;
      font-weight: 700;
    }

    &:hover {
      height: 200px;
      text-align: center;
      font-size: 24px;
      cursor: pointer;
    }
  }

  .avatar-container {
    margin: 0 20px;

    .avatar-wrapper {
      margin-top: 5px;
      position: relative;

      .user-avatar {
        cursor: pointer;
        width: 32px;
        height: 32px;
        border-radius: 10px;
      }

      .el-icon-caret-bottom {
        cursor: pointer;
        position: absolute;
        right: -20px;
        top: 20px;
        font-size: 12px;
      }
    }
  }

  .welcome-user {
    margin-right: 20px;
    font-size: 14px;
  }

  .by {
    cursor: pointer;
    float: right;
    margin: 60px 0;
  }
}
</style>
