import CourseLayout from '@/layouts/course-learning/index.vue'

export default [
  {
    path: '/course',
    component: CourseLayout,
    redirect: '/course/transfer',
    children: [
      {
        path: 'transfer',
        name: 'course-transfer',
        meta: { title: 'courseTransfer' },
        component: () => import('@/views/course-learning/transfer')
      }
    ]
  },

  {
    path: '/explore',
    component: CourseLayout,
    meta: { module: 'course', title: 'explore', image: 'data-analysis.jpg' },
    children: [
      {
        path: 'course-introduce/course-category',
        name: 'course-category-introduce',
        meta: { title: 'courseIntroduce', image: 'introduce.jpg' },
        component: () => import('@/views/course-learning/explore/introduce')
      },
      {
        path: 'course-introduce/course',
        name: 'course-introduce',
        meta: { title: 'course' },
        hidden: true,
        component: () => import('@/views/course-learning/explore/introduce/course/index.vue')
      },
      {
        path: 'course-introduce/course-details',
        name: 'course-details-introduce',
        meta: { title: 'courseDetail' },
        hidden: true,
        component: () => import('@/views/course-learning/explore/introduce/course/details.vue')
      },
      {
        path: 'course-introduce/subject',
        name: 'subject-introduce',
        meta: { title: 'subject' },
        hidden: true,
        component: () => import('@/views/course-learning/explore/introduce/subject/index.vue')
      },
      {
        path: 'course-introduce/subject-details',
        name: 'subject-details-introduce',
        meta: { title: 'subjectDetail' },
        hidden: true,
        component: () => import('@/views/course-learning/explore/introduce/subject/details.vue')
      },
      {
        path: 'my-path',
        name: 'my-path',
        meta: { title: 'myPath', image: 'technological-development-workspace.png' },
        component: () => import('@/views/course-learning/explore/my-path')
      },
      {
        path: 'homework',
        name: 'homework',
        meta: { title: 'homework', image: 'technological-development-coding-practice.jpg' },
        component: () => import('@/views/course-learning/explore/homework')
      }
    ]
  }
]
