import CourseLayout from '@/layouts/course-learning/index.vue'

export default [
  {
    path: '/financial-data-characteristics',
    component: CourseLayout,
    meta: { title: 'courseTechnology', module: 'course', image: 'learning-space-technology.png' },
    children: [
      {
        path: '/financial-data-characteristics',
        name: 'data-analysis-financial-data-characteristics',
        meta: { title: 'financialDataCharacteristics', image: 'data-analysis-financial-data-characteristics.png', source: 'practice:data-analysis:financial-data-characteristics' },
        component: () => import('@/views/course-learning/financial-data-characteristics')
      }
    ]
  }
]
