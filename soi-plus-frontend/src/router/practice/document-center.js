import PracticeLayout from '@/layouts/practice/index.vue'

export default [
  {
    path: '/document-center',
    component: PracticeLayout,
    meta: { module: 'practice', title: 'documentCenter', image: 'document-center.jpg', source: 'practice:document-center' },
    children: [
      {
        path: 'download-center',
        name: 'document-center-download-center',
        meta: { title: 'downloadCenter', image: 'document-center-download-center.jpg' },
        component: () => import('@/views/practice/document-center/download-center')
      },
      {
        path: 'user-guide-category',
        name: 'document-center-user-guide-category',
        meta: { title: 'userGuide', image: 'document-center-user-guide.jpg' },
        component: () => import('@/views/practice/document-center/user-guide')
      },
      {
        path: 'user-guide-document-list',
        name: 'document-center-user-guide-document-list',
        hidden: true,
        meta: { title: 'userGuide' },
        component: () => import('@/views/practice/document-center/user-guide/document-list')
      }
    ]
  }
]
