import PracticeLayout from '@/layouts/practice/index.vue'
import PracticeApiLayout from '@/views/practice/technological-development/api/index.vue'
import PracticeApiApiUsageGuideLayout from '@/views/practice/technological-development/api/api-usage-guide/index.vue'
import { DOCUMENT_URL, JUPYTER_URL } from '@/contains'

export default [
  {
    path: '/practice/technological-development',
    component: PracticeLayout,
    meta: { module: 'practice', title: 'technicalDevelopment', image: 'technological-development.png', source: 'practice:technical-development' },
    children: [
      {
        path: 'api',
        meta: { title: 'api', image: 'technological-development-api.png', source: 'practice:technical-development:api' },
        component: PracticeApiLayout,
        children: [
          {
            path: 'api-market-place-api-provider',
            name: 'technological-development-api-api-market-place-api-provider',
            meta: {
              title: 'apiMarketPlace',
              image: 'technological-development-api-api-market-place.jpg',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/api-market-place')
          },
          {
            path: 'api-market-place-tryout-api/:apiId',
            name: 'technological-development-api-api-market-place-tryout-api',
            hidden: true,
            meta: {
              title: 'tryoutApi',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/api-market-place/tryout-api')
          },
          {
            path: 'api-market-place-custom-api',
            name: 'technological-development-api-api-market-place-custom-api',
            hidden: true,
            meta: {
              title: 'customApi',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/api-market-place/custom-api')
          },
          {
            path: 'api-market-place-api-catalogue/:company',
            name: 'technological-development-api-api-market-place-api-catalogue',
            hidden: true,
            meta: {
              title: 'apiCatalogue',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/api-market-place/api-catalogue')
          },
          {
            path: 'api-market-place-api-list',
            name: 'technological-development-api-api-market-place-api-list',
            hidden: true,
            meta: {
              title: 'apiList',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/api-market-place/api-list')
          },
          {
            path: 'obtain-developer-token',
            name: 'technological-development-api-obtain-developer-token',
            meta: {
              title: 'obtainDeveloperToken',
              image: 'technological-development-api-obtain-developer-token.jpg',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/obtain-developer-token')
          },
          {
            path: 'apply-customer-data-token',
            name: 'technological-development-api-apply-customer-data-token',
            meta: {
              title: 'applyCustomerDataToken',
              image: 'technological-development-api-apply-customer-data-token.png',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/apply-customer-data-token')
          },
          {
            path: 'api-usage-guide',
            meta: { title: 'apiUsageGuide', image: 'technological-development-api-api-usage-guide.jpg' },
            component: PracticeApiApiUsageGuideLayout,
            children: [
              {
                path: 'api-architecture-design',
                name: 'technological-development-api-api-usage-guide-api-architecture-design',
                meta: {
                  title: 'apiArchitectureDesign',
                  image: 'technological-development-api-api-usage-guide-api-architecture-design.png',
                  document: {
                    en: DOCUMENT_URL + '/Technical Development Document.pdf',
                    zh: DOCUMENT_URL + '/技术开发文档.pdf',
                    cht: DOCUMENT_URL + '/技術開發文檔.pdf'
                  }
                },
                component: () => import('@/views/practice/technological-development/api/api-usage-guide/api-architecture-design')
              },
              {
                path: 'api-access-guide',
                name: 'technological-development-api-api-usage-guide-api-access-guide',
                meta: {
                  title: 'apiAccessGuide',
                  image: 'technological-development-api-api-usage-guide-api-access-guide.jpg',
                  document: {
                    en: DOCUMENT_URL + '/Technical Development Document.pdf',
                    zh: DOCUMENT_URL + '/技术开发文档.pdf',
                    cht: DOCUMENT_URL + '/技術開發文檔.pdf'
                  }
                },
                component: () => import('@/views/practice/technological-development/api/api-usage-guide/api-access-guide')
              },
              {
                path: 'token-access-guide',
                name: 'technological-development-api-api-usage-guide-token-access-guide',
                meta: {
                  title: 'tokenAccessGuide',
                  image: 'technological-development-api-api-usage-guide-token-access-guide.png',
                  document: {
                    en: DOCUMENT_URL + '/Technical Development Document.pdf',
                    zh: DOCUMENT_URL + '/技术开发文档.pdf',
                    cht: DOCUMENT_URL + '/技術開發文檔.pdf'
                  }
                },
                component: () => import('@/views/practice/technological-development/api/api-usage-guide/token-access-guide')
              }
            ]
          },
          {
            path: 'sandbox-usage-guide',
            name: 'technological-development-api-sandbox-usage-guide',
            meta: {
              title: 'sandboxUsageGuide',
              image: 'technological-development-api-sandbox-usage-guide.jpg',
              document: {
                en: DOCUMENT_URL + '/Technical Development Document.pdf',
                zh: DOCUMENT_URL + '/技术开发文档.pdf',
                cht: DOCUMENT_URL + '/技術開發文檔.pdf'
              }
            },
            component: () => import('@/views/practice/technological-development/api/sandbox-usage-guide')
          }
        ]
      },
      {
        path: 'workspace',
        name: 'technological-development-workspace',
        meta: {
          title: 'workspace',
          image: 'technological-development-workspace.png',
          source: 'practice:technical-development:workspace',
          document: {
            en: DOCUMENT_URL + '/Technical Development Document.pdf',
            zh: DOCUMENT_URL + '/技术开发文档.pdf',
            cht: DOCUMENT_URL + '/技術開發文檔.pdf'
          }
        },
        component: () => import('@/views/practice/technological-development/workspace')
      },
      {
        path: 'coding-practice-category',
        name: 'technological-development-coding-practice-category',
        meta: {
          title: 'codingPractice',
          image: 'technological-development-coding-practice.jpg',
          source: 'practice:technical-development:coding-practice',
          document: {
            en: DOCUMENT_URL + '/Technical Development Document.pdf',
            zh: DOCUMENT_URL + '/技术开发文档.pdf',
            cht: DOCUMENT_URL + '/技術開發文檔.pdf'
          }
        },
        component: () => import('@/views/practice/technological-development/coding-practice')
      },
      {
        path: 'coding-practice-subcategory/:categoryId',
        name: 'technological-development-coding-practice-subcategory',
        hidden: true,
        meta: {
          title: 'codingPractice',
          document: {
            en: DOCUMENT_URL + '/Technical Development Document.pdf',
            zh: DOCUMENT_URL + '/技术开发文档.pdf',
            cht: DOCUMENT_URL + '/技術開發文檔.pdf'
          }
        },
        component: () => import('@/views/practice/technological-development/coding-practice/subcategory')
      },
      {
        path: 'coding-practice-coding',
        name: 'technological-development-coding-practice-coding',
        hidden: true,
        meta: {
          title: 'codingPractice',
          document: {
            en: DOCUMENT_URL + '/Technical Development Document.pdf',
            zh: DOCUMENT_URL + '/技术开发文档.pdf',
            cht: DOCUMENT_URL + '/技術開發文檔.pdf'
          }
        },
        component: () => import('@/views/practice/technological-development/coding-practice/coding')
      },
      {
        path: JUPYTER_URL + '/?token=8ae8cbb3a122d0ce837ca0d31bfde3f45340c2057b55d10a',
        meta: { title: 'jupyter', image: 'technological-development-jupyter.jpg', source: 'practice:technical-development:jupyter' }
      }
    ]
  }
]
