import Cookies from 'js-cookie'

const accessToken = 'access-token'
const refreshToken = 'refresh-token'

export function getAccessToken() {
  return Cookies.get(accessToken)
}

export function setAccessToken(token) {
  return Cookies.set(accessToken, token)
}

export function removeToken() {
  Cookies.remove(refreshToken)
  Cookies.remove(accessToken)
}

export function getRefreshToken() {
  return Cookies.get(refreshToken)
}

export function setRefreshToken(token) {
  return Cookies.set(refreshToken, token)
}
