<template>
  <div class="update-course-category-container">
    <course-category-form
      v-if="show"
      :course-category-form.sync="courseCategory"
      :confirm-button-loading="confirmButtonLoading"
      @validated="updateCourseCategory()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import CourseCategoryForm from '@/views/admin/course-learning/course-category/CourseCategoryForm.vue'
import { updateCourseCategory, getCourseCategoryDetails } from '@/api/system/course-category'

export default {
  name: 'UpdateCourseCategory',
  components: { CourseCategoryForm },
  props: {
    courseCategoryId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      show: false,
      confirmButtonLoading: false,
      courseCategory: {}
    }
  },
  async mounted() {
    this.show = false
    // 加载课程分类信息
    const { data } = await getCourseCategoryDetails(this.courseCategoryId)
    this.courseCategory = data
    this.show = true
  },
  methods: {
    // 调用API更新角色
    updateCourseCategory() {
      this.confirmButtonLoading = true
      updateCourseCategory(this.courseCategory)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.update-course-category-container {
  padding: 0 20px;
}
</style>
