<template>
  <div v-loading="loading" class="app-container course-category-container">
    <customize-card :title="$t('soi.router.courseCategory')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 课程大分类 -->
        <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
          <el-input
            v-model="searchForm.keywords"
            :placeholder="$t('soi.courseCategory.keywordsPlaceholder')"
            clearable
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getCourseCategoryList()">{{
            $t('soi.common.search')
          }}
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateCourseCategoryDialog()">
            {{ $t('soi.common.create') }}
          </el-button>
          <el-button
            size="small"
            icon="el-icon-delete"
            type="danger"
            plain
            :disabled="!multipleSelection.length"
            @click="batchDeleteCourseCategory()"
          >{{ $t('soi.common.delete') }}
          </el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getCourseCategoryList()" />
      </div>

      <!-- 课程大分类表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="courseCategoryList" stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55px" />
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('soi.courseCategory.courseCategory')"
            prop="courseCategory"
            show-overflow-tooltip
            align="left"
          />
          <el-table-column :label="$t('soi.courseCategory.courseCategoryPicture')" align="left">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 80px"
                :src="scope.row.courseCategoryPicture"
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
          <el-table-column :label="$t('soi.common.updateTime')" prop="lastUpdateTime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center" width="193px">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-edit"
                @click="openUpdateCourseCategoryDialog(scope.row.id)"
              >{{ $t('soi.common.edit') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-delete"
                @click="batchDeleteCourseCategory([scope.row.id])"
              >{{ $t('soi.common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getCourseCategoryList()"
          @current-change="getCourseCategoryList()"
        />
      </div>
    </customize-card>

    <!-- 创建课程大分类的对话框 -->
    <el-dialog
      :title="$t('soi.courseCategory.createCourseCategory')"
      :visible.sync="createCourseCategoryDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <create-course-category
        :key="createKey"
        @success="handlerCreateCourseCategorySuccess()"
        @cancel="closeCreateCourseCategoryDialog()"
      />
    </el-dialog>

    <!-- 更新课程大分类的对话框 -->
    <el-dialog
      :title="$t('soi.courseCategory.editCourseCategory')"
      :visible.sync="updateCourseCategoryDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <!-- key的作用: 重新刷新子组件 -->
      <update-course-category
        :key="updateKey"
        :course-category-id="updateCourseCategoryId"
        @success="handlerUpdateCourseCategorySuccess()"
        @cancel="closeUpdateCourseCategoryDialog()"
      />
    </el-dialog>
  </div>
</template>

<script>
import { getCourseCategoryList, batchDeleteCourseCategory } from '@/api/system/course-category'
import CreateCourseCategory from '@/views/admin/course-learning/course-category/CreateCourseCategory.vue'
import UpdateCourseCategory from '@/views/admin/course-learning/course-category/UpdateCourseCategory.vue'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getCourseByCategoryIds } from '@/api/system/course'

export default {
  name: 'CourseCategory',
  components: { CustomizeCard, UpdateCourseCategory, CreateCourseCategory },
  data() {
    return {
      // 页面loading
      loading: false,
      // 表格loading
      tableLoading: false,
      createKey: '',
      updateKey: '',
      total: 0,
      // 分页查询请求参数
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        keywords: ''
      },
      // 课程大分类列表
      courseCategoryList: [],
      // 创建课程大分类的对话框标记：true 显示，false 隐藏
      createCourseCategoryDialogVisible: false,
      // 更新课程大分类的对话框标记：true 显示，false 隐藏
      updateCourseCategoryDialogVisible: false,
      updateCourseCategoryId: '',
      multipleSelection: []
    }
  },
  mounted() {
    // 页面初始化加载课程大分类列表
    this.getCourseCategoryList()
  },
  methods: {
    // 调用API分页查询课程大分类列表
    getCourseCategoryList() {
      this.tableLoading = true
      getCourseCategoryList(this.searchForm)
        .then((res) => {
          const { total, items: courseCategoryList } = res.data

          this.courseCategoryList = courseCategoryList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 打开创建课程大分类对话框
    openCreateCourseCategoryDialog() {
      this.createKey = String(new Date().getTime())
      this.createCourseCategoryDialogVisible = true
    },
    // 关闭创建课程大分类对话框
    closeCreateCourseCategoryDialog() {
      this.createCourseCategoryDialogVisible = false
    },
    // 创建课程大分类成功
    handlerCreateCourseCategorySuccess() {
      this.createCourseCategoryDialogVisible = false
      this.getCourseCategoryList()
    },
    // 打开更新课程大分类对话框
    openUpdateCourseCategoryDialog(courseCategoryId) {
      this.updateCourseCategoryId = courseCategoryId
      this.updateKey = String(new Date().getTime())
      this.updateCourseCategoryDialogVisible = true
    },
    // 关闭更新课程大分类对话框
    closeUpdateCourseCategoryDialog() {
      this.updateCourseCategoryDialogVisible = false
    },
    // 更新课程大分类成功
    handlerUpdateCourseCategorySuccess() {
      this.updateCourseCategoryDialogVisible = false
      this.getCourseCategoryList()
    },
    // 删除课程大分类
    batchDeleteCourseCategory: function(courseCategoryIds) {
      this.$confirm(this.$t('soi.courseCategory.deleteCourseCategoryPrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedCourseCategoryIds = courseCategoryIds

        if (!deletedCourseCategoryIds || !deletedCourseCategoryIds.length) {
          deletedCourseCategoryIds = this.multipleSelection.map((courseCategory) => courseCategory.id)
        }
        getCourseByCategoryIds(deletedCourseCategoryIds)
          .then((res) => {
            if (res.data.length !== 0) {
              const filter = res.data.filter(item => deletedCourseCategoryIds.includes(item.courseCategoryId))
              if (filter.length !== 0) {
                this.$message({
                  showClose: true,
                  message: filter[0].courseCategory + ': ' + this.$t('soi.courseCategory.deleteError'),
                  type: 'warning'
                })
              }
            } else {
              this.loading = true
              batchDeleteCourseCategory(deletedCourseCategoryIds)
                .then((res) => {
                  // 提示成功
                  this.$message.success(res.message)

                  // 重新加载课程大分类列表
                  this.getCourseCategoryList()
                })
                .finally(() => {
                  this.loading = false
                })
            }
          })
          .finally(() => {
            this.loading = false
          })
      }
      )
    },
    // 保存已选中的课程大分类记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>

<style scoped lang="scss">
.course-category-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
