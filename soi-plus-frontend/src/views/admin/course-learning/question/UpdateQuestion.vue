<template>
  <div class="update-subject-container">
    <question-form
      v-if="show"
      :question-form.sync="question"
      :course-category-options="courseCategoryOptions"
      :course-options="courseOptions"
      :subject-options="subjectOptions"
      :confirm-button-loading="confirmButtonLoading"
      flag="update"
      @validated="updateQuestion()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import QuestionForm from '@/views/admin/course-learning/question/QuestionForm.vue'
import { updateQuestion, getQuestionDetails } from '@/api/system/question'
import { courseCategoryOptions, courseOptions, subjectOptions } from '@/data'

export default {
  name: 'UpdateQuestion',
  components: { QuestionForm },
  props: {
    subjectCode: {
      type: String,
      required: true
    },
    questionId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      show: false,
      confirmButtonLoading: false,
      question: {},
      courseCategoryOptions: [],
      courseOptions: [],
      subjectOptions: []
    }
  },
  async mounted() {
    this.show = false
    // 加载课题信息
    const { data } = await getQuestionDetails(this.subjectCode)
    const question = data.data.filter(item => {
      return item.id === this.questionId
    })
    this.question = question[0]

    // 加载课程大分类列表
    this.courseCategoryOptions = await courseCategoryOptions()
    // 加载课程列表
    this.courseOptions = await courseOptions()
    // 加载课题列表
    this.subjectOptions = await subjectOptions()
    this.show = true
  },
  methods: {
    // 调用API更新角色
    updateQuestion() {
      this.confirmButtonLoading = true
      updateQuestion(this.question)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.update-question-container {
  padding: 0 20px;
}
</style>
