<template>
  <div class="subject-detail-container">
    <el-descriptions v-if="show" class="margin-top" :column="1" border>
      <!-- 课程类型 -->
      <el-descriptions-item :label="$t('soi.course.courseName')" prop="courseName">
        {{ subjectDetail.courseName }}
      </el-descriptions-item>

      <!-- 课题名称 -->
      <el-descriptions-item :label="$t('soi.subject.subjectName')" prop="subjectName">
        {{ subjectDetail.subjectName }}
      </el-descriptions-item>

      <!-- 课题类型 -->
      <el-descriptions-item :label="$t('soi.subject.category')" prop="subjectCategory">
        {{ $t(subjectCategory) }}
      </el-descriptions-item>

      <!-- 课题时长 -->
      <el-descriptions-item :label="$t('soi.subject.subjectHours')" prop="subjectHours">
        {{ subjectDetail.subjectHours }}
      </el-descriptions-item>

      <!-- 课题级别 -->
      <el-descriptions-item :label="$t('soi.subject.level')" prop="subjectLevel">
        {{ $t(subjectLevel) }}
      </el-descriptions-item>

      <!-- 课题目标 -->
      <el-descriptions-item :label="$t('soi.subject.objective')" prop="subjectObjective">
        {{ subjectDetail.subjectObjective }}
      </el-descriptions-item>

      <!-- 课题内容描述 -->
      <el-descriptions-item :label="$t('soi.subject.description')" prop="subjectDescription">
        {{ subjectDetail.subjectDescription }}
      </el-descriptions-item>

      <!-- 课题图片 -->
      <el-descriptions-item :label="$t('soi.subject.picture')" prop="subjectPictureUrl">
        <el-image
          v-if="subjectDetail.subjectPictureUrl"
          class="subjectPicture"
          :src="subjectDetail.subjectPictureUrl"
        />
      </el-descriptions-item>

      <!-- 课题演示材料 -->
      <el-descriptions-item :label="$t('soi.subject.presentationMaterial')" prop="materialsUrl">
        <a v-if="subjectDetail.materialsUrl" @click="viewDetail(subjectDetail.materialsUrl)">{{ materialsName }}</a>
      </el-descriptions-item>

      <!-- 课题视频 -->
      <el-descriptions-item :label="$t('soi.subject.video')" prop="videoUrl">
        <a v-if="subjectDetail.videoUrl" @click="viewDetail(subjectDetail.videoUrl)">{{ videoName }}</a>
      </el-descriptions-item>

      <!-- 课题作业 -->
      <el-descriptions-item :label="$t('soi.subject.assignment')" prop="assignmentUrl">
        <a v-if="subjectDetail.assignmentUrl" @click="viewDetail(subjectDetail.assignmentUrl)">{{ assignmentName }}</a>
      </el-descriptions-item>

      <!-- 创建时间 -->
      <el-descriptions-item :label="$t('soi.common.createTime')" prop="createTime">
        {{ subjectDetail.createTime }}
      </el-descriptions-item>

      <!-- 更新时间 -->
      <el-descriptions-item :label="$t('soi.common.updateTime')" prop="lastUpdateTime">
        {{ subjectDetail.lastUpdateTime }}
      </el-descriptions-item>

      <!-- 创建时间 -->
      <el-descriptions-item :label="$t('soi.common.creator')" prop="creator">
        {{ subjectDetail.creator }}
      </el-descriptions-item>

      <!-- 更新时间 -->
      <el-descriptions-item :label="$t('soi.common.updateBy')" prop="lastUpdateCreator">
        {{ subjectDetail.lastUpdateCreator }}
      </el-descriptions-item>
    </el-descriptions></div>
</template>

<script>
import { getSubjectDetails } from '@/api/system/subject'
import { subjectLevelOptions, subjectCategory } from '@/data'

export default {
  name: 'CourseDetail',
  props: {
    subjectCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      show: false,
      subjectDetail: {},
      materialsName: '',
      videoName: '',
      assignmentName: '',
      subjectLevel: '',
      subjectCategory: ''
    }
  },
  async mounted() {
    this.show = false
    // 加载课题信息
    const { data } = await getSubjectDetails(this.subjectCode)
    this.subjectDetail = data
    // 获取当前课题类型名称
    this.subjectCategory = subjectCategory[this.subjectDetail.subjectCategory]
    // 获取当前课题级别名称
    const currentSubjectLevel = subjectLevelOptions.filter(item => item.value === this.subjectDetail.subjectLevel)
    this.subjectLevel = currentSubjectLevel[0].label
    this.initUploadData()
    this.show = true
  },
  methods: {
    // 关闭课题详情弹窗
    cancel() {
      this.$emit('cancel')
    },
    // 查看文件详细内容
    viewDetail(url) {
      window.open(url)
    },
    // 加载文件名称显示
    initUploadData() {
      if (this.subjectDetail.videoUrl) {
        this.videoName = this.subjectDetail.videoUrl.split('/').pop()
      }
      if (this.subjectDetail.materialsUrl) {
        this.materialsName = this.subjectDetail.materialsUrl.split('/').pop()
      }
      if (this.subjectDetail.assignmentUrl) {
        this.assignmentName = this.subjectDetail.assignmentUrl.split('/').pop()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.subject-detail-container {
  padding: 0 20px 20px;

  .subjectPicture {
    width: 180px;
    height: 100px;
  }
}
</style>
