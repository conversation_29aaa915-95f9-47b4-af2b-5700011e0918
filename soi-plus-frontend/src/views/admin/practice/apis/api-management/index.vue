<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-api-management">
    <customize-card :title="$t('soi.router.apiManagement')" :show-back-btn="false">
      <template #header-actions>
        <el-button size="mini" @click="handleImportApi">{{ $t('soi.apiList.importApi') }}</el-button>
        <el-button size="mini" @click="handleAddApi">{{ $t('soi.apiList.addApi') }}</el-button>
      </template>

      <el-cascader
        style="margin-bottom: 10px"
        size="small"
        :options="apiList"
        :props="{ checkStrictly: true }"
        clearable
        @change="handleChange"
      />
      <el-table
        :data="tableData.slice((currentPage - 1) * pagesize, currentPage * pagesize)"
        style="width: 100%"
        stripe
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="userName" :label="$t('soi.apiMarketPlace.username')" min-width="100" />
        <el-table-column :label="$t('soi.apiMarketPlace.apiName')" align="left">
          <template slot-scope="scope">
            {{ ['zh', 'cht'].includes(language) ? scope.row.apiName : scope.row.apiNameEn }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('soi.apiMarketPlace.catalogName')" prop="classifyName" />
        <el-table-column :label="$t('soi.common.operate')" align="center" width="250">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="deleteApi(scope.row.apiId)"
            >
              {{ $t('soi.common.delete') }}
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="editApi(scope.row)"
            >
              {{ $t('soi.common.edit') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="currentPage"
          :page-size="pagesize"
          :total="tableData.length"
          layout="total, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { adminQueryApis, deleteApi, getAllApi } from '@/api/practice/api-market'

export default {
  name: 'PracticeManagementApiManagement',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      apiList: [],
      currentPage: 1,
      pagesize: 15,
      totalPages: 0,
      tableData: [],
      searchParam: []
    }
  },
  computed: {
    ...mapGetters([
      'language',
      'userDetails'
    ])
  },
  mounted() {
    this.getAllApi()
    this.searchClick('', '', '')
  },
  methods: {
    deleteApi(data) {
      const vue = this
      vue.$confirm(vue.$t('soi.apiList.confirmDeleteTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        deleteApi({ apiIds: [data] })
          .then(() => {
            vue.$message({
              type: 'success',
              message: vue.$t('soi.apiList.deleteSuccess')
            })
            vue.searchClick(this.searchParam[0] || '', this.searchParam[1] || '', this.searchParam[2] || '')
          })
          .finally(() => {
            vue.loading = false
          })
      }).catch(() => {})
    },
    editApi(row) {
      localStorage.setItem('edit-api-info', JSON.stringify(row))
      this.$router.push({ name: 'practice-management-edit-api', params: { classId: row.apiClassId, subclassId: row.apiSubClassId, apiId: row.apiId }})
    },
    handleAddApi() {
      this.$router.push({
        name: 'practice-management-add-api'
      })
    },
    handleImportApi() {
      this.$router.push({
        name: 'practice-management-import-api'
      })
    },
    handleChange(value) {
      const vue = this
      this.searchParam = value
      vue.searchClick(value[0] || '', value[1] || '', value[2] || '')
    },
    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    },
    searchClick(userName, cataLogName, apiName) {
      const vue = this
      vue.loading = true
      adminQueryApis({ FIName: userName, cataLogName: cataLogName, apiName: apiName })
        .then((res) => {
          vue.tableData = res.data
        }).finally(() => {
          vue.loading = false
        })
    },
    getAllApi() {
      const vue = this
      vue.loading = true

      getAllApi()
        .then((res) => {
          if (res.data) {
            const list = res.data
            vue.apiList = []
            for (const key in list) {
              const obj = {}
              obj.label = list[key].fiName
              obj.value = obj.label
              obj.children = []

              for (const key2 in list[key].cataLog) {
                const obj2 = {}
                obj2.label = key2
                obj2.value = obj2.label
                obj2.children = []

                for (let i = 0; i < list[key].cataLog[key2].apiInfos.length; i++) {
                  const obj3 = {}
                  obj3.label = list[key].cataLog[key2].apiInfos[i].apiName
                  obj3.value = obj3.label
                  obj2.children.push(obj3)
                }
                obj.children.push(obj2)
              }
              vue.apiList.push(obj)
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    }
  }
}
</script>
