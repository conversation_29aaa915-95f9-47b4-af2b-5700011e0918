<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-edit-api">
    <customize-card :title="$t('soi.router.editApi')">
      <el-form
        ref="apiForm"
        :rules="apiFormRules"
        :model="apiForm"
        label-width="150px"
      >
        <el-collapse v-model="activeNames">
          <el-collapse-item :title="$t('soi.apiList.apiSettings')" name="1" class="form-block">
            <el-form-item :label="$t('soi.apiList.apiNameZh')" prop="apiName">
              <el-input v-model="apiForm.apiName" />
            </el-form-item>
            <el-form-item :label="$t('soi.apiList.apiNameEn')" prop="apiNameEn">
              <el-input v-model="apiForm.apiNameEn" />
            </el-form-item>
            <el-form-item :label="$t('soi.apiList.descriptionZh')" prop="description">
              <el-input v-model="apiForm.description" />
            </el-form-item>
            <el-form-item :label="$t('soi.apiList.descriptionEn')" prop="descriptionEn">
              <el-input v-model="apiForm.descriptionEn" />
            </el-form-item>
            <el-form-item :label="$t('soi.apiList.targetUrl')" prop="targetUrl">
              <el-input v-model="apiForm.targetUrl" :placeholder="$t('soi.apiList.targetUrlTip')" />
            </el-form-item>
            <el-form-item :label="$t('soi.apiList.className')" prop="apiClassId">
              <el-select v-model="apiForm.apiClassId" @change="handlerQuerySubclass($event, false)">
                <el-option v-for="item in classList" :key="item.value" :label="['zh', 'cht'].includes(language) ? item.label : item.labelEn" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('soi.apiList.subclassName')" prop="apiSubClassId">
              <el-select v-model="apiForm.apiSubClassId">
                <el-option
                  v-for="item in subclassList"
                  :key="item.value"
                  :label="['zh', 'cht'].includes(language) ? item.label : item.labelEn"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('soi.apiList.image')" prop="file">
              <el-upload
                :action="url"
                :limit="1"
                :file-list="imageList"
                :data="{userID: userDetails.id}"
                :on-success="handlerOnSuccess"
                :on-change="handlerOnChange"
                list-type="picture-card"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </el-collapse-item>
          <el-card class="endpoint" style="height: auto;margin-bottom:50px;">
            <div slot="header" class="clearfix">
              <span>{{ $t("soi.apiList.endpointDesigner") }}</span>
              <el-button type="primary" size="mini" style="float:right;" @click="handleAddEndpoint">{{ $t("soi.apiList.addEndpoint") }}</el-button>
            </div>
            <div v-for="(data,index) in apiForm.endpoints" :key="index" style="margin-bottom:20px;">
              <el-form-item :label="$t('soi.apiList.endpoint')" prop="path">
                <el-input v-model="data.path" />
              </el-form-item>
              <el-form-item :label="$t('soi.apiList.description')" prop="description">
                <el-input v-model="data.description" />
              </el-form-item>
              <el-form-item :label="$t('soi.apiList.method')" prop="methodActions">
                <el-select v-model="data.method">
                  <el-option label="GET" value="GET" />
                  <el-option label="POST" value="POST" />
                  <el-option label="UPDATE" value="UPDATE" />
                  <el-option label="PUT" value="PUT" />
                  <el-option label="DELETE" value="DELETE" />
                  <el-option label="OPTIONS" value="OPTIONS" />
                </el-select>
              </el-form-item>
              <div class="text-center">
                <el-button size="small" type="primary" style="width:120px;" @click="handleDeleteEndpoint(index)">{{ $t("soi.common.delete") }}</el-button>
              </div>
            </div>
          </el-card>
          <el-card class="endpoint" style="height: auto;margin-bottom:50px;">
            <div slot="header" class="clearfix">
              <span>{{ $t("soi.apiList.apiIntroduction") }}</span>
            </div>
            <div style="width:700px;">
              <el-row :gutter="10">
                <el-col :span="4">
                  <span style="line-height: 32px">{{ $t("soi.apiList.userGuide") }}: </span>
                </el-col>
                <el-col :span="14">
                  <el-input v-model="attachUrl" size="small" :placeholder="$t('soi.apiList.urlOrFile')" />
                </el-col>
                <el-col :span="6">
                  <el-upload
                    :action="url"
                    :on-success="uploadSuccess"
                    :on-change="handleChange"
                    :file-list="fileList"
                    :data="{userID: userDetails.id}"
                  >
                    <el-button size="small" type="primary">{{ $t("soi.apiList.clickUpload") }}</el-button>
                  </el-upload>
                </el-col>
              </el-row>
            </div>
            <div class="markdown" style="margin-top: 20px">
              <Markdown v-model="mdValue" :toolbars="mdTools" />
            </div>
          </el-card>
        </el-collapse>
      </el-form>

      <el-card class="endpoint" style="height: auto;margin-bottom:50px;">
        <div slot="header" class="clearfix">
          <span>{{ $t("soi.apiList.swaggerSettings") }}</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <p class="text-danger">{{ $t('soi.apiList.swaggerJsonTipZh') }}</p>
            <div class="editor-container">
              <json-editor ref="jsonEditor" v-model="apiForm.value" @change="refreshSwaggerUI" />
            </div>
          </el-col>
          <el-col :span="12">
            <div style="height: auto;">
              <el-alert
                v-show="swaggerError"
                :title="swaggerError"
                type="error"
              />
              <div
                id="swagger"
                style="word-break: break-word;"
              />
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px">
          <el-col :span="12">
            <p class="text-danger">{{ $t('soi.apiList.swaggerJsonTipEn') }}</p>
            <div class="editor-container">
              <json-editor ref="jsonEditorEn" v-model="apiForm.valueEn" @change="refreshSwaggerUIEn" />
            </div>
          </el-col>
          <el-col :span="12">
            <div style="height: auto;">
              <el-alert
                v-show="swaggerErrorEn"
                :title="swaggerErrorEn"
                type="error"
              />
              <div
                id="swaggerEn"
                style="word-break: break-word;"
              />
            </div>
          </el-col>
        </el-row>
      </el-card>

      <div class="btn_group">
        <el-button type="primary" @click="handleSubmit">{{ $t("soi.apiList.editApi") }}</el-button>
      </div>

    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  editApi,
  getApiClassList,
  getApiDetails,
  getFiName,
  getSubclassList,
  getSwagger
} from '@/api/practice/api-market'
import { mapGetters } from 'vuex'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'
import Markdown from 'vue-meditor'
import SwaggerParser from '@apidevtools/swagger-parser'
import JsonEditor from '@/components/JsonEditor'
import SwaggerUI from 'swagger-ui'
import 'swagger-ui/dist/swagger-ui.css'

export default {
  name: 'PracticeManagementEditApi',
  components: { CustomizeCard, Markdown, JsonEditor },
  data() {
    const validateApiName = async(rule, value, callback) => {
      if (value) {
        callback()
      }
    }

    return {
      loading: false,
      fiName: '',
      apiId: this.$route.params.apiId,
      activeNames: ['1', '2', '3', '4'],
      classList: [],
      subclassList: [],
      attachUrl: '',
      imageList: [],
      fileList: [],
      mdValue: '',
      mdTools: {
        exportmd: false,
        importmd: false,
        clear: true
      },
      url: SOI_PLUS_BUSINESS_API_URL + '/v1/practical/document/upload',
      swaggerError: '',
      swaggerErrorEn: '',
      apiForm: {
        apiId: '',
        listenPath: '/',
        apiName: '',
        apiNameEn: '',
        targetUrl: '',
        description: '',
        descriptionEn: '',
        apiClassId: '',
        apiSubClassId: '',
        imageUrl: '',
        valueEn: {
          'swagger': '2.0',
          'info': {
            'version': '1.0',
            'title': 'ClassName Api_Name'
          },
          'host': 'innovation.cloud.tyk.io',
          'basePath': '/',
          'tags': [
            {
              'name': 'Default',
              'description': 'Download red framework',
              'externalDocs': {
                'url': 'http://abc.com',
                'description': 'Find out more'
              }
            }
          ],
          'schemes': ['http'],
          'paths': {
            '/downloadredurl/downloadurl': {
              'get': {
                'tags': ['Default'],
                'produces': ['application/json'],
                'parameters': [

                ],
                'responses': {
                  '200': {
                    'description': 'Status 200',
                    'schema': {
                      'type': 'object'
                    }
                  }
                }
              }
            }
          }
        },
        value: {
          'swagger': '2.0',
          'info': {
            'version': '1.0',
            'title': 'API 分类名称'
          },
          'host': 'innovation.cloud.tyk.io',
          'basePath': '/',
          'tags': [
            {
              'name': '默认',
              'description': '下载红色框架',
              'externalDocs': {
                'url': 'http://abc.com',
                'description': '了解更多信息'
              }
            }
          ],
          'schemes': ['http'],
          'paths': {
            '/downloadredurl/downloadurl': {
              'get': {
                'tags': ['默认'],
                'produces': ['application/json'],
                'parameters': [

                ],
                'responses': {
                  '200': {
                    'description': '状态 200',
                    'schema': {
                      'type': 'object'
                    }
                  }
                }
              }
            }
          }
        },
        endpoints: []
      },
      apiFormRules: {
        apiName: [
          {
            required: true,
            message: this.$t('soi.apiList.apiNameIsRequired'),
            trigger: 'blur'
          },
          {
            validator: validateApiName,
            trigger: 'blur'
          }
        ],
        apiNameEn: [
          {
            required: true,
            message: this.$t('soi.apiList.apiNameEnIsRequired'),
            trigger: 'blur'
          },
          {
            validator: validateApiName,
            trigger: 'blur'
          }
        ],
        targetUrl: [
          {
            required: true,
            message: this.$t('soi.apiList.targetUrlIsRequired'),
            trigger: 'blur'
          }
        ],
        value: [
          {
            required: true,
            message: this.$t('soi.apiList.valueIsRequired'),
            trigger: 'blur'
          }
        ],
        apiClassId: [
          {
            required: true,
            message: this.$t('soi.apiList.classNameIsRequired'),
            trigger: 'blur'
          }
        ],
        classSubName: [
          {
            required: true,
            message: this.$t('soi.apiList.subclassNameIsRequired'),
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'roles',
      'userDetails',
      'language'
    ])
  },
  watch: {
    'apiForm.value'(newValue) {
      if (typeof (newValue) === 'object') {
        this.refreshSwaggerUI(newValue)
      } else {
        this.refreshSwaggerUI(JSON.parse(newValue))
      }
    },
    'apiForm.valueEn'(newValue) {
      if (typeof (newValue) === 'object') {
        this.refreshSwaggerUIEn(newValue)
      } else {
        this.refreshSwaggerUIEn(JSON.parse(newValue))
      }
    }
  },
  mounted() {
    const apiInfo = JSON.parse(localStorage.getItem('edit-api-info'))

    this.apiForm.apiName = apiInfo.apiName
    this.apiForm.apiNameEn = apiInfo.apiNameEn
    this.apiForm.apiClassId = this.$route.params.classId
    this.handlerQuerySubclass(this.apiForm.apiClassId, true)
    this.getFiName()
    this.getClassList()
    this.getApiDetails()
    this.getSwagger()

    if (typeof (this.apiForm.value) === 'object') {
      this.refreshSwaggerUI(this.apiForm.value)
    } else {
      this.refreshSwaggerUI(JSON.parse(this.apiForm.value))
    }

    if (typeof (this.apiForm.valueEn) === 'object') {
      this.refreshSwaggerUIEn(this.apiForm.valueEn)
    } else {
      this.refreshSwaggerUIEn(JSON.parse(this.apiForm.valueEn))
    }
  },
  methods: {
    getApiDetails() {
      const vue = this
      vue.loading = true

      getApiDetails({ apiId: this.apiId })
        .then((res) => {
          const data = res.data
          vue.apiForm.targetUrl = data.proxy.target_url
          vue.apiForm.description = data.apiDescription
          vue.apiForm.descriptionEn = data.apiDescriptionEn
          vue.apiForm.listenPath = data.proxy.listen_path
          vue.apiForm.imageUrl = data.imageUrl

          if (vue.apiForm.imageUrl && vue.apiForm.imageUrl.length > 0) {
            vue.imageList.push({
              name: '',
              url: vue.apiForm.imageUrl
            })
          }

          vue.attachUrl = data.attachUrl
          if (vue.attachUrl && vue.attachUrl.length > 0) {
            vue.fileList.push({
              name: '',
              url: vue.attachUrl
            })
          }
          vue.mdValue = data.markdown || ''
          const pathList = []
          if (data.version_data.versions.Default.extended_paths.white_list !== undefined) {
            const whiteList = data.version_data.versions.Default.extended_paths.white_list
            if (!whiteList || whiteList.length !== 0) {
              for (let i = 0; i < whiteList.length; i++) {
                pathList.push(whiteList[i])
              }
            }
          } else if (data.version_data.versions.Default.extended_paths.ignored !== undefined) {
            const ignoreList = data.version_data.versions.Default.extended_paths.ignored
            if (!ignoreList || ignoreList.length !== 0) {
              for (let j = 0; j < ignoreList.length; j++) {
                pathList.push(ignoreList[j])
              }
            }
          }

          for (let z = 0; z < pathList.length; z++) {
            const obj = {}
            obj.path = pathList[z].path
            obj.description = pathList[z].endpointDescription
            for (const key in pathList[z].method_actions) {
              obj.method = key
            }
            vue.apiForm.endpoints.push(obj)
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    getSwagger() {
      const vue = this
      vue.loading = true
      getSwagger({ apiId: this.apiId })
        .then((res) => {
          const data = res.data
          if (data.zh && typeof data.zh !== 'object') {
            vue.apiForm.value = JSON.parse(data.zh)
          }
          if (data.en && typeof data.en !== 'object') {
            vue.apiForm.valueEn = JSON.parse(data.en)
          }
        })
        .finally(e => {
          vue.loading = false
        })
    },
    handleSubmit() {
      const vue = this
      vue.apiForm.apiId = this.apiId
      this.$refs['apiForm'].validate(valid => {
        if (valid) {
          if (typeof (vue.apiForm.value) !== 'object') {
            vue.apiForm.value = JSON.parse(vue.apiForm.value)
          }
          if (typeof (vue.apiForm.valueEn) !== 'object') {
            vue.apiForm.valueEn = JSON.parse(vue.apiForm.valueEn)
          }
          vue.apiForm.access = true
          vue.apiForm.swagger = vue.apiForm.value
          vue.apiForm.swaggerEn = vue.apiForm.valueEn
          vue.apiForm.markdown = vue.mdValue ? vue.mdValue : ''
          vue.apiForm.attachUrl = vue.attachUrl ? vue.attachUrl : ''

          if (vue.checkSwagger(vue.apiForm.value)) {
            return false
          }
          if (vue.checkSwagger(vue.apiForm.valueEn)) {
            return false
          }

          if (vue.apiForm.endpoints.length <= 0 || vue.apiForm.endpoints[0].path === '') {
            vue.apiForm.endpoints = []
          }

          vue.editApi()
        }
      })
    },
    editApi() {
      const vue = this
      vue.loading = true
      editApi(vue.apiForm)
        .then(() => {
          vue.$alert(vue.$t('soi.apiList.editSuccess'), vue.$t('soi.common.tip'), {
            confirmButtonText: vue.$t('soi.common.confirm'),
            callback: action => {
              vue.$router.go(-1)
            }
          })
        })
        .finally(() => {
          vue.loading = false
        })
    },
    checkSwagger(swagger) {
      const vue = this
      if (!swagger || typeof (swagger) !== 'object') {
        vue.$alert(vue.$t('soi.apiList.jsonErrorTip'), vue.$t('soi.common.tip'))
        return true
      }
    },
    getClassList() {
      const vue = this
      vue.loading = true
      getApiClassList({ fiEmail: this.userDetails.email })
        .then((res) => {
          const data = res.data
          for (let i = 0; i < data.length; i++) {
            vue.classList.push({
              label: data[i].className,
              labelEn: data[i].classNameEn,
              value: data[i].id
            })
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    handlerQuerySubclass(value, isFirst) {
      const vue = this
      this.loading = true
      vue.subclassList = []
      getSubclassList({ apiClassId: value })
        .then((res) => {
          const data = res.data
          for (let i = 0; i < data.length; i++) {
            vue.subclassList.push({
              label: data[i].classSubName,
              labelEn: data[i].classSubNameEn,
              value: data[i].id
            })
          }
          const subclassId = this.$route.params.subclassId
          if (isFirst) {
            this.apiForm.apiSubClassId = subclassId
          } else {
            if (vue.subclassList.length === 0) {
              vue.apiForm.apiSubClassId = ''
            } else {
              vue.apiForm.apiSubClassId = vue.subclassList[0].value
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    handleAddEndpoint() {
      const vue = this
      vue.apiForm.endpoints.push(
        {
          path: '',
          description: '',
          method: 'POST'
        }
      )
    },
    handleDeleteEndpoint(index) {
      const vue = this
      vue.apiForm.endpoints.splice(index, 1)
    },
    handlerOnSuccess(res) {
      const vue = this
      if (res.code === 20000 && res.data) {
        this.apiForm.imageUrl = res.data
        vue.$alert(vue.$t('soi.apiList.uploadSuccess'), vue.$t('soi.common.tip'), {
          confirmButtonText: vue.$t('soi.common.confirm'),
          callback: action => {}
        })
      } else {
        vue.$message({
          showClose: true,
          message: vue.$t('soi.apiList.uploadFailed'),
          type: 'error'
        })
      }
    },
    uploadSuccess(res) {
      const vue = this
      if (res.code === 20000 && res.data) {
        vue.attachUrl = res.data
        vue.$alert(vue.$t('soi.apiList.uploadSuccess'), vue.$t('soi.common.tip'), {
          confirmButtonText: vue.$t('soi.common.confirm'),
          callback: action => {}
        })
      } else {
        vue.$message({
          showClose: true,
          message: vue.$t('soi.apiList.uploadFailed'),
          type: 'error'
        })
      }
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-1)
      this.attachUrl = ''
    },
    handlerOnChange(file, fileList) {
      this.imageList = fileList.slice(-1)
      this.apiForm.imageUrl = ''
    },
    refreshSwaggerUI(data) {
      SwaggerParser.validate(data, (err, api) => {
        if (err) {
          this.swaggerError = err.message
        } else {
          this.swaggerError = ''
        }
      })
      SwaggerUI({
        dom_id: '#swagger',
        spec: data,
        defaultModelsExpandDepth: -1
      })
    },
    refreshSwaggerUIEn(data) {
      SwaggerParser.validate(data, (err, api) => {
        if (err) {
          this.swaggerErrorEn = err.message
        } else {
          this.swaggerErrorEn = ''
        }
      })
      SwaggerUI({
        dom_id: '#swaggerEn',
        spec: data,
        defaultModelsExpandDepth: -1
      })
    },
    getFiName() {
      const vue = this
      vue.loading = true
      if (!vue.roles.includes('Student') && !vue.roles.includes('Examinee')) {
        getFiName(vue.userDetails.email)
          .then((res) => {
            vue.fiName = res.data.finame
          })
          .finally(() => {
            vue.loading = false
          })
      } else {
        vue.fiName = vue.userDetails.email
      }
    }
  }
}
</script>

<style rel='stylesheet/scss' lang='scss'>
$bg: #fff;
$light_gray: #333;
$cursor: #333;
@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
    &::first-line {
      color: $light_gray;
    }
  }
}
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;
      &:-webkit-autofill {
        -webkit-box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>
<style rel='stylesheet/scss' lang='scss' scoped>
.text-center {
  text-align: center;
}
$bg: #FFF;
$dark_gray: #333;
$light_gray: #333;
.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }
  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;
    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }
  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }
  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
    .set-language {
      color: #fff;
      position: absolute;
      top: 5px;
      right: 0px;
    }
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }
  .el-upload__tip {
    color: #fff;
  }
  .el-checkbox {
    color: #fff;
  }
  .el-form-item {
    text-align: center;
  }
}
.demo-ruleForm{
  padding: 0px 100px;
}
.btn_group{
  margin: 20px;
  text-align: center;
}
// <= 768
@media screen and (max-width: 768px) {
  .el-message-box {
    display: inline-block;
    width: 350px;
    padding-bottom: 10px;
    vertical-align: middle;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    font-size: 18px;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    text-align: left;
    overflow: hidden;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  .demo-ruleForm{
    padding: 0px 0px;
  }
}
</style>
<style>
.editor-container {
  position: relative;
  height: 100%;
}
.form-block .el-collapse-item__header {
  font-size: 16px !important;
  background-color: #109eae;
  color: #fff;
  padding: 0 20px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.form-block {
  margin-bottom: 50px;
}
.form-block .el-collapse-item__content {
  padding: 10px 10px;
}
.endpoint .clearfix .el-button--primary{
  color: #109eae;
  background-color: #FFF;
  border-color: #FFF;
  margin: 0px;
}
.el-card__body {
  padding: 10px;
}
.swagger-ui table {
  width: 100%;
  word-break: break-word;
  padding: 0 10px;
  border-collapse: collapse;
}
</style>
