<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-import-api">
    <customize-card :title="$t('soi.router.importApi')">
      123
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'PracticeManagementImportApi',
  components: { CustomizeCard },
  data() {
    return {
      loading: false
    }
  }
}
</script>
