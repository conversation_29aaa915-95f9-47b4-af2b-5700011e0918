<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-document-management-container">
    <customize-card :title="$t('soi.router.documentManagement')" :show-back-btn="false">
      <template #header-actions>
        <!-- <el-button size="mini">{{ $t('soi.common.create') }} </el-button>-->
      </template>

      <el-alert :title="$t('soi.documentManagement.noteTip')" type="error" />

      <el-tabs v-model="activeName" tab-position="left" style="min-height: 200px;">
        <el-tab-pane
          v-for="(item, index) in listBusiness"
          :key="index"
          :name="index.toString()"
          :label="['zh', 'cht'].includes(language) ? item.name : item.nameEn"
        >
          <el-row :gutter="40" class="panel-group">
            <el-col v-for="(item2, index2) in item.list" :key="index2" :span="6" :xs="24">
              <el-card shadow="hover" class="card-list text-center">
                <div class="text-center">
                  <h4>{{ ['zh', 'cht'].includes(language) ? item2.className : item2.classNameEn }}</h4>
                  <el-button size="small" type="primary" @click="handleSetLineChartData(item2)">
                    {{ $t('soi.common.view') }}
                  </el-button>
                </div>
                <div class="card-icon">
                  <i class="el-icon-edit" @click="handleEditData(item2)" />
                  <i class="el-icon-delete" @click="handleDeleteData(item2)" />
                </div>
              </el-card>
            </el-col>
            <el-col :span="6" :xs="24">
              <el-card shadow="hover" class="card-list card-add" @click.native="addClass(item)">
                <i class="el-icon-plus" />
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </customize-card>

    <!-- 创建分类对话框 -->
    <el-dialog
      :title="$t('soi.documentManagement.createCategory')"
      :visible.sync="createCategoryDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      destroy-on-close
      width="40%"
    >
      <el-form ref="categoryForm" :model="categoryForm" :rules="categoryRules" size="small" label-width="120px" label-position="left">
        <el-row :gutter="30">
          <el-col :span="24">
            <!-- 分组中文名称 -->
            <el-form-item :label="$t('soi.documentManagement.groupName')" prop="groupName">
              <el-input v-model="categoryForm.groupName" :placeholder="$t('soi.documentManagement.groupNamePlaceholder')" maxlength="64" clearable disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 分组英文名称 -->
            <el-form-item :label="$t('soi.documentManagement.groupNameEn')" prop="groupNameEn">
              <el-input v-model="categoryForm.groupNameEn" :placeholder="$t('soi.documentManagement.groupNameEnPlaceholder')" maxlength="64" clearable disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 分组中文名称 -->
            <el-form-item :label="$t('soi.documentManagement.className')" prop="className">
              <el-input v-model="categoryForm.className" :placeholder="$t('soi.documentManagement.classNamePlaceholder')" maxlength="64" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 分组英文名称 -->
            <el-form-item :label="$t('soi.documentManagement.classNameEn')" prop="classNameEn">
              <el-input v-model="categoryForm.classNameEn" :placeholder="$t('soi.documentManagement.classNameEnPlaceholder')" maxlength="64" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 操作按钮 -->
            <el-form-item class="bottom-actions-button-group">
              <el-button @click="createCategoryDialogVisible = false">{{ $t('soi.common.cancel') }}</el-button>
              <el-button type="primary" @click="saveClass()">
                {{ $t('soi.common.confirm') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <!-- 创建分类对话框 -->
    <el-dialog
      :title="$t('soi.documentManagement.editCategory')"
      :visible.sync="editCategoryDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      destroy-on-close
      width="40%"
    >
      <el-form ref="editCategoryForm" :model="editCategoryForm" :rules="categoryRules" size="small" label-width="120px" label-position="left">
        <el-row :gutter="30">
          <el-col :span="24">
            <!-- 分组中文名称 -->
            <el-form-item :label="$t('soi.documentManagement.groupName')" prop="groupName">
              <el-input v-model="editCategoryForm.groupName" :placeholder="$t('soi.documentManagement.groupNamePlaceholder')" maxlength="64" clearable disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 分组英文名称 -->
            <el-form-item :label="$t('soi.documentManagement.groupNameEn')" prop="groupNameEn">
              <el-input v-model="editCategoryForm.groupNameEn" :placeholder="$t('soi.documentManagement.groupNameEnPlaceholder')" maxlength="64" clearable disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 分组中文名称 -->
            <el-form-item :label="$t('soi.documentManagement.className')" prop="className">
              <el-input v-model="editCategoryForm.className" :placeholder="$t('soi.documentManagement.classNamePlaceholder')" maxlength="64" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 分组英文名称 -->
            <el-form-item :label="$t('soi.documentManagement.classNameEn')" prop="classNameEn">
              <el-input v-model="editCategoryForm.classNameEn" :placeholder="$t('soi.documentManagement.classNameEnPlaceholder')" maxlength="64" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <!-- 操作按钮 -->
            <el-form-item class="bottom-actions-button-group">
              <el-button @click="editCategoryDialogVisible = false">{{ $t('soi.common.cancel') }}</el-button>
              <el-button type="primary" @click="editClass()">
                {{ $t('soi.common.confirm') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { deleteCategory, getAllUserCase, saveCategory } from '@/api/practice/user-case'

export default {
  name: 'PracticeManagementDocumentManagement',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      createCategoryDialogVisible: false,
      editCategoryDialogVisible: false,
      activeName: '',
      listBusiness: [],
      categoryForm: {
        groupName: '',
        groupNameEn: '',
        className: '',
        classNameEn: ''
      },
      editCategoryForm: {
        id: '',
        groupName: '',
        groupNameEn: '',
        className: '',
        classNameEn: ''
      },
      categoryRules: {
        groupName: [
          { required: true, trigger: 'blur', message: this.$t('soi.documentManagement.groupNameIsRequired') }
        ],
        groupNameEn: [
          { required: true, trigger: 'blur', message: this.$t('soi.documentManagement.groupNameEnIsRequired') }
        ],
        className: [
          { required: true, trigger: 'blur', message: this.$t('soi.documentManagement.classNameIsRequired') }
        ],
        classNameEn: [
          { required: true, trigger: 'blur', message: this.$t('soi.documentManagement.classNameEnIsRequired') }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userDetails',
      'language'
    ])
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      const _this = this
      _this.loading = true
      getAllUserCase()
        .then(res => {
          _this.listBusiness = []
          for (const key in res.data) {
            _this.listBusiness.push({
              name: res.data[key][0].groupName,
              nameEn: res.data[key][0].groupNameEn,
              list: res.data[key]
            })
          }
        }).finally(() => {
          _this.loading = false
        })
    },
    handleSetLineChartData(data) {
      this.$router.push({ name: 'practice-management-document-list', params: { classId: data.id }})
    },
    handleEditData(data) {
      this.categoryForm.id = data.id
      this.categoryForm.groupName = data.groupName
      this.categoryForm.groupNameEn = data.groupNameEn
      this.categoryForm.className = data.className
      this.categoryForm.classNameEn = data.classNameEn

      this.createCategoryDialogVisible = true
    },
    handleDeleteData(data) {
      const vue = this
      this.$confirm(vue.$t('soi.documentManagement.deleteCategoryTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      })
        .then(() => {
          const _this = this
          _this.loading = true
          deleteCategory({ id: data.id })
            .then(res => {
              _this.$message.success(res.message)

              _this.getData()
            }).finally(() => {
              _this.loading = false
            })
        })
        .catch(() => {})
    },
    addClass(row) {
      this.categoryForm.groupName = row.name
      this.categoryForm.groupNameEn = row.nameEn

      this.createCategoryDialogVisible = true
    },
    editClass() {
      this.$refs.editCategoryForm.validate(valid => {
        if (valid) {
          const _this = this
          _this.loading = true
          saveCategory(_this.editCategoryForm)
            .then(res => {
              _this.$message.success(res.message)
              _this.createCategoryDialogVisible = false

              _this.getData()
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    },
    saveClass() {
      this.$refs.categoryForm.validate(valid => {
        if (valid) {
          const _this = this
          _this.loading = true
          saveCategory(_this.categoryForm)
            .then(res => {
              _this.$message.success(res.message)
              _this.createCategoryDialogVisible = false

              _this.getData()
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .text-center {
    text-align: center;
  }

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: left;
      font-weight: bold;
      // margin: 26px;
      // margin-left: 0px;
      .card-panel-text {
        // line-height: 18px;
        // color: rgba(0, 0, 0, 0.45);
        // font-size: 16px;
        // margin-bottom: 12px;
        // width: 100%;
        // word-break: break-all;
        line-height: 108px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-left: 20px;
        word-break: break-all;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

.card-list {
  position: relative;
  height: 110px;
  margin-bottom: 20px;
  border: 1px solid #ccc !important;
  box-shadow: 5px 5px 5px #ccc;

  .card-img {
    text-align: center;

    img {
      width: 80%;
      height: auto;
      vertical-align: middle;
    }
  }

  h4 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 24px;
    margin: 0 0 10px 0;
  }

  p {
    height: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .card-icon {
    position: absolute;
    top: 10px;
    right: 20px;
    cursor: pointer;

    i {
      margin-left: 10px;
    }
  }
}

.card-add {
  cursor: pointer;
  text-align: center;
  line-height: 70px;
  font-size: 60px;
  vertical-align: middle;
  color: #999;
}

.btn-group {
  float: right;
}

// <= 768
@media screen and (max-width: 768px) {
  .el-button + .el-button {
    margin-left: 0 !important;
    margin-top: 10px;
  }
  .btn-group {
    .el-button {
      width: 100%;
    }
  }
}
</style>
<style>
.el-message-box {
  display: inline-block;
  width: 350px;
  padding-bottom: 10px;
  vertical-align: middle;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  font-size: 18px;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: left;
  overflow: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.worksheet .el-card__body {
  padding: 23px 10px;
}

.worksheet .clearfix .el-button--primary {
  color: #109eae;
  background-color: #fff;
  border-color: #fff;
}

.el-tooltip__popper.is-dark {
  width: 500px;
}
</style>
