<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-tc-management-container">
    <customize-card :title="$t('soi.router.tcManagement')" :show-back-btn="false">
      <div style="margin: 20px auto;width: 100%;">
        <quill-editor ref="quillEditor" v-model="tc.content" :options="editorOption" />
      </div>
      <div style="text-align: right">
        <el-button type="primary" size="small" @click="handlerSubmit()">{{ $t("soi.common.submit") }}</el-button>
      </div>

    </customize-card>
  </div>
</template>

<script>
import { quillEditor } from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getTc, updateTC } from '@/api/system/tc'

export default {
  name: 'PracticeManagementTCManagement',
  components: { CustomizeCard, quillEditor },
  data() {
    return {
      loading: false,
      tc: {
        id: '',
        content: ''
      },
      editorOption: {
        theme: 'snow',
        boundary: document.body,
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'],
            ['code-block'],
            [{ 'header': 1 }, { 'header': 2 }],
            [{ 'list': 'ordered' }, { 'list': 'bullet' }],
            [{ 'script': 'sub' }, { 'script': 'super' }],
            [{ 'indent': '-1' }, { 'indent': '+1' }],
            [{ 'direction': 'rtl' }],
            [{ 'size': ['small', false, 'large', 'huge'] }],
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'font': [] }],
            [{ 'align': [] }],
            ['link', 'image', 'video']
          ]
        }
      }
    }
  },
  mounted() {
    this.getTC()
  },
  methods: {
    getTC() {
      const vue = this
      this.loading = true
      getTc()
        .then(res => {
          vue.tc = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerSubmit() {
      const vue = this
      this.loading = true

      updateTC(vue.tc)
        .then(res => {
          vue.$message.success(res.message)
        }).finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
