<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-e-kyc-workflow-details-container">
    <customize-card :title="$t('soi.router.eKYCWorkflowDetails')">
      <h3>{{ $t("soi.workflowConfig.kycAccountOpeningProcess") }}</h3>
      <el-table
        :data="tableDataAPI"
        style="width: 100%"
        highlight-current-row
        :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
      >
        <el-table-column
          prop="processGroup"
          :label="$t('soi.workflowConfig.processGroup')"
          min-width="200"
        />
        <el-table-column
          :label="$t('soi.workflowConfig.process')"
          min-width="200"
        >
          <template slot-scope="scope"><div v-html="scope.row.process" /></template>
        </el-table-column>
      </el-table>

      <h3>{{ $t("soi.workflowConfig.accountOpeningProcessNodeRules") }}</h3>
      <el-table :data="tableData" size="mini" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
        <el-table-column
          v-for="(key, index) in tableHeader"
          :key="index"
          :prop="key"
          :label="key"
        >
          <template slot-scope="scope">
            <el-row :gutter="10">
              <el-col v-if="typeof(scope.row[key]) === 'boolean'" :span="24">
                <el-checkbox v-model="scope.row[key]" size="mini" disabled>
                  <div style="white-space: pre-wrap;">{{ scope.row.Name }}</div>
                </el-checkbox>
              </el-col>
              <el-col v-if="typeof(scope.row[key]) === 'string'" :span="24">
                <span
                  style="color: #777777"
                >
                  {{ scope.row[key] }}
                </span>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>

      <h3>{{ $t("soi.workflowConfig.accountOpeningProcessNodeList") }}</h3>
      <el-table
        class="params-table"
        :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
        :data="tableDataNode"
      >
        <el-table-column type="index" label="#" />
        <el-table-column prop="api_name" :label="$t('soi.workflowConfig.name')" align="center" />
        <el-table-column prop="group" :label="$t('soi.workflowConfig.group')" align="center" />
        <el-table-column prop="api_url" :label="$t('soi.workflowConfig.apiUrl')" />
        <el-table-column prop="api_method" :label="$t('soi.workflowConfig.apiMethod')" align="center" />
      </el-table>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { getFlows, getRules } from '@/api/system/workflow'

export default {
  name: 'PracticeManagementEKYCWorkflowDetails',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      tableDataAPI: [
        {
          processGroup: 'Customer Profile Creation',
          process: 'Checking existing customer<br/>Create prospect customer'
        },
        {
          processGroup: 'Screening',
          process: 'Check Blacklist<br/>Check Political Exposed Person<br/>Check Sanction'
        },
        {
          processGroup: 'ID Verification',
          process: 'Verify ID Document<br/>Verify Address<br/>Verify Biometrics'
        },
        {
          processGroup: 'Global check',
          process: 'PEP/FATCA/CRS'
        },
        {
          processGroup: 'Account Onboarding',
          process: 'Account Opening'
        },
        {
          processGroup: 'e-Document',
          process: 'Acknowledge T&C<br/>Capture Signature'
        },
        {
          processGroup: 'Channel Enablement',
          process: 'Set Pin'
        }
      ],
      tableHeader: [],
      tableData: [],
      tableDataNode: []
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.getAllWorkflowNode()
    this.getWorkflowById()
  },
  methods: {
    getAllWorkflowNode() {
      const _this = this
      _this.loading = true
      getFlows({ group: 'eKYC' })
        .then(response => {
          _this.tableDataNode = response.data
        })
        .finally(() => {
          _this.loading = false
        })
    },
    getWorkflowById() {
      const _this = this
      _this.loading = true
      getRules()
        .then(response => {
          _this.responseData = JSON.parse(response.data[0].json)
          _this.processingResponseData()
        })
        .finally(() => {
          _this.loading = false
        })
    },
    processingResponseData() {
      const keys = []
      for (const key in this.responseData) {
        keys.push(key)
      }

      const tableData = new Array(keys.length)
      for (let i = 0; i < tableData.length; i++) {
        tableData[i] = { Name: keys[i] }
      }
      for (let i = 0; i < keys.length; i++) {
        for (let j = 0; j < keys.length; j++) {
          if (this.responseData[keys[j]][i] !== undefined) {
            tableData[i][keys[j]] = this.responseData[keys[j]][i][keys[i]]
          }
        }
      }

      this.tableHeader = ['Name'].concat(keys)
      this.tableData = tableData
    }
  }
}
</script>
