<template>
  <div class="create-permission-container">
    <permission-form
      :permission-form.sync="permissionForm"
      :permission-options="permissionOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="createPermission()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import PermissionForm from '@/views/admin/system/permissions/PermissionForm.vue'
import { createPermission } from '@/api/system/permission'
import { permissionOptions } from '@/data'

export default {
  name: 'CreatePermission',
  components: { PermissionForm },
  data() {
    return {
      confirmButtonLoading: false,
      permissionForm: {
        parentId: '',
        permissionName: '',
        accredit: '',
        sortNumber: 1
      },
      permissionOptions: []
    }
  },
  async mounted() {
    // 加载权限列表
    this.permissionOptions = await permissionOptions(true)
  },
  methods: {
    // 调用API创建权限
    createPermission() {
      this.confirmButtonLoading = true
      const requestData = { ...this.permissionForm }
      createPermission(requestData)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.create-permission-container {
  padding: 0 20px;
}
</style>
