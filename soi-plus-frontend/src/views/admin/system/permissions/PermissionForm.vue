<template>
  <!-- 权限表单 -->
  <el-form ref="permissionForm" :model="permissionForm" :rules="permissionRules" size="small" label-width="145px" label-position="left">
    <el-row :gutter="30">

      <el-col :span="12">
        <!-- 上级权限 -->
        <el-form-item :label="$t('soi.permission.parentId')" prop="parentId">
          <el-cascader
            v-model="permissionForm.parentId"
            :options="permissionOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'permissionName', emitPath: false }"
            :placeholder="$t('soi.permission.parentIdPlaceholder')"
            class="select-item"
            :show-all-levels="false"
            clearable
          />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <!-- 权限名称 -->
        <el-form-item :label="$t('soi.permission.permissionName')" prop="permissionName">
          <el-input v-model="permissionForm.permissionName" :placeholder="$t('soi.permission.permissionNamePlaceholder')" maxlength="64" clearable />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <!-- 权限标识 -->
        <el-form-item :label="$t('soi.permission.accredit')" prop="accredit">
          <el-input v-model="permissionForm.accredit" :placeholder="$t('soi.permission.accreditPlaceholder')" maxlength="64" clearable />
        </el-form-item>
      </el-col>

      <el-col :span="12">
        <!-- 排序编号 -->
        <el-form-item :label="$t('soi.permission.sortNumber')" prop="sortNumber">
          <el-input-number
            v-model="permissionForm.sortNumber"
            :placeholder="$t('soi.permission.sortNumberPlaceholder')"
            :min="1"
            :max="99"
            :step="1"
            step-strictly
            class="input-number-item"
          />
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <!-- 操作按钮 -->
        <el-form-item class="bottom-actions-button-group">
          <el-button @click="cancel()">{{ $t('soi.common.cancel') }}</el-button>
          <el-button @click="resetForm('permissionForm')">{{ $t('soi.common.reset') }}</el-button>
          <el-button type="primary" :loading="confirmButtonLoading" @click="validateForm('permissionForm')">
            {{ $t('soi.common.confirm') }}
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

export default {
  name: 'PermissionForm',
  props: {
    permissionForm: {
      type: Object,
      required: true
    },
    confirmButtonLoading: {
      type: Boolean,
      required: false,
      default: false
    },
    permissionOptions: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      // 校验规则
      permissionRules: {
        parentId: [
          { required: true, trigger: 'change', message: this.$t('soi.permission.parentIdIsRequired') }
        ],
        permissionName: [
          { required: true, trigger: 'blur', message: this.$t('soi.permission.permissionNameIsRequired') }
        ],
        accredit: [
          { required: true, trigger: 'blur', message: this.$t('soi.permission.accreditIsRequired') }
        ],
        sortNumber: [
          { required: true, trigger: 'blur', message: this.$t('soi.permission.sortNumberIsRequired') }
        ]
      }
    }
  },
  methods: {
    // 验证表单
    validateForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 表单校验成功
          this.$emit('validated')
        }
      })
    },
    // 取消创建
    cancel() {
      this.$emit('cancel')
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.bottom-actions-button-group {
  text-align: right;
}
.select-item, .input-number-item {
  width: 100%;
}
</style>
