<template>
  <div v-loading="loading" class="app-container permission-management-container">
    <customize-card :title="$t('soi.router.permissionManagement')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 权限名称 -->
        <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
          <el-input v-model="searchForm.keywords" :placeholder="$t('soi.permission.keywordsPlaceholder')" clearable />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getPermissionList()">{{ $t('soi.common.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreatePermissionDialog()">{{ $t('soi.common.create') }}</el-button>
          <el-button size="small" icon="el-icon-delete" type="danger" plain :disabled="!multipleSelection.length" @click="batchDeletePermission()">{{ $t('soi.common.delete') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getPermissionList()" />
      </div>

      <!-- 权限表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="permissionList" row-key="id" stripe :tree-props="{children: 'children', hasChildren: 'hasChildren'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55px" />
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('soi.permission.permissionName')" prop="permissionName" align="left" />
          <el-table-column :label="$t('soi.permission.sortNumber')" prop="sortNumber" align="center" />
          <el-table-column :label="$t('soi.permission.accredit')" prop="accredit" align="left" />
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center" width="193px">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdatePermissionDialog(scope.row.id)">{{ $t('soi.common.edit') }}</el-button>
              <el-button type="text" size="mini" text icon="el-icon-delete" @click="batchDeletePermission([scope.row.id])">{{ $t('soi.common.delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </customize-card>

    <!-- 创建权限的对话框 -->
    <el-dialog
      :title="$t('soi.permission.createPermission')"
      :visible.sync="createPermissionDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <create-permission :key="createKey" @success="handlerCreatePermissionSuccess()" @cancel="closeCreatePermissionDialog()" />
    </el-dialog>

    <!-- 更新权限的对话框 -->
    <el-dialog
      :title="$t('soi.permission.editPermission')"
      :visible.sync="updatePermissionDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <!-- key的作用: 重新刷新子组件 -->
      <update-permission
        :key="updateKey"
        :permission-id="updatePermissionId"
        @success="handlerUpdatePermissionSuccess()"
        @cancel="closeUpdatePermissionDialog()"
      />
    </el-dialog>
  </div>
</template>

<script>
import CreatePermission from '@/views/admin/system/permissions/CreatePermission.vue'
import UpdatePermission from '@/views/admin/system/permissions/UpdatePermission.vue'
import { statusOptions } from '@/data'
import { batchDeletePermission, getPermissionList } from '@/api/system/permission'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'PermissionManagement',
  components: { CustomizeCard, UpdatePermission, CreatePermission },
  data() {
    return {
      // 页面loading
      loading: false,
      // 表格loading
      tableLoading: false,
      createKey: '',
      updateKey: '',
      // 状态选项
      statusOptions,
      // 分页查询请求参数
      searchForm: {
        keywords: ''
      },
      // 权限列表
      permissionList: [],
      // 创建权限的对话框标记：true 显示，false 隐藏
      createPermissionDialogVisible: false,
      // 更新权限的对话框标记：true 显示，false 隐藏
      updatePermissionDialogVisible: false,
      updatePermissionId: '',
      multipleSelection: []
    }
  },
  mounted() {
    this.getPermissionList()
  },
  methods: {
    // 删除权限
    batchDeletePermission(permissionIds) {
      this.$confirm(this.$t('soi.permission.deletePermissionPrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedPermissionIds = permissionIds

        if (!deletedPermissionIds || !deletedPermissionIds.length) {
          deletedPermissionIds = this.multipleSelection.map((permission) => permission.id)
        }

        this.loading = true
        batchDeletePermission(deletedPermissionIds)
          .then((res) => {
            // 提示成功
            this.$message.success(res.message)

            // 重新加载权限列表
            this.getPermissionList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 更新权限成功
    handlerUpdatePermissionSuccess() {
      this.updatePermissionDialogVisible = false
      this.getPermissionList()
    },
    // 打开更新权限对话框
    openUpdatePermissionDialog(permissionId) {
      this.updatePermissionId = permissionId
      this.updateKey = String(new Date().getTime())
      this.updatePermissionDialogVisible = true
    },
    // 关闭更新权限对话框
    closeUpdatePermissionDialog() {
      this.updatePermissionDialogVisible = false
    },
    // 创建权限成功
    handlerCreatePermissionSuccess() {
      this.createPermissionDialogVisible = false
      this.getPermissionList()
    },
    // 调用API分页查询权限列表
    getPermissionList() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (this.searchForm.dateRange) {
        requestData.startTime = this.searchForm.dateRange[0]
        requestData.endTime = this.searchForm.dateRange[1]
      }

      delete requestData.dateRange

      getPermissionList(requestData)
        .then((res) => {
          const { data: permissionList } = res

          this.permissionList = permissionList
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 打开创建权限对话框
    openCreatePermissionDialog() {
      this.createKey = String(new Date().getTime())
      this.createPermissionDialogVisible = true
    },
    // 关闭创建权限对话框
    closeCreatePermissionDialog() {
      this.createPermissionDialogVisible = false
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 保存已选中的权限记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>

<style scoped lang="scss">
.permission-management-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
