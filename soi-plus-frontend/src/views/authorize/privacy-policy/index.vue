<template>
  <div class="authorize-privacy-policy-container">
    <div class="privacy-policy" v-html="content" />
    <div style="text-align: center">
      <div class="text-center">
        <el-checkbox v-model="checked">
          {{ $t('soi.authorize.agreeEndUserPrivacyPolicy') }}
        </el-checkbox>
      </div>
      <el-button
        class="register-button"
        type="primary"
        size="small"
        @click.native.prevent="handleRegister"
      >{{ $t('soi.authorize.register') }}</el-button>
    </div>

  </div>
</template>

<script>
import { getTc } from '@/api/system/tc'

export default {
  name: 'PrivacyPolicy',
  data() {
    return {
      content: '',
      checked: false
    }
  },
  mounted() {
    this.getTC()
  },
  methods: {
    getTC() {
      const vue = this
      this.loading = true
      getTc()
        .then(res => {
          vue.content = res.data.content
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleRegister() {
      if (!this.checked) {
        this.$alert(
          this.$t('soi.authorize.agreePrivacyPolicyTip'),
          this.$t('soi.common.error'),
          {
            confirmButtonText: this.$t('soi.common.confirm')
          }
        )
        return false
      }
      this.$router.push({ path: '/authorize/register' })
    }
  }
}
</script>

<style lang="scss" scoped>
$bg: #fff;
$width: 1100px;

.authorize-privacy-policy-container {
  width: $width;
  margin: 0 auto;
  padding-bottom: 50px;
  text-align: left;

  .privacy-policy {
    padding: 0 100px;
    margin: 30px auto;
    font-size: 14px;
    line-height: 1.5;
  }

  .register-button {
    width: 200px;
    margin-top: 15px;
  }
}
</style>
