<template>
  <div v-loading="loading">
    <customize-card :title="$t('soi.homework.homework')">
      <el-tabs v-model="activeName">
        <!-- Homework list -->
        <el-tab-pane :label="$t('soi.homework.homeworkList')" name="first">
          <div>
            <!-- 搜索表单 -->
            <el-form ref="homeworkForm" :model="homeworkForm" size="small" inline>
              <!-- 题干内容 (关键词模糊搜索)-->
              <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
                <el-input
                  v-model="homeworkForm.keywords"
                  :placeholder="$t('soi.homework.keywordsPlaceholder')"
                  clearable
                  @change="getQuestionBankList"
                />
              </el-form-item>

              <!-- 课程大分类列表 -->
              <el-form-item
                :label="$t('soi.courseCategory.courseCategory')"
                prop="courseCategoryId"
              >
                <el-select
                  v-model="homeworkForm.courseCategoryId"
                  :placeholder="$t('soi.courseCategory.keywordsPlaceholder')"
                  clearable
                  @change="loadCurrentCourseList($event)"
                >
                  <el-option
                    v-for="item in courseCategoryOptions"
                    :key="item.id"
                    :label="item.courseCategory"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <!-- 课程列表 -->
              <el-form-item :label="$t('soi.course.courseName')" prop="courseCode">
                <el-select
                  v-model="homeworkForm.courseCode"
                  :placeholder="$t('soi.course.keywordsPlaceholder')"
                  clearable
                  @change="loadCurrentSubjectList($event)"
                >
                  <el-option
                    v-for="item in currentCourseOptions"
                    :key="item.id"
                    :label="item.courseName"
                    :value="item.courseCode"
                  />
                </el-select>
              </el-form-item>

              <!-- 课题列表 -->
              <el-form-item :label="$t('soi.subject.subjectName')" prop="subjectCode">
                <el-select
                  v-model="homeworkForm.subjectCode"
                  :placeholder="$t('soi.subject.keywordsPlaceholder')"
                  clearable
                >
                  <el-option
                    v-for="item in currentSubjectOptions"
                    :key="item.id"
                    :label="item.subjectName"
                    :value="item.subjectCode"
                  />
                </el-select>
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="getQuestionBankList()">
                  {{ $t('soi.common.search') }}
                </el-button>
                <el-button icon="el-icon-refresh" @click="resetForm('homeworkForm')">{{ $t('soi.common.reset') }}</el-button>
                <!--学生角色暂时不需要删除功能-->
                <!--<el-button
                  icon="el-icon-delete"
                  type="danger"
                  :disabled="!multipleSelection.length"
                  @click="batchDeleteHomeworkList()"
                >
                  {{ $t('soi.common.delete') }}
                </el-button>-->
              </el-form-item>

              <!-- 作业问题表格 -->
              <el-card shadow="never">
                <el-table
                  v-loading="tableLoading"
                  :data="questionBankList"
                  stripe
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="50px" />
                  <el-table-column type="index" label="#" align="center" />
                  <el-table-column
                    :label="$t('soi.courseCategory.courseCategory')"
                    prop="courseCategory"
                    show-overflow-tooltip
                    align="center"
                  />
                  <el-table-column :label="$t('soi.course.courseName')" prop="courseName" align="center" />
                  <el-table-column :label="$t('soi.subject.subjectName')" prop="subjectName" align="center" />
                  <el-table-column
                    :label="$t('soi.homework.questionContent')"
                    prop="questionContent"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="light" placement="top-start">
                        <div slot="content" style="width: 500px">{{ scope.row.questionContent }}</div>
                        <span class="table-content">{{ scope.row.questionContent }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <!--隐藏试题标准答案-->
                  <!--<el-table-column
                    :label="$t('soi.homework.questionModelanswer')"
                    prop="questionModelanswer"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="light" placement="top-start">
                        <div slot="content" style="width: 500px">{{ scope.row.questionModelanswer }}</div>
                        <span class="table-content">{{ scope.row.questionModelanswer }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>-->
                  <el-table-column :label="$t('soi.common.creator')" prop="creator" align="center" />
                  <el-table-column :label="$t('soi.common.updateBy')" prop="lastUpdateCreator" align="center" />
                  <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
                  <el-table-column :label="$t('soi.common.updateTime')" prop="lastUpdateTime" align="center" />
                  <el-table-column :label="$t('soi.common.operate')" align="center">
                    <template slot-scope="scope">
                      <el-button type="text" size="mini" text @click="openQuestionDetailDialog(scope.row)">
                        {{ $t('soi.common.view') }}
                      </el-button>
                      <!--学生角色暂时不需要编辑功能-->
                      <!--<el-button type="text" size="small" text @click="editQuestionDialog(scope.row.id)">
                        {{ $t('soi.common.edit') }}
                      </el-button>-->
                      <!--学生角色暂时不需要删除功能-->
                      <!--<el-button
                        type="text"
                        size="small"
                        text
                        icon="el-icon-delete"
                        @click="batchDeleteHomeworkList([scope.row.id])"
                      >{{ $t('soi.common.delete') }}
                      </el-button>-->
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('soi.homework.homework')" align="center" width="100px">
                    <template slot-scope="scope">
                      <el-button type="text" size="small" text @click="answerQuestionDialog(scope.row.id)">
                        {{ $t('soi.common.start') }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-form>
          </div>
          <div class="table-footer">
            <el-pagination
              :current-page.sync="homeworkForm.currentPage"
              :page-sizes="[20, 50, 100, 500, 1000]"
              :page-size.sync="homeworkForm.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="getQuestionBankList()"
              @current-change="getQuestionBankList()"
            />
          </div>

          <!-- 编辑试题对话框 学生角色暂不需要编辑功能，因此暂不需要编辑试题对话框 -->
          <!--<el-dialog
            :title="$t('soi.homework.editQuestion')"
            :visible.sync="editQuestionDialogVisible"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            width="30%"
          >
            <el-form :model="editQuestionDetails" label-width="auto">
              <el-form-item :label="$t('soi.courseCategory.courseCategory')">
                <el-col :offset="1" :span="10">
                  <el-input
                    v-model="editQuestionDetails.courseCategory"
                    :disabled="true"
                  />
                </el-col>
              </el-form-item>
              <el-form-item :label="$t('soi.course.courseName')">
                <el-col :offset="1" :span="10">
                  <el-input
                    v-model="editQuestionDetails.courseName"
                    :disabled="true"
                  />
                </el-col>
              </el-form-item>
              <el-form-item :label="$t('soi.subject.subjectName')">
                <el-col :offset="1" :span="10">
                  <el-input
                    v-model="editQuestionDetails.subjectName"
                    :disabled="true"
                  />
                </el-col>
              </el-form-item>
              <el-form-item :label="$t('soi.homework.questionContent')">
                <el-col :offset="1" :span="20">
                  <el-input
                    v-model="editQuestionDetails.questionContent"
                    type="textarea"
                    :autosize="true"
                  />
                </el-col>
              </el-form-item>
              <el-form-item :label="$t('soi.homework.questionModelanswer')">
                <el-col :offset="1" :span="20">
                  <el-input
                    v-model="editQuestionDetails.questionModelanswer"
                    type="textarea"
                    :autosize="true"
                  />
                </el-col>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="updateQuestionBank">{{ $t('soi.common.confirm') }}</el-button>
              <el-button type="primary" @click="editQuestionDialogVisible = false">{{ $t('soi.common.cancel') }}</el-button>
            </div>
          </el-dialog>-->
          <!-- view试题详情的对话框 -->
          <el-dialog
            :title="$t('soi.homework.questionDetail')"
            :visible.sync="questionDetailDialogVisible"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            width="50%"
          >
            <el-descriptions
              v-if="questionDetailShow"
              class="margin-top"
              :column="1"
              border
              :label-style="{width:'30%'}"
            >
              <!-- 课程大分类名称 -->
              <el-descriptions-item :label="$t('soi.courseCategory.courseCategory')" prop="courseCategory">
                {{ questionDetail.courseCategory }}
              </el-descriptions-item>

              <!-- 课程名称 -->
              <el-descriptions-item :label="$t('soi.course.courseName')" prop="courseName">
                {{ questionDetail.courseName }}
              </el-descriptions-item>

              <!-- 课题名称 -->
              <el-descriptions-item :label="$t('soi.subject.subjectName')" prop="subjectName">
                {{ questionDetail.subjectName }}
              </el-descriptions-item>

              <!-- 试题内容 -->
              <el-descriptions-item :label="$t('soi.homework.questionContent')" prop="questionContent">
                {{ questionDetail.questionContent }}
              </el-descriptions-item>

              <!-- 试题标准答案 -->
              <el-descriptions-item :label="$t('soi.homework.questionModelanswer')" prop="questionModelanswer">
                {{ questionDetail.questionModelanswer }}
              </el-descriptions-item>
            </el-descriptions>
          </el-dialog>

          <!-- 开始答题对话框  -->
          <el-dialog
            :title="$t('soi.homework.answerQuestion')"
            :visible.sync="answerQuestionDialogVisible"
            :close-on-press-escape="false"
            :close-on-click-modal="false"
            :before-close="closeQuestionDialog"
            width="30%"
          >
            <el-form ref="answerQuestionDetails" :model="answerQuestionDetails" label-width="auto">
              <el-form-item :label="$t('soi.courseCategory.courseCategory')" prop="courseCategory">
                <el-col :offset="1" :span="10">
                  <el-input
                    v-model="answerQuestionDetails.courseCategory"
                    :disabled="true"
                  />
                </el-col>
              </el-form-item>
              <el-form-item :label="$t('soi.course.courseName')" prop="courseName">
                <el-col :offset="1" :span="10">
                  <el-input
                    v-model="answerQuestionDetails.courseName"
                    :disabled="true"
                  />
                </el-col>
              </el-form-item>
              <el-form-item :label="$t('soi.subject.subjectName')" prop="subjectName">
                <el-col :offset="1" :span="10">
                  <el-input
                    v-model="answerQuestionDetails.subjectName"
                    :disabled="true"
                  />
                </el-col>
              </el-form-item>
              <el-form-item :label="$t('soi.homework.questionContent')" prop="questionContent">
                <el-col :offset="1" :span="20">
                  <el-input
                    v-model="answerQuestionDetails.questionContent"
                    type="textarea"
                    :autosize="true"
                    readonly
                  />
                </el-col>
              </el-form-item>
              <el-form-item
                :label="$t('soi.homework.studentAnswer')"
                prop="studentAnswer"
                :rules="[
                  { required: true, message: $t('soi.homework.studentAnswerIsRequired'), trigger: ['blur', 'change'] }
                ]"
              >
                <el-col :offset="1" :span="20">
                  <el-input
                    v-model="answerQuestionDetails.studentAnswer"
                    type="textarea"
                    :autosize="true"
                  />
                </el-col>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="commitQuestionAnswer('answerQuestionDetails')">{{ $t('soi.common.confirm') }}</el-button>
              <el-button @click="closeQuestionDialog">{{ $t('soi.common.cancel') }}</el-button>
            </div>
          </el-dialog>
        </el-tab-pane>

        <!-- My Homework -->
        <el-tab-pane :label="$t('soi.homework.myHomework')" name="second">
          <div>
            <!-- 搜索表单 -->
            <el-form ref="homeworkForm" :model="homeworkForm" size="small" inline>
              <!-- 题干内容 (关键词模糊搜索)-->
              <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
                <el-input
                  v-model="homeworkForm.keywords"
                  :placeholder="$t('soi.homework.keywordsPlaceholder')"
                  clearable
                  @change="getStudentExerciseList"
                />
              </el-form-item>

              <!-- 课程大分类列表 -->
              <el-form-item
                :label="$t('soi.courseCategory.courseCategory')"
                prop="courseCategoryId"
              >
                <el-select
                  v-model="homeworkForm.courseCategoryId"
                  :placeholder="$t('soi.courseCategory.keywordsPlaceholder')"
                  clearable
                  @change="loadCurrentCourseList($event)"
                >
                  <el-option
                    v-for="item in courseCategoryOptions"
                    :key="item.id"
                    :label="item.courseCategory"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <!-- 课程列表 -->
              <el-form-item :label="$t('soi.course.courseName')" prop="courseCode">
                <el-select
                  v-model="homeworkForm.courseCode"
                  :placeholder="$t('soi.course.keywordsPlaceholder')"
                  clearable
                  @change="loadCurrentSubjectList($event)"
                >
                  <el-option
                    v-for="item in currentCourseOptions"
                    :key="item.id"
                    :label="item.courseName"
                    :value="item.courseCode"
                  />
                </el-select>
              </el-form-item>

              <!-- 课题列表 -->
              <el-form-item :label="$t('soi.subject.subjectName')" prop="subjectCode">
                <el-select
                  v-model="homeworkForm.subjectCode"
                  :placeholder="$t('soi.subject.keywordsPlaceholder')"
                  clearable
                >
                  <el-option
                    v-for="item in currentSubjectOptions"
                    :key="item.id"
                    :label="item.subjectName"
                    :value="item.subjectCode"
                  />
                </el-select>
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="getStudentExerciseList()">
                  {{ $t('soi.common.search') }}
                </el-button>
                <el-button icon="el-icon-refresh" @click="resetForm('homeworkForm')">{{ $t('soi.common.reset') }}
                </el-button>
              </el-form-item>

              <!-- My Homework表格 -->
              <el-card shadow="never">
                <el-table
                  v-loading="tableLoading"
                  :data="stuQuestionAnswerList"
                  stripe
                >
                  <el-table-column type="index" label="#" align="center" />
                  <el-table-column
                    :label="$t('soi.courseCategory.courseCategory')"
                    prop="courseCategory"
                    show-overflow-tooltip
                    align="center"
                  />
                  <el-table-column :label="$t('soi.course.courseName')" prop="courseName" align="center" />
                  <el-table-column :label="$t('soi.subject.subjectName')" prop="subjectName" align="center" />
                  <el-table-column
                    :label="$t('soi.homework.questionContent')"
                    prop="questionContent"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="light" placement="top-start">
                        <div slot="content" style="width: 500px">{{ scope.row.questionContent }}</div>
                        <span class="table-content">{{ scope.row.questionContent }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('soi.homework.studentAnswer')"
                    prop="studentAnswer"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="light" placement="top-start">
                        <div slot="content" style="width: 500px">{{ scope.row.studentAnswer }}</div>
                        <span class="table-content">{{ scope.row.studentAnswer }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('soi.homework.questionModelanswer')"
                    prop="questionModelanswer"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="light" placement="top-start">
                        <div slot="content" style="width: 500px">{{ scope.row.questionModelanswer }}</div>
                        <span class="table-content">{{ scope.row.questionModelanswer }}</span>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('soi.common.creator')" prop="creator" align="center" />
                  <el-table-column :label="$t('soi.common.updateBy')" prop="lastUpdateCreator" align="center" />
                  <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
                  <el-table-column :label="$t('soi.common.updateTime')" prop="lastUpdateTime" align="center" />
                </el-table>
              </el-card>
            </el-form>
          </div>
          <div class="table-footer">
            <el-pagination
              :current-page.sync="homeworkForm.currentPage"
              :page-sizes="[20, 50, 100, 500, 1000]"
              :page-size.sync="homeworkForm.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="getStudentExerciseList()"
              @current-change="getStudentExerciseList()"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </customize-card>
  </div>
</template>

<script>

import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { courseCategoryOptions, courseOptions, subjectOptions } from '@/data'
import { batchDeleteQuestions, getQuestionBankList, getDetailByQuestionId } from '@/api/question-learning/question-bank'
import { updateQuestion } from '@/api/system/question'
import { createStuExercise, getStuExerciseList } from '@/api/question-learning/student-exercise'

export default {
  name: 'CourseLearningHomework',
  components: { CustomizeCard },
  data() {
    return {
      activeName: 'first',
      // 页面loading
      loading: false,
      // ‘作业列表’表格loading
      tableLoading: false,
      total: 0,
      // ‘作业列表’分页查询请求参数
      homeworkForm: {
        currentPage: 1,
        pageSize: 20,
        keywords: '',
        courseCategoryId: '',
        courseCode: '',
        subjectCode: ''
      },
      // 作业列表
      questionBankList: [],
      stuQuestionAnswerList: [],
      multipleSelection: [],
      courseCategoryOptions: [],
      courseOptions: [],
      currentCourseOptions: [],
      subjectOptions: [],
      currentSubjectOptions: [],
      // 编辑试题对话框内容
      editQuestionDetails: [],
      // 试题详情内容
      questionDetail: [],
      // 学生答题对话框内容
      answerQuestionDetails: {
        courseCategory: '',
        courseName: '',
        subjectName: '',
        questionContent: '',
        studentAnswer: ''
      },
      // dialog相关
      editQuestionDialogVisible: false,
      questionDetailDialogVisible: false,
      answerQuestionDialogVisible: false,
      // 编辑试题页面loading
      editQuestionShow: false,
      // 查看试题页面loading
      questionDetailShow: false,
      // 学生答题页面loading
      answerQuestionShow: false
    }
  },
  async mounted() {
    // 加载试题列表
    this.getQuestionBankList()
    // 加载My Homework列表
    this.getStudentExerciseList()
    // 加载课程大分类列表
    this.courseCategoryOptions = await courseCategoryOptions()
    // 加载课程列表
    this.courseOptions = await courseOptions()
    // 加载课题列表
    this.subjectOptions = await subjectOptions()
  },
  methods: {
    // 调用API分页查询‘问题作业’列表
    getQuestionBankList() {
      this.tableLoading = true
      getQuestionBankList(this.homeworkForm)
        .then((res) => {
          const { total, items: questionBankList } = res.data
          this.questionBankList = questionBankList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 调用API分页查询‘问题作业’列表
    getStudentExerciseList() {
      this.tableLoading = true
      getStuExerciseList(this.homeworkForm)
        .then((res) => {
          const { total, items: stuQuestionAnswerList } = res.data
          this.stuQuestionAnswerList = stuQuestionAnswerList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 保存已选中的问题记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 切换课程大分类时，课程以及课题切换成与之对应的列表
    loadCurrentCourseList(id) {
      this.homeworkForm.courseCategoryId = id
      this.currentCourseOptions = this.courseOptions.filter(item => item.courseCategoryId === this.homeworkForm.courseCategoryId)
      this.homeworkForm.courseCode = ''
      this.loadCurrentSubjectList(this.homeworkForm.courseCode)
    },
    // 切换课程时，课题切换成与之对应的列表
    loadCurrentSubjectList(courseCode) {
      const currentCourse = this.currentCourseOptions.filter(item => item.courseCode === courseCode)
      if (currentCourse.length > 0) {
        const currentCourseId = currentCourse[0].id
        this.currentSubjectOptions = this.subjectOptions.filter(item => item.courseId === currentCourseId)
      } else {
        this.currentSubjectOptions = []
      }
      this.homeworkForm.subjectCode = ''
    },
    // 查看试题详情对话框
    openQuestionDetailDialog(row) {
      this.questionDetailDialogVisible = true
      this.questionDetailShow = false
      getDetailByQuestionId(row.id)
        .then((res) => {
          this.questionDetail = res.data.data
        })
        .finally(() => {
          this.questionDetailShow = true
        })
    },
    // 学生开始答该道试题 对话框
    answerQuestionDialog(id) {
      this.answerQuestionDialogVisible = true
      this.answerQuestionShow = false
      getDetailByQuestionId(id)
        .then((res) => {
          this.answerQuestionDetails = res.data.data
        })
        .finally(() => {
          this.answerQuestionShow = true
        })
    },
    // 学生提交试题答案完成答题
    commitQuestionAnswer(form) {
      if (form.studentAnswer === undefined) {
        this.$refs[form].validate((valid) => {
          if (valid) {
            this.confirmButtonLoading = true
            createStuExercise(this.answerQuestionDetails)
              .then((res) => {
                this.$message.success(res.message)
                this.$emit('success')
                this.getStudentExerciseList()
              })
              .finally(() => {
                this.confirmButtonLoading = false
              })
            this.answerQuestionDialogVisible = false
            this.activeName = 'second'
            const item = this.stuQuestionAnswerList.find(item => item.questionContent === this.answerQuestionDetails.questionContent)
            if (item) {
              item.studentAnswer = this.answerQuestionDetails.studentAnswer
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      }
    },
    // 删除‘Homework list’
    batchDeleteHomeworkList(questionIds) {
      this.$confirm(this.$t('soi.homework.deleteQuestionPrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedQuestionIds = questionIds

        if (!deletedQuestionIds || !deletedQuestionIds.length) {
          deletedQuestionIds = this.multipleSelection.map((question) => question.id)
        }

        this.loading = true
        batchDeleteQuestions(deletedQuestionIds)
          .then((res) => {
            // 提示成功
            this.$message.success(res.message)

            // 重新加载‘我的学习路径’列表
            this.getQuestionBankList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 打开 编辑试题 对话框
    editQuestionDialog(id) {
      this.editQuestionDialogVisible = true
      this.editQuestionShow = false
      getDetailByQuestionId(id)
        .then((res) => {
          this.editQuestionDetails = res.data.data
        })
        .finally(() => {
          this.editQuestionShow = true
        })
    },
    updateQuestionBank() {
      this.confirmButtonLoading = true
      updateQuestion(this.editQuestionDetails)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
      this.editQuestionDialogVisible = false
      this.activeName = 'second'
      const item = this.questionBankList.find(item => item.id === this.editQuestionDetails.id)
      if (item) {
        item.questionContent = this.editQuestionDetails.questionContent
        item.questionModelanswer = this.editQuestionDetails.questionModelanswer
      }
    },
    closeQuestionDialog() {
      this.answerQuestionDialogVisible = false
      this.$refs.answerQuestionDetails.resetFields()
    }
  }
}
</script>

<style lang="scss">
.overflow-tooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-content {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
</style>
