<template>
  <div class="explore-course-details-container">
    <customize-card :title="$t('soi.course.courseDetail')">
      <el-row :gutter="40">
        <el-col :span="8">
          <svg-icon icon-class="bank" class-name="card-panel-icon" />
        </el-col>
        <el-col v-if="show" :span="16">
          <el-descriptions :column="1" :title="$t('soi.courseLearning.contentDetails')">
            <!-- 课程大分类 -->
            <el-descriptions-item :label="$t('soi.courseCategory.courseCategory')" prop="courseCategory">
              {{ courseDetails.courseCategory }}
            </el-descriptions-item>

            <!-- 课程名称 -->
            <el-descriptions-item :label="$t('soi.course.courseName')" prop="courseName">
              {{ courseDetails.courseName }}
            </el-descriptions-item>

            <!-- 课程图片 -->
            <el-descriptions-item :label="$t('soi.course.coursePicture')" prop="coursePicture">
              <el-image
                class="coursePicture"
                :src="courseDetails.coursePicture"
              />
            </el-descriptions-item>
          </el-descriptions>

          <el-descriptions :column="1" :title="$t('soi.courseLearning.contentDescription')" :colon="false" style="margin-top: 30px">
            <!-- 课程描述 -->
            <el-descriptions-item prop="courseDescription">
              {{ courseDetails.courseDescription }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getCourseDetails } from '@/api/system/course'

export default {
  name: 'IntroduceCourseDetails',
  components: { CustomizeCard },
  data() {
    return {
      show: false,
      courseCode: '',
      courseDetails: {}
    }
  },
  async mounted() {
    this.courseCode = localStorage.getItem('courseCode')
    this.show = false
    // 加载课程信息
    const { data } = await getCourseDetails(this.courseCode)
    this.courseDetails = data
    this.show = true
  }
}
</script>

<style scoped lang="scss">
.explore-course-details-container {
  .card-panel-icon{
    width: 100%;
    height: 100%;
    margin-top:50px;
  }
  .coursePicture {
    width: 180px;
    height: 100px;
  }
}
::v-deep .el-descriptions .el-descriptions__header {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

