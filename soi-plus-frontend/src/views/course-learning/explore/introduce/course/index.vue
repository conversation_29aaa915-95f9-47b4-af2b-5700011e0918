<template>
  <div class="explore-course-container">
    <customize-card :title="$t('soi.router.course') + ' - ' + courseCategory">
      <el-row :gutter="40" class="panel-group">
        <el-col v-for="(item, index) in courseOptions" :key="index" :span="8" :xs="24">
          <div class="card-panel" @click="handlerCourse(item.id)">
            <div class="card-panel-icon-wrapper icon-people">
              <svg-icon icon-class="bank" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">{{ $t(item.courseName) }}</div>
            </div>
            <div class="card-panel-detail-btn">
              <el-button type="text" size="mini" text @click="viewCourseDetail(item.courseCode)">{{ $t('soi.common.details') }}</el-button>
            </div>
          </div>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { courseOptions } from '@/data'
import { getCourseCategoryDetails } from '@/api/system/course-category'

export default {
  name: 'IntroduceCourse',
  components: { CustomizeCard },
  data() {
    return {
      courseCategoryId: '',
      courseCategory: '',
      courseOptions: []
    }
  },
  async mounted() {
    this.courseCategoryId = localStorage.getItem('courseCategoryId')
    // 加载课程大分类列表
    const allCourseOptions = await courseOptions()
    this.courseOptions = allCourseOptions.filter(item => item.courseCategoryId === this.courseCategoryId)

    // 获得课程大分类名称
    const courseCategoryDetails = await getCourseCategoryDetails(this.courseCategoryId)
    console.log(courseCategoryDetails)
    this.courseCategory = courseCategoryDetails.data.courseCategory
  },
  methods: {
    handlerCourse(id) {
      localStorage.setItem('courseId', id)
      this.$router.push({ name: 'subject-introduce' })
    },
    viewCourseDetail(courseCode) {
      localStorage.setItem('courseCode', courseCode)
      this.$router.push({ name: 'course-details-introduce' })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.explore-course-container .panel-group {
  .card-panel {
    margin-bottom: 32px;
  }
  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    padding: 0 14px;
    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-people {
        background: #40c9c6;
      }
      .icon-message {
        background: #36a3f7;
      }
      .icon-money {
        background: #f4516c;
      }
      .icon-shopping {
        background: #34bfa3;
      }
    }
    .icon-people {
      color: #40c9c6;
    }
    .icon-message {
      color: #36a3f7;
    }
    .icon-money {
      color: #f4516c;
    }
    .icon-shopping {
      color: #34bfa3;
    }
    .card-panel-icon-wrapper {
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }
    .card-panel-icon {
      float: left;
      font-size: 48px;
    }
    .card-panel-description {
      font-weight: bold;
      .card-panel-text {
        line-height: 1.5;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-left: 20px;
        word-wrap:  break-word;
        word-break: break-word;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .card-panel-num {
        font-size: 20px;
      }
    }
    .card-panel-detail-btn {
      position: absolute;
      bottom: 5px;
      right: 10px;
    }
  }
}
.el-icon-success:before {
  content: "\E62D";
  color: green;
  font-size: 30px;
}
.el-icon-loading:before {
  color: blue;
  font-size: 30px;
}
.el-icon-error:before {
  color: red;
  font-size: 30px;
}
.card_list {
  position: relative;
  height: 195px;
  margin-bottom: 20px;
  border: 1px solid #ccc !important;
  box-shadow: 5px 5px 5px #ccc;
  .card_img {
    text-align: center;
    img {
      width: 80%;
      height: auto;
      vertical-align: middle;
    }
  }
  h2 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.5;
  }
  p {
    height: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
</style>
<style>
.el-button--medium {
  padding: 10px 10px;
  font-size: 14px;
  border-radius: 4px;
}
.worksheet .clearfix .el-button--primary {
  color: #109eae;
  background-color: #fff;
  border-color: #fff;
}
</style>
