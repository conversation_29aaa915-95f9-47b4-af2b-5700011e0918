<template>
  <div class="explore-subject-details-container">
    <customize-card :title="$t('soi.subject.subjectDetail')">
      <el-row :gutter="40">
        <el-col :span="8">
          <svg-icon icon-class="bank" class-name="card-panel-icon" />
        </el-col>
        <el-col v-if="show" :span="16">
          <el-descriptions :column="1" :title="$t('soi.courseLearning.contentDetails')">
            <!-- 课程名称 -->
            <el-descriptions-item :label="$t('soi.course.courseName')" prop="courseName">
              {{ subjectDetails.courseName }}
            </el-descriptions-item>

            <!-- 课题名称 -->
            <el-descriptions-item :label="$t('soi.subject.subjectName')" prop="subjectName">
              {{ subjectDetails.subjectName }}
            </el-descriptions-item>

            <!-- 课题类型 -->
            <el-descriptions-item :label="$t('soi.subject.category')" prop="subjectCategory">
              {{ $t(subjectCategory) }}
            </el-descriptions-item>

            <!-- 课题时长 -->
            <el-descriptions-item :label="$t('soi.subject.subjectHours')" prop="subjectHours">
              {{ subjectDetails.subjectHours }}
            </el-descriptions-item>

            <!-- 课题级别 -->
            <el-descriptions-item :label="$t('soi.subject.level')" prop="subjectLevel">
              {{ $t(subjectLevel) }}
            </el-descriptions-item>

            <!-- 课题目标 -->
            <el-descriptions-item :label="$t('soi.subject.objective')" prop="subjectObjective">
              {{ subjectDetails.subjectObjective }}
            </el-descriptions-item>

            <!-- 课题图片 -->
            <el-descriptions-item :label="$t('soi.subject.picture')" prop="subjectPictureUrl">
              <el-image
                class="subjectPicture"
                :src="subjectDetails.subjectPictureUrl"
              />
            </el-descriptions-item>
          </el-descriptions>

          <el-descriptions :column="1" :title="$t('soi.courseLearning.contentDescription')" :colon="false" style="margin-top: 30px">
            <!-- 课程描述 -->
            <!-- 课题内容描述 -->
            <el-descriptions-item :label="$t('soi.subject.description')" prop="subjectDescription">
              {{ subjectDetails.subjectDescription }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getSubjectDetails } from '@/api/system/subject'
import { subjectCategory, subjectLevelOptions } from '@/data'

export default {
  name: 'IntroduceSubjectDetails',
  components: { CustomizeCard },
  data() {
    return {
      show: false,
      subjectCode: '',
      subjectDetails: {},
      subjectCategory: '',
      subjectLevel: ''
    }
  },
  async mounted() {
    this.subjectCode = localStorage.getItem('subjectCode')
    this.show = false
    // 加载课程信息
    const { data } = await getSubjectDetails(this.subjectCode)
    this.subjectDetails = data
    // 获取当前课题类型名称
    this.subjectCategory = subjectCategory[this.subjectDetails.subjectCategory]
    // 获取当前课题级别名称
    const currentSubjectLevel = subjectLevelOptions.filter(item => item.value === this.subjectDetails.subjectLevel)
    this.subjectLevel = currentSubjectLevel[0].label
    this.show = true
  }
}
</script>

<style scoped lang="scss">
.explore-subject-details-container {
  .card-panel-icon{
    width: 100%;
    height: 100%;
    margin-top:50px;
  }
  .subjectPicture {
    width: 180px;
    height: 100px;
  }
}
::v-deep .el-descriptions .el-descriptions__header {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

