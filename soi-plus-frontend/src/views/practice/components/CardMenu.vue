<template>
  <el-row :gutter="20">
    <el-col v-for="(route, index) in routes" :key="index" :span="6">
      <div v-if="!route.hidden">
        <div v-if="hasOneShowingChild(route.children, route) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !route.alwaysShow" @click="handlerClickLinkMenu(route)">
          <el-card v-if="onlyOneChild.meta" shadow="hover" class="menu-card-item" :style="{ backgroundImage: onlyOneChild.meta.image ? 'url(' + require(`@/assets/images/router/${onlyOneChild.meta.image}`) : 'none'}">
            <div class="content">
              {{ generateTitle(onlyOneChild.meta.title) }}
              <el-button v-show="route.meta.document && route.meta.showDocToCard" class="doc" plain size="small" @click.prevent.stop="openDocument(route.meta.document)">{{ $t('soi.common.helpDocument') }}</el-button>
            </div>
          </el-card>
        </div>

        <div v-else @click="handlerClickGroupMenu(route)">
          <el-card v-if="route.meta" shadow="hover" class="menu-card-item" :style="{ backgroundImage: route.meta.image ? 'url(' + require(`@/assets/images/router/${route.meta.image}`) : 'none'}">
            <div class="content">
              {{ generateTitle(route.meta.title) }}
            </div>
          </el-card>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import { generateTitle } from '@/utils/i18n'
import { mapGetters } from 'vuex'

export default {
  name: 'CardMenu',
  props: {
    routes: {
      type: Array,
      required: true
    }
  },
  data() {
    this.onlyOneChild = null
    return {
      generateTitle
    }
  },
  computed: {
    ...mapGetters(['language'])
  },
  methods: {
    openDocument(document) {
      console.log(document)
      window.open(document[this.language], '_blank')
    },
    handlerClickLinkMenu(route) {
      let value = route.name ? route.name : route.path
      if (route.children) {
        value = route.children[0].name ? route.children[0].name : route.children[0].path
      }
      this.$emit('click-link', value)
    },
    handlerClickGroupMenu(route) {
      this.$emit('click-group', route)
    },
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-card-item {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  font-size: 18px;
  cursor: pointer;
  background: no-repeat center center;
  position: relative;
  background-size: 100% 100%;
  .content {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    background:rgba(255,255,255,.75);

    .doc {
      position: absolute;
      bottom: 10px;
      right: 10px;
      color: #109eae;
      border-color: #109eae;
    }
  }
}
</style>
