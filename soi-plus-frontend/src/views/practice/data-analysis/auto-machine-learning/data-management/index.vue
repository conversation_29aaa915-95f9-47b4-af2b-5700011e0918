<template>
  <div v-loading.fullscreen.lock="loading" class="data-analysis-auto-machine-learning-data-management">
    <customize-card :title="$t('soi.autoMachineLearning.dataManagement')">
      <template #header-actions>
        <el-button size="mini" @click="$router.push({ name: 'data-analysis-auto-machine-learning-model-management' })">
          {{ $t('soi.autoMachineLearning.modelManagement') }}
        </el-button>
      </template>
      <div class="ml-button-group">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="deleteCantClick"
          @click="deleteSelectedFileData()"
        >
          {{ $t('soi.common.delete') }}
        </el-button>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="uploadDialog = true">
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
      <el-card shadow="never" :body-style="{ padding: '5px 20px 20px 20px' }">
        <!-- Data Management Table -->
        <el-table
          ref="dataManagementTable"
          height="624px"
          :data="tableData"
          tooltip-effect="dark"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column :label="$t('soi.autoMachineLearning.name')" prop="filename" align="center" />
          <el-table-column :label="$t('soi.common.description')" prop="description" align="center" />
          <el-table-column :label="$t('soi.autoMachineLearning.type')" prop="datatype" align="center" />
          <el-table-column :label="$t('soi.autoMachineLearning.uploadTime')" prop="uploadtime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-view"
                @click="viewFileDetails(scope.row.fileid)"
              >
                {{ $t('soi.common.view') }}
              </el-button>
              <el-button
                class="ml-button-add-model"
                size="mini"
                icon="el-icon-circle-plus-outline"
                @click="addModel(scope.row.fileid)"
              >
                {{ $t('soi.autoMachineLearning.addModel') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <el-pagination
            layout="total, prev, pager, next"
            :page-size="pageSize"
            :current-page="currentPage"
            :total="total"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
      <!-- Upload Dialog -->
      <el-dialog
        :title="$t('soi.autoMachineLearning.uploadData')"
        :visible.sync="uploadDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleDialogClose"
        width="60%"
      >
        <!-- Steps -->
        <el-steps :active="currentStep" align-center class="upload-steps">
          <el-step :title="$t('soi.autoMachineLearning.uploadData')" icon="el-icon-upload" />
          <el-step :title="$t('soi.autoMachineLearning.setColumnRoles')" icon="el-icon-setting" />
        </el-steps>

        <div class="upload-form">
          <el-form
            ref="uploadForm"
            :model="uploadForm"
            :rules="uploadFormRules"
            label-position="left"
            label-width="300px"
            size="small"
          >
            <!-- Step 1: Upload Data -->
            <div v-show="currentStep === 0">
              <el-form-item :label="$t('soi.autoMachineLearning.type')" prop="datatype">
                <el-select v-model="uploadForm.datatype" class="input-and-select">
                  <el-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('soi.common.description')" prop="description">
                <el-input
                  v-model="uploadForm.description"
                  class="input-and-select"
                  type="textarea"
                  maxlength="255"
                  :placeholder="$t('soi.autoMachineLearning.descriptionPlaceholder')"
                />
              </el-form-item>
              <el-form-item :label="$t('soi.autoMachineLearning.dataFile')">
                <el-upload
                  class="ml-upload"
                  drag
                  :action="uploadUrl"
                  :data="{userid: userDetails.id}"
                  :file-list="fileList"
                  :on-change="handleChange"
                  :on-success="handleSuccess"
                  :before-upload="handleBeforeUpload"
                  :on-error="handleFail"
                  :on-remove="handleRemove"
                  :accept="ACCEPT_FILE_TYPE"
                  :multiple="false"
                >
                  <div class="el-upload__text">{{ $t('soi.autoMachineLearning.uploadFirstTip') }} <em>{{ $t('soi.autoMachineLearning.uploadLastTip') }}</em>
                    <i class="el-icon-upload upload-icon" /></div>
                </el-upload>
                <span v-show="fileList.length === 0" class="ml-upload-tip">{{ $t('soi.autoMachineLearning.uploadAcceptFileType') }}</span>
              </el-form-item>
            </div>

            <!-- Step 2: Set Column Roles -->
            <div v-show="currentStep === 1">
              <el-form-item :label="$t('soi.autoMachineLearning.responseColumn')" prop="responsecolumn">
                <el-select
                  v-model="uploadForm.responsecolumn"
                  class="input-and-select"
                  :placeholder="$t('soi.autoMachineLearning.columnPlaceholder')"
                >
                  <el-option
                    v-for="(item, index) in columns"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.value === uploadForm.indexcolumn || uploadForm.ignore_columns.indexOf(item.value) !== -1"
                  />
                </el-select>
              </el-form-item>
              <div v-if="uploadForm.datatype === 'Text Classification'">
                <el-form-item :label="$t('soi.autoMachineLearning.TextColumn')" prop="textcolumn">
                  <el-select
                    v-model="uploadForm.textcolumn"
                    class="input-and-select"
                    clearable
                    :placeholder="$t('soi.autoMachineLearning.columnPlaceholder')"
                  >
                    <el-option v-for="(item, index) in columns" :key="index" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </div>
              <div v-else>
                <el-form-item :label="$t('soi.autoMachineLearning.indexColumn')" prop="indexcolumn">
                  <el-select
                    v-model="uploadForm.indexcolumn"
                    class="input-and-select"
                    clearable
                    :placeholder="$t('soi.autoMachineLearning.columnPlaceholder')"
                  >
                    <el-option
                      v-for="(item, index) in columns"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="uploadForm.ignore_columns.indexOf(item.value) !== -1 || uploadForm.responsecolumn === item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.autoMachineLearning.ignoreColumn')" prop="ignore_columns">
                  <el-select
                    v-model="uploadForm.ignore_columns"
                    class="input-and-select"
                    multiple
                    collapse-tags
                    clearable
                    :placeholder="$t('soi.autoMachineLearning.columnPlaceholder')"
                  >
                    <el-option
                      v-for="(item, index) in columns"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.value === uploadForm.indexcolumn || item.value === uploadForm.responsecolumn"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelUpload">{{ $t('soi.common.cancel') }}</el-button>
          <el-button v-if="currentStep > 0" @click="previousStep">{{ $t('soi.common.Previous') }}</el-button>
          <el-button v-if="currentStep < 1" type="primary" @click="nextStep">{{ $t('soi.common.Next') }}</el-button>
          <el-button v-if="currentStep === 1" type="primary" @click="submitForm('uploadForm')">{{ $t('soi.common.confirm') }}</el-button>
        </span>
      </el-dialog>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { deleteFile, getFileColumns, getFiles, updateFile } from '@/api/practice/auto-machine-learning'
import { mapGetters } from 'vuex'
import { MACHINE_LEARNING_API_URL } from '@/contains'

export default {
  name: 'AutoMachineLearningDataManagement',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      ACCEPT_FILE_TYPE: '.csv,.txt',
      tableData: [],
      multipleSelection: [],
      deleteCantClick: true,
      uploadDialog: false,
      currentStep: 0,
      uploadForm: {
        fileid: '',
        datatype: 'Numerical Classification',
        description: '',
        responsecolumn: '',
        indexcolumn: '',
        ignore_columns: [],
        textcolumn: ''
      },
      uploadFormRules: {},
      typeOptions: [],
      uploadUrl: '',
      fileList: [],
      columns: [],
      total: 0,
      currentPage: 1,
      pageSize: 15,
      items: [],
      updateCantClick: true
    }
  },
  computed: {
    ...mapGetters(['userDetails']),
    language() {
      return this.$store.state.app.language
    }
  },
  watch: {
    'multipleSelection'(newValue) {
      // 删除按钮是否能点击
      this.deleteCantClick = newValue.length <= 0
      this.updateCantClick = newValue.length !== 1
    },
    'uploadForm.datatype'(newValue) {
      if (newValue === 'Text Classification') {
        this.uploadForm.indexcolumn = ''
        this.uploadForm.ignore_columns = []
      } else {
        this.uploadForm.textcolumn = ''
      }
    },
    'language'() {
      this.switchLanguage()
    }
  },
  created() {
    this.uploadUrl = `${MACHINE_LEARNING_API_URL}/uploadfile`
    this.getFileDataList()
    this.switchLanguage()
  },
  methods: {
    viewFileDetails(fileId) {
      this.$router.push({ name: 'data-analysis-auto-machine-learning-file-details', query: { fileId }})
    },
    addModel(fileId) {
      this.$router.push({ name: 'data-analysis-auto-machine-learning-add-model', query: { fileId }})
    },
    // 删除选中文件数据
    deleteSelectedFileData() {
      const _this = this
      _this.$confirm(_this.$t('soi.autoMachineLearning.deleteTip'), _this.$t('soi.common.tip'), {
        confirmButtonText: _this.$t('soi.common.confirm'),
        showCancelButton: false,
        type: _this.$t('soi.common.warning')
      }).then(() => {
        _this.loading = true
        const fileids = { fileids: [] }
        for (const item of _this.multipleSelection) {
          fileids.fileids.push(item.fileid)
        }
        const formData = new FormData()
        formData.append('data', JSON.stringify(fileids))

        deleteFile(formData)
          .then(() => {
            _this.$message.success(_this.$t('soi.autoMachineLearning.deleteSuccessTip'))
            _this.getFileDataList()
          })
          .finally(() => {
            _this.loading = false
          })
      })
    },
    // 获取文件数据列表
    getFileDataList() {
      const _this = this
      _this.loading = true

      getFiles({ userid: _this.userDetails.id })
        .then(response => {
          _this.items = response.info
          _this.total = _this.items.length
          if (_this.total % _this.pageSize === 0) {
            if (_this.currentPage > _this.total / _this.pageSize) {
              _this.currentPage = _this.currentPage - 1
            }
          }
          if (_this.currentPage === 0) {
            _this.currentPage = 1
          }
          _this.tableData = _this.items.slice((_this.currentPage - 1) * _this.pageSize, _this.currentPage * _this.pageSize)
        })
        .finally(() => {
          _this.loading = false
        })
    },
    // 提交表单
    submitForm(formName) {
      const _this = this
      if (this.fileList.length > 0) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            _this.loading = true
            const formData = new FormData()
            formData.append('data', JSON.stringify(_this.uploadForm))

            updateFile(formData)
              .then(() => {
                _this.$message.success(_this.$t('soi.autoMachineLearning.saveSuccessTip'))
                _this.resetForm()
                // 取消表单校验提示
                _this.$refs[formName].resetFields()
                _this.getFileDataList()
              })
              .finally(() => {
                _this.loading = false
              })
          }
        })
      } else {
        _this.$message.warning(_this.$t('soi.autoMachineLearning.uploadFileFirstTip'))
      }
    },
    // 获取表格中选中的行
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },
    // 文件上传成功后获取列信息
    handleSuccess(response) {
      const _this = this
      if (response.result === 'success') {
        _this.uploadForm.fileid = response.fileid
        this.$message.success(_this.$t('soi.autoMachineLearning.uploadSuccessTip'))
        setTimeout(function() {
          // 获取列信息
          getFileColumns({ fileid: response.fileid })
            .then(response => {
              const result = []
              for (const item of response.columns) {
                result.push({ label: item, value: item })
              }
              _this.columns = result
              // 文件上传成功并获取列信息后，可以进入下一步
            })
            .finally(() => {
              _this.loading = false
            })
        }, 5000)
      } else {
        _this.loading = false
        _this.$message.error(`${_this.$t('soi.autoMachineLearning.uploadFailTip')} [${response.info}]`)
      }
    },
    // 只显示最新上传成功文件信息
    handleChange(file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      }
    },
    // 上传前显示loading
    handleBeforeUpload() {
      this.loading = true
    },
    handleFail() {
      this.loading = false
    },
    handleRemove() {
      this.fileList = []
      this.columns = []
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.tableData = this.items.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
    },
    resetForm() {
      const _this = this
      // 关闭 Upload Dialog
      _this.uploadDialog = false
      // 重置步骤
      _this.currentStep = 0
      // 清空 Upload Form
      _this.uploadForm = {
        fileid: '',
        datatype: 'Numerical Classification',
        description: '',
        responsecolumn: '',
        indexcolumn: '',
        ignore_columns: [],
        textcolumn: ''
      }
      // 清空上传文件列表
      _this.fileList = []
      // 清空列选项
      _this.columns = []
      // 清除表单验证状态
      if (_this.$refs.uploadForm) {
        _this.$refs.uploadForm.resetFields()
        _this.$refs.uploadForm.clearValidate()
      }
    },
    // 取消上传
    cancelUpload() {
      this.resetForm()
    },
    // 处理对话框关闭
    handleDialogClose(done) {
      // 重置步骤
      this.currentStep = 0
      // 清空 Upload Form
      this.uploadForm = {
        fileid: '',
        datatype: 'Numerical Classification',
        description: '',
        responsecolumn: '',
        indexcolumn: '',
        ignore_columns: [],
        textcolumn: ''
      }
      // 清空上传文件列表
      this.fileList = []
      // 清空列选项
      this.columns = []
      // 清除表单验证状态
      if (this.$refs.uploadForm) {
        this.$refs.uploadForm.resetFields()
        this.$refs.uploadForm.clearValidate()
      }
      // 关闭对话框
      done()
    },
    // 下一步
    nextStep() {
      const _this = this
      if (_this.currentStep === 0) {
        // 验证第一步的必填项
        if (!_this.uploadForm.datatype) {
          _this.$message.warning(this.$t('soi.autoMachineLearning.pleaseSelectAdataType'))
          return
        }
        if (_this.fileList.length === 0) {
          _this.$message.warning(this.$t('soi.autoMachineLearning.pleasUploadFileFirst'))
          return
        }
        if (_this.columns.length === 0) {
          _this.$message.warning(this.$t('soi.autoMachineLearning.pleaseWaitTip'))
          return
        }
        _this.currentStep = 1
      }
    },
    // 上一步
    previousStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    // 切换语言
    switchLanguage() {
      this.uploadFormRules = {
        datatype: [{ required: true, message: '', trigger: 'change' }],
        responsecolumn: [{
          required: true,
          message: this.$t('soi.autoMachineLearning.selectResponseColumnTip'),
          trigger: 'change'
        }],
        textcolumn: [{
          required: true,
          message: this.$t('soi.autoMachineLearning.selectTextColumnTip'),
          trigger: 'change'
        }]
      }
      this.typeOptions = [
        { label: this.$t('soi.autoMachineLearning.numericalClassification'), value: 'Numerical Classification' },
        { label: this.$t('soi.autoMachineLearning.numericalPrediction'), value: 'Numerical Prediction' }
        // {label: this.$t('soi.autoMachineLearning.textClassification'), value: 'Text Classification'},
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
  .data-analysis-auto-machine-learning-data-management {

    .ml-title {
      // line-height: 40px;
    }

    .ml-button-group {
      margin: 0 0 10px 0;
    }

    .input-and-select {
      width: 300px;
    }

    .upload-form {
      width: 600px;
      margin: 15px auto 0;
    }

    .upload-steps {
      margin-bottom: 30px;
    }

    .ml-upload {
      position: relative;
      margin-bottom: 5px;
    }

    .upload-icon {
      position: absolute;
      top: 6px;
      left: 15px;
    }

    ::v-deep .el-list-enter-active,
    ::v-deep .el-list-leave-active {
      transition: none;
    }

    .ml-upload-tip {
      font-size: 12px;
      color: #606266;
      margin-top: 0;
      height: 0;
      position: absolute;
      top: 32px;
    }
  }
</style>
<style lang="scss">
  .data-analysis-auto-machine-learning-data-management {
    .el-upload-dragger {
      width: 300px;
      height: 34px;
    }

    .el-upload-dragger .el-icon-upload {
      font-size: 20px;
      color: #22C1E6;
      margin: 0;
      line-height: 1;
    }

    .el-upload-list__item:first-child {
      margin-top: 0;
    }

    .el-upload-list__item:first-child {
      margin-top: 0;
      position: absolute;
      top: 35px;
    }
  }
</style>
