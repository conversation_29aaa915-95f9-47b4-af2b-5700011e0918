<template>
  <div v-loading.fullscreen.lock="loading" class="data-analysis-data-analysis-use-case-list">
    <customize-card :title="`${$t('soi.router.dataAnalysisUseCaseList')} - ${$t(categoryName)} - ${['zh', 'cht'].includes(language) ? subcategory.className : subcategory.classNameEn}`">
      <el-row v-if="docList.length > 0" :gutter="20">
        <el-table
          :data="docList"
          :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
          style="width: 100%"
          stripe
          highlight-current-row
        >
          <el-table-column label="#" type="index" width="50" />
          <el-table-column :label="$t('soi.dataAnalysisUseCase.name')" prop="docName" align="center">
            <template slot-scope="scope">
              {{ ['zh', 'cht'].includes(language) ? scope.row.docName : scope.row.docNameEn }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.dataAnalysisUseCase.document')" min-width="100" align="center">
            <template slot-scope="scope">
              <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnDocUrl" icon="el-icon-view" size="mini" @click="showDocument(scope.row)">
                {{ $t('soi.common.view') }}
              </el-button>
              <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnDocUrl" icon="el-icon-download" size="mini" @click="downloadDocument(scope.row)">
                {{ $t('soi.common.download') }}
              </el-button>

              <el-button v-if="['en'].includes(language) && scope.row.enDocUrl" icon="el-icon-view" size="mini" @click="showDocument(scope.row)">
                {{ $t('soi.common.view') }}
              </el-button>
              <el-button v-if="['en'].includes(language) && scope.row.enDocUrl" icon="el-icon-download" size="mini" @click="downloadDocument(scope.row)">
                {{ $t('soi.common.download') }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.dataAnalysisUseCase.video')" min-width="100" align="center">
            <template slot-scope="scope">
              <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnVideoUrl" icon="el-icon-view" size="mini" @click="showVideo(scope.row)">
                {{ $t('soi.common.view') }}
              </el-button>
              <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnVideoUrl" icon="el-icon-download" size="mini" @click="downloadVideo(scope.row)">
                {{ $t('soi.common.download') }}
              </el-button>

              <el-button v-if="['en'].includes(language) && scope.row.enVideoUrl" icon="el-icon-view" size="mini" @click="showVideo(scope.row)">
                {{ $t('soi.common.view') }}
              </el-button>
              <el-button v-if="['en'].includes(language) && scope.row.enVideoUrl" icon="el-icon-download" size="mini" @click="downloadVideo(scope.row)">
                {{ $t('soi.common.download') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <div v-else class="text-center">{{ $t('soi.common.noData') }}</div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { findCategoryNameById } from '@/views/practice/data-analysis/data-analysis-use-case/data'
import { getUserCaseSubcategory } from '@/api/practice/user-case'
import { getDocumentList } from '@/api/practice/document'
import { downloadMp4, downloadPdf } from '@/utils/download'

export default {
  name: 'DataAnalysisUseCaseList',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      categoryId: this.$route.params.categoryId,
      subcategoryId: this.$route.params.subcategoryId,
      subcategory: {},
      docList: []
    }
  },
  computed: {
    ...mapGetters([
      'language',
      'userDetails'
    ]),
    categoryName() {
      return findCategoryNameById(this.categoryId)
    }
  },
  mounted() {
    this.loadUserCaseSubcategory()
    this.getUserCaseList()
  },
  methods: {
    getUserCaseList() {
      const vue = this
      vue.loading = true

      getDocumentList({ classId: this.subcategoryId, userId: '<EMAIL>' })
        .then((res) => {
          vue.docList = []
          const { data } = res
          for (let i = 0; i < data.length; i++) {
            vue.docList.push({
              id: data[i].id,
              courseId: data[i].courseId,
              docName: data[i].docName,
              docNameEn: data[i].docNameEn,
              classId: data[i].classId,
              userId: data[i].userId,
              enDocUrl: data[i].enDocUrl || '',
              cnDocUrl: data[i].cnDocUrl || '',
              enVideoUrl: data[i].enVideoUrl || '',
              cnVideoUrl: data[i].cnVideoUrl || '',
              topic: data[i].topic || ''
            })
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    showDocument(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnDocUrl.startsWith('https://')) {
          row.cnDocUrl = 'https://' + row.cnDocUrl
        }
        window.open(row.cnDocUrl)
      }
      if (['en'].includes(this.language)) {
        if (!row.enDocUrl.startsWith('https://')) {
          row.enDocUrl = 'https://' + row.enDocUrl
        }
        window.open(row.enDocUrl)
      }
    },
    downloadDocument(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnDocUrl.startsWith('https://')) {
          row.cnDocUrl = 'https://' + row.cnDocUrl
        }
        downloadPdf(row.cnDocUrl, row.docName)
      }
      if (['en'].includes(this.language)) {
        if (!row.enDocUrl.startsWith('https://')) {
          row.enDocUrl = 'https://' + row.enDocUrl
        }
        downloadPdf(row.enDocUrl, row.docNameEn)
      }
    },
    showVideo(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnVideoUrl.startsWith('https://')) {
          row.cnVideoUrl = 'https://' + row.cnVideoUrl
        }
        window.open(row.cnVideoUrl)
      }
      if (['en'].includes(this.language)) {
        if (!row.enVideoUrl.startsWith('https://')) {
          row.enVideoUrl = 'https://' + row.enVideoUrl
        }
        window.open(row.enVideoUrl)
      }
    },
    downloadVideo(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnVideoUrl.startsWith('https://')) {
          row.cnVideoUrl = 'https://' + row.cnVideoUrl
        }

        downloadMp4(row.cnVideoUrl, row.docName)
      }
      if (['en'].includes(this.language)) {
        if (!row.enVideoUrl.startsWith('https://')) {
          row.enVideoUrl = 'https://' + row.enVideoUrl
        }
        downloadMp4(row.enVideoUrl, row.docNameEn)
      }
    },
    loadUserCaseSubcategory() {
      const vue = this
      switch (this.categoryId) {
        case '1':
          vue.getBankCase('Data Analysis Case')
          break
        case '2':
          vue.getBankCase('Data Analysis Case Insurance')
          break
        case '3':
          vue.getBankCase('Data Analysis Case Stock')
          break
        default:
          vue.getBankCase('Data Analysis Case')
          break
      }
    },
    async getBankCase(groupName) {
      const { data } = await getUserCaseSubcategory({ group: groupName })
      for (const item of data) {
        if (String(item.id) === String(this.subcategoryId)) {
          this.subcategory = item
        }
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

.data-analysis-data-analysis-use-case-list {
  .text-center {
    text-align: center;
  }
}
</style>
