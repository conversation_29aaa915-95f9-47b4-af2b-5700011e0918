<template>
  <el-form
    ref="CustomerForm"
    :model="customerForm"
    :rules="customerFormRules"
    label-position="left"
    label-width="220px"
  >
    <!-- 国家代码<CountryCode> -->
    <el-form-item :label="$t('soi.customerSingleCreation.countryCode')" prop="countryCode">
      <el-input
        v-model="customerForm.countryCode"
        :placeholder="$t('soi.customerSingleCreation.countryCodePlaceholder')"
        class="input"
        maxlength="2"
      />
    </el-form-item>
    <!-- 清算代码<ClearingCode> -->
    <el-form-item :label="$t('soi.customerSingleCreation.clearingCode')" prop="clearingCode">
      <el-input
        v-model="customerForm.clearingCode"
        :placeholder="$t('soi.customerSingleCreation.clearingCodePlaceholder')"
        class="input"
      />
    </el-form-item>
    <!-- 分行编号<BranchCode> -->
    <el-form-item :label="$t('soi.customerSingleCreation.branchCode')">
      <!-- Form -->
      <el-col :span="11">
        <el-form-item
          :label="$t('soi.customerSingleCreation.branchNumberFrom')"
          prop="branchNumberFrom"
          label-width="50px"
        >
          <el-input
            v-model="customerForm.branchNumberFrom"
            :placeholder="$t('soi.customerSingleCreation.branchNumberFromPlaceholder')"
            class="input"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2" class="line" style="text-align: center;">-</el-col>
      <!-- To -->
      <el-col :span="11">
        <el-form-item :label="$t('soi.customerSingleCreation.branchNumberTo')" prop="branchNumberTo" label-width="50px">
          <el-input
            v-model="customerForm.branchNumberTo"
            :placeholder="$t('soi.customerSingleCreation.branchNumberToPlaceholder')"
            class="input"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
    </el-form-item>
    <!-- 年龄阶层<AgeGroup> -->
    <el-form-item :label="$t('soi.customerSingleCreation.ageGroup')" prop="ageGroup">
      <el-select
        v-model="customerForm.ageGroup"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.ageGroupPlaceholder')"
      >
        <el-option
          v-for="item in ageGroupOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 出生国家<CountryOfBirth> -->
    <el-form-item :label="$t('soi.customerSingleCreation.countryOfBirth')" prop="countryOfBirth">
      <el-select
        v-model="customerForm.countryOfBirth"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.countryOfBirthPlaceholder')"
      >
        <el-option
          v-for="item in countryOfBirthList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 国籍<Nationality> -->
    <el-form-item :label="$t('soi.customerSingleCreation.nationality')" prop="nationality1">
      <el-select
        v-model="customerForm.nationality1"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.nationalityPlaceholder')"
      >
        <el-option
          v-for="item in nationality"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 永久居留身份<PermanentResidenceStatus> -->
    <el-form-item :label="$t('soi.customerSingleCreation.permanentResidenceStatus')" prop="permanentResidenceStatus">
      <el-select
        v-model="customerForm.permanentResidenceStatus"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.permanentResidenceStatusPlaceholder')"
      >
        <el-option
          v-for="item in permanentResidenceStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 性别<Gender> -->
    <el-form-item prop="gender" :label="$t('soi.customerSingleCreation.gender')">
      <el-select
        v-model="customerForm.gender"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.genderPlaceholder')"
      >
        <el-option
          v-for="item in genderOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 婚姻状况<MaritalStatus> -->
    <el-form-item prop="maritalStatus" :label="$t('soi.customerSingleCreation.maritalStatus')">
      <el-select
        v-model="customerForm.maritalStatus"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.maritalStatusPlaceholder')"
      >
        <el-option
          v-for="item in maritalStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 家庭收入<HouseholdIncome> -->
    <el-form-item :label="$t('soi.customerSingleCreation.householdIncome')">
      <!-- Form -->
      <el-col :span="11">
        <el-form-item
          :label="$t('soi.customerSingleCreation.householdIncomeAmountFrom')"
          prop="householdIncomeAmountFrom"
          label-width="50px"
        >
          <el-input
            v-model="customerForm.householdIncomeAmountFrom"
            :placeholder="$t('soi.customerSingleCreation.householdIncomeAmountFromPlaceholder')"
            class="input"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2" class="line" style="text-align: center;">-</el-col>
      <!-- To -->
      <el-col :span="11">
        <el-form-item
          :label="$t('soi.customerSingleCreation.householdIncomeAmountTo')"
          prop="householdIncomeAmountTo"
          label-width="50px"
        >
          <el-input
            v-model="customerForm.householdIncomeAmountTo"
            :placeholder="$t('soi.customerSingleCreation.householdIncomeAmountToPlaceholder')"
            class="input"
          />
        </el-form-item>
      </el-col>
    </el-form-item>
    <!-- 教育<Education> -->
    <el-form-item prop="education" :label="$t('soi.customerSingleCreation.education')">
      <el-select
        v-model="customerForm.education"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.educationPlaceholder')"
      >
        <el-option
          v-for="item in educationOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- Accommodation<Accommodation> -->
    <el-form-item prop="accommodation" :label="$t('soi.customerSingleCreation.accommodation')">
      <el-select
        v-model="customerForm.accommodation"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.accommodationPlaceholder')"
      >
        <el-option
          v-for="item in accommodation"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 地区<Region> -->
    <el-form-item :label="$t('soi.customerSingleCreation.region')" prop="region">
      <el-select
        v-model="customerForm.region"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.regionPlaceholder')"
      >
        <el-option
          v-for="item in residentialRegionName"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 就业状况<EmploymentStatus> -->
    <el-form-item prop="employmentStatus" :label="$t('soi.customerSingleCreation.employmentStatus')">
      <el-select
        v-model="customerForm.employmentStatus"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.employmentStatusPlaceholder')"
      >
        <el-option
          v-for="item in employmentStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 其他每月收入<OtherMonthlyIncome> -->
    <el-form-item :label="$t('soi.customerSingleCreation.otherMonthlyIncome')">
      <!-- Form -->
      <el-col :span="11">
        <el-form-item
          :label="$t('soi.customerSingleCreation.otherMonthlyIncomeFrom')"
          prop="otherMonthlyIncomeFrom"
          label-width="50px"
        >
          <el-input
            v-model="customerForm.otherMonthlyIncomeFrom"
            :placeholder="$t('soi.customerSingleCreation.otherMonthlyIncomeFromPlaceholder')"
            class="input"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2" class="line" style="text-align: center;">-</el-col>
      <!-- To -->
      <el-col :span="11">
        <el-form-item
          :label="$t('soi.customerSingleCreation.otherMonthlyIncomeTo')"
          prop="otherMonthlyIncomeTo"
          label-width="50px"
        >
          <el-input
            v-model="customerForm.otherMonthlyIncomeTo"
            :placeholder="$t('soi.customerSingleCreation.otherMonthlyIncomeToPlaceholder')"
            class="input"
          />
        </el-form-item>
      </el-col>
    </el-form-item>
    <!-- 每月收入<MonthlyIncome> -->
    <el-form-item :label="$t('soi.customerSingleCreation.monthlyIncome')">
      <!-- Form -->
      <el-col :span="11">
        <el-form-item
          :label="$t('soi.customerSingleCreation.monthlyIncomeFrom')"
          prop="monthlyIncomeFrom"
          label-width="50px"
        >
          <el-input
            v-model="customerForm.monthlyIncomeFrom"
            :placeholder="$t('soi.customerSingleCreation.monthlyIncomeFromPlaceholder')"
            class="input"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2" class="line" style="text-align: center;">-</el-col>
      <!-- To -->
      <el-col :span="11">
        <el-form-item
          :label="$t('soi.customerSingleCreation.monthlyIncomeTo')"
          prop="monthlyIncomeTo"
          label-width="50px"
        >
          <el-input
            v-model="customerForm.monthlyIncomeTo"
            :placeholder="$t('soi.customerSingleCreation.monthlyIncomeToPlaceholder')"
            class="input"
          />
        </el-form-item>
      </el-col>
    </el-form-item>
    <!-- 雇主行业<EmployerIndustry> -->
    <el-form-item :label="$t('soi.customerSingleCreation.employerIndustry')" prop="employerIndustry">
      <el-select
        v-model="customerForm.employerIndustry"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.employerIndustryPlaceholder')"
      >
        <el-option
          v-for="item in employerIndustry"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 职业<Occupation> -->
    <el-form-item :label="$t('soi.customerSingleCreation.occupation')" prop="occupation">
      <el-select
        v-model="customerForm.occupation"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.occupationPlaceholder')"
      >
        <el-option
          v-for="item in occupationOptions"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 首选方法<ContactPreferredMethod> -->
    <el-form-item :label="$t('soi.customerSingleCreation.contactPreferredMethod')" prop="contactPreferredMethod">
      <el-select
        v-model="customerForm.contactPreferredMethod"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.contactPreferredMethodPlaceholder')"
      >
        <el-option
          v-for="item in contactPreferredMethod"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 之前的语言<PreLanguage> -->
    <el-form-item :label="$t('soi.customerSingleCreation.preLanguage')" prop="preLanguage1">
      <el-select
        v-model="customerForm.preLanguage1"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.permanentResidenceStatusPlaceholder')"
      >
        <el-option
          v-for="item in preLanguage1"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 敏感状态<SensitiveStatus> -->
    <el-form-item :label="$t('soi.customerSingleCreation.sensitiveStatus')" prop="sensitiveStatus">
      <el-select
        v-model="customerForm.sensitiveStatus"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.sensitiveStatusPlaceholder')"
      >
        <el-option
          v-for="item in permanentResidenceStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 创建时间<CreationDate> -->
    <el-form-item :label="$t('soi.customerSingleCreation.createDate')" prop="createDate">
      <el-date-picker
        v-model="customerForm.createDate"
        type="daterange"
        value-format="timestamp"
        range-separator="-"
        unlink-panels
        :start-placeholder="$t('soi.customerSingleCreation.fromCreateDate')"
        :end-placeholder="$t('soi.customerSingleCreation.toCreateDate')"
        :default-time="['00:00:00', '23:59:59']"
      />
    </el-form-item>
    <!-- 客户状态<CustomerStatus> -->
    <el-form-item prop="customerStatus" :label="$t('soi.customerSingleCreation.customerStatus')">
      <el-select
        v-model="customerForm.customerStatus"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.customerStatusPlaceholder')"
      >
        <el-option
          v-for="item in customerStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 表名<TableName> -->
    <el-form-item :label="$t('soi.customerSingleCreation.tableName')" prop="tableName" hidden="hidden">
      <el-select
        v-model="customerForm.tableName"
        filterable
        allow-create
        class="input"
        default-first-option
        :placeholder="$t('soi.customerSingleCreation.tableNamePlaceholder')"
      >
        <el-option
          v-for="item in customerTableList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('soi.customerSingleCreation.accountDataCreation')" />
    <el-form-item :label="$t('soi.customerSingleCreation.accountType')" prop="accountType">
      <el-checkbox-group v-model="customerForm.accountType">
        <el-checkbox
          v-for="(item, index) in accountTypeOptions"
          :key="index"
          :disabled="(item.value === '001' || item.value === '002')"
          :label="item.value"
          name="accountType"
        >
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item
      v-if="customerForm.accountType.includes('003')"
      :label="$t('soi.customerSingleCreation.fexAccountCurrencyCode')"
      prop="currencyCode"
    >
      <el-select
        v-model="customerForm.currencyCode"
        class="select"
        :placeholder="$t('soi.customerSingleCreation.fexAccountCurrencyCodePlaceholder')"
      >
        <el-option
          v-for="(item, index) in currencyTypeOptions"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <div style="text-align:center;">
      <el-button type="primary" @click="submitForm()">{{ $t('soi.common.submit') }}</el-button>
      <el-button @click="resetForm()">{{ $t('soi.common.reset') }}</el-button>
    </div>
  </el-form>
</template>
<script>

import { getCustomerOccupationList, getTableList, optionalGenerate, singleGenerate } from '@/api/practice/data-creation'
import {
  accountTableMap,
  accountTypeOptions,
  ageGroupOptions,
  currencyTypeOptions,
  customerStatusOptions,
  educationOptions,
  employmentStatusOptions,
  genderOptions,
  getAccommodationLabelByValue,
  maritalStatusOptions,
  nationalityOptions,
  permanentResidenceStatusOptions
} from '@/views/practice/data-analysis/data-creation/customer/js'
import { mapGetters } from 'vuex'

export default {
  name: 'SingleCreation',
  data() {
    return {
      customerForm: {
        countryCode: '',
        clearingCode: '0001',
        branchNumberFrom: 177,
        branchNumberTo: 199,
        ageGroup: '26-35',
        countryOfBirth: 'HK',
        nationality1: 'HK',
        permanentResidenceStatus: 'Y',
        gender: 'F',
        maritalStatus: 'M',
        householdIncomeAmountFrom: null,
        householdIncomeAmountTo: null,
        education: 'U',
        accommodation: '',
        region: '',
        employmentStatus: 'F',
        otherMonthlyIncomeFrom: null,
        otherMonthlyIncomeTo: null,
        monthlyIncomeFrom: null,
        monthlyIncomeTo: null,
        employerIndustry: '',
        occupation: 'Advertising',
        contactPreferredMethod: '',
        preLanguage1: '',
        sensitiveStatus: '',
        createDate: [],
        customerStatusOptions: '',
        customerStatus: '',
        tableName: 'customer_data',
        userId: '',
        accountType: ['001', '002'],
        currencyCode: '',
        accountTotal: 1,
        accountTable: '',
        relAccountTable: ''
      },
      ageGroupOptions,
      nationalityOptions,
      permanentResidenceStatusOptions,
      genderOptions,
      maritalStatusOptions,
      educationOptions,
      employmentStatusOptions,
      customerStatusOptions,
      accountTypeOptions,
      currencyTypeOptions,
      accountTableMap,
      customerFormRules: {
        countryCode: [
          { required: true, message: this.$t('soi.customerSingleCreation.countryCodePlaceholder'), trigger: 'change' }
        ],
        clearingCode: [
          { required: true, message: this.$t('soi.customerSingleCreation.clearingCodePlaceholder'), trigger: 'change' }
        ],
        branchNumberFrom: [
          { required: true, message: this.$t('soi.customerSingleCreation.branchNumberFromPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        branchNumberTo: [
          { required: true, message: this.$t('soi.customerSingleCreation.branchNumberToPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        ageGroup: [
          { required: true, message: this.$t('soi.customerSingleCreation.ageGroupPlaceholder'), trigger: 'change' }
        ],
        countryOfBirth: [
          { required: true, message: this.$t('soi.customerSingleCreation.countryOfBirthPlaceholder'), trigger: 'change' }
        ],
        nationality1: [
          { required: true, message: this.$t('soi.customerSingleCreation.nationalityPlaceholder'), trigger: 'change' }
        ],
        permanentResidenceStatus: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.permanentResidenceStatusPlaceholder'),
            trigger: 'change'
          }
        ],
        gender: [
          { required: true, message: this.$t('soi.customerSingleCreation.genderPlaceholder'), trigger: 'change' }
        ],
        maritalStatus: [
          { required: true, message: this.$t('soi.customerSingleCreation.maritalStatusPlaceholder'), trigger: 'change' }
        ],
        householdIncomeAmountFrom: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.householdIncomeAmountFromPlaceholder'),
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        householdIncomeAmountTo: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.householdIncomeAmountToPlaceholder'),
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' },
          { validator: this.fromAndToValidator('householdIncomeAmountFrom', 'householdIncomeAmountTo'), trigger: 'blur' }
        ],
        education: [
          { required: true, message: this.$t('soi.customerSingleCreation.educationPlaceholder'), trigger: 'change' }
        ],
        accommodation: [
          { required: true, message: this.$t('soi.customerSingleCreation.accommodationPlaceholder'), trigger: 'change' }
        ],
        region: [
          { required: true, message: this.$t('soi.customerSingleCreation.regionPlaceholder'), trigger: 'change' }
        ],
        employmentStatus: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.employmentStatusPlaceholder'),
            trigger: 'change'
          }
        ],
        otherMonthlyIncomeFrom: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.otherMonthlyIncomeFromPlaceholder'),
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        otherMonthlyIncomeTo: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.otherMonthlyIncomeToPlaceholder'),
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' },
          { validator: this.fromAndToValidator('otherMonthlyIncomeFrom', 'otherMonthlyIncomeTo'), trigger: 'blur' }
        ],
        monthlyIncomeFrom: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.monthlyIncomeFromPlaceholder'),
            trigger: 'blur'
          },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        monthlyIncomeTo: [
          { required: true, message: this.$t('soi.customerSingleCreation.monthlyIncomeToPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' },
          { validator: this.fromAndToValidator('monthlyIncomeFrom', 'monthlyIncomeTo'), trigger: 'blur' }
        ],
        employerIndustry: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.employerIndustryPlaceholder'),
            trigger: 'change'
          }
        ],
        occupation: [
          { required: true, message: this.$t('soi.customerSingleCreation.occupationPlaceholder'), trigger: 'change' }
        ],
        contactPreferredMethod: [
          {
            required: true,
            message: this.$t('soi.customerSingleCreation.contactPreferredMethodPlaceholder'),
            trigger: 'change'
          }
        ],
        preLanguage1: [
          { required: true, message: this.$t('soi.customerSingleCreation.preLanguagePlaceholder'), trigger: 'change' }
        ],
        sensitiveStatus: [
          { required: true, message: this.$t('soi.customerSingleCreation.sensitiveStatusPlaceholder'), trigger: 'change' }
        ],
        createDate: [
          { required: true, message: this.$t('soi.customerSingleCreation.createDatePlaceholder'), trigger: 'blur' }
        ],
        customerStatusOptions: [
          { required: true, message: this.$t('soi.customerSingleCreation.customerStatusPlaceholder'), trigger: 'change' }
        ],
        customerStatus: [
          { required: true, message: this.$t('soi.customerSingleCreation.customerStatusPlaceholder'), trigger: 'change' }
        ],
        tableName: [
          { required: true, message: this.$t('soi.customerSingleCreation.tableNamePlaceholder'), trigger: 'change' }
        ],
        accountType: [
          { required: true, message: this.$t('soi.customerSingleCreation.accountTypePlaceholder'), trigger: 'change' }
        ],
        currencyCode: [
          { required: true, message: this.$t('soi.customerSingleCreation.currencyCodePlaceholder'), trigger: 'change' }
        ]
      },
      accommodation: [],
      countryOfBirthList: [],
      customerIDType: [],
      employerIndustry: [],
      nationality: [],
      occupationOptions: [],
      contactPreferredMethod: [],
      preLanguage1: [],
      customerTableList: [],
      residentialRegionName: [],
      sandBoxIdList: ['********************************', '55ce606d4e7441e193a58686d57318cb']
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    const _this = this
    _this.$emit('changeLoadingStatus', true)
    optionalGenerate().then(function(res) {
      _this.accommodation = res.data.accommodation.map(item => {
        return {
          value: item,
          label: getAccommodationLabelByValue(item)
        }
      })
      _this.countryOfBirthList = res.data.countryOfBirth
      _this.customerIDType = res.data.customerIDType
      _this.employerIndustry = res.data.employerIndustry
      _this.nationality = res.data.nationality
      _this.contactPreferredMethod = res.data.contactPreferredMethod
      _this.preLanguage1 = res.data.preLanguage1
      _this.residentialRegionName = res.data.residentialRegionName

      getCustomerOccupationList()
        .then(function(res) {
          _this.occupationOptions = res.data
        })
      getTableList({ type: 'Customer' })
        .then(function(res) {
          _this.customerTableList = res.data
          if (_this.customerTableList && _this.customerTableList.length > 0) {
            _this.customerForm.tableName = _this.customerTableList[0]
          }
        })
    }).finally(() => {
      _this.$emit('changeLoadingStatus', false)
    })
  },
  methods: {
    submitForm() {
      this.$refs['CustomerForm'].validate((valid) => {
        if (valid) {
          const _this = this
          _this.$emit('changeLoadingStatus', true)
          const requestData = _this.getCustomerRequestData()
          singleGenerate(requestData)
            .then(function(res) {
              _this.$message.success(res.message)
            })
            .finally(() => {
              _this.$emit('changeLoadingStatus', false)
            })
        } else {
          return false
        }
      })
    },
    getCustomerRequestData() {
      return {
        countryCode: this.customerForm.countryCode,
        clearingCode: this.customerForm.clearingCode,
        branchNumber: {
          'fr': this.customerForm.branchNumberFrom,
          'to': this.customerForm.branchNumberTo
        },
        ageGroup: this.customerForm.ageGroup,
        countryOfBirth: [this.customerForm.countryOfBirth],
        nationality1: [this.customerForm.nationality1],
        permanentResidenceStatus: this.customerForm.permanentResidenceStatus,
        gender: this.customerForm.gender,
        maritalStatus: this.customerForm.maritalStatus,
        householdIncome: {
          'fr': this.customerForm.householdIncomeAmountFrom,
          'to': this.customerForm.householdIncomeAmountTo
        },
        education: this.customerForm.education,
        accommodation: [this.customerForm.accommodation],
        region: [this.customerForm.region],
        employmentStatus: [this.customerForm.employmentStatus],
        otherMonthlyIncome: {
          'fr': this.customerForm.otherMonthlyIncomeFrom,
          'to': this.customerForm.otherMonthlyIncomeTo
        },
        monthlyIncome: {
          'fr': this.customerForm.monthlyIncomeFrom,
          'to': this.customerForm.monthlyIncomeTo
        },
        employerIndustry: [this.customerForm.employerIndustry],
        occupation: [this.customerForm.occupation],
        contactPreferredMethod: [this.customerForm.contactPreferredMethod],
        preLanguage1: [this.customerForm.preLanguage1],
        sensitiveStatus: this.customerForm.sensitiveStatus,
        createDate: {
          'fr': this.customerForm.createDate[0],
          'to': this.customerForm.createDate[1]
        },
        customerStatus: this.customerForm.customerStatus,
        tableName: this.customerForm.tableName,
        userId: this.userDetails.id,
        accountList: this.customerForm.accountType.map(item => {
          return {
            accountTable: this.accountTableMap.get(item),
            accountType: item,
            creationDate: {
              fr: this.customerForm.createDate[0],
              to: this.customerForm.createDate[1]
            },
            currencyCode: item === '003' ? this.customerForm.currencyCode : 'HKD',
            customerTable: this.customerForm.tableName,
            relAccountTable: item !== '001' && item !== '002' ? 'savingaccountmaster' : '',
            total: this.customerForm.accountTotal,
            userId: this.userDetails.id
          }
        }),
        sandBoxIdList: this.sandBoxIdList
      }
    },
    resetForm() {
      this.$refs['CustomerForm'].resetFields()
      this.customerForm = {
        countryCode: '',
        clearingCode: '0001',
        branchNumberFrom: 177,
        branchNumberTo: 199,
        ageGroup: '26-35',
        countryOfBirth: 'HK',
        nationality1: 'HK',
        permanentResidenceStatus: 'Y',
        gender: 'F',
        maritalStatus: 'M',
        householdIncomeAmountFrom: null,
        householdIncomeAmountTo: null,
        education: 'U',
        accommodation: '',
        region: '',
        employmentStatus: 'F',
        otherMonthlyIncomeFrom: null,
        otherMonthlyIncomeTo: null,
        monthlyIncomeFrom: null,
        monthlyIncomeTo: null,
        employerIndustry: '',
        occupation: 'Advertising',
        contactPreferredMethod: '',
        preLanguage1: '',
        sensitiveStatus: '',
        createDate: [],
        customerStatusOptions: '',
        customerStatus: '',
        tableName: 'customer_data',
        userId: '',
        accountType: ['001', '002'],
        currencyCode: 'HKD',
        accountTotal: 1,
        accountTable: 'savingaccountmaster',
        relAccountTable: 'savingaccountmaster'
      }
    },
    fromAndToValidator(fromField, toField) {
      return (rule, value, callback) => {
        if (Number.parseInt(this.customerForm[toField]) < Number.parseInt(this.customerForm[fromField])) {
          callback(
            new Error(
              this.$t(
                'soi.customerSingleCreation.numberGreaterThanPlaceholder',
                {
                  form: this.$t(`soi.customerSingleCreation.${fromField}`),
                  to: this.$t(`soi.customerSingleCreation.${toField}`)
                }
              )
            )
          )
        } else {
          callback()
        }
      }
    }
  }
}
</script>

<style>
@import url('../css/main.css');

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.generatData .el-input--medium .el-input__inner {
  margin: 0;
}

.el-date-editor .el-range-separator {
  width: 8%;
}
</style>
