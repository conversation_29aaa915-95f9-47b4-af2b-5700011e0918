<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.fundTradingDataCreation')">
      <!--Fund Trading-->
      <el-form
        ref="fundTradingForm"
        :rules="fundTradingFormrules"
        :model="fundTradingForm"
        label-position="left"
        label-width="160px"
      >
        <el-form-item :label="$t('soi.fundCreation.dataSource')" hidden="hidden">
          <el-select
            v-model="fundTradingForm.dataSource"
            style="width:100%;"
            :placeholder="$t('soi.fundCreation.dataSourcePlaceholder')"
          >
            <el-option label="10W" value="2" />
            <el-option label="Migration" value="1" />
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="Account Type From: " prop="accountTypeFr">-->
        <!--          <el-select v-model="fundTradingForm.accountTypeFr" style="width:100%;" placeholder="">-->
        <!--            <el-option label="Saving" value="Saving" />-->
        <!--            <el-option label="Current" value="Current" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item :label="$t('soi.fundCreation.channel')" prop="channelID">
          <el-select
            v-model="fundTradingForm.channelID"
            style="width:100%;"
            :placeholder="$t('soi.fundCreation.channelPlaceholder')"
          >
            <el-option v-for="item in channelList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.fundCreation.accountNumber')">
          <el-radio-group v-model="fundTradingFormSelectType">
            <el-radio-button label="1">{{ $t('soi.fundCreation.accountNumber') }}</el-radio-button>
            <el-radio-button label="2">{{ $t('soi.fundCreation.linkToDatatable') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="fundTradingFormSelectType === '1'" prop="branchFr">
          <el-input v-model="fundTradingForm.branchFr" :placeholder="$t('soi.fundCreation.accountNumberPlaceholder')" />
        </el-form-item>
        <el-form-item v-if="fundTradingFormSelectType === '2'">
          <el-upload
            :action="dataHost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="fundTradingFormFileListSuccess"
            :file-list="fundTradingFormFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary" @click="downloadTemplate('FundAccountTemplate.xlsx')">
              {{ $t('soi.fundCreation.downloadAccountTemplate') }}
            </el-button>
            <el-button size="small" type="primary">{{ $t('soi.common.upload') }}</el-button>
            <span slot="tip" class="el-upload__tip">{{ $t('soi.fundCreation.fileSizeAndTypeTip') }}</span>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('soi.fundCreation.transactionType')">
          <el-select v-model="fundTradingForm.transType" style="width:100%;" placeholder="">
            <el-option :label="$t('soi.fundCreation.buy')" value="Buy" />
            <el-option :label="$t('soi.fundCreation.sell')" value="Sell" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.fundCreation.transactionDetails')">
          <el-button
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="exportAvailableFundCode('hasData')"
          >
            {{ $t('soi.fundCreation.fundCodeList') }}
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="exportAvailableFundCode('')"
          >
            {{ $t('soi.fundCreation.fundCodeTemplate') }}
          </el-button>
          <el-row
            v-for="(data,index) in fundTradingForm.transToList"
            :key="index"
            :gutter="20"
            :style="{ margin: index > 0 ? '20px 0 0 0' : '0' }"
          >
            <el-col :span="5">
              <el-form-item :label="'0'+(index+1)" label-width="20px">
                <div v-show="data.type === '1'">
                  <el-upload
                    :action="dataHost+'/file/upload'"
                    :before-remove="beforeRemove"
                    :limit="1"
                    :on-change="exportData"
                    :on-exceed="handleExceed"
                    :on-success="fundTradingFormFileList2Success"
                    :file-list="fundTradingFormFileList2[index]"
                    class="upload-demo"
                    accept=".csv,.xls,.xlsx"
                  >
                    <el-button size="small" type="primary" @click="setFundTradingFormFileIndex(index)">
                      {{ $t('soi.fundCreation.batchUploadFundCode') }}
                    </el-button>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item
                :prop="'transToList.' + index + '.amountFr'"
                :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}"
                :label="fundTradingForm.transType ==='Buy'?$t('soi.fundCreation.amountFrom'):$t('soi.fundCreation.shareFrom')"
                label-width="130px"
              >
                <el-input
                  v-model="data.amountFr"
                  style="margin:0;"
                  :placeholder="fundTradingForm.transType ==='Buy'?$t('soi.fundCreation.amountFromPlaceholder'):$t('soi.fundCreation.shareFromPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :prop="'transToList.' + index + '.amountTo'"
                :rules="[
                  {required: true, validator: validateLimitValue, trigger: 'blur'},
                  {required: true, validator: (rule, value, callback) => validateAmount(rule, value, callback, data.amountFr,'amountFrom','amountTo'), trigger: 'blur'}
                ]"
                :label="$t('soi.fundCreation.amountTo')"
                label-width="40px"
              >
                <el-input
                  v-model="data.amountTo"
                  style="margin:0;"
                  :placeholder="fundTradingForm.transType ==='Buy'?$t('soi.fundCreation.amountToPlaceholder'):$t('soi.fundCreation.shareToPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="index === 0"
                type="primary"
                @click="addFundTradingFormCondition()"
              >{{
                $t('soi.fundCreation.add')
              }}
              </el-button>
              <el-button
                v-if="index>0"
                type="primary"
                @click="handleDeleteFundTradingForm(index)"
              >{{ $t('soi.common.delete') }}
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t('soi.fundCreation.transactionDate')">
          <el-col :span="11">
            <el-form-item :label="$t('soi.fundCreation.fromDate')" prop="transactionFromDate" label-width="100px">
              <el-date-picker
                v-model="fundTradingForm.transactionFromDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.fundCreation.fromDatePlaceholder')"
                :picker-options="pickerOptionsStart"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item :label="$t('soi.fundCreation.toDate')" prop="transactionToDate" label-width="100px">
              <el-date-picker
                v-model="fundTradingForm.transactionToDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.fundCreation.toDatePlaceholder')"
                :picker-options="pickerOptionsEnd"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('soi.fundCreation.frequency')" prop="frequency">
          <el-select v-model="fundTradingForm.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencyList" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.fundCreation.frequencyRate')" prop="frequencyRate">
          <el-input v-model="fundTradingForm.frequencyRate" />
        </el-form-item>
        <el-form-item :label="$t('soi.fundCreation.totalTransaction')" prop="total">
          <el-input-number
            v-model="fundTradingForm.total"
            :max="1000"
            :min="1"
            style="width: 100%;"
            :placeholder="$t('soi.fundCreation.totalPlaceholder')"
          />
        </el-form-item>
        <el-form-item label="Fund Table: " prop="fundTable" hidden="hidden">
          <el-select
            v-model="fundTradingForm.fundTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in fundTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable" hidden="hidden">
          <el-select
            v-model="fundTradingForm.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in transferTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm()">Submit</el-button>
          <el-button @click="resetForm()">Reset</el-button>
        </div>
      </el-form>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  fundGenerate,
  getAllFrequencyList,
  getChannelList,
  getFundCcyList,
  getFundRegionList,
  getFundTypeList,
  getTableList
} from '@/api/practice/data-creation'
import { DATA_CREATION_API_URL } from '@/contains'
import { downloadTemplate } from '@/api/practice/document'
import { mapGetters } from 'vuex'
import { exportAvailableFundCode } from '@/api/practice/date-export'

export default {
  name: 'FundTradingDataCreation',
  components: { CustomizeCard },
  data() {
    const validateLimitValue = (rule, value, callback) => {
      let a
      a = a = /^[0-9]\d*$/g

      if (!a.test(value)) {
        callback(new Error(this.$t('soi.common.invalidNumber')))
      } else {
        callback()
      }
    }
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
    const endDate = new Date()
    endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
    return {
      validateLimitValue: validateLimitValue,
      dataHost: DATA_CREATION_API_URL,
      loading: true,
      channelList: [],
      fundCurrencyList: [],
      fundTypeList: [],
      fundRegionList: [],
      frequencyList: [],
      fundTableNameList: [],
      transferTableNameList: [],
      fundTradingFormFileList: [],
      fundTradingFormFileList2: [],
      fundTradingFormSelectType: '1',
      fundTradingFormFileIndex: null,
      fundTradingForm: {
        dataSource: '1',
        accountTypeFr: 'Current',
        branchFr: '',
        branchFrLink: '',
        transType: 'Buy',
        transToList: [
          {
            settleAccType: 'Saving',
            amountFr: '100',
            amountTo: '1000',
            fundLink: '',
            type: '1',
            ccy: 'USD',
            fundType: 'Equity Funds',
            region: 'Europe'
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        fundTable: 'fund_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      },
      fundTradingFormrules: {
        branchFr: [{ required: true, message: this.$t('soi.fundCreation.accountNumberPlaceholder'), trigger: 'blur' }],
        transactionFromDate: [{
          required: true,
          message: this.$t('soi.fundCreation.fromDatePlaceholder'),
          trigger: 'change'
        }],
        transactionToDate: [{
          required: true,
          message: this.$t('soi.fundCreation.toDatePlaceholder'),
          trigger: 'change'
        }],
        frequency: [{ required: true, message: this.$t('soi.fundCreation.frequencyPlaceholder'), trigger: 'change' }],
        frequencyRate: [{
          required: true,
          message: this.$t('soi.fundCreation.frequencyRatePlaceholder'),
          trigger: 'blur'
        }],
        total: [{ required: true, message: this.$t('soi.fundCreation.totalPlaceholder'), trigger: 'blur' }],
        fundTable: [{ required: true, message: this.$t('soi.fundCreation.accountNumberPlaceholder'), trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table name.', trigger: 'change' }]
      },
      pickerOptionsStart: {
        disabledDate: time => {
          const beginDateVal = this.fundTradingForm.transactionToDate
          if (beginDateVal) {
            return (
              time.getTime() > new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
            )
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.fundTradingForm.transactionFromDate
          if (beginDateVal) {
            return (
              time.getTime() < new Date(beginDateVal).getTime() + 24 * 60 * 60 * 1000
            )
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  watch: {
    'fundTradingFormSelectType'(newValue) {
      if (newValue === '2') {
        this.$refs['fundTradingForm'].clearValidate(['branchFr'])
      }
    }
  },
  mounted() {
    this.initTableName()
    this.initSelectOptions()
    this.loading = false
  },
  methods: {
    initTableName() {
      const _this = this
      getTableList({ type: 'Fund' })
        .then(function(res) {
          _this.fundTableNameList = res.data
        })
        .catch(() => {
          _this.fundTableNameList = []
        })
      getTableList({ type: 'Transfer' })
        .then(function(res) {
          _this.transferTableNameList = res.data
        })
        .catch(() => {
          _this.transferTableNameList = []
        })
    },
    initSelectOptions() {
      const _this = this
      getAllFrequencyList()
        .then(function(res) {
          _this.frequencyList = res.data
        })
        .catch(() => {
          _this.frequencyList = []
        })
      getChannelList()
        .then(function(res) {
          _this.channelList = res.data.filter(item => item.id === 1)
        })
        .catch(() => {
          _this.channelList = []
        })
      getFundCcyList()
        .then(function(res) {
          _this.fundCurrencyList = res.data
        })
        .catch(() => {
          _this.fundCurrencyList = []
        })
      getFundTypeList()
        .then(function(res) {
          _this.fundTypeList = res.data
        })
        .catch(() => {
          _this.fundTypeList = []
        })
      getFundRegionList()
        .then(function(res) {
          _this.fundRegionList = res.data
        })
        .catch(() => {
          _this.fundRegionList = []
        })
    },
    handleExceed(files, fileList) {
      this.$message.warning(this.$t('soi.fundCreation.handleExceedMessage', {
        thisTimeChoose: files.length,
        totallyChoose: files.length + fileList.length
      }))
    },
    beforeRemove(file, fileList) {
      return this.$confirm(this.$t('soi.fundCreation.beforeRemoveMessage', { fileName: file.name }))
    },
    exportData(file, fileList) {
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.$message.error(this.$t('soi.fundCreation.fileSizeAndTypeTip'))
        fileList = []
        return isLt2M
      }
    },
    fundTradingFormFileListSuccess(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.fundTradingFormFileList = []
        return
      }
      this.fundTradingForm.branchFrLink = res.data
    },
    addFundTradingFormCondition() {
      const _this = this
      _this.fundTradingForm.transToList.push(
        {
          settleAccType: 'Saving',
          amountFr: '',
          amountTo: '',
          fundLink: '',
          type: '1',
          ccy: 'USD',
          fundType: 'Equity Funds',
          region: 'Europe'
        }
      )
    },
    fundTradingFormFileList2Success(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.fundTradingFormFileList2 = []
        return
      }
      this.fundTradingForm.transToList[this.fundTradingFormFileIndex].fundLink = res.data
    },
    setFundTradingFormFileIndex(index) {
      this.fundTradingFormFileIndex = index
    },
    handleDeleteFundTradingForm(index) {
      this.fundTradingForm.transToList.splice(index, 1)
    },
    submitForm() {
      this.$refs['fundTradingForm'].validate((valid) => {
        const _this = this
        if (valid) {
          _this.loading = true
          _this.fundTradingFormSelectType === '1' ? _this.fundTradingForm.branchFrLink = '' : _this.fundTradingForm.branchFr = ''
          _this.fundTradingForm.transToList.forEach((item, index) => {
            switch (item.type) {
              case '1':
                item.ccy = ''
                item.fundType = ''
                item.region = ''
                break
              case '2':
                item.fundLink = ''
                item.fundType = ''
                item.region = ''
                break
              case '3':
                item.fundLink = ''
                item.ccy = ''
                item.region = ''
                break
              case '4':
                item.fundLink = ''
                item.ccy = ''
                item.fundType = ''
                break
              default:
                break
            }
          })
          _this.fundTradingForm.userId = _this.userDetails.id
          fundGenerate(_this.fundTradingForm)
            .then(function(res) {
              _this.$alert(res.message)
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      const startDate = new Date()
      startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
      this.fundTradingForm = {
        dataSource: '1',
        accountTypeFr: 'Current',
        branchFr: '',
        branchFrLink: '',
        transType: 'Buy',
        transToList: [
          {
            settleAccType: 'Saving',
            amountFr: '',
            amountTo: '',
            fundLink: '',
            type: '1',
            ccy: 'USD',
            fundType: 'Equity Funds',
            region: 'Europe'
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '',
        fundTable: 'fund_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      }
    },
    exportAvailableFundCode,
    downloadTemplate,
    validateAmount(rule, value, callback, amountFrValue, amountFr, amountTo) {
      if (value === '' || value === null) {
        callback(new Error(this.$t('soi.creditCardCreation.transactionAmountToPlaceholder')))
      } else if (Number(value) < Number(amountFrValue)) {
        callback(new Error(
          this.$t(
            'soi.fundCreation.numberGreaterThanPlaceholder',
            {
              form: this.$t(`soi.fundCreation.${amountFr}`),
              to: this.$t(`soi.fundCreation.${amountTo}`)
            }
          )
        ))
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
