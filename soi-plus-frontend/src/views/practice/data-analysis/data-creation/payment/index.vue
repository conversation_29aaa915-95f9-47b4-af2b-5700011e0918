<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.paymentDataCreation')">
      <!--Payment-->
      <el-form
        ref="paymentForm"
        :rules="paymentFormRules"
        :model="paymentForm"
        label-position="left"
        label-width="160px"
      >
        <el-form-item :label="$t('soi.paymentCreation.dataSource')" hidden="hidden">
          <el-select
            v-model="paymentForm.dataSource"
            style="width:100%;"
            :placeholder="$t('soi.paymentCreation.dataSourcePlaceholder')"
          >
            <el-option label="10W" value="2" />
            <el-option label="Migration" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.accountTypeFrom')" prop="accountTypeFr">
          <el-select
            v-model="paymentForm.accountTypeFr"
            style="width:100%;"
            :placeholder="$t('soi.paymentCreation.accountTypeFromPlaceholder')"
          >
            <el-option label="Saving" value="Saving" />
            <el-option label="Current" value="Current" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.channel')" prop="channelID">
          <el-select
            v-model="paymentForm.channelID"
            style="width:100%;"
            :placeholder="$t('soi.paymentCreation.channelPlaceholder')"
          >
            <el-option v-for="item in channelList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.moveMoneyFrom')">
          <el-radio-group v-model="paymentFormSelectType">
            <el-radio-button label="1">{{ $t('soi.paymentCreation.accountNumber') }}</el-radio-button>
            <el-radio-button label="2">{{ $t('soi.paymentCreation.linkToDatatable') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="paymentFormSelectType === '1'"
          prop="moveFr"
        >
          <el-input v-model="paymentForm.moveFr" :placeholder="$t('soi.paymentCreation.moveMoneyFromPlaceholder')" />
        </el-form-item>
        <el-form-item v-if="paymentFormSelectType === '2'">
          <el-upload
            :action="dataHost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="paymentFormFileListSuccess"
            :file-list="paymentFormFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary" @click="downloadTemplate('PaymentAccountTemplate.xlsx')">
              {{ $t('soi.paymentCreation.downloadBranchTemplate') }}
            </el-button>
            <el-button size="small" type="primary">{{ $t('soi.paymentCreation.upload') }}</el-button>
            <span slot="tip" class="el-upload__tip">{{ $t('soi.paymentCreation.uploadFileRule') }}</span>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.moveMoneyTo')">
          <el-row
            v-for="(data,index) in paymentForm.moveToList"
            :key="index"
            :gutter="20"
            :style="{ margin: index > 0 ? '20px 0 0 0' : '0' }"
          >
            <el-col :span="8">
              <el-form-item
                :label="$t('soi.paymentCreation.industry')+(index+1)"
                :prop="'moveToList.' + index + '.payeeCategoryID'"
                :rules="{required: true, message: $t('soi.paymentCreation.industryPlaceholder'), trigger: 'change'}"
                label-width="80px"
              >
                <el-select
                  v-model="data.payeeCategoryID"
                  style="width:100%;"
                  :placeholder="$t('soi.paymentCreation.industryPlaceholder')"
                >
                  <el-option
                    v-for="item in paymentIndustryList"
                    :key="item.payeeCategoryID"
                    :value="item.payeeCategoryID"
                    :label="item.payeeCategory"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                :prop="'moveToList.' + index + '.amountFr'"
                :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}"
                :label="$t('soi.paymentCreation.amountFrom')"
                label-width="120px"
              >
                <el-input
                  v-model="data.amountFr"
                  style="margin:0;"
                  :placeholder="$t('soi.paymentCreation.amountFromPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :prop="'moveToList.' + index + '.amountTo'"
                :rules="[
                  {required: true, validator: validateLimitValue, trigger: 'blur'},
                  {required: true, validator: (rule, value, callback) => validateAmount(rule, value, callback, data.amountFr,'amountFrom','amountTo'), trigger: 'blur'}
                ]"
                :label="$t('soi.paymentCreation.amountTo')"
                label-width="40px"
              >
                <el-input
                  v-model="data.amountTo"
                  style="margin:0;"
                  :placeholder="$t('soi.paymentCreation.amountToPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="index === 0"
                type="primary"
                @click="addPaymentFormCondition()"
              >{{
                $t('soi.common.add')
              }}
              </el-button>
              <el-button
                v-if="index > 0"
                type="primary"
                @click="handleDeletePaymentForm(index)"
              >{{ $t('soi.common.delete') }}
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.transactionDate')">
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.paymentCreation.transactionDateFrom')"
              prop="transactionFromDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="paymentForm.transactionFromDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.paymentCreation.transactionDateFromPlaceholder')"
                :picker-options="pickerOptionsStart"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center">-</el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.paymentCreation.transactionDateTo')"
              prop="transactionToDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="paymentForm.transactionToDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.paymentCreation.transactionDateToPlaceholder')"
                :picker-options="pickerOptionsEnd"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.frequency')" prop="frequency">
          <el-select
            v-model="paymentForm.frequency"
            style="width:100%;"
            :placeholder="$t('soi.paymentCreation.frequencyPlaceholder')"
          >
            <el-option v-for="item in frequencyList" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.frequencyRate')" prop="frequencyRate">
          <el-input
            v-model="paymentForm.frequencyRate"
            :placeholder="$t('soi.paymentCreation.frequencyRatePlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('soi.paymentCreation.totalTransaction')" prop="total">
          <el-input-number
            v-model="paymentForm.total"
            :max="1000"
            :min="1"
            style="width: 100%;"
            :placeholder="$t('soi.paymentCreation.totalTransactionPlaceholder')"
          />
        </el-form-item>
        <el-form-item label="Payment Table: " prop="paymentTable" hidden="hidden">
          <el-select
            v-model="paymentForm.paymentTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in paymentTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable" hidden="hidden">
          <el-select
            v-model="paymentForm.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in transferTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm()">{{ $t('soi.common.submit') }}</el-button>
          <el-button @click="resetForm()">{{ $t('soi.common.reset') }}</el-button>
        </div>
      </el-form>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  getAllFrequencyList,
  getChannelList,
  getPaymentIndustryList,
  getTableList,
  paymentGenerate
} from '@/api/practice/data-creation'
import { DATA_CREATION_API_URL } from '@/contains'
import { downloadTemplate } from '@/api/practice/document'
import { mapGetters } from 'vuex'

export default {
  name: 'PaymentDataCreation',
  components: { CustomizeCard },
  data() {
    const validateLimitValue = (rule, value, callback) => {
      let a
      a = a = /^[0-9]\d*$/g

      if (!a.test(value)) {
        callback(new Error(this.$t('soi.common.invalidNumber')))
      } else {
        callback()
      }
    }
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
    const endDate = new Date()
    endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
    return {
      validateLimitValue: validateLimitValue,
      dataHost: DATA_CREATION_API_URL,
      loading: true,
      channelList: [],
      transferTableNameList: [],
      paymentTableNameList: [],
      paymentFormSelectType: '1',
      paymentFormFileList: [],
      paymentIndustryList: [],
      frequencyList: [],
      paymentForm: {
        userId: '',
        dataSource: '1',
        accountTypeFr: 'Saving',
        moveFr: '',
        moveFrLink: '',
        moveToList: [
          {
            payeeCategoryID: '003',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        paymentTable: 'payment_transaction',
        transferTable: 'transfer_transaction'
      },
      paymentFormRules: {
        accountTypeFr: [{
          required: true,
          message: this.$t('soi.paymentCreation.accountTypeFromPlaceholder'),
          trigger: 'change'
        }],
        moveFr: [{ required: true, message: this.$t('soi.paymentCreation.moveMoneyFromPlaceholder'), trigger: 'blur' }],
        transactionFromDate: [{
          required: true,
          message: this.$t('soi.paymentCreation.transactionDateFromPlaceholder'),
          trigger: 'change'
        }],
        transactionToDate: [{
          required: true,
          message: this.$t('soi.paymentCreation.transactionDateToPlaceholder'),
          trigger: 'change'
        }],
        frequency: [{ required: true, message: this.$t('soi.paymentCreation.frequencyPlaceholder'), trigger: 'change' }],
        frequencyRate: [{
          required: true,
          message: this.$t('soi.paymentCreation.frequencyRatePlaceholder'),
          trigger: 'blur'
        }],
        total: [{ required: true, message: this.$t('soi.paymentCreation.totalTransactionPlaceholder'), trigger: 'blur' }],
        paymentTable: [{ required: true, message: 'Please input the payment table.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table.', trigger: 'change' }]
      },
      pickerOptionsStart: {
        disabledDate: time => {
          const beginDateVal = this.paymentForm.transactionToDate
          if (beginDateVal) {
            return (
              time.getTime() > new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
            )
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.paymentForm.transactionFromDate
          if (beginDateVal) {
            return (
              time.getTime() < new Date(beginDateVal).getTime() + 24 * 60 * 60 * 1000
            )
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  watch: {
    'paymentFormSelectType'(newValue) {
      if (newValue === '2') {
        this.$refs['paymentForm'].clearValidate(['moveFr'])
      }
    }
  },
  mounted() {
    this.initTableName()
    this.initSelectOptions()
    this.loading = false
  },
  methods: {
    initSelectOptions() {
      const _this = this
      getAllFrequencyList()
        .then(function(res) {
          _this.frequencyList = res.data
        })
        .catch(() => {
          _this.frequencyList = []
        })
      getChannelList()
        .then(function(res) {
          _this.channelList = res.data
        })
        .catch(() => {
          _this.channelList = []
        })
      getPaymentIndustryList()
        .then(function(res) {
          _this.paymentIndustryList = res.data
        })
        .catch(() => {
          _this.paymentIndustryList = []
        })
    },
    initTableName() {
      const _this = this
      getTableList({ type: 'Payment' })
        .then(function(res) {
          _this.paymentTableNameList = res.data
        })
        .catch(() => {
          _this.paymentTableNameList = []
        })
      getTableList({ type: 'Transfer' })
        .then(function(res) {
          _this.transferTableNameList = res.data
        })
        .catch(() => {
          _this.transferTableNameList = []
        })
    },
    handleExceed(files, fileList) {
      this.$message.warning(this.$t('soi.paymentCreation.handleExceedMessage', {
        thisTimeChoose: files.length,
        totallyChoose: files.length + fileList.length
      }))
    },
    beforeRemove(file, fileList) {
      return this.$confirm(this.$t('soi.paymentCreation.beforeRemoveMessage', { fileName: file.name }))
    },
    exportData(file, fileList) {
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.$message.error(this.$t('soi.paymentCreation.uploadFileRule'))
        fileList = []
        return isLt2M
      }
    },
    paymentFormFileListSuccess(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.paymentFormFileList = []
        return
      }
      this.paymentForm.moveFrLink = res.data
    },
    addPaymentFormCondition() {
      var _this = this
      _this.paymentForm.moveToList.push(
        {
          payeeCategoryID: '',
          amountFr: '',
          amountTo: ''
        }
      )
    },
    handleDeletePaymentForm(index) {
      this.paymentForm.moveToList.splice(index, 1)
    },
    submitForm() {
      this.$refs['paymentForm'].validate((valid) => {
        const _this = this
        if (valid) {
          _this.loading = true
          _this.paymentFormSelectType === '1' ? _this.paymentForm.moveFrLink = '' : _this.paymentForm.moveFr = ''
          _this.paymentForm.userId = _this.userDetails.id
          paymentGenerate(_this.paymentForm)
            .then(function(res) {
              _this.$alert(res.message)
              _this.resetForm()
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      const startDate = new Date()
      startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
      this.paymentForm = {
        userId: '',
        dataSource: '1',
        accountTypeFr: 'Saving',
        moveFr: '',
        moveFrLink: '',
        moveToList: [
          {
            payeeCategoryID: '003',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        paymentTable: 'payment_transaction',
        transferTable: 'transfer_transaction'
      }
    },
    downloadTemplate,
    validateAmount(rule, value, callback, amountFrValue, amountFr, amountTo) {
      if (value === '' || value === null) {
        callback(new Error(this.$t('soi.paymentCreation.amountToPlaceholder')))
      } else if (Number(value) < Number(amountFrValue)) {
        callback(new Error(
          this.$t(
            'soi.paymentCreation.numberGreaterThanPlaceholder',
            {
              form: this.$t(`soi.paymentCreation.${amountFr}`),
              to: this.$t(`soi.paymentCreation.${amountTo}`)
            }
          )
        ))
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
