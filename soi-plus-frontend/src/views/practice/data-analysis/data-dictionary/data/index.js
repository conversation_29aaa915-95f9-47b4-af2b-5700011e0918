import { category } from '@/views/practice/data-analysis/data-dictionary/data/category'
import { useCaseDataDataDictionary } from '@/views/practice/data-analysis/data-dictionary/data/data-dictionary'

export function findSubcategoryById(id) {
  if (id === '1') {
    return useCaseDataDataDictionary
  }

  return useCaseDataDataDictionary
}

export function findCategoryNameById(id) {
  for (let i = 0; i < category.length; i++) {
    if (category[i].id === id) {
      return category[i].name
    }
  }
  return category[0].name
}

export function findDataDictionaryDetails(id, index) {
  const dataDictionary = findSubcategoryById(id)

  return dataDictionary[index].tables || dataDictionary[0].tables
}
