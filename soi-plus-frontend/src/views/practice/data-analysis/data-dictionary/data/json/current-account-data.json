{"zh": {"title": "活期账户数据", "data_table_description": ["该数据表包含了活期账户的所有参数信息，例如账户号码，余额，状态等。 ", "可能用到的业务场景：查询银行客户个人账户信息。"], "data_table_type": "SimBank数据字典 - 活期账户数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["客户编号。该编号是系统自动生成的唯一标志银行客户。"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["账户号码"], "description": []}, {"column_name": "Account<PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["账户状态"], "description": ["可能的值：A-激活"]}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["本地货币类型"], "description": ["默认情况下，货币类型设置为HKD"]}, {"column_name": "LedgeBalance", "data_type": "decimal", "length": 18, "remark": ["账户余额"], "description": []}, {"column_name": "HoldingBalance", "data_type": "decimal", "length": 18, "remark": ["账户被冻结的金额"], "description": []}, {"column_name": "AvailableBalance", "data_type": "decimal", "length": 18, "remark": ["账户可用余额"], "description": []}, {"column_name": "LastUpdatedDate", "data_type": "decimal", "length": 20, "remark": ["账户最近更新日期"], "description": []}, {"column_name": "ChequeBookType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["支票类型"], "description": ["可能的值：", "S-短", "L-长"]}, {"column_name": "ChequeBookSize", "data_type": "decimal", "length": 3, "remark": ["支票长度"], "description": ["可能的值：", "0-50, ", "50-100"]}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["账户创建时间"], "description": []}]}, "en": {"title": "Current Account Data", "data_table_description": ["This data table contains all the parameter information of Current Account, such as account number, balance, status, etc.", "Possible business scenarios: querying personal account information of bank customers."], "data_table_type": "SimBank Data Dictionary - Current Account Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["A country code is a short, standardized sequence of letters or numbers used to represent a specific country or geographic region. e.g., HK for Hong Kong."], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Clearing code is code for different bank. for example BBIC,HSBC eg"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Branch code is code for different branch of the same bank"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Used for sandbox allocation for each user."], "description": []}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["Branch code + runing squence that  was defined in system configeration table. Each bank customer has a unique customernumber"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["The current account number of the customer"], "description": []}, {"column_name": "Account<PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["The state of the account"], "description": ["Possible: A-active"]}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Local currency type"], "description": ["By default, the local ccy is HKD."]}, {"column_name": "LedgeBalance", "data_type": "decimal", "length": 18, "remark": ["The balance of the account"], "description": []}, {"column_name": "HoldingBalance", "data_type": "decimal", "length": 18, "remark": ["The amount holding in the account"], "description": []}, {"column_name": "AvailableBalance", "data_type": "decimal", "length": 18, "remark": ["The available amount in the account"], "description": []}, {"column_name": "LastUpdatedDate", "data_type": "decimal", "length": 20, "remark": ["The last update date of the account"], "description": []}, {"column_name": "ChequeBookType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["When it is set S, that means the chequebooktype is Short"], "description": ["Possible values:", "S-<PERSON>,", "L-Long."]}, {"column_name": "ChequeBookSize", "data_type": "decimal", "length": 3, "remark": ["The size of a cheque book"], "description": ["Possible values:", "0-50, ", "50-100"]}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["Account creation date in the bank system"], "description": []}]}, "cht": {"title": "活期账户数据", "data_table_description": ["该数据表包含了活期账户的所有参数信息，例如账户号码，余额，状态等。 ", "可能用到的业务场景：查询银行客户个人账户信息。"], "data_table_type": "SimBank数据字典 - 活期账户数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["客户编号。该编号是系统自动生成的唯一标志银行客户。"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["账户号码"], "description": []}, {"column_name": "Account<PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["账户状态"], "description": ["可能的值：A-激活"]}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["本地货币类型"], "description": ["默认情况下，货币类型设置为HKD"]}, {"column_name": "LedgeBalance", "data_type": "decimal", "length": 18, "remark": ["账户余额"], "description": []}, {"column_name": "HoldingBalance", "data_type": "decimal", "length": 18, "remark": ["账户被冻结的金额"], "description": []}, {"column_name": "AvailableBalance", "data_type": "decimal", "length": 18, "remark": ["账户可用余额"], "description": []}, {"column_name": "LastUpdatedDate", "data_type": "decimal", "length": 20, "remark": ["账户最近更新日期"], "description": []}, {"column_name": "ChequeBookType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["支票类型"], "description": ["可能的值：", "S-短", "L-长"]}, {"column_name": "ChequeBookSize", "data_type": "decimal", "length": 3, "remark": ["支票长度"], "description": ["可能的值：", "0-50, ", "50-100"]}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["账户创建时间"], "description": []}]}}