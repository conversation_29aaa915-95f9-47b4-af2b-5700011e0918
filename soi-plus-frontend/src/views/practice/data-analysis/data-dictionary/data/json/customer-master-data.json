{"zh": {"title": "客户信息数据", "data_table_description": ["本数据表中包含了每个银行客户的所有个人信息（如身份证号码，姓名，国籍，居住地区等）。", "在该表中， CustomerNumber 作为每一个客户在整个数据库中的唯一标志，可以与其他表中的 CustomerNumber 参数关联，做联表查询。 可能用到的业务场景： 查询银行客户个人基础信息，从不同维度分析银行客户的交易行为等等。"], "data_table_type": "SimBank数据字典 - 客户信息数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["客户编号。该编号是系统自动生成的唯一标志银行客户。"], "description": []}, {"column_name": "FirstName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户名"], "description": []}, {"column_name": "LastName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户姓"], "description": []}, {"column_name": "CustomerID1", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["身份证件号码1"], "description": []}, {"column_name": "CustomerIDType1", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["身份证件类型1"], "description": []}, {"column_name": "IssueCountry1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["证件1发证国家"], "description": []}, {"column_name": "IssueDate1", "data_type": "decimal", "length": 20, "remark": ["证件1 颁发时间"], "description": []}, {"column_name": "CustomerID2", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["身份证件号码2"], "description": []}, {"column_name": "CustomerIDType2", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["身份证件类型2"], "description": []}, {"column_name": "IssueCountry2", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["证件2发证国家"], "description": []}, {"column_name": "IssueDate2", "data_type": "decimal", "length": 20, "remark": ["证件2颁发时间"], "description": []}, {"column_name": "DateOfBirth", "data_type": "decimal", "length": 20, "remark": ["出生日期"], "description": []}, {"column_name": "Age", "data_type": "int", "length": 11, "remark": ["年龄"], "description": []}, {"column_name": "AgeGroup", "data_type": "<PERSON><PERSON><PERSON>", "length": 100, "remark": ["年龄组"], "description": []}, {"column_name": "SeniorInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["是否老年人"], "description": []}, {"column_name": "MinorInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["是否未成年人"], "description": []}, {"column_name": "ChineseName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["中文名称"], "description": []}, {"column_name": "Gender", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["性别"], "description": ["可能的值:", "M-<PERSON> （男性）", "F-Female （女性）"]}, {"column_name": "Nationality1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["国籍1"], "description": []}, {"column_name": "Nationality2", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["国籍2"], "description": []}, {"column_name": "Nationality3", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["国籍3"], "description": []}, {"column_name": "PermanentResidenceStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["永久居住状态"], "description": ["可能的值:", "Y-Yes, ", "N-No."]}, {"column_name": "MaritalStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["婚姻状态"], "description": ["可能的值: ", "S-Single（单身）", "M-<PERSON>（已婚）", "D-Divorced （离异）", "W - <PERSON> （丧偶）"]}, {"column_name": "Education", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["受教育程度"], "description": ["可能的值: ", "U - University （大学）", "M - <PERSON> （硕士）", "S - Secondary （中学）", "P - Post Secondary （职业学院）", "X - Others （其他）", "J - Primary/Junior （中小学）"]}, {"column_name": "WeChatID", "data_type": "<PERSON><PERSON><PERSON>", "length": 30, "remark": ["微信号"], "description": []}, {"column_name": "Accommodation", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["居住性质"], "description": ["可能的值:", "S-Self Owned （自置物业）", "P-Private property （私人住宅）", "H-Home ownership scheme（居者有其屋计划（通常指政府资助的购房计划）", "F-Friends/Relatives （亲友住处）", "U-Public Housing （公共房屋（通常指政府提供的廉租房屋）", "R-Rented （租住）", "Q-Quarters  宿舍（通常指提供给特定群体如员工或学生的住宿）"]}, {"column_name": "MobilePhoneNumber1", "data_type": "<PERSON><PERSON><PERSON>", "length": 30, "remark": ["移动电话号码1"], "description": []}, {"column_name": "ResidentialAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 1000, "remark": ["居住地址1"], "description": []}, {"column_name": "ResidentialAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["居住地址2"], "description": []}, {"column_name": "ResidentialAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["居住地址3"], "description": []}, {"column_name": "ResidentialAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["居住地址4"], "description": []}, {"column_name": "ResidentialDistrictName", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户居住所在行政区"], "description": []}, {"column_name": "ResidentialRegionName", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户居住所在行政区的子区域"], "description": []}, {"column_name": "EmailAddress1", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["邮箱1"], "description": []}, {"column_name": "CountryOfResidence", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["居住国家"], "description": []}, {"column_name": "CountryOfBirth", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["客户出生国家"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["客户创建时间"], "description": []}, {"column_name": "ContactPreferredMethod", "data_type": "<PERSON><PERSON><PERSON>", "length": 6, "remark": ["客户期望的联系方式"], "description": ["可能的值: ", "Phonecall （电话呼叫）", "<PERSON><PERSON> （邮件）", "Mail （信件）", "SMS （短信）"]}, {"column_name": "PreLanguage1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["客户偏好的服务语言"], "description": ["可能的值:", "English （英语）", "Cantonese （粤语）", "Mandarin （普通话）"]}, {"column_name": "PreTimeFrom1", "data_type": "decimal", "length": 20, "remark": ["客户偏好的服务开始时间"], "description": []}, {"column_name": "PreTimeTo1", "data_type": "decimal", "length": 20, "remark": ["客户偏好的服务结束时间"], "description": []}, {"column_name": "CustPresentAddPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户当前住址的居住期限"], "description": []}, {"column_name": "CustPresentAddUpdateDate", "data_type": "decimal", "length": 20, "remark": ["客户当前住址的更新时间"], "description": []}, {"column_name": "CustPreviousAddPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户历史住址的居住期限"], "description": []}, {"column_name": "CustPreviousAddUpdateDate", "data_type": "decimal", "length": 20, "remark": ["客户历史住址的更新时间"], "description": []}, {"column_name": "SensitiveStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["客户信息被访问的敏感状态。Yes表示该客户的信息访问会有限制。"], "description": ["可能的值:", "Yes", "No"]}, {"column_name": "HKIDFirstIssue", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["香港身份证首次颁发时间"], "description": []}, {"column_name": "HKIDIssueDate", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["当前所持香港身份证颁发时间"], "description": []}, {"column_name": "PersonalInfoUpdateDate", "data_type": "decimal", "length": 20, "remark": ["最近一次个人信息修改时间"], "description": []}, {"column_name": "MaritalDate", "data_type": "decimal", "length": 20, "remark": ["结婚日期"], "description": []}, {"column_name": "SpouseName", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["配偶姓名"], "description": []}, {"column_name": "SpouseIDType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["配偶证件类型"], "description": []}, {"column_name": "SpouseID", "data_type": "<PERSON><PERSON><PERSON>", "length": 12, "remark": ["配偶证件号码"], "description": []}, {"column_name": "SpouseDateOfBirth", "data_type": "decimal", "length": 20, "remark": ["配偶生日"], "description": []}, {"column_name": "HouseholdIncome", "data_type": "decimal", "length": 18, "remark": ["家庭年收入"], "description": []}, {"column_name": "EmploymentStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["客户雇佣状态"], "description": ["可能的值:", "F - Full time （全职）", "P - Part time （兼职）", "S - Self employed （自雇）", "T - Student （学生）", "R - Retired （退休人员）", "U - Unemployed （失业）"]}, {"column_name": "Occupation", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["职业"], "description": []}, {"column_name": "EmployerCompanyName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["客户所在的公司名称"], "description": []}, {"column_name": "Position", "data_type": "<PERSON><PERSON><PERSON>", "length": 500, "remark": ["客户的工作职位"], "description": []}, {"column_name": "MonthlySalary", "data_type": "decimal", "length": 18, "remark": ["月工资"], "description": []}, {"column_name": "OtherMonthlyIncome", "data_type": "decimal", "length": 18, "remark": ["其他月收入"], "description": []}, {"column_name": "CompanyAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 4000, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CompanyAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CompanyAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CompanyAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CustPresentEmploymentPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["当前工作的工作年限"], "description": []}, {"column_name": "CusPrestEmploymentSinceDate", "data_type": "decimal", "length": 20, "remark": ["当前工作开始时间"], "description": []}, {"column_name": "CustPreviousEmploymntPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["上一份工作的工作年限"], "description": []}, {"column_name": "CustPreviousEmploymntDate", "data_type": "decimal", "length": 20, "remark": ["上一份工作的工作开始时间"], "description": []}, {"column_name": "EmployerIndustry", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["公司所在行业"], "description": []}, {"column_name": "CustRelationMgrCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户经理，"], "description": []}, {"column_name": "ResidentAddressMaintBranch", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["客户居住地的分行编号"], "description": []}, {"column_name": "CustomerStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户状态"], "description": ["可能的值: ", "Null - Others （其他）", "S-Staff （员工）", "R-Relative of staff （员工亲戚）", "P-Spouse of staff （员工配偶）", "D-Bank director （银行董事）"]}, {"column_name": "UserId", "data_type": "<PERSON><PERSON><PERSON>", "length": 32, "remark": ["用户ID，唯一标志该平台的一个用户"], "description": []}]}, "en": {"title": "Customer Master Data", "data_table_description": ["This data table contains all demographic  information of each bank customer (such as ID card number, name, nationality, residence area, etc.).", "In this table, CustomerNumber is the unique identifier of each customer in the entire database. It can be associated with the CustomerNumber parameter in other tables for associated table query.\nPossible business scenarios: query the basic personal information of bank customers, analyze the transaction behavior of bank customers from multiple dimensions, etc."], "data_table_type": "SimBank Data Dictionary - Customer Master Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["A country code is a short, standardized sequence of letters or numbers used to represent a specific country or geographic region. e.g., HK for Hong Kong."], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Clearing code is code for different bank. for example BBIC,HSBC eg."], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Branch code is code for different branch of the same bank."], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Used for sandbox allocation for each user."], "description": []}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["Branch code + runing squence that  was defined in system configeration table. Each bank customer has a unique customernumber."], "description": []}, {"column_name": "FirstName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Customer first name"], "description": []}, {"column_name": "LastName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Customer last name"], "description": []}, {"column_name": "CustomerID1", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["The person's ID was assigned by government."], "description": []}, {"column_name": "CustomerIDType1", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Customer ID, Passport ID,...."], "description": []}, {"column_name": "IssueCountry1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["The issue country of the customer’s ID/passport."], "description": []}, {"column_name": "IssueDate1", "data_type": "decimal", "length": 20, "remark": ["Customer ID issue date "], "description": []}, {"column_name": "CustomerID2", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["The person's ID was assigned by government."], "description": []}, {"column_name": "CustomerIDType2", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Customer ID, Passport ID,...."], "description": []}, {"column_name": "IssueCountry2", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["The issue country of the customer’s ID/passport."], "description": []}, {"column_name": "IssueDate2", "data_type": "decimal", "length": 20, "remark": ["Customer ID issue date "], "description": []}, {"column_name": "DateOfBirth", "data_type": "decimal", "length": 20, "remark": ["The birthday of the customer(yyyy-MM-dd)"], "description": []}, {"column_name": "Age", "data_type": "int", "length": 11, "remark": ["Cutomer age"], "description": []}, {"column_name": "AgeGroup", "data_type": "<PERSON><PERSON><PERSON>", "length": 100, "remark": ["Customer age group"], "description": []}, {"column_name": "SeniorInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Whether the customer is a senior  customer"], "description": []}, {"column_name": "MinorInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Whether the customer is a minor customer."], "description": []}, {"column_name": "ChineseName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["The Chinese name of the customer shown in his/her ID card"], "description": []}, {"column_name": "Gender", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["The gender of the customer.When it is set M,that means the customer is a Male."], "description": ["Possbile values:", "M-Male", "F-Female"]}, {"column_name": "Nationality1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Nationality is a legal relationship between an individual person and a state, e.g US, HK, CN . "], "description": []}, {"column_name": "Nationality2", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Nationality is a legal relationship between an individual person and a state, e.g US, HK, CN . "], "description": []}, {"column_name": "Nationality3", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Nationality is a legal relationship between an individual person and a state, e.g US, HK, CN . "], "description": []}, {"column_name": "PermanentResidenceStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["falseWhether the customer is a Hong Kong permanent resident or not."], "description": ["Possible values:", "Y-Yes, ", "N-No."]}, {"column_name": "MaritalStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Marital status describes a person’s relationship with a significant person"], "description": ["Possible values: ", "S-Single, ", "M-Married, ", "D-Divorced, ", "W - Widow."]}, {"column_name": "Education", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["The education level of this customer. "], "description": ["Possible values: ", "U - University", "M - Master", "S - Secondary", "P - Post Secondary", "X - Others", "J - Primary/Junior"]}, {"column_name": "WeChatID", "data_type": "<PERSON><PERSON><PERSON>", "length": 30, "remark": ["The customer’s WeChat ID"], "description": []}, {"column_name": "Accommodation", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["The ownership of the provided residential address.If it is set S,that means the accommodation is self owned."], "description": ["Possible values:", "S-Self Owned, ", "P-Private property,", "H-Home ownership scheme,", "F-Friends/Relatives,", "U-Public Housing,", "R-Ren<PERSON>, ", "Q-Quarters."]}, {"column_name": "MobilePhoneNumber1", "data_type": "<PERSON><PERSON><PERSON>", "length": 30, "remark": ["Customer’s mobile phone number"], "description": []}, {"column_name": "ResidentialAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 1000, "remark": ["Information that locates and identifies a specific address, as defined by postal services, presented in free format text."], "description": []}, {"column_name": "ResidentialAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Information that locates and identifies a specific address, as defined by postal services, presented in free format text."], "description": []}, {"column_name": "ResidentialAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Information that locates and identifies a specific address, as defined by postal services, presented in free format text."], "description": []}, {"column_name": "ResidentialAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Information that locates and identifies a specific address, as defined by postal services, presented in free format text."], "description": []}, {"column_name": "ResidentialDistrictName", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Customer residential district name"], "description": []}, {"column_name": "ResidentialRegionName", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Customer resiedential region name"], "description": []}, {"column_name": "EmailAddress1", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["Customer email address"], "description": []}, {"column_name": "CountryOfResidence", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["Country in which a person resides (the place of a person's home). "], "description": []}, {"column_name": "CountryOfBirth", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["Customer country of birth"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["Customer creation date in the bank system"], "description": []}, {"column_name": "ContactPreferredMethod", "data_type": "<PERSON><PERSON><PERSON>", "length": 6, "remark": ["Customer preferred contact method"], "description": ["Possible values: ", "Phonecall,", "<PERSON><PERSON>,", "Mail, ", "SMS"]}, {"column_name": "PreLanguage1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Customer preferred language"], "description": ["Possible values:", "English, ", "Cantonese,", "Mandarin"]}, {"column_name": "PreTimeFrom1", "data_type": "decimal", "length": 20, "remark": ["Custoemr preferred service start time"], "description": []}, {"column_name": "PreTimeTo1", "data_type": "decimal", "length": 20, "remark": ["Custoemr preferred service end time"], "description": []}, {"column_name": "CustPresentAddPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Customer present address period"], "description": []}, {"column_name": "CustPresentAddUpdateDate", "data_type": "decimal", "length": 20, "remark": ["Customer present address update date"], "description": []}, {"column_name": "CustPreviousAddPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Customer previous address period"], "description": []}, {"column_name": "CustPreviousAddUpdateDate", "data_type": "decimal", "length": 20, "remark": ["Customer previous address update date"], "description": []}, {"column_name": "SensitiveStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Whether the customer info is sensitive to be accessed."], "description": ["Possible values:", "Yes", "No"]}, {"column_name": "HKIDFirstIssue", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Customer HK ID first issue date"], "description": []}, {"column_name": "HKIDIssueDate", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Customer HK ID issue date "], "description": []}, {"column_name": "PersonalInfoUpdateDate", "data_type": "decimal", "length": 20, "remark": ["Customer personal information update date"], "description": []}, {"column_name": "MaritalDate", "data_type": "decimal", "length": 20, "remark": ["Customer marital date"], "description": []}, {"column_name": "SpouseName", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["Customer spouse name"], "description": []}, {"column_name": "SpouseIDType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Customer spouse ID type"], "description": []}, {"column_name": "SpouseID", "data_type": "<PERSON><PERSON><PERSON>", "length": 12, "remark": ["Customer spouse ID"], "description": []}, {"column_name": "SpouseDateOfBirth", "data_type": "decimal", "length": 20, "remark": ["Customer spouse date of birth"], "description": []}, {"column_name": "HouseholdIncome", "data_type": "decimal", "length": 18, "remark": ["Customer yearly household income"], "description": []}, {"column_name": "EmploymentStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Employment Status of the customer"], "description": ["Possible values:", "F - Full time", "P - Part time", "S - Self employed", "T - Student", "R - Retired", "U - Unemployed"]}, {"column_name": "Occupation", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["The job or profession of the customer."], "description": []}, {"column_name": "EmployerCompanyName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["The name of the company for whom the customer works."], "description": []}, {"column_name": "Position", "data_type": "<PERSON><PERSON><PERSON>", "length": 500, "remark": ["The job title of this customer in his/her company.Possible value:CEO, Senior Manager,Consultant, Business Analyst, Chairman, staff, Engineer, Manager, Director."], "description": []}, {"column_name": "MonthlySalary", "data_type": "decimal", "length": 18, "remark": ["The salary paid by his/her employer company every month."], "description": []}, {"column_name": "OtherMonthlyIncome", "data_type": "decimal", "length": 18, "remark": ["Other monthly income, such as rent "], "description": []}, {"column_name": "CompanyAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 4000, "remark": ["The physical address of the company where the customer works."], "description": []}, {"column_name": "CompanyAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["The physical address of the company where the customer works."], "description": []}, {"column_name": "CompanyAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["The physical address of the company where the customer works."], "description": []}, {"column_name": "CompanyAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["The physical address of the company where the customer works."], "description": []}, {"column_name": "CustPresentEmploymentPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["The year of the customer working in this company."], "description": []}, {"column_name": "CusPrestEmploymentSinceDate", "data_type": "decimal", "length": 20, "remark": ["The start date of the customer working in this company."], "description": []}, {"column_name": "CustPreviousEmploymntPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["The year of the customer working in his previous company."], "description": []}, {"column_name": "CustPreviousEmploymntDate", "data_type": "decimal", "length": 20, "remark": ["The start date of the customer working in his previous company."], "description": []}, {"column_name": "EmployerIndustry", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["The specific sector or field in which a company or organization operates. "], "description": []}, {"column_name": "CustRelationMgrCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Customer bank manager"], "description": []}, {"column_name": "ResidentAddressMaintBranch", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Customer Address maintenance branch number"], "description": []}, {"column_name": "CustomerStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Customer Status"], "description": ["Possible values: ", "Null - Others", "S-Staff", "R-Relative of staff", "P-Spouse of staff", "D-Bank director"]}, {"column_name": "UserId", "data_type": "<PERSON><PERSON><PERSON>", "length": 32, "remark": ["A unique id number of the platform user. "], "description": []}]}, "cht": {"title": "客户信息数据", "data_table_description": ["本数据表中包含了每个银行客户的所有个人信息（如身份证号码，姓名，国籍，居住地区等）。", "在该表中， CustomerNumber 作为每一个客户在整个数据库中的唯一标志，可以与其他表中的 CustomerNumber 参数关联，做联表查询。 可能用到的业务场景： 查询银行客户个人基础信息，从不同维度分析银行客户的交易行为等等。"], "data_table_type": "SimBank数据字典 - 客户信息数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["客户编号。该编号是系统自动生成的唯一标志银行客户。"], "description": []}, {"column_name": "FirstName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户名"], "description": []}, {"column_name": "LastName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户姓"], "description": []}, {"column_name": "CustomerID1", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["身份证件号码1"], "description": []}, {"column_name": "CustomerIDType1", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["身份证件类型1"], "description": []}, {"column_name": "IssueCountry1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["证件1发证国家"], "description": []}, {"column_name": "IssueDate1", "data_type": "decimal", "length": 20, "remark": ["证件1 颁发时间"], "description": []}, {"column_name": "CustomerID2", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["身份证件号码2"], "description": []}, {"column_name": "CustomerIDType2", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["身份证件类型2"], "description": []}, {"column_name": "IssueCountry2", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["证件2发证国家"], "description": []}, {"column_name": "IssueDate2", "data_type": "decimal", "length": 20, "remark": ["证件2颁发时间"], "description": []}, {"column_name": "DateOfBirth", "data_type": "decimal", "length": 20, "remark": ["出生日期"], "description": []}, {"column_name": "Age", "data_type": "int", "length": 11, "remark": ["年龄"], "description": []}, {"column_name": "AgeGroup", "data_type": "<PERSON><PERSON><PERSON>", "length": 100, "remark": ["年龄组"], "description": []}, {"column_name": "SeniorInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["是否老年人"], "description": []}, {"column_name": "MinorInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["是否未成年人"], "description": []}, {"column_name": "ChineseName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["中文名称"], "description": []}, {"column_name": "Gender", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["性别"], "description": ["可能的值:", "M-<PERSON> （男性）", "F-Female （女性）"]}, {"column_name": "Nationality1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["国籍1"], "description": []}, {"column_name": "Nationality2", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["国籍2"], "description": []}, {"column_name": "Nationality3", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["国籍3"], "description": []}, {"column_name": "PermanentResidenceStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["永久居住状态"], "description": ["可能的值:", "Y-Yes, ", "N-No."]}, {"column_name": "MaritalStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["婚姻状态"], "description": ["可能的值: ", "S-Single（单身）", "M-<PERSON>（已婚）", "D-Divorced （离异）", "W - <PERSON> （丧偶）"]}, {"column_name": "Education", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["受教育程度"], "description": ["可能的值: ", "U - University （大学）", "M - <PERSON> （硕士）", "S - Secondary （中学）", "P - Post Secondary （职业学院）", "X - Others （其他）", "J - Primary/Junior （中小学）"]}, {"column_name": "WeChatID", "data_type": "<PERSON><PERSON><PERSON>", "length": 30, "remark": ["微信号"], "description": []}, {"column_name": "Accommodation", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["居住性质"], "description": ["可能的值:", "S-Self Owned （自置物业）", "P-Private property （私人住宅）", "H-Home ownership scheme（居者有其屋计划（通常指政府资助的购房计划）", "F-Friends/Relatives （亲友住处）", "U-Public Housing （公共房屋（通常指政府提供的廉租房屋）", "R-Rented （租住）", "Q-Quarters  宿舍（通常指提供给特定群体如员工或学生的住宿）"]}, {"column_name": "MobilePhoneNumber1", "data_type": "<PERSON><PERSON><PERSON>", "length": 30, "remark": ["移动电话号码1"], "description": []}, {"column_name": "ResidentialAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 1000, "remark": ["居住地址1"], "description": []}, {"column_name": "ResidentialAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["居住地址2"], "description": []}, {"column_name": "ResidentialAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["居住地址3"], "description": []}, {"column_name": "ResidentialAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["居住地址4"], "description": []}, {"column_name": "ResidentialDistrictName", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户居住所在行政区"], "description": []}, {"column_name": "ResidentialRegionName", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户居住所在行政区的子区域"], "description": []}, {"column_name": "EmailAddress1", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["邮箱1"], "description": []}, {"column_name": "CountryOfResidence", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["居住国家"], "description": []}, {"column_name": "CountryOfBirth", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["客户出生国家"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["客户创建时间"], "description": []}, {"column_name": "ContactPreferredMethod", "data_type": "<PERSON><PERSON><PERSON>", "length": 6, "remark": ["客户期望的联系方式"], "description": ["可能的值: ", "Phonecall （电话呼叫）", "<PERSON><PERSON> （邮件）", "Mail （信件）", "SMS （短信）"]}, {"column_name": "PreLanguage1", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["客户偏好的服务语言"], "description": ["可能的值:", "English （英语）", "Cantonese （粤语）", "Mandarin （普通话）"]}, {"column_name": "PreTimeFrom1", "data_type": "decimal", "length": 20, "remark": ["客户偏好的服务开始时间"], "description": []}, {"column_name": "PreTimeTo1", "data_type": "decimal", "length": 20, "remark": ["客户偏好的服务结束时间"], "description": []}, {"column_name": "CustPresentAddPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户当前住址的居住期限"], "description": []}, {"column_name": "CustPresentAddUpdateDate", "data_type": "decimal", "length": 20, "remark": ["客户当前住址的更新时间"], "description": []}, {"column_name": "CustPreviousAddPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户历史住址的居住期限"], "description": []}, {"column_name": "CustPreviousAddUpdateDate", "data_type": "decimal", "length": 20, "remark": ["客户历史住址的更新时间"], "description": []}, {"column_name": "SensitiveStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["客户信息被访问的敏感状态。Yes表示该客户的信息访问会有限制。"], "description": ["可能的值:", "Yes", "No"]}, {"column_name": "HKIDFirstIssue", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["香港身份证首次颁发时间"], "description": []}, {"column_name": "HKIDIssueDate", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["当前所持香港身份证颁发时间"], "description": []}, {"column_name": "PersonalInfoUpdateDate", "data_type": "decimal", "length": 20, "remark": ["最近一次个人信息修改时间"], "description": []}, {"column_name": "MaritalDate", "data_type": "decimal", "length": 20, "remark": ["结婚日期"], "description": []}, {"column_name": "SpouseName", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["配偶姓名"], "description": []}, {"column_name": "SpouseIDType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["配偶证件类型"], "description": []}, {"column_name": "SpouseID", "data_type": "<PERSON><PERSON><PERSON>", "length": 12, "remark": ["配偶证件号码"], "description": []}, {"column_name": "SpouseDateOfBirth", "data_type": "decimal", "length": 20, "remark": ["配偶生日"], "description": []}, {"column_name": "HouseholdIncome", "data_type": "decimal", "length": 18, "remark": ["家庭年收入"], "description": []}, {"column_name": "EmploymentStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["客户雇佣状态"], "description": ["可能的值:", "F - Full time （全职）", "P - Part time （兼职）", "S - Self employed （自雇）", "T - Student （学生）", "R - Retired （退休人员）", "U - Unemployed （失业）"]}, {"column_name": "Occupation", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["职业"], "description": []}, {"column_name": "EmployerCompanyName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["客户所在的公司名称"], "description": []}, {"column_name": "Position", "data_type": "<PERSON><PERSON><PERSON>", "length": 500, "remark": ["客户的工作职位"], "description": []}, {"column_name": "MonthlySalary", "data_type": "decimal", "length": 18, "remark": ["月工资"], "description": []}, {"column_name": "OtherMonthlyIncome", "data_type": "decimal", "length": 18, "remark": ["其他月收入"], "description": []}, {"column_name": "CompanyAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 4000, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CompanyAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CompanyAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CompanyAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户所在公司地址"], "description": []}, {"column_name": "CustPresentEmploymentPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["当前工作的工作年限"], "description": []}, {"column_name": "CusPrestEmploymentSinceDate", "data_type": "decimal", "length": 20, "remark": ["当前工作开始时间"], "description": []}, {"column_name": "CustPreviousEmploymntPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["上一份工作的工作年限"], "description": []}, {"column_name": "CustPreviousEmploymntDate", "data_type": "decimal", "length": 20, "remark": ["上一份工作的工作开始时间"], "description": []}, {"column_name": "EmployerIndustry", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["公司所在行业"], "description": []}, {"column_name": "CustRelationMgrCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["客户经理，"], "description": []}, {"column_name": "ResidentAddressMaintBranch", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["客户居住地的分行编号"], "description": []}, {"column_name": "CustomerStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["客户状态"], "description": ["可能的值: ", "Null - Others （其他）", "S-Staff （员工）", "R-Relative of staff （员工亲戚）", "P-Spouse of staff （员工配偶）", "D-Bank director （银行董事）"]}, {"column_name": "UserId", "data_type": "<PERSON><PERSON><PERSON>", "length": 32, "remark": ["用户ID，唯一标志该平台的一个用户"], "description": []}]}}