{"zh": {"title": "个人消费贷款合同数据", "data_table_description": ["该数据表包含了个人消费贷款合同的详细信息，包含贷款金额，分期情况等。", "可能用到的业务场景：查询贷款合同详情。"], "data_table_type": "SimBank数据字典 - 个人消费贷款合同数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["贷款账号"], "description": []}, {"column_name": "ProductCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["贷款产品编号"], "description": []}, {"column_name": "ContractNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["贷款合同号"], "description": []}, {"column_name": "LoanType", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["贷款类型"], "description": []}, {"column_name": "LoanAmount", "data_type": "decimal", "length": 18, "remark": ["贷款金额"], "description": []}, {"column_name": "TotalInterestPayment", "data_type": "decimal", "length": 18, "remark": ["贷款总利息"], "description": []}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["货币类型"], "description": []}, {"column_name": "LoanAnnualRate", "data_type": "decimal", "length": 18, "remark": ["年化利率"], "description": []}, {"column_name": "RepaymentPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 5, "remark": ["贷款分期周期"], "description": []}, {"column_name": "RepaymentAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["合同到期日"], "description": []}, {"column_name": "ContractEndDate", "data_type": "decimal", "length": 20, "remark": ["合同结束日"], "description": []}, {"column_name": "ContractStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["合同状态"], "description": ["合同状态", "ACT--Active正常 ", "POF--Pa<PERSON> Off", "OVD--Overdue逾期", "DEF--Default违约"]}, {"column_name": "DrawdownTime", "data_type": "decimal", "length": 20, "remark": ["放款日期"], "description": []}, {"column_name": "CreationTime", "data_type": "decimal", "length": 20, "remark": ["最后一次更新时间"], "description": []}, {"column_name": "LastUpdateTime", "data_type": "decimal", "length": 20, "remark": ["创建时间"], "description": []}]}, "en": {"title": "Personal Loan Contract Data", "data_table_description": ["This data table contains detailed information about personal consumption loan contracts, including loan amounts, installment payments, etc.", "Possible business scenarios: querying loan contract details."], "data_table_type": "SimBank Data Dictionary - Personal Loan Contract Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["A unique number used to identify a loan account"], "description": []}, {"column_name": "ProductCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["Loan product code"], "description": []}, {"column_name": "ContractNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["A unique number used to identify a personal loan contract"], "description": []}, {"column_name": "LoanType", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["Loan type"], "description": []}, {"column_name": "LoanAmount", "data_type": "decimal", "length": 18, "remark": ["Loan amount"], "description": []}, {"column_name": "TotalInterestPayment", "data_type": "decimal", "length": 18, "remark": ["All interest to be paid on a loan"], "description": []}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Currency code of the loan contract"], "description": []}, {"column_name": "LoanAnnualRate", "data_type": "decimal", "length": 18, "remark": ["Annual rate of the loan interest"], "description": []}, {"column_name": "RepaymentPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 5, "remark": [" The timeframe to fully repay a loan, including principal and interest"], "description": ["Possible values:", "6M - 6months,", "12M - 12months, ", "18M-18months, ", "24M-24months, ", "30M-30months,", "36M- 36months, ", "42M-42months,", "48M-48month, ", "54M-54months, ", "60M-60months"]}, {"column_name": "RepaymentAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["The debit account number that is used to make payment"], "description": []}, {"column_name": "ContractEndDate", "data_type": "decimal", "length": 20, "remark": ["Contract end date"], "description": []}, {"column_name": "ContractStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Contract Status"], "description": ["Possible values:", "ACT--Active ", "POF--Pa<PERSON> Off", "OVD--Overdue", "DEF--<PERSON><PERSON><PERSON>"]}, {"column_name": "DrawdownTime", "data_type": "decimal", "length": 20, "remark": ["Loan disbursement date"], "description": []}, {"column_name": "CreationTime", "data_type": "decimal", "length": 20, "remark": ["The last update date of the record"], "description": []}, {"column_name": "LastUpdateTime", "data_type": "decimal", "length": 20, "remark": ["Creation date of the record"], "description": []}]}, "cht": {"title": "个人消费贷款合同数据", "data_table_description": ["该数据表包含了个人消费贷款合同的详细信息，包含贷款金额，分期情况等。", "可能用到的业务场景：查询贷款合同详情。"], "data_table_type": "SimBank数据字典 - 个人消费贷款合同数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["贷款账号"], "description": []}, {"column_name": "ProductCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["贷款产品编号"], "description": []}, {"column_name": "ContractNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["贷款合同号"], "description": []}, {"column_name": "LoanType", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["贷款类型"], "description": []}, {"column_name": "LoanAmount", "data_type": "decimal", "length": 18, "remark": ["贷款金额"], "description": []}, {"column_name": "TotalInterestPayment", "data_type": "decimal", "length": 18, "remark": ["贷款总利息"], "description": []}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["货币类型"], "description": []}, {"column_name": "LoanAnnualRate", "data_type": "decimal", "length": 18, "remark": ["年化利率"], "description": []}, {"column_name": "RepaymentPeriod", "data_type": "<PERSON><PERSON><PERSON>", "length": 5, "remark": ["贷款分期周期"], "description": []}, {"column_name": "RepaymentAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["合同到期日"], "description": []}, {"column_name": "ContractEndDate", "data_type": "decimal", "length": 20, "remark": ["合同结束日"], "description": []}, {"column_name": "ContractStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["合同状态"], "description": ["合同状态", "ACT--Active正常 ", "POF--Pa<PERSON> Off", "OVD--Overdue逾期", "DEF--Default违约"]}, {"column_name": "DrawdownTime", "data_type": "decimal", "length": 20, "remark": ["放款日期"], "description": []}, {"column_name": "CreationTime", "data_type": "decimal", "length": 20, "remark": ["最后一次更新时间"], "description": []}, {"column_name": "LastUpdateTime", "data_type": "decimal", "length": 20, "remark": ["创建时间"], "description": []}]}}