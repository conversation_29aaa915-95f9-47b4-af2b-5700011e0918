{"zh": {"title": "股票市场信息数据", "data_table_description": ["该数据表包含了股票的市场信息，如股票开盘价，收盘价，交易量等信息。", "可能用到的业务场景：查询股票市场信息。"], "data_table_type": "SimBank数据字典 - 股票市场信息数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "StockCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["股票代码"], "description": []}, {"column_name": "StockName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["股票名称"], "description": []}, {"column_name": "Open", "data_type": "decimal", "length": 18, "remark": ["开盘价"], "description": []}, {"column_name": "High", "data_type": "decimal", "length": 18, "remark": ["当日最高价"], "description": []}, {"column_name": "Low", "data_type": "decimal", "length": 18, "remark": ["当日最低价"], "description": []}, {"column_name": "LastPrice", "data_type": "decimal", "length": 18, "remark": ["最近收盘价"], "description": []}, {"column_name": "PreviousClose", "data_type": "decimal", "length": 18, "remark": ["最新价格"], "description": []}, {"column_name": "Changed", "data_type": "<PERSON><PERSON><PERSON>", "length": 18, "remark": ["涨幅"], "description": []}, {"column_name": "ChangedPercent", "data_type": "<PERSON><PERSON><PERSON>", "length": 18, "remark": ["涨幅百分比"], "description": []}, {"column_name": "BuyPrice", "data_type": "decimal", "length": 18, "remark": ["买入价格"], "description": []}, {"column_name": "SellPrice", "data_type": "decimal", "length": 18, "remark": ["卖出价格"], "description": []}, {"column_name": "Volume", "data_type": "decimal", "length": 18, "remark": ["交易量"], "description": []}, {"column_name": "Turnover", "data_type": "decimal", "length": 18, "remark": ["交易额"], "description": []}, {"column_name": "EPS", "data_type": "decimal", "length": 18, "remark": ["每股利润"], "description": []}, {"column_name": "<PERSON><PERSON>", "data_type": "decimal", "length": 18, "remark": ["比率"], "description": []}, {"column_name": "LotSize", "data_type": "decimal", "length": 18, "remark": ["每手股数"], "description": []}, {"column_name": "TradingPoint", "data_type": "decimal", "length": 18, "remark": ["交易点"], "description": []}, {"column_name": "LastTradingDay", "data_type": "decimal", "length": 20, "remark": ["上个交易日"], "description": []}, {"column_name": "Industry", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["行业"], "description": []}, {"column_name": "MarketCap", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["市值"], "description": []}, {"column_name": "PE", "data_type": "decimal", "length": 18, "remark": ["市盈率"], "description": []}, {"column_name": "Yield", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["收益率"], "description": []}, {"column_name": "SHSC", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["沪股通"], "description": []}, {"column_name": "HSI", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["恒生指数"], "description": []}, {"column_name": "HSCEI", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["恒生中国企业指数"], "description": []}, {"column_name": "HS_TECH", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["恒生科技指数"], "description": []}]}, "en": {"title": "Stock Market Information Data", "data_table_description": ["This data table contains market information of stocks, such as opening price, closing price, trading volume, and other related details.", "Possible business use case: Querying stock market information."], "data_table_type": "SimBank Data Dictionary - Stock Market Information Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "StockCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["Stock code"], "description": []}, {"column_name": "StockName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["Stock name"], "description": []}, {"column_name": "Open", "data_type": "decimal", "length": 18, "remark": ["The price at which the stock first trades when the market opens"], "description": []}, {"column_name": "High", "data_type": "decimal", "length": 18, "remark": ["The highest price at which the stock traded during the day"], "description": []}, {"column_name": "Low", "data_type": "decimal", "length": 18, "remark": ["The lowest price at which the stock traded during the day"], "description": []}, {"column_name": "LastPrice", "data_type": "decimal", "length": 18, "remark": ["The most recent price at which the stock was traded"], "description": []}, {"column_name": "PreviousClose", "data_type": "decimal", "length": 18, "remark": ["The closing price of the stock on the previous trading day"], "description": []}, {"column_name": "Changed", "data_type": "<PERSON><PERSON><PERSON>", "length": 18, "remark": ["The difference between the current price and the previous close"], "description": []}, {"column_name": "ChangedPercent", "data_type": "<PERSON><PERSON><PERSON>", "length": 18, "remark": ["The percentage change in price compared to the previous close"], "description": []}, {"column_name": "BuyPrice", "data_type": "decimal", "length": 18, "remark": ["The highest price a buyer is willing to pay for the stock"], "description": []}, {"column_name": "SellPrice", "data_type": "decimal", "length": 18, "remark": ["The lowest price a seller is willing to accept for the stock"], "description": []}, {"column_name": "Volume", "data_type": "decimal", "length": 18, "remark": ["The total number of shares traded during the day"], "description": []}, {"column_name": "Turnover", "data_type": "decimal", "length": 18, "remark": ["The total value of shares traded during the day"], "description": []}, {"column_name": "EPS", "data_type": "decimal", "length": 18, "remark": ["Earnings Per Share, representing the company's profit divided by its outstanding shares"], "description": []}, {"column_name": "<PERSON><PERSON>", "data_type": "decimal", "length": 18, "remark": ["A financial metric used to evaluate the stock's performance or valuation"], "description": []}, {"column_name": "LotSize", "data_type": "decimal", "length": 18, "remark": ["The minimum number of shares that can be traded in a single transaction"], "description": []}, {"column_name": "TradingPoint", "data_type": "decimal", "length": 18, "remark": ["The specific price level at which trading activity is concentrated"], "description": []}, {"column_name": "LastTradingDay", "data_type": "decimal", "length": 20, "remark": ["The most recent day on which the stock was traded"], "description": []}, {"column_name": "Industry", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["The sector or category to which the company belongs"], "description": []}, {"column_name": "MarketCap", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["The total market value of the company's outstanding shares"], "description": []}, {"column_name": "PE", "data_type": "decimal", "length": 18, "remark": ["Price-to-Earnings ratio, a valuation metric comparing the stock price to earnings per share"], "description": []}, {"column_name": "Yield", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["The dividend yield, representing the annual dividends per share relative to the stock price"], "description": []}, {"column_name": "SHSC", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["Shanghai-Hong Kong Stock Connect, a program linking the Shanghai and Hong Kong stock markets"], "description": []}, {"column_name": "HSI", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["Hang Seng Index, the benchmark index of the Hong Kong stock market"], "description": []}, {"column_name": "HSCEI", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["Hang Seng China Enterprises Index, tracking H-share companies listed in Hong Kong"], "description": []}, {"column_name": "HS_TECH", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["Hang Seng Tech Index, representing the performance of the technology sector in Hong Kong"], "description": []}]}, "cht": {"title": "股票市场信息数据", "data_table_description": ["该数据表包含了股票的市场信息，如股票开盘价，收盘价，交易量等信息。", "可能用到的业务场景：查询股票市场信息。"], "data_table_type": "SimBank数据字典 - 股票市场信息数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "StockCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["股票代码"], "description": []}, {"column_name": "StockName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["股票名称"], "description": []}, {"column_name": "Open", "data_type": "decimal", "length": 18, "remark": ["开盘价"], "description": []}, {"column_name": "High", "data_type": "decimal", "length": 18, "remark": ["当日最高价"], "description": []}, {"column_name": "Low", "data_type": "decimal", "length": 18, "remark": ["当日最低价"], "description": []}, {"column_name": "LastPrice", "data_type": "decimal", "length": 18, "remark": ["最近收盘价"], "description": []}, {"column_name": "PreviousClose", "data_type": "decimal", "length": 18, "remark": ["最新价格"], "description": []}, {"column_name": "Changed", "data_type": "<PERSON><PERSON><PERSON>", "length": 18, "remark": ["涨幅"], "description": []}, {"column_name": "ChangedPercent", "data_type": "<PERSON><PERSON><PERSON>", "length": 18, "remark": ["涨幅百分比"], "description": []}, {"column_name": "BuyPrice", "data_type": "decimal", "length": 18, "remark": ["买入价格"], "description": []}, {"column_name": "SellPrice", "data_type": "decimal", "length": 18, "remark": ["卖出价格"], "description": []}, {"column_name": "Volume", "data_type": "decimal", "length": 18, "remark": ["交易量"], "description": []}, {"column_name": "Turnover", "data_type": "decimal", "length": 18, "remark": ["交易额"], "description": []}, {"column_name": "EPS", "data_type": "decimal", "length": 18, "remark": ["每股利润"], "description": []}, {"column_name": "<PERSON><PERSON>", "data_type": "decimal", "length": 18, "remark": ["比率"], "description": []}, {"column_name": "LotSize", "data_type": "decimal", "length": 18, "remark": ["每手股数"], "description": []}, {"column_name": "TradingPoint", "data_type": "decimal", "length": 18, "remark": ["交易点"], "description": []}, {"column_name": "LastTradingDay", "data_type": "decimal", "length": 20, "remark": ["上个交易日"], "description": []}, {"column_name": "Industry", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["行业"], "description": []}, {"column_name": "MarketCap", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["市值"], "description": []}, {"column_name": "PE", "data_type": "decimal", "length": 18, "remark": ["市盈率"], "description": []}, {"column_name": "Yield", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["收益率"], "description": []}, {"column_name": "SHSC", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["沪股通"], "description": []}, {"column_name": "HSI", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["恒生指数"], "description": []}, {"column_name": "HSCEI", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["恒生中国企业指数"], "description": []}, {"column_name": "HS_TECH", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["恒生科技指数"], "description": []}]}}