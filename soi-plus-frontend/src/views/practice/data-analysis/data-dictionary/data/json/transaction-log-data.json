{"zh": {"title": "交易记录数据", "data_table_description": ["本数据表包含了所有储蓄账户和往来账户的交易记录。包含了不同类型的交易，如转账，取款。还有很多的交易是因为该储蓄账户/往来账户关联了其他账户，比如定期存款账户，股票账户，基金账户等投资类型账户，这些投资账户发生交易的时候都会同时在这里产生一条交易。", "例如： 买入股票的时候，交易金额是5000 HKD，那么这5000HKD是需要从该股票账户所关联的储蓄账户/往来账户中扣取的，那么扣取之后，这里就会产生一条扣取5000 HKD的交易，交易类型就会标记为0009 - Buy Stock（买入股票)。", "用户可以根据自己的业务场景单独分析这些交易数据。也可以通过CustomerNumber参数关联Customer data表中的CustomerNumber（客户编码）做联表查询，将用户信息和交易数据结合起来做分析。"], "data_table_type": "SimBank数据字典 - 交易记录数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "Reference", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": [], "description": []}, {"column_name": "TranSeq", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": [], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": [], "description": []}, {"column_name": "TranDate", "data_type": "decimal", "length": 20, "remark": [], "description": []}, {"column_name": "Channel", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": [], "description": []}, {"column_name": "ChannelID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": [], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": [], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": [], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": [], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": [], "description": []}, {"column_name": "TranType", "data_type": "<PERSON><PERSON><PERSON>", "length": 6, "remark": [], "description": []}, {"column_name": "TranAmt", "data_type": "decimal", "length": 18, "remark": [], "description": []}, {"column_name": "PreviousBalAmt", "data_type": "decimal", "length": 18, "remark": ["初期余额"], "description": []}, {"column_name": "ActualBalAmt", "data_type": "decimal", "length": 18, "remark": ["实际余额"], "description": []}, {"column_name": "RefAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["对方账户"], "description": []}, {"column_name": "TfrSeqNo", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["对方交易流水号"], "description": []}, {"column_name": "CrDrMaintInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["借贷标志"], "description": ["借贷标志：", "D:取款,", "C：存入"]}, {"column_name": "TranDesc", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["交易描述"], "description": []}, {"column_name": "Ccy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["交易币种"], "description": []}]}, "en": {"title": "Transaction Log Data", "data_table_description": ["This data sheet contains the transaction records of all savings accounts and current accounts. It includes different types of transactions, such as transfer and withdrawal. There are also many transactions because the savings account / current account is associated with other accounts, such as time deposit account, stock account, fund account and other investment type accounts. When these investment accounts trade, a transaction will be generated here at the same time.", "For example, when buying stocks, the transaction amount is 5000 HKD, which needs to be deducted from the savings account / current account associated with the stock account. After deduction, a transaction of 5000 HKD will be generated, and the transaction type will be marked as 0009 - buy stock.", "Users can analyze these transaction data separately according to their own business scenarios. You can also use the customernumber parameter to associate the customernumber (customer code) in the customer data table to do linked table query, and combine the user information and transaction data for analysis."], "data_table_type": "SimBank Data Dictionary - Transaction Log Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "Reference", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Transaction reference number"], "description": []}, {"column_name": "TranSeq", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Transaction sequence number"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["Account number"], "description": []}, {"column_name": "TranDate", "data_type": "decimal", "length": 20, "remark": ["Transaction date"], "description": []}, {"column_name": "Channel", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["Transaction channel"], "description": []}, {"column_name": "ChannelID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Transaction channel ID"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["A country code is a short, standardized sequence of letters or numbers used to represent a specific country or geographic region. e.g., HK for Hong Kong"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Clearing code is code for different bank. for example BBIC,HSBC eg."], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Branch code is code for different branch of the same bank"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Used for sandbox allocation for each user"], "description": []}, {"column_name": "TranType", "data_type": "<PERSON><PERSON><PERSON>", "length": 6, "remark": ["Transcation type"], "description": []}, {"column_name": "TranAmt", "data_type": "decimal", "length": 18, "remark": ["Transaction amount"], "description": []}, {"column_name": "PreviousBalAmt", "data_type": "decimal", "length": 18, "remark": ["Account balance before transaction"], "description": []}, {"column_name": "ActualBalAmt", "data_type": "decimal", "length": 18, "remark": ["Account balance after transaction"], "description": []}, {"column_name": "RefAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["Countr account number"], "description": []}, {"column_name": "TfrSeqNo", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Transaction sequence number of counter account"], "description": []}, {"column_name": "CrDrMaintInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": [" ", "Credit and debit indicator:", "C - Credit", "D - Debit"], "description": []}, {"column_name": "TranDesc", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": [" ", "Transaction description."], "description": []}, {"column_name": "Ccy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Currency of transaction"], "description": []}]}, "cht": {"title": "交易记录数据", "data_table_description": ["本数据表包含了所有储蓄账户和往来账户的交易记录。包含了不同类型的交易，如转账，取款。还有很多的交易是因为该储蓄账户/往来账户关联了其他账户，比如定期存款账户，股票账户，基金账户等投资类型账户，这些投资账户发生交易的时候都会同时在这里产生一条交易。", "例如： 买入股票的时候，交易金额是5000 HKD，那么这5000HKD是需要从该股票账户所关联的储蓄账户/往来账户中扣取的，那么扣取之后，这里就会产生一条扣取5000 HKD的交易，交易类型就会标记为0009 - Buy Stock（买入股票)。", "用户可以根据自己的业务场景单独分析这些交易数据。也可以通过CustomerNumber参数关联Customer data表中的CustomerNumber（客户编码）做联表查询，将用户信息和交易数据结合起来做分析。"], "data_table_type": "SimBank数据字典 - 交易记录数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "Reference", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": [], "description": []}, {"column_name": "TranSeq", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": [], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": [], "description": []}, {"column_name": "TranDate", "data_type": "decimal", "length": 20, "remark": [], "description": []}, {"column_name": "Channel", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": [], "description": []}, {"column_name": "ChannelID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": [], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": [], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": [], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": [], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": [], "description": []}, {"column_name": "TranType", "data_type": "<PERSON><PERSON><PERSON>", "length": 6, "remark": [], "description": []}, {"column_name": "TranAmt", "data_type": "decimal", "length": 18, "remark": [], "description": []}, {"column_name": "PreviousBalAmt", "data_type": "decimal", "length": 18, "remark": ["初期余额"], "description": []}, {"column_name": "ActualBalAmt", "data_type": "decimal", "length": 18, "remark": ["实际余额"], "description": []}, {"column_name": "RefAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["对方账户"], "description": []}, {"column_name": "TfrSeqNo", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["对方交易流水号"], "description": []}, {"column_name": "CrDrMaintInd", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["借贷标志"], "description": ["借贷标志：", "D:取款,", "C：存入"]}, {"column_name": "TranDesc", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["交易描述"], "description": []}, {"column_name": "Ccy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["交易币种"], "description": []}]}}