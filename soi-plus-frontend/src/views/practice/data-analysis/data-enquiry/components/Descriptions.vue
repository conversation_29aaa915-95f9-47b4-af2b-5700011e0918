<template>
  <el-descriptions class="descriptions" :column="2" border>
    <el-descriptions-item v-for="(item, index) in viewRow" :key="index">
      <template slot="label">
        {{ item.key }}
      </template>
      {{ item.value }}
    </el-descriptions-item>
  </el-descriptions>
</template>
<script>
export default {
  name: 'Descriptions',
  props: {
    viewRow: {
      require: true,
      type: Array,
      default: null
    }
  }
}
</script>
<style lang="scss" scoped>
.descriptions {
  max-height: 600px;
  overflow-y: auto;
}
</style>
