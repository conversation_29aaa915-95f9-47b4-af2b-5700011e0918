<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-enquiry-container">
    <customize-card :title="$t('soi.router.customerDataEnquiry')">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-position="left"
        label-width="210px"
      >
        <el-row :gutter="20">
          <!--          <el-col :span="8">
            &lt;!&ndash; 表名<TableName> &ndash;&gt;
            <el-form-item label="Table Name" prop="tableName">
              <el-select
                v-model="searchForm.tableName"
                class="input"
              >
                <el-option
                  v-for="item in customerTableList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <!-- 创建时间<CreationDate> -->
            <el-form-item :label="$t('soi.customerEnquiry.createDate')" prop="createDate">
              <el-date-picker
                v-model="searchForm.createDate"
                type="daterange"
                value-format="timestamp"
                range-separator="To"
                unlink-panels
                :start-placeholder="$t('soi.customerEnquiry.fromCreateDate')"
                :end-placeholder="$t('soi.customerEnquiry.toCreateDate')"
                class="date-picker"
                :default-time="['00:00:00', '23:59:59']"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 性别<Gender> -->
            <el-form-item prop="gender" :label="$t('soi.customerEnquiry.gender')">
              <el-select v-model="searchForm.gender" class="select" clearable :placeholder="$t('soi.customerEnquiry.genderPlaceholder')">
                <el-option
                  v-for="item in genderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 国籍<Nationality> -->
            <el-form-item :label="$t('soi.customerEnquiry.nationality')" prop="nationality1">
              <el-select v-model="searchForm.nationality1" class="select" clearable :placeholder="$t('soi.customerEnquiry.nationalityPlaceholder')">
                <el-option
                  v-for="item in nationality"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <!-- 永久居留身份<PermanentResidenceStatus> -->
            <el-form-item :label="$t('soi.customerEnquiry.permanentResidenceStatus')" prop="permanentResidenceStatus">
              <el-select v-model="searchForm.permanentResidenceStatus" class="select" clearable :placeholder="$t('soi.customerEnquiry.permanentResidenceStatusPlaceholder')">
                <el-option
                  v-for="item in permanentResidenceStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 首选联系语言<PreLanguage> -->
            <el-form-item :label="$t('soi.customerEnquiry.preLanguage')" prop="preLanguage1">
              <el-select
                v-model="searchForm.preLanguage1"
                class="select"
                clearable
                :placeholder="$t('soi.customerEnquiry.preLanguagePlaceholder')"
              >
                <el-option
                  v-for="item in preLanguage1"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 敏感状态<SensitiveStatus> -->
            <el-form-item :label="$t('soi.customerEnquiry.sensitiveStatus')" prop="sensitiveStatus">
              <el-select v-model="searchForm.sensitiveStatus" class="select" clearable :placeholder="$t('soi.customerEnquiry.sensitiveStatusPlaceholder')">
                <el-option
                  v-for="item in permanentResidenceStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 首选方法<ContactPreferredMethod> -->
            <el-form-item :label="$t('soi.customerEnquiry.contactPreferredMethod')" prop="contactPreferredMethod">
              <el-select
                v-model="searchForm.contactPreferredMethod"
                class="select"
                clearable
                :placeholder="$t('soi.customerEnquiry.contactPreferredMethodPlaceholder')"
              >
                <el-option
                  v-for="item in contactPreferredMethod"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 婚姻状况<MaritalStatus> -->
            <el-form-item prop="maritalStatus" :label="$t('soi.customerEnquiry.maritalStatus')">
              <el-select v-model="searchForm.maritalStatus" class="select" clearable :placeholder="$t('soi.customerEnquiry.maritalStatusPlaceholder')">
                <el-option
                  v-for="item in maritalStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 地区<Region> -->
            <el-form-item :label="$t('soi.customerEnquiry.residentialRegionName')" prop="residentialRegionName">
              <el-select v-model="searchForm.residentialRegionName" class="select" clearable :placeholder="$t('soi.customerEnquiry.residentialRegionNamePlaceholder')">
                <el-option
                  v-for="item in residentialRegionName"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 年龄阶层<AgeGroup> -->
            <el-form-item :label="$t('soi.customerEnquiry.ageGroup')" prop="ageGroup">
              <el-select v-model="searchForm.ageGroup" class="select" clearable :placeholder="$t('soi.customerEnquiry.ageGroupPlaceholder')">
                <el-option
                  v-for="item in ageGroupOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 就业状况<EmploymentStatus> -->
            <el-form-item prop="employmentStatus" :label="$t('soi.customerEnquiry.employmentStatus')">
              <el-select v-model="searchForm.employmentStatus" class="select" clearable :placeholder="$t('soi.customerEnquiry.employmentStatusPlaceholder')">
                <el-option
                  v-for="item in employmentStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 教育<Education> -->
            <el-form-item prop="education" :label="$t('soi.customerEnquiry.education')">
              <el-select v-model="searchForm.education" class="select" clearable :placeholder="$t('soi.customerEnquiry.educationPlaceholder')">
                <el-option
                  v-for="item in educationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- Accommodation<Accommodation> -->
            <el-form-item prop="accommodation" :label="$t('soi.customerEnquiry.accommodation')">
              <el-select v-model="searchForm.accommodation" class="select" clearable :placeholder="$t('soi.customerEnquiry.accommodationPlaceholder')">
                <el-option
                  v-for="item in accommodation"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" icon="el-icon-search" @click="getDataList()">{{
              $t('soi.common.search')
            }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('soi.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 操作按钮 -->
      <div class="data-action-button-group">
        <!-- 新增按钮 -->
        <!-- <el-button icon="el-icon-plus" size="small" circle /> -->
        <!-- 导出按钮 -->
        <el-button icon="el-icon-download" size="small" circle @click="exportList()" />
        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getDataList()" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="dataList" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('soi.customerEnquiry.countryCode')" prop="countryCode" align="center" />
          <el-table-column :label="$t('soi.customerEnquiry.clearingCode')" prop="clearingCode" align="center" />
          <el-table-column :label="$t('soi.customerEnquiry.branchCode')" prop="branchCode" align="center" />
          <el-table-column :label="$t('soi.customerEnquiry.customerNumber')" prop="customerNumber" align="center" width="150" />
          <el-table-column :label="$t('soi.customerEnquiry.firstName')" prop="firstName" align="center" />
          <el-table-column :label="$t('soi.customerEnquiry.lastName')" prop="lastName" align="center" />
          <el-table-column :label="$t('soi.customerEnquiry.customerId')" prop="customerID1" align="center" />
          <el-table-column :label="$t('soi.customerEnquiry.customerIdType')" prop="customerIDType1" align="center" width="150" />
          <el-table-column :label="$t('soi.common.operate')" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdateDialog(scope.row)">
                {{ $t('soi.common.edit') }}
              </el-button>
              <el-button type="text" size="mini" text icon="el-icon-view" @click="openViewDialog(scope.row)">
                {{ $t('soi.common.view') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.page"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getDataList()"
          @current-change="getDataList()"
        />
      </div>
    </customize-card>

    <!-- 更新的对话框 -->
    <el-dialog
      :title="$t('soi.customerUpdate.title')"
      :visible.sync="updateDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <customize-form
        ref="form"
        form-ref="customerForm"
        :model="customerForm"
        :form-item-config="customizeFormJson"
        :rules="customizeFormRules"
        label-width="250px"
        label-position="left"
        style="max-height: 600px; overflow-y: auto"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUpdateDialog">{{ $t('soi.common.cancel') }}</el-button>
        <el-button
          type="primary"
          :loading="customerUpdateLoading"
          @click="handlerUpdateSuccess"
        >{{ $t('soi.common.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('soi.customerView.title')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>
  </div>
</template>

<script>
import { enquiryCustomerByPage } from '@/api/practice/data-enquiry'
import Descriptions from '@/views/practice/data-analysis/data-enquiry/components/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { mapGetters } from 'vuex'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  ageGroupOptions,
  customerStatusOptions,
  educationOptions,
  employmentStatusOptions,
  genderOptions,
  maritalStatusOptions,
  nationalityOptions,
  permanentResidenceStatusOptions,
  getAccommodationLabelByValue
} from '@/views/practice/data-analysis/data-creation/customer/js'
import { getCustomerOccupationList, getTableList, optionalGenerate, updateCustomer } from '@/api/practice/data-creation'
import moment from 'moment'
import {
  customizeFormJson,
  customizeFormRules
} from '@/views/practice/data-analysis/data-enquiry/customer/js/customizeForm'
import { exportCustomerList } from '@/api/practice/date-export'

export default {
  name: 'CustomerDataEnquiry',
  components: { CustomizeForm, Descriptions, CustomizeCard },
  data() {
    return {
      // 页面loading
      loading: true,
      moment,
      // 表格loading
      tableLoading: false,
      updateKey: '',
      total: 0,
      // 分页查询请求参数
      searchForm: {
        page: 1,
        pageSize: 20,
        keywords: '',
        createDate: [],
        gender: '',
        nationality1: '',
        sensitiveStatus: '',
        preLanguage1: '',
        permanentResidenceStatus: '',
        contactPreferredMethod: '',
        maritalStatus: '',
        residentialRegionName: '',
        ageGroup: '',
        employmentStatus: '',
        education: '',
        accommodation: '',
        tableName: '',
        userId: ''
      },
      // 更新的对话框标记：true 显示，false 隐藏
      updateDialogVisible: false,
      customerForm: {},
      customizeFormJson: [],
      customizeFormRules,
      customerUpdateLoading: false,
      // 查看的对话框标记：true 显示，false 隐藏
      viewDialogVisible: false,
      viewRow: [],
      // 列表
      dataList: [],
      ageGroupOptions,
      nationalityOptions,
      permanentResidenceStatusOptions,
      genderOptions,
      maritalStatusOptions,
      educationOptions,
      employmentStatusOptions,
      customerStatusOptions,
      nationality: [],
      preLanguage1: [],
      contactPreferredMethod: [],
      residentialRegionName: [],
      accommodation: [],
      customerTableList: [],
      customerIdTypeList: [],
      employerIndustry: [],
      occupationOptions: [],
      countryOfBirthList: []
    }
  },
  computed: {
    ...mapGetters(['language', 'userDetails'])
  },
  mounted() {
    const _this = this
    optionalGenerate()
      .then(function(res) {
        _this.accommodation = res.data.accommodation.map(item => {
          return {
            value: item,
            label: getAccommodationLabelByValue(item)
          }
        })
        _this.nationality = res.data.nationality
        _this.contactPreferredMethod = res.data.contactPreferredMethod
        _this.preLanguage1 = res.data.preLanguage1
        _this.residentialRegionName = res.data.residentialRegionName
        _this.customerIdTypeList = res.data.customerIDType
        _this.employerIndustry = res.data.employerIndustry
        _this.countryOfBirthList = res.data.countryOfBirth
        return getTableList({ type: 'Customer' })
      })
      .then(function(res) {
        _this.customerTableList = res.data
        if (_this.customerTableList && _this.customerTableList.length > 0) {
          _this.searchForm.tableName = _this.customerTableList[0]
        }
        getCustomerOccupationList()
          .then(function(res) {
            _this.occupationOptions = res.data
          })
      })
      .then(() => {
        // 页面初始化加载列表
        this.getDataList()
        this.customizeFormJson = customizeFormJson(
          _this.countryOfBirthList,
          _this.accommodation,
          _this.nationality,
          _this.contactPreferredMethod,
          _this.preLanguage1,
          _this.residentialRegionName,
          _this.employerIndustry,
          _this.occupationOptions
        )
      }).finally(() => {
        this.loading = false
      })
  },
  methods: {
    // 更新成功
    handlerUpdateSuccess() {
      // 调用 FreeForm 组件的 validate() 方法，验证表单
      this.$refs.form.validate((valid, formData) => {
        if (valid) {
          this.customerUpdateLoading = true
          updateCustomer(formData)
            .then(res => {
              this.$message.success(res.message)
            })
            .finally(() => {
              this.customerUpdateLoading = false
              this.updateDialogVisible = false
              this.getDataList()
            })
        } else {
          return false
        }
      })
    },
    // 打开更新对话框
    openUpdateDialog(row) {
      // 暂存需要更新的信息，往子组件传
      this.customerForm = JSON.parse(JSON.stringify({
        ...row,
        'issueDate1': String(row.issueDate1),
        'issueDate2': String(row.issueDate2),
        'hkidissueDate': String(row.hkidissueDate),
        'hkidfirstIssue': String(row.hkidfirstIssue),
        'tableName': this.searchForm.tableName
      }))
      this.updateDialogVisible = true
    },
    // 关闭更新对话框
    closeUpdateDialog() {
      this.updateDialogVisible = false
    },
    // 打开查看对话框
    openViewDialog(row) {
      const customizeJson = customizeFormJson(
        this.countryOfBirthList,
        this.accommodation,
        this.nationality,
        this.contactPreferredMethod,
        this.preLanguage1,
        this.residentialRegionName,
        this.employerIndustry,
        this.occupationOptions
      ).map(item => item.prop)
      customizeJson.unshift('id')
      this.viewRow = customizeJson
        .map(key => {
          const value = row[key]
          if (key.toLowerCase().includes('date') && !isNaN(value)) {
            if (String(value).length > 8) {
              const formattedValue = moment(Number(value)).format('YYYY-MM-DD HH:mm:ss')
              return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: formattedValue }
            }
            return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: value }
          } else {
            return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: value }
          }
        })
      this.viewDialogVisible = true
    },
    // 调用API分页查询列表
    getDataList() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.tableLoading = true

          const requestData = {
            ...this.searchForm,
            'userId': this.userDetails.id,
            'fromCreateDate': this.searchForm.createDate[0],
            'toCreateDate': this.searchForm.createDate[1]
          }

          delete requestData.createDate

          enquiryCustomerByPage(requestData)
            .then((res) => {
              const { totalCount, customerMaster: dataList } = res.data
              this.dataList = dataList
              this.total = totalCount
            })
            .finally(() => {
              this.tableLoading = false
            })
        } else {
          return false
        }
      })
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    exportList() {
      const requestData = {
        ...this.searchForm,
        'userId': this.userDetails.id,
        'fromCreateDate': this.searchForm.createDate.length ? this.searchForm.createDate[0] : '',
        'toCreateDate': this.searchForm.createDate.length ? this.searchForm.createDate[1] : ''
      }
      delete requestData.createDate
      exportCustomerList(requestData)
    }
  }
}
</script>
<style scoped lang="scss">
.date-picker, .select, .input {
  width: 220px;
}

.data-action-button-group {
  display: flex;
  margin-bottom: 10px;
  justify-content: right;
}

</style>
