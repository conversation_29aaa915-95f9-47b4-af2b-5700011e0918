import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import {
  ageGroupOptions,
  permanentResidenceStatusOptions,
  genderOptions,
  maritalStatusOptions,
  educationOptions,
  employmentStatusOptions,
  customerStatusOptions
} from '@/views/practice/data-analysis/data-creation/customer/js'
export const customizeFormJson = (
  countryOfBirthList,
  accommodation,
  nationality,
  contactPreferredMethod,
  preLanguage1,
  residentialRegionName,
  employerIndustry,
  occupationOptions
) => {
  return [
    {
      'label': 'soi.customerUpdate.countryCode',
      'placeholder': 'soi.customerUpdate.countryCodePlaceholder',
      'prop': 'countryCode',
      'component': 'el-input',
      'span': 12,
      'disabled': true,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.clearingCode',
      'placeholder': 'soi.customerUpdate.clearingCodePlaceholder',
      'prop': 'clearingCode',
      'component': 'el-input',
      'span': 12,
      'disabled': true,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.branchCode',
      'placeholder': 'soi.customerUpdate.branchCodePlaceholder',
      'prop': 'branchCode',
      'component': 'el-input',
      'span': 12,
      'disabled': true,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.customerNumber',
      'placeholder': 'soi.customerUpdate.customerNumberPlaceholder',
      'prop': 'customerNumber',
      'component': 'el-input',
      'span': 12,
      'disabled': true,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.firstName',
      'placeholder': 'soi.customerUpdate.firstNamePlaceholder',
      'prop': 'firstName',
      'component': 'el-input',
      'span': 12,
      'disabled': true,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.lastName',
      'placeholder': 'soi.customerUpdate.lastNamePlaceholder',
      'prop': 'lastName',
      'component': 'el-input',
      'span': 12,
      'disabled': true,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.age',
      'placeholder': 'soi.customerUpdate.agePlaceholder',
      'prop': 'age',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 18,
      'step': 1,
      'step-strictly': true,
      'controls': false,
      'precision': 0,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.ageGroup',
      'placeholder': 'soi.customerUpdate.ageGroupPlaceholder',
      'prop': 'ageGroup',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': ageGroupOptions
    },
    {
      'label': 'soi.customerUpdate.seniorInd',
      'placeholder': 'soi.customerUpdate.seniorIndPlaceholder',
      'prop': 'seniorInd',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.minorInd',
      'placeholder': 'soi.customerUpdate.minorIndPlaceholder',
      'prop': 'minorInd',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.countryOfBirth',
      'placeholder': 'soi.customerUpdate.countryOfBirthPlaceholder',
      'prop': 'countryOfBirth',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': countryOfBirthList.map(item => ({ label: item, value: item }))
    },
    {
      'label': 'soi.customerUpdate.chineseName',
      'placeholder': 'soi.customerUpdate.chineseNamePlaceholder',
      'prop': 'chineseName',
      'component': 'el-input',
      'span': 12,
      'disabled': true,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.nationality1',
      'placeholder': 'soi.customerUpdate.nationality1Placeholder',
      'prop': 'nationality1',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': nationality.map(item => ({ label: item, value: item }))
    },
    {
      'label': 'soi.customerUpdate.customerIDType1',
      'placeholder': 'soi.customerUpdate.customerIDType1Placeholder',
      'prop': 'customerIDType1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.customerID1',
      'placeholder': 'soi.customerUpdate.customerID1Placeholder',
      'prop': 'customerID1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.issueCountry1',
      'placeholder': 'soi.customerUpdate.issueCountry1Placeholder',
      'prop': 'issueCountry1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.issueDate1',
      'placeholder': 'soi.customerUpdate.issueDate1Placeholder',
      'prop': 'issueDate1',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'month',
      'format': 'yyyy-MM',
      'valueFormat': 'yyyyMM',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.customerIDType2',
      'placeholder': 'soi.customerUpdate.customerIDType2Placeholder',
      'prop': 'customerIDType2',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.customerID2',
      'placeholder': 'soi.customerUpdate.customerID2Placeholder',
      'prop': 'customerID2',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.issueCountry2',
      'placeholder': 'soi.customerUpdate.issueCountry2Placeholder',
      'prop': 'issueCountry2',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.issueDate2',
      'placeholder': 'soi.customerUpdate.issueDate2Placeholder',
      'prop': 'issueDate2',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'month',
      'format': 'yyyy-MM',
      'valueFormat': 'yyyyMM',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.hkidissueDate',
      'placeholder': 'soi.customerUpdate.hkidissueDatePlaceholder',
      'prop': 'hkidissueDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'date',
      'format': 'yyyy-MM-dd',
      'valueFormat': 'yyyyMMdd',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.hkidfirstIssue',
      'placeholder': 'soi.customerUpdate.hkidfirstIssuePlaceholder',
      'prop': 'hkidfirstIssue',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'month',
      'format': 'yyyy-MM',
      'valueFormat': 'yyyyMM',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.permanentResidenceStatus',
      'placeholder': 'soi.customerUpdate.permanentResidenceStatusPlaceholder',
      'prop': 'permanentResidenceStatus',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': permanentResidenceStatusOptions
    },
    {
      'label': 'soi.customerUpdate.gender',
      'placeholder': 'soi.customerUpdate.genderPlaceholder',
      'prop': 'gender',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': genderOptions
    },
    {
      'label': 'soi.customerUpdate.maritalStatus',
      'placeholder': 'soi.customerUpdate.maritalStatusPlaceholder',
      'prop': 'maritalStatus',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': maritalStatusOptions
    },
    {
      'label': 'soi.customerUpdate.householdIncome',
      'placeholder': 'soi.customerUpdate.householdIncomePlaceholder',
      'prop': 'householdIncome',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.education',
      'placeholder': 'soi.customerUpdate.educationPlaceholder',
      'prop': 'education',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': educationOptions
    },
    {
      'label': 'soi.customerUpdate.weChatID',
      'placeholder': 'soi.customerUpdate.weChatIDPlaceholder',
      'prop': 'weChatID',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.accommodation',
      'placeholder': 'soi.customerUpdate.accommodationPlaceholder',
      'prop': 'accommodation',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': accommodation
    },
    {
      'label': 'soi.customerUpdate.sensitiveStatus',
      'placeholder': 'soi.customerUpdate.sensitiveStatusPlaceholder',
      'prop': 'sensitiveStatus',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': permanentResidenceStatusOptions
    },
    {
      'label': 'soi.customerUpdate.residentialRegionName',
      'placeholder': 'soi.customerUpdate.residentialRegionNamePlaceholder',
      'prop': 'residentialRegionName',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': residentialRegionName.map(item => ({ label: item, value: item }))
    },
    {
      'label': 'soi.customerUpdate.residentialDistrictName',
      'placeholder': 'soi.customerUpdate.residentialDistrictNamePlaceholder',
      'prop': 'residentialDistrictName',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.residentAddressMaintBranch',
      'placeholder': 'soi.customerUpdate.residentAddressMaintBranchPlaceholder',
      'prop': 'residentAddressMaintBranch',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.spouseName',
      'placeholder': 'soi.customerUpdate.spouseNamePlaceholder',
      'prop': 'spouseName',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.spouseIDType',
      'placeholder': 'soi.customerUpdate.spouseIDTypePlaceholder',
      'prop': 'spouseIDType',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.spouseID',
      'placeholder': 'soi.customerUpdate.spouseIDPlaceholder',
      'prop': 'spouseID',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.spouseDateOfBirth',
      'placeholder': 'soi.customerUpdate.spouseDateOfBirthPlaceholder',
      'prop': 'spouseDateOfBirth',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'date',
      'format': 'yyyy-MM-dd',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.maritalDate',
      'placeholder': 'soi.customerUpdate.maritalDatePlaceholder',
      'prop': 'maritalDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'date',
      'format': 'yyyy-MM-dd',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.countryOfResidence',
      'placeholder': 'soi.customerUpdate.countryOfResidencePlaceholder',
      'prop': 'countryOfResidence',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.residentialAddressLine1',
      'placeholder': 'soi.customerUpdate.residentialAddressLine1Placeholder',
      'prop': 'residentialAddressLine1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.residentialAddressLine2',
      'placeholder': 'soi.customerUpdate.residentialAddressLine2Placeholder',
      'prop': 'residentialAddressLine2',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.residentialAddressLine3',
      'placeholder': 'soi.customerUpdate.residentialAddressLine3Placeholder',
      'prop': 'residentialAddressLine3',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.residentialAddressLine4',
      'placeholder': 'soi.customerUpdate.residentialAddressLine4Placeholder',
      'prop': 'residentialAddressLine4',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.custPresentAddUpdateDate',
      'placeholder': 'soi.customerUpdate.custPresentAddUpdateDatePlaceholder',
      'prop': 'custPresentAddUpdateDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.custPresentAddPeriod',
      'placeholder': 'soi.customerUpdate.custPresentAddPeriodPlaceholder',
      'prop': 'custPresentAddPeriod',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.custPreviousAddPeriod',
      'placeholder': 'soi.customerUpdate.custPreviousAddPeriodPlaceholder',
      'prop': 'custPreviousAddPeriod',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.custPreviousAddUpdateDate',
      'placeholder': 'soi.customerUpdate.custPreviousAddUpdateDatePlaceholder',
      'prop': 'custPreviousAddUpdateDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'date',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.employmentStatus',
      'placeholder': 'soi.customerUpdate.employmentStatusPlaceholder',
      'prop': 'employmentStatus',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': employmentStatusOptions
    },
    {
      'label': 'soi.customerUpdate.employerCompanyName',
      'placeholder': 'soi.customerUpdate.employerCompanyNamePlaceholder',
      'prop': 'employerCompanyName',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.position',
      'placeholder': 'soi.customerUpdate.positionPlaceholder',
      'prop': 'position',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.companyAddressLine1',
      'placeholder': 'soi.customerUpdate.companyAddressLine1Placeholder',
      'prop': 'companyAddressLine1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.companyAddressLine2',
      'placeholder': 'soi.customerUpdate.companyAddressLine2Placeholder',
      'prop': 'companyAddressLine2',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.companyAddressLine3',
      'placeholder': 'soi.customerUpdate.companyAddressLine3Placeholder',
      'prop': 'companyAddressLine3',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.companyAddressLine4',
      'placeholder': 'soi.customerUpdate.companyAddressLine4Placeholder',
      'prop': 'companyAddressLine4',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.emailAddress1',
      'placeholder': 'soi.customerUpdate.emailAddress1Placeholder',
      'prop': 'emailAddress1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.mobilePhoneNumber1',
      'placeholder': 'soi.customerUpdate.mobilePhoneNumber1Placeholder',
      'prop': 'mobilePhoneNumber1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.occupation',
      'placeholder': 'soi.customerUpdate.occupationPlaceholder',
      'prop': 'occupation',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': occupationOptions
    },
    {
      'label': 'soi.customerUpdate.employerIndustry',
      'placeholder': 'soi.customerUpdate.employerIndustryPlaceholder',
      'prop': 'employerIndustry',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': employerIndustry.map(item => ({ label: item, value: item }))
    },
    {
      'label': 'soi.customerUpdate.custPresentEmploymentPeriod',
      'placeholder': 'soi.customerUpdate.custPresentEmploymentPeriodPlaceholder',
      'prop': 'custPresentEmploymentPeriod',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.cusPrestEmploymentSinceDate',
      'placeholder': 'soi.customerUpdate.cusPrestEmploymentSinceDatePlaceholder',
      'prop': 'cusPrestEmploymentSinceDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'date',
      'format': 'yyyy-MM-dd',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.custPreviousEmploymntPeriod',
      'placeholder': 'soi.customerUpdate.custPreviousEmploymntPeriodPlaceholder',
      'prop': 'custPreviousEmploymntPeriod',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.custPreviousEmploymntDate',
      'placeholder': 'soi.customerUpdate.custPreviousEmploymntDatePlaceholder',
      'prop': 'custPreviousEmploymntDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'date',
      'format': 'yyyy-MM-dd',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.otherMonthlyIncome',
      'placeholder': 'soi.customerUpdate.otherMonthlyIncomePlaceholder',
      'prop': 'otherMonthlyIncome',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5
    },
    {
      'label': 'soi.customerUpdate.monthlySalary',
      'placeholder': 'soi.customerUpdate.monthlySalaryPlaceholder',
      'prop': 'monthlySalary',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5
    },
    {
      'label': 'soi.customerUpdate.customerStatus',
      'placeholder': 'soi.customerUpdate.customerStatusPlaceholder',
      'prop': 'customerStatus',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': customerStatusOptions
    },
    {
      'label': 'soi.customerUpdate.preTimeFrom1',
      'placeholder': 'soi.customerUpdate.preTimeFrom1Placeholder',
      'prop': 'preTimeFrom1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.preTimeTo1',
      'placeholder': 'soi.customerUpdate.preTimeTo1Placeholder',
      'prop': 'preTimeTo1',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.customerUpdate.contactPreferredMethod',
      'placeholder': 'soi.customerUpdate.contactPreferredMethodPlaceholder',
      'prop': 'contactPreferredMethod',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': contactPreferredMethod.map(item => ({ label: item, value: item }))
    },
    {
      'label': 'soi.customerUpdate.preLanguage1',
      'placeholder': 'soi.customerUpdate.preLanguage1Placeholder',
      'prop': 'preLanguage1',
      'component': CustomizeSelect,
      'clearable': true,
      'width': '100%',
      'span': 12,
      'options': preLanguage1.map(item => ({ label: item, value: item }))
    },
    {
      'label': 'soi.customerUpdate.personalInfoUpdateDate',
      'placeholder': 'soi.customerUpdate.personalInfoUpdateDatePlaceholder',
      'prop': 'personalInfoUpdateDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.customerUpdate.createDate',
      'placeholder': 'soi.customerUpdate.createDatePlaceholder',
      'prop': 'createDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    }
  ]
}
export const customizeFormRules = {}
