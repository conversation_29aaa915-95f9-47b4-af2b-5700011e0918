export const formConfig = [
  {
    'label': 'soi.fundUpdate.countryCode',
    'placeholder': 'soi.fundUpdate.countryCodePlaceholder',
    'prop': 'countryCode',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.clearingCode',
    'placeholder': 'soi.fundUpdate.clearingCodePlaceholder',
    'prop': 'clearingCode',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.branchCode',
    'placeholder': 'soi.fundUpdate.branchCodePlaceholder',
    'prop': 'branchCode',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.accountNumber',
    'placeholder': 'soi.fundUpdate.accountNumberPlaceholder',
    'prop': 'accountNumber',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.tradingOption',
    'placeholder': 'soi.fundUpdate.tradingOptionPlaceholder',
    'prop': 'tradingOption',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.fundCode',
    'placeholder': 'soi.fundUpdate.fundCodePlaceholder',
    'prop': 'fundCode',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.sharingNo',
    'placeholder': 'soi.fundUpdate.sharingNoPlaceholder',
    'prop': 'sharingNo',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.tradingAmount',
    'placeholder': 'soi.fundUpdate.tradingAmountPlaceholder',
    'prop': 'tradingAmount',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.trdingCommission',
    'placeholder': 'soi.fundUpdate.trdingCommissionPlaceholder',
    'prop': 'trdingCommission',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.fundCcy',
    'placeholder': 'soi.fundUpdate.fundCcyPlaceholder',
    'prop': 'fundCcy',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.transactionAmount',
    'placeholder': 'soi.fundUpdate.transactionAmountPlaceholder',
    'prop': 'transactionAmount',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.transactionDate',
    'placeholder': 'soi.fundUpdate.transactionDatePlaceholder',
    'prop': 'transactionDate',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.fundUpdate.fundPrice',
    'placeholder': 'soi.fundUpdate.fundPricePlaceholder',
    'prop': 'fundPrice',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'width': '100%',
    'clearable': true
  }
]
