export const formConfig = [
  {
    'label': 'soi.transferUpdate.countryCode',
    'placeholder': 'soi.transferUpdate.countryCodePlaceholder',
    'prop': 'countryCode',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.clearingCode',
    'placeholder': 'soi.transferUpdate.clearingCodePlaceholder',
    'prop': 'clearingCode',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.branchCode',
    'placeholder': 'soi.transferUpdate.branchCodePlaceholder',
    'prop': 'branchCode',
    'component': 'el-input',
    'span': 12,
    'disabled': true,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.tranSeq',
    'placeholder': 'soi.transferUpdate.tranSeqPlaceholder',
    'prop': 'tranSeq',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.accountNumber',
    'placeholder': 'soi.transferUpdate.accountNumberPlaceholder',
    'prop': 'accountNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true
  },
  {
    'label': 'soi.transferUpdate.tranDate',
    'placeholder': 'soi.transferUpdate.tranDatePlaceholder',
    'prop': 'tranDate',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.channel',
    'placeholder': 'soi.transferUpdate.channelPlaceholder',
    'prop': 'channel',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.channelID',
    'placeholder': 'soi.transferUpdate.channelIDPlaceholder',
    'prop': 'channelID',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.tranType',
    'placeholder': 'soi.transferUpdate.tranTypePlaceholder',
    'prop': 'tranType',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.tranAmt',
    'placeholder': 'soi.transferUpdate.tranAmtPlaceholder',
    'prop': 'tranAmt',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.refAccountNumber',
    'placeholder': 'soi.transferUpdate.refAccountNumberPlaceholder',
    'prop': 'refAccountNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true
  },
  {
    'label': 'soi.transferUpdate.tfrSeqNo',
    'placeholder': 'soi.transferUpdate.tfrSeqNoPlaceholder',
    'prop': 'tfrSeqNo',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.crDrMaintInd',
    'placeholder': 'soi.transferUpdate.crDrMaintIndPlaceholder',
    'prop': 'crDrMaintInd',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.tranDesc',
    'placeholder': 'soi.transferUpdate.tranDescPlaceholder',
    'prop': 'tranDesc',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.transferUpdate.ccy',
    'placeholder': 'soi.transferUpdate.ccyPlaceholder',
    'prop': 'ccy',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  }
]
