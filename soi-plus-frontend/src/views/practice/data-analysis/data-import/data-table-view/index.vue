<template>
  <div v-loading.fullscreen.lock="loading" class="data-import-data-table-view-container">
    <customize-card :title="$t('soi.router.dataImport')">
      <template #header-actions>
        <el-button size="mini" @click="importData()">{{ $t('soi.dataImport.importData') }}</el-button>
        <el-button size="mini" @click="importDataDescription">{{ $t('soi.dataImport.importDataDescription') }}</el-button>
      </template>

      <!--      <el-collapse v-model="activeNames">-->
      <!--        <el-collapse-item :title="$t('soi.dataImport.publicDataSource')" name="1">-->
      <!--          <el-table-->
      <!--            :data="commonTableData"-->
      <!--            style="width: 100%"-->
      <!--            stripe-->
      <!--            highlight-current-row-->
      <!--            :header-cell-style="{'background': '#BEEEDE', 'color': '#333'}"-->
      <!--          >-->
      <!--            <el-table-column type="index" label="#" width="50" />-->
      <!--            <el-table-column :label="$t('soi.dataImport.dataTableName')" prop="tablename" />-->
      <!--            <el-table-column :label="$t('soi.common.operate')" width="110">-->
      <!--              <template slot-scope="scope">-->
      <!--                <el-button size="mini" @click="viewData('1', scope.row.tablename)">-->
      <!--                  {{ $t("soi.dataImport.previewData") }}-->
      <!--                </el-button>-->
      <!--              </template>-->
      <!--            </el-table-column>-->
      <!--          </el-table>-->
      <!--        </el-collapse-item>-->
      <!--        <el-collapse-item :title="$t('soi.dataImport.customDataSource')" name="2">-->
      <!--          -->
      <!--        </el-collapse-item>-->
      <!--      </el-collapse>-->
      <el-table
        :data="tableData"
        style="width: 100%"
        stripe
        highlight-current-row
        :header-cell-style="{'background': '#BEEEDE', 'color': '#333'}"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column :label="$t('soi.dataImport.dataTableName')" prop="tablename" />
        <el-table-column :label="$t('soi.common.operate')" align="center" width="620">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewData('0', scope.row.tablename)">
              {{ $t("soi.dataImport.previewData") }}
            </el-button>
            <el-button size="mini" @click="updateData()">
              {{ $t("soi.dataImport.updateData") }}
            </el-button>
            <el-button size="mini" @click="updateDataDescription()">
              {{ $t("soi.dataImport.updateDataDescription") }}
            </el-button>
            <el-button size="mini" @click="deleteTable(scope.row.tablename)">
              {{ $t("soi.common.delete") }}
            </el-button>
            <el-button size="mini" @click="exportData(scope.row.tablename)">
              {{ $t("soi.common.export") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { deleteTable, getDataTableList } from '@/api/practice/data-import'
import { mapGetters } from 'vuex'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export default {
  name: 'DataImportDataTableView',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      dialogFormVisible2: true,
      activeNames: ['1', '2'],
      tableData: [],
      commonTableData: []
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const vue = this
      vue.loading = true

      getDataTableList({ userId: this.userDetails.id })
        .then((res) => {
          vue.tableData = []
          vue.commonTableData = []
          if (res.data) {
            const list = res.data.self
            if (list) {
              for (let i = 0; i < list.length; i++) {
                vue.tableData.push({ tablename: list[i] })
              }
            }
            const listpublic = res.data.public
            if (listpublic) {
              for (let i = 0; i < listpublic.length; i++) {
                vue.commonTableData.push({ tablename: listpublic[i] })
              }
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    viewData(publicSource, tableName) {
      this.$router.push({ name: 'data-analysis-data-import-data-view', params: { publicSource, tableName }})
    },
    updateData() {
      this.importData()
    },
    updateDataDescription() {
      this.importDataDescription()
    },
    exportData(tableName) {
      window.open(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/export?tableName=' + tableName + '&userID=' + this.userDetails.email)
    },
    deleteTable(tableName) {
      const vue = this

      vue.$confirm(vue.$t('soi.dataImport.deleteDataTableTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true

        deleteTable({ userId: this.userDetails.id, tableName })
          .then(() => {
            vue.init()
          })
          .finally(() => {
            vue.loading = false
          })
      })
    },
    importData() {
      this.$router.push({ name: 'data-analysis-data-import-import-data' })
    },
    importDataDescription() {
      this.$router.push({ name: 'data-analysis-data-import-import-data-description' })
    }
  }
}
</script>
