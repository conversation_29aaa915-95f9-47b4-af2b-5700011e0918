<template>
  <div v-loading.fullscreen.lock="loading" class="data-import-import-data-description-container">
    <customize-card :title="$t('soi.dataImport.importDataDescription')">
      <template #header-actions>

        <el-button type="primary" size="mini" @click="downloadTemplateFile()">
          {{ $t("soi.dataImport.downloadTemplateFile") }}
        </el-button>
      </template>

      <div class="upload-form-content">
        <el-form v-model="uploadForm">
          <el-form-item prop="file">
            <el-upload
              ref="upload"
              action="/"
              :auto-upload="false"
              :show-file-list="true"
              :drag="true"
              :limit="1"
              accept=".xls,.xlsx"
              :on-exceed="handlerExceed"
              :on-success="handlerSuccess"
              :on-error="handlerError"
              :on-change="handlerChange"
              :file-list="fileList"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                <em>{{ $t("soi.dataImport.clickUploadTip") }}</em>
              </div>
              <div slot="tip" class="el-upload__tip">{{ $t("soi.dataImport.uploadTip") }}</div>
            </el-upload>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="submitUpload">{{ $t("soi.common.upload") }}</el-button>
          </el-form-item>
        </el-form>
      </div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { uploadDataDescription } from '@/api/practice/data-import'
import { SOI_PLUS_CORE_API_URL } from '@/contains'

export default {
  name: 'DataImportImportDataDescription',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      uploadForm: {
        file: ''
      },
      fileList: [],
      importDataTemplateUrl: SOI_PLUS_CORE_API_URL + '/v1/system/template/download?fileName=data-desc-import-template.xlsx'
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  methods: {
    downloadTemplateFile() {
      window.open(this.importDataTemplateUrl, '_self')
    },
    handlerExceed() {
      const vue = this
      vue.$alert(vue.$t('Database.onlySelectOneFileTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      }
      )
    },
    handlerSuccess() {
      const vue = this
      vue.$alert(vue.$t('soi.dataImport.uploadSuccess'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      })
    },
    handlerError() {
      const vue = this
      vue.$alert(vue.$t('soi.dataImport.uploadFailed'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      })
    },
    handlerChange(file, fileList) {
      const vue = this
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error(vue.$t('soi.dataImport.fileSizeTooLargeTip'))
        vue.fileList = []
        return isLt2M
      } else {
        vue.uploadForm.file = file.raw
        vue.fileList = fileList.slice(-1)
      }
    },
    submitUpload() {
      const vue = this
      if (vue.fileList.length === 0) {
        vue.$alert(vue.$t('soi.dataImport.pleaseSelectFile'), vue.$t('soi.common.tip'), {
          confirmButtonText: vue.$t('soi.common.confirm'),
          callback: action => {}
        })
        return
      }
      vue.loading = true
      const data = new FormData()
      data.append('userId', this.userDetails.id)
      data.append('file', vue.uploadForm.file)
      data.append('type', 'data-upload')

      uploadDataDescription(data)
        .then(() => {
          vue.$alert(vue.$t('soi.dataImport.importSuccess'), vue.$t('soi.common.tip'))
          this.$router.go(-1)
        })
        .finally(() => {
          vue.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-form-content {
  text-align: center;
}
</style>
