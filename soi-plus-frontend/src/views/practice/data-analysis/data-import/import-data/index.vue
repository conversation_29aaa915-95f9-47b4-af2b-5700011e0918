<template>
  <div v-loading.fullscreen.lock="loading" class="data-import-import-data-container">
    <customize-card :title="$t('soi.dataImport.importData')">
      <template #header-actions>

        <el-button type="primary" size="mini" @click="downloadTemplateFile()">
          {{ $t("soi.dataImport.downloadTemplateFile") }}
        </el-button>
      </template>

      <div v-if="isExistDB" class="upload-form-content">
        <el-form v-model="uploadForm">
          <el-form-item>
            <div>
              {{ $t("soi.dataImport.onlySupported") }}<b style="color:red;">{{ $t("soi.dataImport.mysql") }}</b>{{ $t("soi.dataImport.databaseTableBackupFile") }}
            </div>
            <div>
              {{ $t("soi.dataImport.pleaseUpload") }}<b style="color:red;">{{ $t("soi.dataImport.supportedFileTypes") }}</b>{{ $t("soi.dataImport.fromYour") }}
              <b style="color:red;">{{ $t("soi.dataImport.databaseTable") }}</b>{{ $t("soi.dataImport.period") }}
            </div>
          </el-form-item>

          <el-form-item prop="file">
            <el-upload
              ref="upload"
              action="/"
              :auto-upload="false"
              :show-file-list="true"
              :drag="true"
              :limit="1"
              accept=".xls,.xlsx"
              :on-exceed="handlerExceed"
              :on-success="handlerSuccess"
              :on-error="handlerError"
              :on-change="handlerChange"
              :file-list="fileList"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                <em>{{ $t("soi.dataImport.clickUploadTip") }}</em>
              </div>
              <div slot="tip" class="el-upload__tip">{{ $t("soi.dataImport.uploadTip") }}</div>
            </el-upload>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="submitUpload">{{ $t("soi.common.upload") }}</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div v-else>
        <el-alert
          :title="$t('soi.dataImport.createDatabaseTip')"
          type="success"
          :closable="false"
          style="margin-bottom:20px;"
        />
        <el-form ref="databaseForm" :model="databaseForm" :rules="databaseFormRules" label-width="130px">
          <el-form-item :label="$t('soi.dataImport.databaseName')" prop="databaseName">
            <el-input v-model="databaseForm.databaseName" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="createDatabase('databaseForm')">
              {{ $t("soi.common.confirm") }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { createDatabase, isExistDatabase, uploadDataTable } from '@/api/practice/data-import'
import { SOI_PLUS_CORE_API_URL } from '@/contains'

export default {
  name: 'DataImportImportData',
  components: { CustomizeCard },
  data() {
    const validateDBName = (rule, value, callback) => {
      const a = /^[a-z0-9_]*$/g

      if (!a.test(value)) {
        callback(new Error(this.$t('soi.dataImport.databaseNameFormatError')))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      isExistDB: false,
      uploadForm: {
        file: ''
      },
      databaseForm: {
        databaseName: ''
      },
      databaseFormRules: {
        databaseName: [
          { required: true, validator: validateDBName, trigger: 'blur' },
          { min: 5, max: 30, message: this.$t('soi.dataImport.databaseNameLengthError'), trigger: 'blur' }
        ]
      },
      fileList: [],
      importDataTemplateUrl: SOI_PLUS_CORE_API_URL + '/v1/system/template/download?fileName=data-import-template.xls'
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.isExistDatabase()
  },
  methods: {
    createDatabase(formName) {
      const vue = this
      this.$refs[formName].validate(valid => {
        if (valid) {
          vue.loading = true
          var requestData = new FormData()
          requestData.append('userId', this.userDetails.id)
          requestData.append('dbName', vue.databaseForm.databaseName)
          createDatabase(requestData)
            .then(() => {
              vue.$message({
                showClose: true,
                message: vue.$t('soi.dataImport.createDatabaseSuccess'),
                type: 'success'
              })
              vue.isExistDB = true
            })
            .finally(() => {
              vue.loading = false
            })
        }
      })
    },
    isExistDatabase() {
      const vue = this
      vue.loading = true
      isExistDatabase({ userId: this.userDetails.id })
        .then((res) => {
          this.isExistDB = res.data
        })
        .finally(() => {
          vue.loading = false
        })
    },
    downloadTemplateFile() {
      window.open(this.importDataTemplateUrl, '_self')
    },
    handlerExceed() {
      const vue = this
      vue.$alert(vue.$t('Database.onlySelectOneFileTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      }
      )
    },
    handlerSuccess() {
      const vue = this
      vue.$alert(vue.$t('soi.dataImport.uploadSuccess'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      })
    },
    handlerError() {
      const vue = this
      vue.$alert(vue.$t('soi.dataImport.uploadFailed'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      })
    },
    handlerChange(file, fileList) {
      const vue = this
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error(vue.$t('soi.dataImport.fileSizeTooLargeTip'))
        vue.fileList = []
        return isLt2M
      } else {
        vue.uploadForm.file = file.raw
        vue.fileList = fileList.slice(-1)
      }
    },
    submitUpload() {
      const vue = this
      if (vue.fileList.length === 0) {
        vue.$alert(vue.$t('soi.dataImport.pleaseSelectFile'), vue.$t('soi.common.tip'), {
          confirmButtonText: vue.$t('soi.common.confirm'),
          callback: action => {}
        })
        return
      }
      vue.loading = true
      const data = new FormData()
      data.append('userId', this.userDetails.id)
      data.append('file', vue.uploadForm.file)
      data.append('type', 'data-upload')

      uploadDataTable(data)
        .then(() => {
          vue.$alert(vue.$t('soi.dataImport.importSuccess'), vue.$t('soi.common.tip'))
          this.$router.go(-1)
        })
        .finally(() => {
          vue.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-form-content {
  text-align: center;
}
</style>
