<template>
  <div v-loading.fullscreen="loading" class="customize-make-table-container">
    <el-button :size="size" @click="handlerViewData">{{ buttonValue }}</el-button>
    <customize-dialog
      :title="title"
      :dialog-visible="dialogVisible"
      @update:dialogVisible="handlerUpdateDialogVisible($event)"
    >
      <el-table :data="tableData" height="440" border :cell-style="cellStyle">
        <el-table-column v-for="(item, index) in tableHeader" :key="index" :label="item" :prop="item" />
      </el-table>
      <div style="text-align: right; margin-top: 10px">
        <el-pagination
          :current-page.sync="page"
          :page-size="100"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </customize-dialog>
  </div>
</template>

<script>
import CustomizeDialog from '@/views/practice/data-analysis/data-preparation/components/CustomizeDialog'
import { getDirty } from '@/api/practice/data-preparation'

export default {
  name: 'CustomizeMarkTable',
  components: { CustomizeDialog },
  props: {
    fileId: {
      type: String,
      required: true
    },
    buttonValue: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    size: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      page: 1,
      tableData: [],
      tableHeader: [],
      total: 0
    }
  },
  methods: {
    handleCurrentChange(current) {
      this.page = current
      this.handlerViewData()
    },
    handlerViewData() {
      this.loading = true
      getDirty({ fileId: this.fileId, page: this.page })
        .then(response => {
          const { data } = response
          this.tableHeader = data.columns
          this.tableData = this.handlerResponseData(data)
          this.total = data.total
          this.dialogVisible = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerResponseData(data) {
      const tableData = []
      for (const value of data.values) {
        const obj = {}
        for (let i = 0; i < data.columns.length; i++) {
          obj[data.columns[i]] = value[i]
        }
        tableData.push(obj)
      }
      return tableData
    },
    handlerUpdateDialogVisible(value) {
      this.dialogVisible = value
    },
    cellStyle({ row, column }) {
      if (typeof row[column.property] === 'string') {
        return { background: '#f6f7fa' }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.customize-make-table-container {
  .ps {
    height: 500px;
  }
}
</style>
