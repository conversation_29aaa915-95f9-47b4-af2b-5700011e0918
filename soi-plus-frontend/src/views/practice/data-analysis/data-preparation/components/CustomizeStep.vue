<template>
  <div class="customize-step-container">
    <el-steps :active="active" align-center>
      <el-step icon="upload-file">
        <template #icon>
          <svg-icon icon-class="upload-file" class-name="step-height" @click="handlerChangeStep(0)" />
        </template>
        <template #title>
          <div class="tabs-title" @click="handlerChangeStep(0)">Data Upload</div>
        </template>
      </el-step>
      <el-step icon="upload-file">
        <template #icon>
          <svg-icon icon-class="data-labeling" class-name="step-height" @click="handlerChangeStep(1)" />
        </template>
        <template #title>
          <div class="tabs-title" @click="handlerChangeStep(1)">One Hot</div>
        </template>
      </el-step>
      <el-step icon="data-cleansing">
        <template #icon>
          <svg-icon icon-class="data-cleansing" class-name="step-height" @click="handlerChangeStep(2)" />
        </template>
        <template #title>
          <div class="tabs-title" @click="handlerChangeStep(2)">Data Cleansing</div>
        </template>
      </el-step>
      <el-step icon="simple-balancing">
        <template #icon>
          <svg-icon icon-class="simple-balancing" class-name="step-height" @click="handlerChangeStep(3)" />
        </template>
        <template #title>
          <div class="tabs-title" @click="handlerChangeStep(3)">Sample Balancing</div>
        </template>
      </el-step>
      <el-step icon="feature-selection">
        <template #icon>
          <svg-icon icon-class="feature-selection" class-name="step-height" @click="handlerChangeStep(4)" />
        </template>
        <template #title>
          <div class="tabs-title" @click="handlerChangeStep(4)">Dimensionality Reduction</div>
        </template>
      </el-step>
      <el-step icon="data-convert">
        <template #icon>
          <svg-icon icon-class="data-convert" class-name="step-height" @click="handlerChangeStep(5)" />
        </template>
        <template #title>
          <div class="tabs-title" @click="handlerChangeStep(5)">PCA(Principal Component Analysis)</div>
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script>
export default {
  name: 'CustomizeStep',
  props: {
    active: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    handlerChangeStep(value) {
      this.$emit('click-active', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.customize-step-container {
  margin-bottom: 20px;
  .step-height {
    width: 2em;
    height: 2em;
    cursor: pointer;
  }
  .tabs-title {
    line-height: 1.5;
    cursor: pointer;
  }
}
</style>
