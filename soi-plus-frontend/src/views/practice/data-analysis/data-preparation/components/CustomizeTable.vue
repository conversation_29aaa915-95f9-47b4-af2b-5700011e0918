<template>
  <div class="customize-table-container">
    <el-table :data="fileList" border>
      <el-table-column
        v-for="(item ,index) in cols"
        :key="index"
        :label="item.label"
        :prop="item.prop"
        align="center"
      />
      <el-table-column v-if="showOperate" label="Operate" align="center" :width="240">
        <template slot-scope="scope">
          <div v-if="!isEnd">
            <customize-view-data v-if="showView" :file-id="scope.row.fileId" size="mini" />
            <el-button
              type="primary"
              icon="el-icon-download"
              size="mini"
              @click="handlerDownload(scope.row.fileId)"
            > Download </el-button>
          </div>
          <div v-if="isEnd">
            <customize-view-data v-if="showView" :file-id="scope.row.testingLink" size="mini" label="Testing" />
            <el-button
              type="primary"
              icon="el-icon-download"
              size="mini"
              style="width: 100px;"
              @click="handlerDownload(scope.row.testingLink)"
            > Testing </el-button>
            <customize-view-data v-if="showView" :file-id="scope.row.trainingLink" size="mini" label="Training" />
            <el-button
              type="primary"
              icon="el-icon-download"
              size="mini"
              style="width: 100px;"
              @click="handlerDownload(scope.row.trainingLink)"
            > Training </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import CustomizeViewData from '@/views/practice/data-analysis/data-preparation/components/CustomizeViewData'
import { DATA_PREPARATION_API_URL } from '@/contains'

export default {
  name: 'CustomizeTable',
  components: { CustomizeViewData },
  props: {
    fileList: {
      type: Array,
      required: true
    },
    cols: {
      type: Array,
      required: true
    },
    showOperate: {
      type: Boolean,
      required: false,
      default: true
    },
    isEnd: {
      type: Boolean,
      required: false,
      default: false
    },
    showView: {
      type: Boolean,
      required: false,
      default: true
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    handlerDownload(link) {
      window.location.href = `${DATA_PREPARATION_API_URL}/dp/download/?fileId=${link}`
    }
  }
}
</script>

<style scoped>

</style>
