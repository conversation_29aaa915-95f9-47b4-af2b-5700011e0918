<template>
  <el-form-item>
    <el-button v-if="previous" @click="previousStep">Previous</el-button>
    <el-button v-if="next" type="primary" @click="nextStep">Next</el-button>
  </el-form-item>
</template>

<script>
export default {
  props: {
    next: {
      type: Boolean,
      required: false,
      default: true
    },
    previous: {
      type: Boolean,
      required: false,
      default: true
    },
    current: {
      type: Number,
      required: true
    }
  },
  methods: {
    previousStep() {
      this.$emit('switchPage', this.current - 1)
    },
    nextStep() {
      this.$emit('switchPage', this.current + 1)
    }
  }
}
</script>
