<template>
  <div class="customize-upload-container">
    <el-upload
      class="customize-upload"
      :action="action"
      :drag="true"
      accept=".xlsx"
      :file-list="fileList"
      :on-change="handlerOnChange"
      :on-success="handlerOnSuccess"
      :before-upload="handlerBeforeUpload"
      :on-error="handlerOnError"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text">Drag the file here, or <em>click upload</em></div>
      <div slot="tip" class="el-upload__tip">Only .xlsx files can be uploaded, and they should not exceed 10 MB</div>
    </el-upload>
  </div>
</template>

<script>
export default {
  name: 'CustomizeUpload',
  props: {
    action: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      drag: true,
      fileList: [],
      fileName: null
    }
  },
  methods: {
    handlerBeforeUpload(file) {
      this.fileName = file.name
      const isAllowFileType = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'text/csv' || file.type === 'application/vnd.ms-excel'
      const size20M = file.size / 1024 / 1024 < 10
      if (!size20M) {
        this.$message.warning('The uploaded file size cannot exceed 10 MB!')
        return false
      }
      if (!isAllowFileType) {
        this.$message.warning('Only .xlsx format files can be uploaded!')
        return false
      }
    },
    handlerOnChange(file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]
      }
    },
    handlerOnSuccess(response) {
      console.log(response)
      const { code, data, desc } = response
      if (code === 20000) {
        this.$message.success('Uploaded file successfully')
        this.$emit('upload-success', { fileId: data.fileId || '', items: data.columns || [], fileName: this.fileName || '' })
      } else {
        this.$confirm('Upload file failed: ' + desc, 'Tip', {
          confirmButtonText: 'Confirm',
          showCancelButton: false,
          type: 'error'
        }).then(() => {}).catch(() => {})
      }
    },
    handlerOnError(response) {
      this.$confirm('Upload file failed: ' + response.message || response, 'Tip', {
        confirmButtonText: 'Confirm',
        showCancelButton: false,
        type: 'error'
      }).then(() => {}).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
.customize-upload-container {
  .el-upload-list__item {
    transition: none;
  }
  .customize-upload {
    width: 360px;
  }
}
</style>
