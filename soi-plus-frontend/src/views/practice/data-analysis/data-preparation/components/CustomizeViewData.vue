<template>
  <div v-loading.fullscreen="loading" class="view-data-container">
    <el-button style="width: 100px" :size="size" icon="el-icon-view" @click="handlerViewData()">{{ label }}</el-button>
    <customize-dialog
      :title="fileId"
      :dialog-visible="dialogVisible"
      :max-width="1200"
      @update:dialogVisible="handlerUpdateViewDataDialogVisible($event)"
      @confirm="handlerConfirm"
    >
      <div class="customize-table-container">
        <el-table :data="tableData" height="440" border>
          <el-table-column
            v-for="(item ,index) in tableHeader"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            align="center"
          />
        </el-table>
      </div>
      <div style="text-align: right; margin-top: 10px">
        <el-pagination
          :current-page.sync="page"
          :page-size="100"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </customize-dialog>
  </div>
</template>

<script>
import CustomizeDialog from '@/views/practice/data-analysis/data-preparation/components/CustomizeDialog'
import { getView } from '@/api/practice/data-preparation'
import { DATA_PREPARATION_API_URL } from '@/contains'

export default {
  name: 'CustomizeViewData',
  components: { CustomizeDialog },
  props: {
    fileId: {
      type: String,
      required: true
    },
    size: {
      type: String,
      required: false,
      default: ''
    },
    label: {
      type: String,
      required: false,
      default: 'View'
    }
  },
  data() {
    return {
      loading: false,
      dialogVisible: false,
      tableHeader: [],
      tableData: [],
      page: 1,
      total: 0
    }
  },
  methods: {
    handleCurrentChange(current) {
      this.page = current
      this.handlerViewData()
    },
    handlerViewData() {
      if (!this.fileId) {
        return
      }
      this.loading = true
      getView({ fileId: this.fileId, page: this.page })
        .then(response => {
          const { data } = response
          this.total = data.total
          this.tableData = this.processResData(data.columns, data.values)
          this.tableHeader = this.processResHeader(data.columns)
          this.dialogVisible = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    processResHeader(headers) {
      const tableHeader = []
      for (const header of headers) {
        tableHeader.push({
          label: header,
          prop: header
        })
      }
      return tableHeader
    },
    processResData(headers, data) {
      const tableData = []
      for (const d of data) {
        const item = {}
        for (let i = 0; i < headers.length; i++) {
          item[headers[i]] = d[i]
        }
        tableData.push(item)
      }
      return tableData
    },
    handlerUpdateViewDataDialogVisible(value) {
      this.dialogVisible = value
    },
    handlerConfirm() {
      this.dialogVisible = false
    },
    handlerDownload(row) {
      window.location.href = `${DATA_PREPARATION_API_URL}/dp/download/?fileId=${row.fileId}`
    }
  }
}
</script>

<style lang="scss" scoped>
.view-data-container {
  margin: 0 5px 5px 0;
  display: inline-block;
}
</style>
