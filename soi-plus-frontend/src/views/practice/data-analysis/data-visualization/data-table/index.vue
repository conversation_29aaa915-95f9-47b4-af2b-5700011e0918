<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-table-container">
    <customize-card :title="`${$t('soi.router.dataVisualization')} - ${$t('soi.router.dataTable')}`">
      <template #header-actions>
        <el-button size="mini" @click="handlerCreateDataTable()">{{ $t('soi.dataVisualization.createDataTable') }}</el-button>
      </template>

      <div class="data-visualization-menu">
        <el-button size="mini" type="primary" @click="$router.push({ name: 'data-analysis-data-visualization-data-source' })">
          {{ $t('soi.router.dataSource') }}
        </el-button>
        <el-button size="mini" type="primary" @click="$router.push({ name: 'data-analysis-data-visualization-my-charts' })">
          {{ $t('soi.router.myCharts') }}
        </el-button>
      </div>

      <el-table :data="list" :header-cell-style="{'background': '#109eae42', 'color': '#333'}" tooltip-effect="dark">
        <el-table-column :label="$t('soi.dataVisualization.dataSource')" prop="source.name" align="center" />
        <el-table-column :label="$t('soi.dataVisualization.dataTableName')" align="center">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="right">
              <span v-for="(database,index) in scope.row.view.description" :key="index">
                <el-tag style="margin: 5px" type="info">{{ database.db }}</el-tag>
                <span v-for="(table, x) in database.table" :key="x">
                  <el-tag style="margin: 5px" type="success">{{ table }}</el-tag>
                </span>
                <br>
              </span>
              <div slot="reference" class="name-wrapper" style="display: inline-block">
                <p style="color:#22C1E6;margin: 5px 0 0;display: inline-block">{{ scope.row.view.name }}</p>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column :label="$t('soi.common.createTime')" prop="view.createTime" sortable align="center" />
        <el-table-column :label="$t('soi.common.operate')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="editTable(scope.row)">{{ $t("soi.common.edit") }}</el-button>
            <el-button size="mini" @click="deleteTable(scope.row)">{{ $t("soi.common.delete") }}</el-button>
            <el-button size="mini" @click="createCharts(scope.row)">{{ $t("soi.dataVisualization.createChart") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { deleteDataTable, getDataTableList } from '@/api/practice/data-visualization'

export default {
  name: 'DataVisualizationDataTable',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      list: [],
      currentPage: 1,
      pageSize: 12,
      total: 0
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  created() {
    this.init()
  },
  methods: {
    handlerCreateDataTable() {
      this.$router.push({ name: 'data-analysis-data-visualization-data-table-form' })
    },
    editTable(row) {
      this.$router.push({ name: 'data-analysis-data-visualization-data-table-form', query: { viewId: row.view.id }})
    },
    createCharts(row) {
      this.$router.push({ name: 'data-analysis-data-visualization-my-charts-form', query: { viewId: row.view.id }})
    },
    deleteTable(row) {
      const vue = this
      this.$confirm(vue.$t('soi.dataVisualization.deleteTableTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true

        deleteDataTable(row.view.id, vue.userDetails.id)
          .then((res) => {
            const currentPage = (vue.total - 1) % vue.pageSize === 0 ? parseInt((vue.total - 1) / vue.pageSize) : parseInt((vue.total - 1) / vue.pageSize) + 1
            if (vue.currentPage > currentPage) {
              vue.currentPage = currentPage
            }

            vue.$message({
              type: 'success',
              message: res.message
            })

            vue.init()
          })
          .finally(() => {
            vue.loading = false
          })
      })
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.init()
    },
    init() {
      const vue = this
      vue.loading = true
      const requestData = {
        page: this.currentPage,
        pageSize: this.pageSize,
        userId: this.userDetails.id
      }

      getDataTableList(requestData)
        .then((res) => {
          vue.total = res.data.total
          const result = res.data.items
          for (let i = 0; i < result.length; i++) {
            result[i].view.createTime = new Date(result[i].view.createTime).toLocaleString()
            result[i].view.description = JSON.parse(result[i].view.description)
          }
          vue.list = result
        })
        .finally(() => {
          vue.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.data-visualization-menu {
  text-align: right;
  margin-bottom: 10px;
}
</style>
