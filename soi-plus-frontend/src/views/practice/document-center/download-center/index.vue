<template>
  <div class="document-center-download-center-container">
    <customize-card :title="$t('soi.router.downloadCenter')">
      <el-tabs type="border-card">
        <el-tab-pane :label="$t('soi.downloadCenter.installIde')">
          <div style="margin:30px;">
            <h3>{{ $t('soi.downloadCenter.title') }}</h3>
            <p>{{ $t('soi.downloadCenter.subtitle') }}</p>
            <el-tabs type="border-card">
              <!-- windows -->
              <el-tab-pane :label="$t('soi.downloadCenter.windowsInstall')">
                <el-tabs tab-position="left">
                  <el-tab-pane :label="$t('soi.downloadCenter.installDocker')" v-html="$t('soi.downloadCenter.dockerInstall')" />
                  <el-tab-pane :label="$t('soi.downloadCenter.downloadService')" v-html="$t('soi.downloadCenter.downloadServiceContent')" />
                  <el-tab-pane :label="$t('soi.downloadCenter.verifyService')" v-html="$t('soi.downloadCenter.verifyServiceContent')" />
                  <el-tab-pane :label="$t('soi.downloadCenter.runAndUse')" v-html="$t('soi.downloadCenter.runAndUseContent')" />
                </el-tabs>
              </el-tab-pane>
              <!-- macos -->
              <el-tab-pane :label="$t('soi.downloadCenter.macOSInstall')">
                <el-tabs tab-position="left">
                  <el-tab-pane :label="$t('soi.downloadCenter.installDocker')" v-html="$t('soi.downloadCenter.dockerInstallMacos')" />
                  <el-tab-pane :label="$t('soi.downloadCenter.downloadService')" v-html="$t('soi.downloadCenter.downloadServiceContentMacos')" />
                  <el-tab-pane :label="$t('soi.downloadCenter.verifyService')" v-html="$t('soi.downloadCenter.verifyServiceContentMacos')" />
                  <el-tab-pane :label="$t('soi.downloadCenter.runAndUse')" v-html="$t('soi.downloadCenter.runAndUseContentMacos')" />
                </el-tabs>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('soi.downloadCenter.installMl')">
          <div class="file-details">
            <div style="margin:30px;">
              <h3>{{ $t('soi.downloadCenter.titleMl') }}</h3>
              <p>{{ $t('soi.downloadCenter.subtitleMl') }}</p>
              <el-tabs type="border-card">
                <!-- windows -->
                <el-tab-pane :label="$t('soi.downloadCenter.windowsInstall')">
                  <el-tabs tab-position="left">
                    <el-tab-pane :label="$t('soi.downloadCenter.installDocker')" v-html="$t('soi.downloadCenter.dockerInstall')" />
                    <el-tab-pane :label="$t('soi.downloadCenter.downloadService')" v-html="$t('soi.downloadCenter.downloadServiceContentMl')" />
                    <el-tab-pane :label="$t('soi.downloadCenter.verifyService')" v-html="$t('soi.downloadCenter.verifyServiceContentMl')" />
                    <el-tab-pane :label="$t('soi.downloadCenter.runAndUse')" v-html="$t('soi.downloadCenter.runAndUseContentMl')" />
                  </el-tabs>
                </el-tab-pane>
                <!-- macos -->
                <el-tab-pane :label="$t('soi.downloadCenter.macOSInstall')">
                  <el-tabs tab-position="left">
                    <el-tab-pane :label="$t('soi.downloadCenter.installDocker')" v-html="$t('soi.downloadCenter.dockerInstallMacos')" />
                    <el-tab-pane :label="$t('soi.downloadCenter.downloadService')" v-html="$t('soi.downloadCenter.downloadServiceContentMacosMl')" />
                    <el-tab-pane :label="$t('soi.downloadCenter.verifyService')" v-html="$t('soi.downloadCenter.verifyServiceContentMacosMl')" />
                    <el-tab-pane :label="$t('soi.downloadCenter.runAndUse')" v-html="$t('soi.downloadCenter.runAndUseContentMacosMl')" />
                  </el-tabs>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'DocumentCenterDownloadCenter',
  components: { CustomizeCard }
}
</script>

<style lang="scss" scoped>
.document-center-download-center-container {
  font-size: 14px;
}
</style>
