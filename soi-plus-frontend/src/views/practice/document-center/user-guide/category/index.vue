<template>
  <div v-loading.fullscreen.lock="loading" class="document-center-user-guide-category-container">
    <customize-card :title="$t('soi.router.userGuide')">
      <el-row :gutter="40" class="panel-group">
        <el-col v-for="item in category" :key="item.id" :span="8" :xs="24" class="card-panel-col">
          <div class="card-panel" @click="itemHandle(item)">
            <div class="card-panel-icon-wrapper icon-people">
              <svg-icon icon-class="docs" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">{{ ['zh', 'cht'].includes(language) ? item.className : item.classNameEn }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getUserCaseSubcategory } from '@/api/practice/user-case'
import { mapGetters } from 'vuex'

export default {
  name: 'DocumentCenterUserGuideCategory',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      category: []
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const vue = this
      vue.loading = true
      getUserCaseSubcategory({ group: 'User Guide' })
        .then((res) => {
          vue.loading = false
          vue.category = []

          res.data.forEach((item) => {
            vue.category.push({ className: item.className, classNameEn: item.classNameEn, id: item.id })
          })
        }).finally(() => {
          vue.loading = false
        })
    },
    itemHandle(item) {
      localStorage.setItem('document-subcategory', JSON.stringify(item))

      this.$router.push({ name: 'document-center-user-guide-document-list' })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.panel-group {
  .card-panel-col {
    margin-bottom: 32px;
  }
  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);
    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-people {
        background: #40c9c6;
      }
      .icon-message {
        background: #36a3f7;
      }
      .icon-money {
        background: #f4516c;
      }
      .icon-shopping {
        background: #34bfa3;
      }
    }
    .icon-people {
      color: #40c9c6;
    }
    .icon-message {
      color: #36a3f7;
    }
    .icon-money {
      color: #f4516c;
    }
    .icon-shopping {
      color: #34bfa3;
    }
    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }
    .card-panel-icon {
      float: left;
      font-size: 48px;
    }
    .card-panel-description {
      font-weight: bold;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      .card-panel-text {
        line-height: 108px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-left: 20px;
        word-break: break-all;
      }
      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
</style>
