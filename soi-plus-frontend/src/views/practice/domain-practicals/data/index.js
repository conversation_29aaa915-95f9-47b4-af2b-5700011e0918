import { category } from '@/views/practice/domain-practicals/data/category'

export function findCategoryNameById(id) {
  for (let i = 0; i < category.length; i++) {
    if (category[i].id === id) {
      return category[i].name
    }
  }
  return category[0].name
}

export function findCategoryValueById(id) {
  for (let i = 0; i < category.length; i++) {
    if (category[i].id === id) {
      return category[i].value
    }
  }
  return category[0].value
}
