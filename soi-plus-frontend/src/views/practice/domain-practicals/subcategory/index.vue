<template>
  <div v-loading.fullscreen.lock="loading" class="domain-practicals-subcategory-container">
    <customize-card :title="$t(categoryName)">
      <el-row v-if="tableData.length > 0" :gutter="20">
        <el-col v-for="(item, index) in tableData" :key="index" style="padding:10px;" :span="8" :xs="24">
          <el-card shadow="hover" class="card-list" style="height:100%">
            <el-row type="flex" align="middle" style="height:100%">
              <el-col :span="6" class="card-img">
                <img :src="image" alt="image">
              </el-col>
              <el-col :span="16" :offset="2">
                <h2>{{ ['zh', 'cht'].includes(language) ? item.className : item.classNameEn }}</h2>
                <el-button size="small" type="primary" @click="getDocList(item)">{{ $t("soi.common.view") }}</el-button>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      <div v-else class="text-center">{{ $t('soi.common.noData') }}</div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { findCategoryNameById, findCategoryValueById } from '@/views/practice/domain-practicals/data'
import { getListByGroup } from '@/api/practice/user-case'

export default {
  name: 'DomainPracticalsSubcategory',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      id: this.$route.params.id,
      image: require(`@/assets/images/doc.png`),
      tableData: []
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ]),
    categoryName() {
      return findCategoryNameById(this.id)
    },
    categoryValue() {
      return findCategoryValueById(this.id)
    }
  },
  mounted() {
    this.getUseCase()
  },
  methods: {
    getUseCase() {
      const vue = this
      vue.loading = true

      getListByGroup({ group: vue.categoryValue })
        .then((res) => {
          vue.tableData = res.data
          vue.addOtherModule()
        }).finally(() => {
          vue.loading = false
        })
    },
    addOtherModule() {
      if (this.categoryValue === 'Technical Use Case') {
        // Technical Use Case 分组下添加代码库
        this.tableData.push({
          className: '代码库',
          classNameEn: 'Code Lib',
          groupName: 'Technical Use Case',
          groupNameEn: 'Technical Use Case',
          id: -1
        })
      }

      if (this.categoryValue === 'Business Use Case') {
        // Business Use Case 分组下添加工作流配置
        this.tableData.push({
          className: '工作流配置',
          classNameEn: 'Low Code Exercise',
          groupName: 'Business Use Case',
          groupNameEn: 'Business Use Case',
          id: -2
        })
      }
    },
    getDocList(item) {
      localStorage.setItem('document-subcategory', JSON.stringify(item))

      this.$router.push({ name: 'domain-practicals-document-list' })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.card-list {
  position: relative;
  height: 195px;
  border: 1px solid #ccc !important;
  box-shadow: 5px 5px 5px #ccc;
  .card-img {
    text-align: center;
    img {
      width: 90%;
      height: auto;
      vertical-align: middle;
    }
  }
  h2 {
    margin-top: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.5;
  }
  p {
    height: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
.text-center {
  text-align: center;
}
</style>
