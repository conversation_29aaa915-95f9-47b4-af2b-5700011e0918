<template>
  <div v-loading.fullscreen.lock="loading" class="simulated-systems-insurance-claims-container">
    <customize-card :title="$t('soi.router.insuranceClaims')">
      <h4 style="margin-bottom:10px;">{{ $t("soi.simulatedSystems.selectVisCustomerTip") }}</h4>

      <el-collapse v-model="activeNames">
        <el-collapse-item :title="$t('soi.simulatedSystems.staffList')" name="1">
          <el-table v-if="uploadLange" :data="staffTokenOptions" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
            <el-table-column type="index" width="50" label="#" />
            <el-table-column prop="cardId" :label="$t('soi.simulatedSystems.loginName')" width="200px" />
            <el-table-column prop="pwd" :label="$t('soi.simulatedSystems.loginPassword')" />
            <el-table-column prop="name" :label="$t('soi.simulatedSystems.username')" />
            <el-table-column width="150px" :prop="['zh', 'cht'].includes(language) ? 'role' : 'roleEn'" :label="$t('soi.simulatedSystems.role')" />
            <el-table-column :label="$t('soi.common.operate')" width="100px">
              <template slot-scope="scope">
                <el-button type="text" @click="saveCustomerProfile(scope.row, 'staff')">{{ $t('soi.simulatedSystems.login') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { getInsuranceUser } from '@/api/practice/sandbox'
import { VIS_URL } from '@/contains'

export default {
  name: 'SimulatedSystemsInsuranceClaims',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      activeNames: ['1'],
      uploadLange: true,
      staffTokenOptions: []
    }
  },
  computed: {
    ...mapGetters([
      'language',
      'userDetails'
    ])
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const vue = this
      vue.loading = true
      getInsuranceUser({ userId: this.userDetails.id })
        .then((res) => {
          vue.staffTokenOptions = []
          if (res.data.length > 0) {
            vue.staffTokenOptions = res.data.filter(item => {
              return item.roleEn !== 'Customer'
            })
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    saveCustomerProfile(row, role) {
      window.open(VIS_URL + '?role=' + role + '&userID=' + row.cardId + '&pwd=' + row.pwd, '_blank')
    }
  }
}
</script>
