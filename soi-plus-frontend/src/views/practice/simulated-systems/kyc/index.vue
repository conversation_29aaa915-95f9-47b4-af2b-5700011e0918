<template>
  <div v-loading.fullscreen.lock="loading" class="simulated-systems-kyc-container">
    <customize-card :title="$t('soi.router.kyc')">
      <h4 style="margin-bottom:10px;">{{ $t("soi.simulatedSystems.selectVbsCustomerTip") }}</h4>

      <p class="subtitle">{{ $t("soi.simulatedSystems.hkVbsAccount") }}</p>
      <el-collapse v-model="activeNames">
        <el-collapse-item :title="$t('soi.simulatedSystems.staffList')" name="1">
          <el-table :data="staffTokenOptions" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
            <el-table-column type="index" width="50" label="#" />
            <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
            <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
            <el-table-column prop="role" :label="$t('soi.simulatedSystems.role')" />
            <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
            <el-table-column :label="$t('soi.common.operate')" width="100px">
              <template slot-scope="scope">
                <el-button type="text" @click="saveCustomerProfile(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { getLbsStaff } from '@/api/practice/sandbox'
import { HK_VBS_URL } from '@/contains'

export default {
  name: 'SimulatedSystemsKYC',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      activeNames: ['1'],
      staffTokenOptions: []
    }
  },
  computed: {
    ...mapGetters([
      'userDetails'
    ])
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const vue = this
      vue.loading = true

      return getLbsStaff({ userId: this.userDetails.id })
        .then((res) => {
          vue.staffTokenOptions = []
          if (res.data.length > 0) {
            res.data.forEach(item => {
              if (item.role === 'CRM') {
                vue.staffTokenOptions.push({
                  customerNumber: item.staffNumber,
                  loginName: item.userName,
                  loginPwd: item.pwd,
                  role: item.role
                })
              }
            })
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    saveCustomerProfile(row) {
      window.open(HK_VBS_URL +
        '?customerNumber=' + row.customerNumber +
        '&loginPassword=' + row.loginPwd +
        '&loginName=' + row.loginName,
      '_blank',
      ''
      )
    }
  }
}
</script>
