<template>
  <div class="technological-development-api-api-market-place-container">
    <api-market-place-api-provider />
  </div>
</template>

<script>
import ApiMarketPlaceApiProvider
  from '@/views/practice/technological-development/api/api-market-place/api-provider/index.vue'

export default {
  name: 'ApiApiMarketPlace',
  components: { ApiMarketPlaceApiProvider },
  data() {
    return {}
  }
}
</script>
