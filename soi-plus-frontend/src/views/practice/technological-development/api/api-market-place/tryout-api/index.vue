<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="technological-development-api-api-market-place-tryout-api-container"
  >
    <customize-card
      :title="
        `${$t('soi.router.tryoutApi')} - ${
          ['zh', 'cht'].includes(language) ? apiInfo.apiName : apiInfo.apiNameEn
        }`
      "
    >
      <div class="details">
        <div class="details-right">
          <el-row style="width:100%;margin-bottom:20px;">
            <el-col :span="6" :xs="24" class="item">
              {{ $t("soi.apiMarketPlace.apiStatus") }}:
              <span style="color: green">{{ $t("soi.common.normal") }}</span>
            </el-col>
            <el-col :span="6" :xs="24" class="item">
              {{ $t("soi.apiMarketPlace.apiProvider") }}:
              <span>{{ apiInfo.fiName }}</span>
            </el-col>
            <el-col v-if="isDeveloper" :span="6" :xs="24" class="item">
              {{ $t("soi.apiMarketPlace.sandboxToken") }}:
              <el-select
                v-model="selectToken"
                size="mini"
                class="periodSelect"
                @change="changeToken"
              >
                <el-option
                  v-for="item in tokenOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="6" :xs="24" class="item">
              {{ $t("soi.apiMarketPlace.downloadSwagger") }}:
              <el-button size="mini" @click="downloadSwagger">{{
                $t("soi.common.download")
              }}</el-button>
            </el-col>
            <el-col :span="18" :xs="24" class="item">
              {{ $t("soi.common.token") }}:
              <span v-if="devToken.length > 0">
                <el-select
                  v-model="clientId"
                  size="mini"
                  @change="changeDevToken"
                >
                  <el-option
                    v-for="item in devToken"
                    :key="item.id"
                    :label="item.clientId"
                    :value="item.clientId"
                  />
                </el-select>
                <span>{{ token }}</span>
              </span>
              <el-button v-else size="mini" @click="createToken">{{
                $t("soi.apiMarketPlace.generateToken")
              }}</el-button>
            </el-col>
            <el-col v-if="attachUrl" :span="6" :xs="24" class="item">
              {{ $t("soi.apiMarketPlace.userGuide") }}:
              <a :href="attachUrl" target="_blank">{{
                $t("soi.common.view")
              }}</a>
            </el-col>
          </el-row>
        </div>
      </div>
      <div v-if="MDValue" class="markdown">
        <MarkdownPreview :height="400" :initial-value="MDValue" />
      </div>
      <div style="height: 100%">
        <div id="swagger" style="word-break: break-word;" />
      </div>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getApiInfo, getClientToken, getSwagger } from '@/api/practice/api-market'
import { mapGetters } from 'vuex'
import SwaggerUI from 'swagger-ui'
import 'swagger-ui/dist/swagger-ui.css'
import { MarkdownPreview } from 'vue-meditor'
import { DEFAULT_LBS_TOKEN, SOI_PLUS_BUSINESS_API_URL } from '@/contains'
import { getTokenInfo } from '@/api/practice/sandbox'

export default {
  name: 'ApiMarketPlaceTryoutApi',
  components: { CustomizeCard, MarkdownPreview },
  data() {
    return {
      loading: false,
      apiId: this.$route.params.apiId,
      swagger: null,
      MDValue: '',
      apiInfo: {},
      isDeveloper: true,
      selectToken: DEFAULT_LBS_TOKEN,
      lastToken: DEFAULT_LBS_TOKEN,
      tokenOptions: [
        { value: DEFAULT_LBS_TOKEN, label: this.$t('soi.common.default') }
      ],
      devToken: [],
      clientId: '',
      token: '',
      attachUrl: ''
    }
  },
  computed: {
    ...mapGetters(['userDetails', 'language'])
  },
  watch: {
    language(newValue) {
      const swagger = this.swagger[this.getLanguage(newValue)]
      if (swagger) {
        this.initSwagger(JSON.parse(swagger))
      }
    }
  },
  mounted() {
    this.apiInfo = JSON.parse(localStorage.getItem('api-info'))
    this.getSwagger()
    this.getClientToken()
    this.getApiInfo()
    this.getSandboxToken()
  },
  methods: {
    getClientToken() {
      const vue = this
      vue.loading = true

      getClientToken({ userId: this.userDetails.id })
        .then(res => {
          vue.devToken = []
          res.data.forEach(item => {
            if (!item.code || (item.client && item.client.archived)) {
              return true
            }
            for (const key in item.client) {
              item[key] = item.client[key]
            }
            vue.devToken.push(item)
          })
          if (vue.devToken[0]) {
            vue.clientId = vue.devToken[0].clientId
            vue.token = vue.devToken[0].code
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    createToken() {
      this.$router.push({
        name: 'technological-development-api-obtain-developer-token'
      })
    },
    changeToken() {
      if (this.swagger) {
        const keys = Object.keys(this.swagger)

        keys.forEach(key => {
          this.swagger[key] = this.swagger[key].replaceAll(
            this.lastToken,
            this.selectToken
          )
        })

        this.lastToken = this.selectToken

        this.initSwagger(
          JSON.parse(this.swagger[this.getLanguage(this.language)])
        )
      }
    },
    changeDevToken() {
      this.devToken.forEach(item => {
        if (item.clientId === this.clientId) {
          this.token = item.code
        }
      })
    },
    downloadSwagger() {
      window.location.href =
        SOI_PLUS_BUSINESS_API_URL +
        '/v1/practical/api-market/swagger/download?apiId=' +
        this.apiId
    },
    getSandboxToken() {
      const vue = this
      vue.loading = true

      getTokenInfo({ userId: this.userDetails.id })
        .then(res => {
          if (res.data.length > 0) {
            const data = res.data
            for (let i = 0; i < data.length; i++) {
              vue.tokenOptions.push({
                value: data[i].userToken,
                label: data[i].customerNumber
              })
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    getLanguage(langauge) {
      return ['zh', 'cht'].includes(langauge) ? 'zh' : 'en'
    },
    getApiInfo() {
      const vue = this
      vue.loading = true

      // 获取API信息
      getApiInfo({ apiId: this.apiId })
        .then(res => {
          vue.attachUrl = res.data.attachUrl
          vue.MDValue = res.data.markdown
        })
        .finally(() => {
          vue.loading = false
        })
    },
    getSwagger() {
      const vue = this
      vue.loading = true

      getSwagger({ apiId: this.apiId })
        .then(res => {
          vue.swagger = res.data
          vue.initSwagger(
            JSON.parse(vue.swagger[this.getLanguage(this.language)])
          )
        })
        .finally(() => {
          vue.loading = false
        })
    },
    initSwagger(data) {
      SwaggerUI({
        dom_id: '#swagger',
        spec: data,
        defaultModelsExpandDepth: -1
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.technological-development-api-api-market-place-tryout-api-container {
  .item {
    margin: 10px 0;
  }
}
</style>
