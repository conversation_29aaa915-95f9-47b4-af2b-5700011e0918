<template>
  <div class="technological-development-api-api-usage-guide-api-architecture-design-container">
    <customize-card :title="$t('soi.router.apiArchitectureDesign')">
      <el-row type="flex" justify="center">
        <el-col>
          <h1 class="api-title">{{ $t('soi.apiArchitectureDesign.primaryTitle') }}</h1>
          <h4 class="tip-item">{{ $t('soi.apiArchitectureDesign.description1') }}</h4>
          <h4 class="tip-item">{{ $t('soi.apiArchitectureDesign.description2') }}</h4>
          <h4 class="tip-item">{{ $t('soi.apiArchitectureDesign.description3') }}</h4>
          <h4 class="tip-item">{{ $t('soi.apiArchitectureDesign.description4') }}</h4>
          <div style="display: flex">
            <div>
              <div class="image-item">
                <img :src="require('@/assets/images/interface.png')" alt="interface">
                <div>
                  <h4>{{ $t('soi.apiArchitectureDesign.interfaceTitle') }}</h4>
                  <p><b>{{ $t('soi.apiArchitectureDesign.interfaceDescriptionKey') }}</b>{{ $t('soi.apiArchitectureDesign.interfaceDescriptionValue') }}</p>
                </div>
              </div>
              <div class="image-item">
                <img :src="require('@/assets/images/orchestration.png')" alt="orchestration">
                <div>
                  <h4>{{ $t('soi.apiArchitectureDesign.orchestrationTitle') }}</h4>
                  <p><b>{{ $t('soi.apiArchitectureDesign.orchestrationDescriptionKey') }}</b>{{ $t('soi.apiArchitectureDesign.orchestrationDescriptionValue') }}</p>
                </div>
              </div>
              <div class="image-item">
                <img :src="require('@/assets/images/connectivity.png')" alt="connectivity">
                <div>
                  <h4>{{ $t('soi.apiArchitectureDesign.connectivityTitle') }}</h4>
                  <p><b>{{ $t('soi.apiArchitectureDesign.connectivityDescriptionKey') }}</b>{{ $t('soi.apiArchitectureDesign.connectivityDescriptionValue') }}</p>
                </div>
              </div>
            </div>
            <img :src="require(`@/assets/images/channels-${language}.png`)" alt="connectivity">
          </div>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'ApiUsageGuideApiArchitectureDesign',
  components: { CustomizeCard },
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  }
}
</script>

<style lang="scss" scoped>
.technological-development-api-api-usage-guide-api-architecture-design-container {
  .api-item {
    height: 160px;
    display: flex;
    font-size: 20px;
    justify-content: center;
    align-items: center;
  }

  .api-title {
    margin: 30px 0;
  }

  .tip-item {
    margin: 20px 0 ;
    line-height: 2;
    font-weight: 400;
  }
  .image-item {
    display: flex;
    align-items: center;
  }
}
</style>
