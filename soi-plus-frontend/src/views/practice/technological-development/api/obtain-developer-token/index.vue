<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="technological-development-api-obtain-developer-token-container"
  >
    <customize-card :title="$t('soi.router.obtainDeveloperToken')">
      <template #header-actions>
        <el-button size="mini" @click="addClient">{{
          $t("soi.obtainDeveloperToken.client")
        }}</el-button>
      </template>

      <div>
        <div v-if="list.length <= 0">
          <p class="text-center">{{ $t("soi.common.noData") }}</p>
        </div>

        <ul v-for="(item, index) in list" :key="index">
          <li class="list-group-item-text text-muted">
            <el-row>
              <el-col :span="20" style="line-height: 30px;">
                <span
                  class="label"
                >{{ $t("soi.obtainDeveloperToken.clientId") }}:
                </span>
                <span class="text-danger">{{ item.clientId }}</span><br>
                <span
                  class="label"
                >{{ $t("soi.obtainDeveloperToken.secret") }}:
                </span>
                <span class="text-primary">{{ item.unencryptSecret }}</span><br>
                <span
                  class="label"
                >{{ $t("soi.obtainDeveloperToken.grantTypes") }}:
                </span>
                <span class="text-primary">{{ item.authorizedGrantTypes }}</span><br>
                <span
                  class="label"
                >{{ $t("soi.obtainDeveloperToken.redirectUri") }}:
                </span>
                <span class="text-primary">{{ item.webServerRedirectUri }}</span><br>
                <span
                  class="label"
                >{{ $t("soi.obtainDeveloperToken.status") }}:
                </span>
                <strong class="text-primary">
                  {{
                    item.archived
                      ? $t("soi.obtainDeveloperToken.cancelled")
                      : $t("soi.obtainDeveloperToken.inEffect")
                  }} </strong><br>
                <span
                  class="label"
                >{{ $t("soi.obtainDeveloperToken.developerToken") }}:
                </span>
                <strong class="text-primary">{{ item.code }}</strong><br>
              </el-col>
              <el-col v-if="!item.archived" :span="4">
                <el-button
                  size="small"
                  style="width: 120px"
                  type="primary"
                  @click="testClient(item)"
                >{{ $t("soi.obtainDeveloperToken.test") }}</el-button>
                <br>
                <br>
                <el-button
                  size="small"
                  style="width: 120px"
                  type="primary"
                  @click="refreshToken(item)"
                >{{ $t("soi.obtainDeveloperToken.generateToken") }}</el-button>
                <br>
                <br>
                <el-button
                  size="small"
                  style="width: 120px"
                  type="primary"
                  @click="editClient(item)"
                >{{ $t("soi.common.edit") }}</el-button>
                <br>
                <br>
                <el-button
                  size="small"
                  style="width: 120px"
                  type="primary"
                  @click="archiveClient(item)"
                >{{ $t("soi.obtainDeveloperToken.archive") }}</el-button>
              </el-col>
            </el-row>
            <el-divider />
          </li>
        </ul>
      </div>
    </customize-card>

    <el-dialog
      width="50%"
      :title="
        isUpdate
          ? $t('soi.obtainDeveloperToken.editClient')
          : $t('soi.obtainDeveloperToken.addClient')
      "
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="addClientVisible"
      :destroy-on-close="true"
    >
      <el-alert
        :title="$t('soi.obtainDeveloperToken.addClientTip')"
        type="success"
        style="margin-bottom: 20px"
        :closable="false"
      />
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item
          :label="$t('soi.obtainDeveloperToken.clientId')"
          prop="clientId"
        >
          <el-input
            v-model="form.clientId"
            type="text"
            :placeholder="$t('soi.obtainDeveloperToken.clientIdTip')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('soi.obtainDeveloperToken.secret')"
          prop="clientSecret"
        >
          <el-input
            v-model="form.clientSecret"
            type="text"
            :placeholder="$t('soi.obtainDeveloperToken.secretTip')"
          />
        </el-form-item>
        <el-form-item
          :label="$t('soi.obtainDeveloperToken.grantTypes')"
          prop="authorizedGrantTypes"
        >
          <el-checkbox-group
            v-model="form.authorizedGrantTypes"
            @change="refreshForm"
          >
            <el-checkbox label="authorization_code" />
            <el-checkbox label="password" />
            <el-checkbox label="client_credentials" disabled />
            <el-checkbox label="refresh_token" />
          </el-checkbox-group>
          <p>{{ $t("soi.obtainDeveloperToken.authorizedGrantTypesTip") }}</p>
        </el-form-item>
        <el-form-item
          v-if="
            form.authorizedGrantTypes.includes('authorization_code') ||
              form.authorizedGrantTypes.includes('implicit')
          "
          :label="$t('soi.obtainDeveloperToken.redirectUri')"
          prop="webServerRedirectUri"
        >
          <el-input
            v-model="form.webServerRedirectUri"
            type="text"
            :placeholder="
              $t('soi.obtainDeveloperToken.webServerRedirectUriTip')
            "
          />
        </el-form-item>
        <el-form-item
          v-if="form.authorizedGrantTypes.includes('authorization_code')"
          :label="$t('soi.obtainDeveloperToken.trusted')"
          prop="trusted"
        >
          <el-select v-model="form.trusted">
            <el-option label="Yes" :value="true" />
            <el-option label="No" :value="false" />
          </el-select>
          <p>{{ $t("soi.obtainDeveloperToken.trustedTip") }}</p>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button size="small" @click="addClientVisible = false">
          {{ $t("soi.common.cancel") }}
        </el-button>
        <el-button size="small" type="primary" @click="createClient()">
          {{ $t("soi.obtainDeveloperToken.createNow") }}
        </el-button>
      </div>
    </el-dialog>

    <el-drawer
      :title="$t('soi.obtainDeveloperToken.test')"
      :visible.sync="testVisible"
      direction="rtl"
      :wrapper-closable="false"
    >
      <el-alert
        :title="$t('soi.obtainDeveloperToken.testTip')"
        type="success"
        effect="dark"
      />

      <div>
        <div
          v-if="
            testForm.authorizedGrantTypes.includes('authorization_code') > -1
          "
          class="panel panel-default"
        >
          <div class="panel-heading">
            {{ testForm.clientId }} [authorization_code]
          </div>
          <div class="panel-body">
            <p class="text-muted text-small">
              {{ $t("soi.obtainDeveloperToken.authorizationCodeTip") }}
            </p>
            <ol>
              <li>
                <p style="line-height: 2">
                  <span>{{
                    $t("soi.obtainDeveloperToken.authorizationCodeTip1")
                  }}</span>
                  <br>
                  <span>{{ $t("soi.obtainDeveloperToken.redirectUri") }}:
                  </span>
                  <el-input
                    v-model="testForm.webServerRedirectUri"
                    type="text"
                    size="70"
                    required="required"
                    class="ng-pristine ng-valid ng-valid-required"
                  />
                </p>
                <form
                  :action="authorization_code_url"
                  method="post"
                  target="_blank"
                  class="ng-pristine ng-valid"
                >
                  <button
                    class="btn btn-link ng-binding text-primary"
                    type="submit"
                  >
                    {{ authorization_code_url }}
                  </button>
                  <el-tag type="success">GET</el-tag>
                </form>
              </li>
              <li>
                <p style="line-height: 2">
                  <span>{{
                    $t("soi.obtainDeveloperToken.authorizationCodeTip2")
                  }}</span>
                  <br>
                  <span>{{ $t("soi.obtainDeveloperToken.authorizationCodeTip3") }}:
                  </span>
                  <el-input
                    v-model="code"
                    type="text"
                    name="code"
                    required="required"
                    class="ng-pristine ng-invalid ng-invalid-required"
                  />
                </p>

                <form
                  :action="
                    authorization_code_token_url1 +
                      code +
                      authorization_code_token_url2
                  "
                  method="post"
                  target="_blank"
                  class="ng-pristine ng-valid"
                >
                  <button
                    class="btn btn-link ng-binding text-primary"
                    type="submit"
                  >
                    {{
                      authorization_code_token_url1 +
                        code +
                        authorization_code_token_url2
                    }}
                  </button>
                  <el-tag type="warning">POST</el-tag>
                </form>
              </li>
            </ol>
          </div>
        </div>
        <div
          v-if="testForm.authorizedGrantTypes.includes('password') > -1"
          class="panel panel-default"
        >
          <div class="panel-heading">{{ testForm.clientId }} [password]</div>
          <div class="panel-body">
            <p class="text-muted text-small">
              {{ $t("soi.obtainDeveloperToken.passwordTip") }}
            </p>
            <p>
              <span
                style="line-height: 2"
              >{{ $t("soi.obtainDeveloperToken.email") }}:</span>
              <el-input
                v-model="username"
                type="text"
                required="required"
                class="ng-pristine ng-valid ng-valid-required"
              />
              <br>
              <span
                style="line-height: 2"
              >{{ $t("soi.obtainDeveloperToken.password") }}:</span>
              <el-input
                v-model="password"
                type="text"
                required="required"
                class="ng-pristine ng-valid ng-valid-required"
              />
            </p>

            <form
              :action="password_url + username + '&password=' + password"
              method="post"
              target="_blank"
              class="ng-pristine ng-valid"
            >
              <button
                class="btn btn-link ng-binding text-primary"
                type="submit"
              >
                {{ password_url + username + "&password=" + password }}
              </button>
              <el-tag type="warning">POST</el-tag>
            </form>
          </div>
        </div>
        <div
          v-if="
            testForm.authorizedGrantTypes.includes('client_credentials') > -1
          "
          class="panel panel-default"
        >
          <div class="panel-heading">
            {{ testForm.clientId }} [client_credentials]
          </div>
          <div class="panel-body">
            <p class="text-muted text-small">
              {{ $t("soi.obtainDeveloperToken.clientCredentialsTip") }}
            </p>

            <form
              :action="client_credentials_url"
              method="post"
              target="_blank"
              class="ng-pristine ng-valid"
            >
              <button
                class="btn btn-link ng-binding text-primary"
                type="submit"
              >
                {{ client_credentials_url }}
              </button>
              <el-tag type="warning">POST</el-tag>
            </form>
          </div>
        </div>
        <div
          v-if="testForm.authorizedGrantTypes.includes('refresh_token') > -1"
          class="panel panel-default"
        >
          <div class="panel-heading">
            {{ testForm.clientId }} [refresh_token]
          </div>
          <div class="panel-body">
            <p class="text-muted text-small">
              {{ $t("soi.obtainDeveloperToken.refreshTokenTip") }}
            </p>
            <p>
              <span style="line-height: 2">refresh_token:</span>
              <el-input
                v-model="refreshTokenData"
                type="text"
                required="required"
                size="70"
                class="ng-pristine ng-valid ng-valid-required"
              />
            </p>

            <form
              :action="refreshTokenData_url + refreshTokenData"
              method="post"
              target="_blank"
              class="ng-pristine ng-valid"
            >
              <button
                class="btn btn-link ng-binding text-primary"
                type="submit"
              >
                {{ refreshTokenData_url + refreshTokenData }}
              </button>
              <el-tag type="warning">POST</el-tag>
            </form>
          </div>
        </div>
        <div class="panel panel-default">
          <div class="panel-heading">Verify [access_token]</div>
          <div class="panel-body">
            <p class="text-muted text-small">
              {{ $t("soi.obtainDeveloperToken.accessTokenTip") }}
            </p>
            <p>
              <span style="line-height: 2">access_token: </span>
              <el-input
                v-model="accessToken"
                type="text"
                required="required"
                size="70"
                placeholder="access_token"
                class="ng-pristine ng-valid ng-valid-required"
              />
            </p>

            <form
              :action="accessToken_url1 + accessToken + accessToken_url2"
              method="post"
              target="_blank"
              class="ng-pristine ng-valid"
            >
              <button
                class="btn btn-link ng-binding text-primary"
                type="submit"
              >
                {{ accessToken_url1 + accessToken + accessToken_url2 }}
              </button>
              <el-tag type="warning">POST</el-tag>
            </form>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { archiveClient, clientCredentials, getUserDeveloperToken, registerClient } from '@/api/practice/developerToken'
import { mapGetters } from 'vuex'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export default {
  name: 'ApiObtainDeveloperToken',
  components: { CustomizeCard },
  data() {
    const validatesecret = async(rule, value, callback) => {
      if (value.length < 8 || !value) {
        callback(new Error(this.$t('soi.obtainDeveloperToken.secretTip')))
      } else {
        callback()
      }
    }
    const validateclientid = async(rule, value, callback) => {
      if (value.length < 5 || !value) {
        callback(new Error(this.$t('soi.obtainDeveloperToken.clientIdTip')))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      list: [],
      addClientVisible: false,
      isUpdate: false,
      form: {
        scope: 'Free',
        resourceIds: '',
        authorities: '',
        authorizedGrantTypes: ['client_credentials'],
        clientSecret: '',
        trusted: true,
        clientId: '',
        webServerRedirectUri: '',
        accessTokenValidity: '',
        refreshTokenValidity: '',
        additionalInformation: '',
        userId: ''
      },
      testForm: {
        scope: '',
        resourceIds: '',
        authorities: '',
        authorizedGrantTypes: ['client_credentials'],
        secret: '',
        trusted: true,
        clientId: '',
        webServerRedirectUri: '',
        accessTokenValidity: '',
        refreshTokenValidity: '',
        additionalInformation: '',
        userId: ''
      },
      rules: {
        clientId: {
          required: true,
          validator: validateclientid,
          trigger: 'blur'
        },
        clientSecret: {
          required: true,
          validator: validatesecret,
          trigger: 'blur'
        },
        authorizedGrantTypes: {
          required: true,
          message: this.$t('soi.obtainDeveloperToken.authorizedGrantTypesTip'),
          trigger: 'blur'
        },
        webServerRedirectUri: {
          required: true,
          message: this.$t('soi.obtainDeveloperToken.redirectUriIsRequired'),
          trigger: 'blur'
        }
      },
      testVisible: false,
      authorization_code_url: '',
      code: '',
      authorization_code_token_url1: '',
      authorization_code_token_url2: '',
      username: '',
      password: '',
      password_url: '',
      implicit_url: '',
      implicitRedirectUri: '',
      client_credentials_url: '',
      refreshTokenData: '',
      refreshTokenData_url: '',
      accessToken: '',
      accessToken_url1: '',
      accessToken_url2: ''
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.getToken()
  },
  methods: {
    getToken() {
      const vue = this
      vue.loading = true

      getUserDeveloperToken({ userId: this.userDetails.id })
        .then(res => {
          vue.list = []
          res.data.forEach(item => {
            if (item.client.archived) {
              return true
            }
            for (const key in item.client) {
              item[key] = item.client[key]
            }
            vue.list.push(item)
          })
        })
        .finally(() => {
          vue.loading = false
        })
    },
    addClient() {
      this.form = {
        scope: 'Free',
        resourceIds: '',
        authorities: '',
        authorizedGrantTypes: ['client_credentials'],
        clientSecret: '',
        trusted: true,
        clientId: '',
        webServerRedirectUri: '',
        accessTokenValidity: '',
        refreshTokenValidity: '',
        additionalInformation: '',
        userId: this.userDetails.id
      }
      this.isUpdate = false
      this.addClientVisible = true
    },
    refreshForm() {
      this.$forceUpdate()
    },
    createClient() {
      const vue = this
      this.$refs.form.validate(valid => {
        if (valid) {
          vue.loading = true

          const requestData = JSON.parse(JSON.stringify(vue.form))
          requestData.authorizedGrantTypes = requestData.authorizedGrantTypes.join(
            ','
          )
          registerClient(requestData)
            .then(() => {
              vue.$alert(
                vue.$t('soi.obtainDeveloperToken.createSuccess'),
                vue.$t('soi.common.tip'),
                {
                  confirmButtonText: vue.$t('soi.common.confirm'),
                  callback: () => {
                    vue.addClientVisible = false
                    vue.getToken()
                  }
                }
              )
            })
            .finally(() => {
              vue.loading = false
            })
        }
      })
    },
    testClient(row) {
      this.testVisible = true
      this.testForm = row

      this.authorization_code_url =
        SOI_PLUS_BUSINESS_API_URL +
        `/oauth/authorize?client_id=${row.clientId}&redirect_uri=${
          row.webServerRedirectUri
        }&response_type=code&scope=${row.scope.replaceAll(',', ' ')}`

      this.authorization_code_token_url1 =
        SOI_PLUS_BUSINESS_API_URL +
        `/oauth2/authorization_code?client_id=${row.clientId}&client_secret=${row.unencryptSecret}&grant_type=authorization_code&code=`
      this.authorization_code_token_url2 = `&redirect_uri=${row.webServerRedirectUri}`

      this.password_url =
        SOI_PLUS_BUSINESS_API_URL +
        `/oauth2/password?client_id=${row.clientId}&client_secret=${
          row.unencryptSecret
        }&grant_type=password&scope=${row.scope.replaceAll(
          ',',
          ' '
        )}&username=`

      this.implicitRedirectUri = row.webServerRedirectUri
      this.implicit_url =
        SOI_PLUS_BUSINESS_API_URL +
        `/oauth2/implicit?client_id=${
          row.clientId
        }&response_type=token&scope=${row.scope.replaceAll(
          ',',
          ' '
        )}&redirect_uri=`

      this.client_credentials_url =
        SOI_PLUS_BUSINESS_API_URL +
        `/oauth2/client_credentials?client_id=${row.clientId}&client_secret=${
          row.unencryptSecret
        }&grant_type=client_credentials&scope=${row.scope.replaceAll(
          ',',
          ' '
        )}`

      this.refreshTokenData_url =
        SOI_PLUS_BUSINESS_API_URL +
        `/oauth2/refresh_token?client_id=${row.clientId}&client_secret=${row.unencryptSecret}&grant_type=refresh_token&refresh_token=`

      this.accessToken_url1 =
        SOI_PLUS_BUSINESS_API_URL + `/oauth2/verify_token?token=`
      this.accessToken_url2 = `&client_id=${row.clientId}`
    },
    refreshToken(row) {
      const vue = this
      vue.loading = true
      clientCredentials({
        client_id: row.clientId,
        client_secret: row.unencryptSecret,
        grant_type: 'client_credentials',
        scope: 'Free'
      })
        .then(() => {
          vue.$alert(
            vue.$t('soi.obtainDeveloperToken.generateDeveloperTokenSuccess'),
            vue.$t('soi.common.tip'),
            {
              confirmButtonText: vue.$t('soi.common.confirm'),
              callback: () => {
                vue.getToken()
              }
            }
          )
        })
        .finally(() => {
          vue.loading = false
        })
    },
    editClient(row) {
      const vue = this
      if (vue.$refs.form) vue.$refs.form.resetFields()

      vue.form = JSON.parse(JSON.stringify(row))
      vue.form.clientSecret = vue.form.unencryptSecret
      vue.form.scope = 'Free'
      vue.form.authorizedGrantTypes = vue.form.authorizedGrantTypes.split(',')
      this.isUpdate = true
      vue.addClientVisible = true
    },
    archiveClient(row) {
      const vue = this
      vue.loading = true
      archiveClient(row.clientId)
        .then(() => {
          vue.getToken()
        })
        .finally(() => {
          vue.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.technological-development-api-obtain-developer-token-container {
  .text-center {
    text-align: center;
  }

  ul {
    list-style-type: none;
  }

  .panel-default > .panel-heading {
    background-color: transparent;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    color: #333;
    padding: 10px 15px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }

  .panel-default > .panel-body {
    padding: 0 20px;
  }

  .text-muted {
    color: #6c757d;
  }

  .text-small {
    font-size: 14px;
  }

  .panel-default {
    button {
      background-color: transparent;
      border: 1px solid transparent;
      cursor: pointer;
    }
  }

  .btn-link:hover {
    text-decoration: underline;
  }
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
.chart-wrapper {
  background: #fff;
  padding: 16px 16px 0;
  margin-bottom: 32px;
}
.inner-left {
  margin: 20px;
  line-height: 10px;
}
</style>
<style>
.el-card__body {
  padding: 10px;
}
.worksheet .clearfix .el-button--primary {
  color: #109eae;
  background-color: #fff;
  border-color: #fff;
}
.el-drawer__body {
  height: 80vh;
  overflow-y: auto;
}
.el-drawer__body button,
.el-drawer__body a {
  white-space: initial;
  text-align: left;
  word-break: break-all;
}
.el-select__tags {
  top: 35%;
}
.panel-default {
  padding: 10px;
}
</style>
