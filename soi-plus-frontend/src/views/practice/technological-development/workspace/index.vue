<template>
  <div v-loading.fullscreen.lock="loading" class="technological-development-workspace-container">
    <customize-card :title="$t('soi.router.workspace')">
      <div v-if="hasCreated">
        <el-table
          :data="list"
          :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
          tooltip-effect="dark"
          stripe
          highlight-current-row
        >
          <el-table-column :label="$t('soi.workspace.workspaceName')" prop="name" min-width="150px" />
          <el-table-column :label="$t('soi.workspace.url')" prop="accessUrl" min-width="250px" />
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" min-width="180px" />
          <el-table-column :label="$t('soi.workspace.runStatus')" prop="status" min-width="120px">
            <template slot-scope="scope">
              <i v-if="scope.row.status === '0'" class="el-icon-circle-close" />
              <i v-if="scope.row.status === '1'" class="el-icon-circle-check" />
              <i v-if="scope.row.status === '2'" class="el-icon-loading" />
              <i v-if="scope.row.status === '3'" class="el-icon-loading" />
              <i v-if="scope.row.status === '4'" class="el-icon-loading" />
              {{ statusMapping[scope.row.status] }}
            </template>
          </el-table-column>
          <el-table-column v-if="list.length > 0 && list[0].status === '0'" :label="$t('soi.workspace.offTime')" prop="shutDownTime" min-width="180px" />
          <el-table-column :label="$t('soi.common.operate')" align="center" min-width="215">
            <template slot-scope="scope">
              <!--              <el-button v-if="scope.row.status === '1'" size="mini" @click="shutdown(scope.row)">{{ $t("soi.common.close") }}</el-button>-->
              <el-button v-if="scope.row.status === '1'" size="mini" @click="entry(scope.row)">{{ $t("soi.workspace.entry") }}</el-button>
              <el-button v-if="scope.row.status === '0'" size="mini" @click="del(scope.row)">{{ $t("soi.common.delete") }}</el-button>
              <el-button v-if="scope.row.status === '0'" size="mini" @click="start(scope.row)">{{ $t("soi.workspace.start") }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else style="width:500px;margin:0 auto;text-align:center;">
        <div>
          <svg-icon icon-class="websites" style="font-size: 200px;" />
          <p>{{ $t('soi.workspace.createWorkspaceTip') }}</p>
        </div>
        <div slot="footer" class="dialog-footer text-center">
          <el-button type="primary" @click="createClient()">{{ $t("soi.workspace.createNow") }}</el-button>
        </div>
      </div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  createWorkspace,
  deleteWorkspace,
  getWorkspace,
  getWorkspaceStatus,
  shutdownWorkspace, startWorkspace
} from '@/api/practice/workspace'
import { mapGetters } from 'vuex'

export default {
  name: 'Workspace',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      list: [],
      hasCreated: false,
      statusMapping: {
        0: this.$t('soi.workspace.shutdown'),
        1: this.$t('soi.workspace.running'),
        2: this.$t('soi.workspace.starting'),
        3: this.$t('soi.workspace.shuttingDown'),
        4: this.$t('soi.workspace.deleting')
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const vue = this
      vue.loading = true

      getWorkspace({ userID: vue.userDetails.email })
        .then((res) => {
          vue.loading = false
          vue.list = [res.data]
          if (res.data.status === '2' || res.data.status === '3' || res.data.status === '4') {
            vue.refreshData(res.data.id)
          }
          vue.hasCreated = true
        })
        .catch((error) => {
          if (error.code) {
            vue.hasCreated = false
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    refreshData(id) {
      const vue = this
      vue.loading = true

      getWorkspaceStatus({ id })
        .then((res) => {
          if (res.data.status === '0' || res.data.status === '1') {
            vue.list[0].stauts = res.data.status
          } else {
            vue.refreshData(id)
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    shutdown(row) {
      const vue = this
      vue.$confirm(vue.$t('soi.workspace.shutDownWorkspaceTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        const requestData = new FormData()
        requestData.append('id', row.containerID)

        shutdownWorkspace(requestData)
          .then(() => {
            vue.init()
          }).catch(() => {
            vue.loading = false
          })
      }).catch(() => {})
    },
    entry(row) {
      window.open(row.accessUrl)
    },
    del(row) {
      const vue = this
      vue.$confirm(vue.$t('soi.workspace.deleteWorkspaceTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        const requestData = new FormData()
        requestData.append('id', row.containerID)
        deleteWorkspace(requestData)
          .then(() => {
            vue.init()
          })
          .finally(() => {
            vue.loading = false
          })
      }).catch(() => {})
    },
    start(row) {
      const vue = this
      vue.loading = true
      const requestData = new FormData()
      requestData.append('id', row.containerID)
      startWorkspace(requestData)
        .then(() => {
          vue.init()
        })
        .finally(() => {
          vue.loading = false
        })
    },
    createClient() {
      const vue = this
      const flag = true
      if (flag) {
        vue.$alert(vue.$t('soi.workspace.noResource'), vue.$t('soi.common.tip'))
        return
      }
      vue.$prompt(vue.$t('soi.workspace.workspaceNameTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        inputPattern: /^[A-Za-z0-9_\-]+$/,
        inputErrorMessage: vue.$t('soi.workspace.workspaceNameErrorTip')
      }).then(({ value }) => {
        vue.loading = true
        const requestData = new FormData()
        requestData.append('userID', vue.userDetails.email)
        requestData.append('name', value)

        createWorkspace(requestData)
          .then(() => {
            vue.init()
          })
          .finally(() => {
            vue.loading = false
          })
      }).catch(() => {})
    }
  }
}
</script>
