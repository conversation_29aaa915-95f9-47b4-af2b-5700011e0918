<template>
  <div id="app">
    <transition name="fade-transform" mode="out-in">
      <router-view />
    </transition>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
/* 防止页面切换时抖动 */
html {
  overflow-x: hidden;
  overflow-y: auto;
}

body {
  width: 100%;
  overflow: auto;
  padding-left: calc(100vw - 100%);
}

/* 修改滚动条样式 */
::-webkit-scrollbar {
  width : 8px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background   : #bbbbbb;
}
::-webkit-scrollbar-track {
  border-radius: 8px;
  background   : #ededed;
}
</style>
