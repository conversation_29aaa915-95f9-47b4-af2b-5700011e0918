<template>
  <el-select v-model="modelValue" v-bind="$attrs" v-on="$listeners">
    <el-option
      v-for="(option, index) in options"
      :key="index"
      :label="$t(option.label)"
      :value="option.value"
    />
  </el-select>
</template>

<script>
export default {
  props: {
    value: {
      required: true,
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    modelValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>
