import Vue from 'vue'
import Router from 'vue-router'
import lbsLayout from '@/views/lbsLayout/Layout.vue'
import lbsLoginLayout from '@/views/lbsLayout/loginLayout/loginLayout.vue'
import CNLBSLoginLayout from '@/views/CNLBS/loginLayout'

Vue.use(Router)

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
const lbsRouter = [
  // HK LBS
  {
    path: '/lbsusercenter',
    redirect: '/lbsusercenter/userinformation',
    component: lbsLayout,
    hidden: true,
    meta: { title: 'userCenter', type: 'HKLBS' },
    children: [
      {
        path: '/lbsusercenter/userinformation',
        hidden: true,
        component: () => import('@/views/lbsLayout/usercenter/userinformation.vue'),
        name: 'usercenter',
        meta: { title: 'userCenter', noCache: true }
      },
      {
        path: '/lbsusercenter/edituserinfo',
        hidden: true,
        component: () => import('@/views/lbsLayout/usercenter/edituserinfo.vue'),
        name: 'usercenter1',
        meta: { title: 'userCenter', noCache: true }
      }
    ]
  },
  {
    path: '/lbsIndex',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/lbsIndex',
    meta: { title: 'customerPortfolio', icon: 'account-box', type: 'HKLBS' },
    children: [
      // lbs Index
      {
        path: '/lbsIndex',
        component: () => import('@/views/lbsLayout/index/Index.vue'),
        name: 'lbsIndex',
        meta: { title: 'customerPortfolio', icon: 'default', affix: true }
      },
      // Account Overview
      {
        path: '/accountoverview',
        component: () => import('@/views/lbsLayout/accountservice/accountoverview.vue'),
        name: 'accountoverview',
        meta: { title: 'accountOverview', icon: 'default', noCache: true }
      }
    ]
  },
  {
    path: '/lbstransaction',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/lbstransaction/transfer',
    meta: { title: 'transaction', icon: 'account-box', type: 'HKLBS' },
    children: [
      {
        path: '/lbstransaction/transfer',
        component: () => import('@/views/lbsLayout/transaction/transfer'),
        name: 'transfer',
        meta: { title: 'transfer', icon: 'default', affix: true }
      },
      // Term Deposit
      {
        path: '/lbsaccountservice/termdeposit',
        component: () => import('@/views/lbsLayout/accountservice/termdeposit'),
        name: 'termdeposit',
        meta: { title: 'termDeposit', icon: 'default' },
        children: [
          // Term Deposit Index
          {
            path: '/lbsaccountservice/termdeposit',
            component: () => import('@/views/lbsLayout/accountservice/termdeposit/index'),
            name: 'termdeposit1',
            alwaysShow: true,
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Deposit Detail
          {
            path: '/lbsaccountservice/termdeposit/depositdetail',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/termdeposit/depositdetail'),
            name: 'termdeposit2',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Term Deposit
          {
            path: '/lbsaccountservice/termdeposit/applytermdeposit',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/termdeposit/applytermdeposit'),
            name: 'termdeposit3',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Term Deposit Info
          {
            path: '/lbsaccountservice/termdeposit/termdepositform',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/termdeposit/termdepositform'),
            name: 'termdeposit4',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Term Deposit Result
          {
            path: '/lbsaccountservice/termdeposit/termdepositresult',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/termdeposit/termdepositresult'),
            name: 'termdeposit5',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // View Deposit
          {
            path: '/lbsaccountservice/termdeposit/viewdeposit',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/termdeposit/viewdeposit'),
            name: 'termdeposit6',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      // Deposit
      {
        path: '/lbsaccountservice/termdeposit/applydeposit',
        component: () => import('@/views/lbsLayout/accountservice/termdeposit/applydeposit'),
        name: 'termdeposit7',
        meta: { title: 'deposit', icon: 'default', breadcrumb: false, noCache: true }
      },
      // Deposit Info
      {
        path: '/lbsaccountservice/termdeposit/depositform',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/termdeposit/depositform'),
        name: 'termdeposit8',
        meta: { title: 'deposit', icon: 'default', breadcrumb: false, noCache: true }
      },
      // Deposit Result
      {
        path: '/lbsaccountservice/termdeposit/depositresult',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/termdeposit/depositresult'),
        name: 'termdeposit9',
        meta: { title: 'deposit', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: '/lbstransaction/withdraw',
        component: () => import('@/views/lbsLayout/transaction/withdraw'),
        name: 'withdraw',
        meta: { title: 'withdraw', icon: 'default', affix: true }
      },
      {
        path: '/lbstransaction/withdrawform',
        hidden: true,
        component: () => import('@/views/lbsLayout/transaction/withdraw/withdrawform'),
        name: 'withdrawform',
        meta: { title: 'withdraw', icon: 'default', affix: true }
      }
    ]
  },
  {
    path: '/lbspayment',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/lbspayment/makepayment',
    meta: { title: 'payment', icon: 'material-payment', type: 'HKLBS' },
    children: [
      {
        path: '/lbspayment/makepayment',
        component: () => import('@/views/lbsLayout/payment/makepayment'),
        name: 'makepayment',
        meta: { title: 'makeAPayment', icon: 'default' },
        children: [
          {
            path: '/lbspayment/makepayment',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/makepayment/index'),
            name: 'index',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbspayment/makepayment/steptwo',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/makepayment/steptwo'),
            name: 'steptwo',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbspayment/makepayment/stepthree',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/makepayment/stepthree'),
            name: 'stepthree',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbspayment/makepayment/stepfour',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/makepayment/stepfour'),
            name: 'stepfour',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbspayment/makepayment/stepform',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/makepayment/stepform'),
            name: 'stepform',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      {
        path: '/lbspayment/payeelist',
        component: () => import('@/views/lbsLayout/payment/payeelist'),
        name: 'payeelist',
        meta: { title: 'payeeList', icon: 'default' },
        children: [
          {
            path: '/lbspayment/payeelist',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/payeelist/mypayee'),
            name: 'mypayee',
            meta: { title: 'payeeList', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbspayment/payeelist/addpayee',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/payeelist/addpayee'),
            name: 'addpayee',
            meta: { title: 'payeeList', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbspayment/payeelist/merchantlist',
            hidden: true,
            component: () => import('@/views/lbsLayout/payment/payeelist/merchantlist'),
            name: 'merchantlist',
            meta: { title: 'payeeList', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      }
    ]
  },
  {
    path: '/lbsinvestment',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/lbsinvestment/stocktrading',
    meta: { title: 'investment', icon: 'awesome-chart-bar', type: 'HKLBS' },
    children: [
      {
        path: '/lbsinvestment/stocktrading',
        component: () => import('@/views/lbsLayout/investment/stocktrading/index'),
        name: 'stocktrading1',
        meta: { title: 'stockTrading', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: '/lbsinvestment/stocktrading/stockportfolio',
        component: () => import('@/views/lbsLayout/investment/stocktrading/stockportfolio'),
        name: 'stocktrading2',
        hidden: true,
        meta: { title: 'stockTrading', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: '/lbsinvestment/stocktrading/trade',
        component: () => import('@/views/lbsLayout/investment/stocktrading/trade'),
        name: 'trade',
        hidden: true,
        meta: { title: 'stockTrading', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: '/lbsinvestment/stocktrading/stockholding',
        component: () => import('@/views/lbsLayout/investment/stocktrading/stockholding'),
        name: 'stockholding',
        hidden: true,
        meta: { title: 'stockHolding', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: '/lbsinvestment/stocktrading/stockaccount',
        component: () => import('@/views/lbsLayout/investment/stocktrading/stockaccount'),
        name: 'stockaccount',
        hidden: true,
        meta: { title: 'stockHolding', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: '/lbsinvestment/stocktrading/transactionhistory',
        component: () => import('@/views/lbsLayout/investment/stocktrading/TransactionHistory'),
        name: 'TransactionHistory',
        hidden: true,
        meta: { title: 'transactionHistory', icon: 'default', breadcrumb: false, noCache: true }
      },
      // Foreign Exchange
      {
        path: '/lbsinvestment/foreignexchange',
        component: () => import('@/views/lbsLayout/investment/foreignexchange'),
        name: 'foreignexchange',
        meta: { title: 'foreignExchange', icon: 'default' },
        children: [
          {
            path: '/lbsinvestment/foreignexchange',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/foreignexchange/index'),
            name: 'foreignexchangeindex',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'transaction',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/foreignexchange/transaction'),
            name: 'foreignexchangetransaction',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'summary',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/foreignexchange/summary'),
            name: 'foreignexchangesummary',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'mycurrency',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/foreignexchange/mycurrency'),
            name: 'foreignexchangemycurrency',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'transactionhistory',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/foreignexchange/TransactionHistory'),
            name: 'TransactionHistory1',
            meta: { title: 'transactionHistory', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      {
        path: '/lbsinvestment/fundtrading',
        component: () => import('@/views/lbsLayout/investment/fundtrading'),
        name: 'FundTrading',
        meta: { title: 'fundTrading', icon: 'default' },
        children: [
          {
            path: '/lbsinvestment/fundtrading',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/fundtrading/index'),
            name: 'FundIndex',
            meta: { title: 'fundTrading', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'detailquotation',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/fundtrading/detailquotation'),
            name: 'DetailQuotation',
            meta: { title: 'fundTrading', icon: 'default', breadcrumb: false, noCache: true }
          },

          {
            path: 'fundlist',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/fundtrading/fundlist'),
            name: 'FundList',
            meta: { title: 'fundList', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'fundorder',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/fundtrading/fundorder'),
            name: 'FundOrder',
            meta: { title: 'fundOrder', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'fundorderdetail',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/fundtrading/fundorderdetail'),
            name: 'FundOrderDetail',
            meta: { title: 'fundOrder', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'transactionhistory',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/fundtrading/TransactionHistory'),
            name: 'TransactionHistory2',
            meta: { title: 'transactionHistory', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'fundaccount',
            hidden: true,
            component: () => import('@/views/lbsLayout/investment/fundtrading/FundAccount'),
            name: 'FundAccount',
            meta: { title: 'fundAccount', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      }
    ]
  },
  {
    path: '/lbsinsurance',
    component: lbsLayout,
    children: [
      {
        path: '/lbsinsurance',
        component: () => import('@/views/lbsLayout/insurance/insurance'),
        name: 'insurance',
        meta: { title: 'insurance', icon: 'map-insurance-agency', type: 'HKLBS' }
      },
      {
        path: 'getaquote',
        hidden: true,
        component: () => import('@/views/lbsLayout/insurance/getaquote'),
        name: 'Insurance1',
        meta: { title: 'insurance', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'personalinfo',
        hidden: true,
        component: () => import('@/views/lbsLayout/insurance/personalinfo'),
        name: 'PersonalInfo',
        meta: { title: 'insurance', icon: 'default', breadcrumb: false, noCache: true }
      },

      {
        path: 'payment',
        hidden: true,
        component: () => import('@/views/lbsLayout/insurance/payment'),
        name: 'Payment',
        meta: { title: 'insurance', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'finish',
        hidden: true,
        component: () => import('@/views/lbsLayout/insurance/finish'),
        name: 'Finish',
        meta: { title: 'insurance', icon: 'default', breadcrumb: false, noCache: true }
      }
    ]
  },
  {
    path: '/lbsmortgage',
    component: lbsLayout,
    meta: { type: 'HKLBS' },
    children: [
      {
        path: '/lbsmortgage',
        component: () => import('@/views/lbsLayout/mortgage/mortgage'),
        name: 'mortgage',
        meta: { title: 'mortgage', icon: 'awesome-file-invoice-dollar' }
      },
      {
        path: 'mortgagecalculator',
        hidden: true,
        component: () => import('@/views/lbsLayout/mortgage/mortgagecalculator'),
        name: 'mortgagecalculator',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'mortgagevaluation',
        hidden: true,
        component: () => import('@/views/lbsLayout/mortgage/mortgagevaluation'),
        name: 'mortgagevaluation',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'applymortgage',
        hidden: true,
        component: () => import('@/views/lbsLayout/mortgage/applymortgage'),
        name: 'applymortgage',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'finishmortgage',
        hidden: true,
        component: () => import('@/views/lbsLayout/mortgage/finishmortgage'),
        name: 'finishmortgage',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'repaymentplan',
        hidden: true,
        component: () => import('@/views/lbsLayout/mortgage/repaymentplan'),
        name: 'repaymentplan',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      }
    ]
  },
  {
    path: '/lbsservicerequest',
    component: lbsLayout,
    hidden: true,
    meta: { title: 'serviceRequest', icon: 'awesome-servicestack', type: 'HKLBS' },
    children: [
      {
        path: '/lbsaccountservice/accountdetail',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/accountdetail'),
        name: 'accountdetail',
        meta: { title: 'accountDetail', icon: 'default', noCache: true }
      },
      // Credit Card Detail
      {
        path: '/lbsaccountservice/creditcarddetail',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/creditcarddetail'),
        name: 'creditcarddetail',
        meta: { title: 'creditCard', icon: 'default', noCache: true }
      },
      // Change Credit Limit
      {
        path: '/lbsaccountservice/changecreditlimit',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/changecreditlimit'),
        name: 'changecreditlimit',
        meta: { title: 'changeCreditLimit', icon: 'default' },
        children: [
          // Index
          {
            path: '/lbsaccountservice/changecreditlimit',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/changecreditlimit/index'),
            name: 'changecreditlimit1',
            meta: { title: 'changeCreditLimit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Select Increase Or Decrease
          {
            path: '/lbsaccountservice/changecreditlimit/selectchangetype',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/changecreditlimit/selectchangetype'),
            name: 'selectchangetype',
            meta: { title: 'changeCreditLimit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Increase Limit Info
          {
            path: '/lbsaccountservice/changecreditlimit/increaselimitform',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/changecreditlimit/increaselimitform'),
            name: 'increaselimitform',
            meta: { title: 'changeCreditLimit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Decrease Limit Info
          {
            path: '/lbsaccountservice/changecreditlimit/decreaselimitform',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/changecreditlimit/decreaselimitform'),
            name: 'decreaselimitform',
            meta: { title: 'changeCreditLimit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Change Limit Result
          {
            path: '/lbsaccountservice/changecreditlimit/changeresult',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/changecreditlimit/changeresult'),
            name: 'changeresult',
            meta: { title: 'changeCreditLimit', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      // Credit Card Cancellation
      {
        path: '/lbsaccountservice/creditcardcancellation',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/creditcardcancellation'),
        name: 'creditcardcancellation',
        meta: { title: 'creditCardCancellation', icon: 'default' },
        children: [
          // Index
          {
            path: '/lbsaccountservice/creditcardcancellation',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardcancellation/index'),
            name: 'creditcardcancellation1',
            meta: { title: 'creditCardCancellation', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Why Cancellation
          {
            path: '/lbsaccountservice/creditcardcancellation/cancellationinfo',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardcancellation/cancellationinfo'),
            name: 'cancellationinfo',
            meta: { title: 'creditCardCancellation', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/creditcardcancellation/cancellationform',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardcancellation/cancellationform'),
            name: 'cancellationform',
            meta: { title: 'creditCardCancellation', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/creditcardcancellation/cancellationresult',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardcancellation/cancellationresult'),
            name: 'cancellationresult',
            meta: { title: 'creditCardCancellation', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      // Credit Card Loss Reporting
      {
        path: '/lbsaccountservice/cardlossreporting',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/cardlossreporting'),
        name: 'cardlossreporting',
        meta: { title: 'cardLossReporting', icon: 'default' },
        children: [
          {
            path: '/lbsaccountservice/cardlossreporting',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardloss/index'),
            name: 'cardlossreporting1',
            meta: { title: 'cardLossReporting', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/cardlossreporting/lossinfo',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardloss/lossinfo'),
            name: 'lossinfo',
            meta: { title: 'cardLossReporting', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/cardlossreporting/lossform',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardloss/lossform'),
            name: 'lossform',
            meta: { title: 'cardLossReporting', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/cardlossreporting/lossresult',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/creditcardloss/lossresult'),
            name: 'lossresult',
            meta: { title: 'cardLossReporting', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      // Credit Card Repayment
      {
        path: '/lbsaccountservice/repayment',
        hidden: true,
        component: () => import('@/views/lbsLayout/accountservice/makepayment'),
        name: 'repayment',
        meta: { title: 'repayment', icon: 'default' },
        children: [
          {
            path: '/lbsaccountservice/repayment',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/makepayment/index'),
            name: 'repaymentindex',
            meta: { title: 'repayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/repayment/steptwo',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/makepayment/steptwo'),
            name: 'repaymentsteptwo',
            meta: { title: 'repayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/repayment/stepthree',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/makepayment/stepthree'),
            name: 'repaymentstepthree',
            meta: { title: 'repayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/repayment/stepfour',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/makepayment/stepfour'),
            name: 'repaymentstepfour',
            meta: { title: 'repayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/lbsaccountservice/repayment/paymentform',
            hidden: true,
            component: () => import('@/views/lbsLayout/accountservice/makepayment/paymentform'),
            name: 'repaymentform',
            meta: { title: 'repayment', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      {
        path: '/lbschequebookrequest',
        hidden: true,
        component: () => import('@/views/lbsLayout/servicerequest/chequebookrequest'),
        name: 'Chequebookrequest',
        meta: { title: 'chequeBookRequest', icon: 'default' }
      },
      {
        path: '/lbsstatementrequest',
        hidden: true,
        component: () => import('@/views/lbsLayout/servicerequest/statementrequest'),
        name: 'Statementrequest',
        meta: { title: 'statementRequest', icon: 'default' }
      }
    ]
  },
  {
    path: '/lbscrossboardpayment',
    component: lbsLayout,
    meta: { type: 'HKLBS' },
    children: [
      {
        path: '/lbscrossboardpayment',
        component: () => import('@/views/lbsLayout/crossboardpayment/index'),
        name: 'cross-board-payment',
        meta: { title: 'crossBoardPayment', icon: 'awesome-file-invoice-dollar' }
      }
    ]
  },

  // CN LBS
  {
    path: '/cn/lbs/cross/board/payment',
    component: lbsLayout,
    meta: { title: 'crossBoardPayment', icon: 'awesome-file-invoice-dollar', type: 'CNLBS' },
    children: [
      {
        path: '/cn/lbs/cross/board/payment',
        component: () => import('@/views/CNLBS/crossboardpayment/index'),
        name: 'cross-board-payment',
        meta: { title: 'crossBoardPayment', icon: 'awesome-file-invoice-dollar' }
      }
    ]
  },

  // -------------------------------------------------------------------

  // Teller
  {
    path: '/tellerlbscustomeroverview',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/tellerlbscustomeroverview/checkcustomer',
    meta: {
      title: 'customerOverview',
      icon: 'workflow',
      resource: 'business:customer-overview'
    },
    children: [
      {
        path: 'checkcustomer',
        name: 'checkcustomer',
        meta: { title: 'checkCustomer', icon: 'workflow', noCache: true, resource: 'business:customer-overview:check-customer' },
        component: () => import('@/views/lbsLayout/staff/checkcustomer')
      },
      {
        path: 'workflowhistory',
        name: 'workflowhistory',
        meta: { title: 'workflowHistory', icon: 'workflow', noCache: true, resource: 'business:customer-overview:workflow' },
        component: () => import('@/views/lbsLayout/workflow/workflow')
      }
    ]
  },
  {
    path: '/tellerlbstransaction',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/tellerlbstransaction/transfer',
    meta: { title: 'transaction', icon: 'account-box', resource: 'business:transaction' },
    children: [
      {
        path: '/tellerlbstransaction/transfer',
        component: () => import('@/views/lbsLayout/staff/transaction/transfer'),
        name: 'transfer',
        meta: { title: 'transfer', icon: 'default', affix: true, resource: 'business:transaction:transfer' }
      },
      // Term Deposit
      {
        path: '/tellerlbsaccountservice/termdeposit',
        component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit'),
        name: 'termdeposit',
        meta: { title: 'termDeposit', icon: 'default', resource: 'business:transaction:term-deposit' },
        children: [
          // Term Deposit Index
          {
            path: '/tellerlbsaccountservice/termdeposit',
            component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/index'),
            name: 'termdeposit1',
            alwaysShow: true,
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Deposit Detail
          {
            path: '/tellerlbsaccountservice/termdeposit/depositdetail',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/depositdetail'),
            name: 'termdeposit2',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Term Deposit
          {
            path: '/tellerlbsaccountservice/termdeposit/applytermdeposit',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/applytermdeposit'),
            name: 'termdeposit3',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Term Deposit Info
          {
            path: '/tellerlbsaccountservice/termdeposit/termdepositform',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/termdepositform'),
            name: 'termdeposit4',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // Term Deposit Result
          {
            path: '/tellerlbsaccountservice/termdeposit/termdepositresult',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/termdepositresult'),
            name: 'termdeposit5',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          },
          // View Deposit
          {
            path: '/tellerlbsaccountservice/termdeposit/viewdeposit',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/viewdeposit'),
            name: 'termdeposit6',
            meta: { title: 'termDeposit', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      // Deposit
      {
        path: '/tellerlbsaccountservice/termdeposit/applydeposit',
        component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/applydeposit'),
        name: 'termdeposit7',
        meta: { title: 'deposit', icon: 'default', breadcrumb: false, noCache: true, resource: 'business:transaction:deposit' }
      },
      // Deposit Info
      {
        path: '/tellerlbsaccountservice/termdeposit/depositform',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/depositform'),
        name: 'termdeposit8',
        meta: { title: 'deposit', icon: 'default', breadcrumb: false, noCache: true }
      },
      // Deposit Result
      {
        path: '/tellerlbsaccountservice/termdeposit/depositresult',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/accountservice/termdeposit/depositresult'),
        name: 'termdeposit9',
        meta: { title: 'deposit', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: '/tellerlbstransaction/withdraw',
        component: () => import('@/views/lbsLayout/staff/transaction/withdraw'),
        name: 'withdraw',
        meta: { title: 'withdraw', icon: 'default', affix: true, resource: 'business:transaction:withdraw' }
      },
      {
        path: '/tellerlbstransaction/withdrawform',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/transaction/withdraw/withdrawform'),
        name: 'withdrawform',
        meta: { title: 'withdraw', icon: 'default', affix: true }
      }
    ]
  },
  {
    path: '/tellerlbspayment',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/tellerlbspayment/makepayment',
    meta: { title: 'payment', icon: 'material-payment', resource: 'business:payment' },
    children: [
      {
        path: '/tellerlbspayment/makepayment',
        component: () => import('@/views/lbsLayout/staff/payment/makepayment'),
        name: 'makepayment',
        meta: { title: 'makeAPayment', icon: 'default', resource: 'business:payment:make' },
        children: [
          {
            path: '/tellerlbspayment/makepayment',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/makepayment/index'),
            name: 'index',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbspayment/makepayment/steptwo',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/makepayment/steptwo'),
            name: 'steptwo',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbspayment/makepayment/stepthree',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/makepayment/stepthree'),
            name: 'stepthree',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbspayment/makepayment/stepfour',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/makepayment/stepfour'),
            name: 'stepfour',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbspayment/makepayment/stepform',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/makepayment/stepform'),
            name: 'stepform',
            meta: { title: 'makeAPayment', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      {
        path: '/tellerlbspayment/payeelist',
        component: () => import('@/views/lbsLayout/staff/payment/payeelist'),
        name: 'payeelist',
        meta: { title: 'payeeList', icon: 'default', resource: 'business:payment:payee' },
        children: [
          {
            path: '/tellerlbspayment/payeelist',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/payeelist/mypayee'),
            name: 'mypayee',
            meta: { title: 'payeeList', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbspayment/payeelist/addpayee',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/payeelist/addpayee'),
            name: 'addpayee',
            meta: { title: 'payeeList', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbspayment/payeelist/merchantlist',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/payment/payeelist/merchantlist'),
            name: 'merchantlist',
            meta: { title: 'payeeList', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      }
    ]
  },
  {
    path: '/tellerlbsinvestment',
    component: lbsLayout,
    alwaysShow: true,
    meta: { title: 'investment', icon: 'awesome-chart-bar', resource: 'business:investment' },
    children: [
      {
        path: '/tellerlbsinvestment/stocktrading',
        component: () => import('@/views/lbsLayout/staff/investment/stocktrading'),
        name: 'stocktrading',
        meta: { title: 'stockTrading', icon: 'default', resource: 'business:investment:stock' },
        children: [
          {
            path: '/tellerlbsinvestment/stocktrading',
            component: () => import('@/views/lbsLayout/staff/investment/stocktrading/index'),
            name: 'stocktrading1',
            meta: { title: 'stockTrading', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbsinvestment/stocktrading/stockportfolio',
            component: () => import('@/views/lbsLayout/staff/investment/stocktrading/stockportfolio'),
            name: 'stocktrading2',
            meta: { title: 'stockTrading', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbsinvestment/stocktrading/trade',
            component: () => import('@/views/lbsLayout/staff/investment/stocktrading/trade'),
            name: 'trade',
            meta: { title: 'stockTrading', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbsinvestment/stocktrading/stockholding',
            component: () => import('@/views/lbsLayout/staff/investment/stocktrading/stockholding'),
            name: 'stockholding',
            meta: { title: 'stockHolding', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbsinvestment/stocktrading/stockaccount',
            component: () => import('@/views/lbsLayout/staff/investment/stocktrading/stockaccount'),
            name: 'stockaccount',
            meta: { title: 'stockHolding', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: '/tellerlbsinvestment/stocktrading/transactionhistory',
            component: () => import('@/views/lbsLayout/staff/investment/stocktrading/TransactionHistory'),
            name: 'TransactionHistory',
            meta: { title: 'transactionHistory', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      // Foreign Exchange
      {
        path: '/tellerlbsinvestment/foreignexchange',
        component: () => import('@/views/lbsLayout/staff/investment/foreignexchange'),
        name: 'foreignexchange',
        meta: { title: 'foreignExchange', icon: 'default', resource: 'business:investment:foreign-exchange' },
        children: [
          {
            path: '/tellerlbsinvestment/foreignexchange',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/foreignexchange/index'),
            name: 'foreignexchangeindex',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'transaction',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/foreignexchange/transaction'),
            name: 'foreignexchangetransaction',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'summary',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/foreignexchange/summary'),
            name: 'foreignexchangesummary',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'mycurrency',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/foreignexchange/mycurrency'),
            name: 'foreignexchangemycurrency',
            meta: { title: 'foreignExchange', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'transactionhistory',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/foreignexchange/TransactionHistory'),
            name: 'TransactionHistory1',
            meta: { title: 'transactionHistory', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      },
      {
        path: '/tellerlbsinvestment/fundtrading',
        component: () => import('@/views/lbsLayout/staff/investment/fundtrading'),
        name: 'FundTrading',
        meta: { title: 'fundTrading', icon: 'default', resource: 'business:investment:fund' },
        children: [
          {
            path: '/tellerlbsinvestment/fundtrading',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/fundtrading/index'),
            name: 'FundIndex',
            meta: { title: 'fundTrading', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'detailquotation',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/fundtrading/detailquotation'),
            name: 'DetailQuotation',
            meta: { title: 'fundTrading', icon: 'default', breadcrumb: false, noCache: true }
          },

          {
            path: 'fundlist',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/fundtrading/fundlist'),
            name: 'FundList',
            meta: { title: 'fundList', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'fundorder',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/fundtrading/fundorder'),
            name: 'FundOrder',
            meta: { title: 'fundOrder', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'fundorderdetail',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/fundtrading/fundorderdetail'),
            name: 'FundOrderDetail',
            meta: { title: 'fundOrder', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'transactionhistory',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/fundtrading/TransactionHistory'),
            name: 'TransactionHistory2',
            meta: { title: 'transactionHistory', icon: 'default', breadcrumb: false, noCache: true }
          },
          {
            path: 'fundaccount',
            hidden: true,
            component: () => import('@/views/lbsLayout/staff/investment/fundtrading/FundAccount'),
            name: 'FundAccount',
            meta: { title: 'fundAccount', icon: 'default', breadcrumb: false, noCache: true }
          }
        ]
      }
    ]
  },
  {
    path: '/tellerlbsmortgage',
    component: lbsLayout,
    meta: { resource: 'business:mortgage' },
    children: [
      {
        path: '/tellerlbsmortgage',
        component: () => import('@/views/lbsLayout/staff/mortgage/mortgage'),
        name: 'mortgage',
        meta: { title: 'mortgage', icon: 'awesome-file-invoice-dollar' }
      },
      {
        path: 'mortgagecalculator',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/mortgage/mortgagecalculator'),
        name: 'mortgagecalculator',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'mortgagevaluation',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/mortgage/mortgagevaluation'),
        name: 'mortgagevaluation',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'applymortgage',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/mortgage/applymortgage'),
        name: 'applymortgage',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'finishmortgage',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/mortgage/finishmortgage'),
        name: 'finishmortgage',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      },
      {
        path: 'repaymentplan',
        hidden: true,
        component: () => import('@/views/lbsLayout/staff/mortgage/repaymentplan'),
        name: 'repaymentplan',
        meta: { title: 'mortgage', icon: 'default', breadcrumb: false, noCache: true }
      }
    ]
  },

  // Operator | Manager
  {
    path: '/system',
    component: lbsLayout,
    alwaysShow: true,
    redirect: '/system/apply',
    meta: { title: 'operate', icon: 'workflow', resource: 'system' },
    children: [
      {
        path: '/system/apply',
        name: 'lbsSystemManager',
        meta: { title: 'systemManager', icon: 'workflow', noCache: true, resource: 'system:parameter-configuration:update-apply-list' },
        component: () => import('@/views/lbsLayout/system/apply')
      },
      {
        path: '/system/approve',
        name: 'lbsOperate',
        meta: { title: 'employee', icon: 'workflow', noCache: true, resource: 'system:parameter-configuration:update-approve-list' },
        component: () => import('@/views/lbsLayout/system/approve')
      },
      {
        path: '/system/staffer',
        name: 'lbsSuperOne',
        meta: { title: 'stafferList', icon: 'workflow', noCache: true, resource: 'system:staffer' },
        component: () => import('@/views/lbsLayout/system/stafferr')
      },
      {
        path: '/system/role',
        name: 'lbsSupersix',
        meta: { title: 'roleManagement', icon: 'workflow', noCache: true, resource: 'system:role' },
        component: () => import('@/views/lbsLayout/system/role')
      },
      {
        path: '/system/permission',
        name: 'lbsPermission',
        meta: { title: 'lbsPermission', icon: 'workflow', noCache: true, resource: 'system:permission' },
        component: () => import('@/views/lbsLayout/system/permissions')
      },
      {
        path: '/system/limit',
        name: 'lbsLimitConfig',
        meta: { title: 'lbsLimitConfig', icon: 'workflow', noCache: true, resource: 'system:limit' },
        component: () => import('@/views/lbsLayout/system/limit')
      },
      {
        path: '/system/configuration',
        name: 'configuration',
        meta: { title: 'configuration', icon: 'workflow', noCache: true, resource: 'system:parameter-configuration' },
        component: () => import('@/views/lbsLayout/system/configuration')
      },
      {
        path: '/system/kyc',
        alwaysShow: true,
        component: () => import('@/views/lbsLayout/system/kyc'),
        redirect: '/system/kyc/blacklist',
        meta: { title: 'kyc', icon: 'workflow', resource: 'system:kyc' },
        children: [
          {
            path: '/system/kyc/blacklist',
            name: 'blacklist',
            meta: { title: 'blacklist', icon: 'workflow', noCache: true, resource: 'system:kyc:blacklist' },
            component: () => import('@/views/lbsLayout/system/kyc/blacklist')
          },
          {
            path: '/system/kyc/sanction-country',
            name: 'sanction-country',
            meta: { title: 'sanctionCountry', icon: 'workflow', noCache: true, resource: 'system:kyc:sanction-country' },
            component: () => import('@/views/lbsLayout/system/kyc/sanction/country')
          },
          {
            path: '/system/kyc/sanction-personal',
            name: 'sanction-personal',
            meta: { title: 'sanctionPersonal', icon: 'workflow', noCache: true, resource: 'system:kyc:sanction-personal' },
            component: () => import('@/views/lbsLayout/system/kyc/sanction/personal')
          },
          {
            path: '/system/kyc/pep',
            name: 'pep',
            meta: { title: 'pep', icon: 'workflow', noCache: true, resource: 'system:kyc:pep' },
            component: () => import('@/views/lbsLayout/system/kyc/pep')
          },
          {
            path: '/system/kyc/facta',
            name: 'blacklist',
            meta: { title: 'facta', icon: 'workflow', noCache: true, resource: 'system:kyc:facta' },
            component: () => import('@/views/lbsLayout/system/kyc/facta')
          },
          {
            path: '/system/kyc/crs',
            name: 'crs',
            meta: { title: 'crs', icon: 'workflow', noCache: true, resource: 'system:kyc:crs' },
            component: () => import('@/views/lbsLayout/system/kyc/crs')
          }
        ]
      }
    ]
  }
]

export const constantRoutes = [
  {
    path: '/',
    redirect: '/lbslogin'
  },
  {
    path: '/lbslogin',
    component: lbsLoginLayout,
    redirect: '/lbslogin',
    hidden: true,
    children: [
      //  LBS Login
      {
        path: '/lbslogin',
        component: () => import('@/views/lbsLayout/login/index.vue'),
        hidden: true,
        meta: { title: 'login' }
      }
    ]
  },
  {
    path: '/cn/lbs/login',
    component: CNLBSLoginLayout,
    redirect: '/cn/lbs/login',
    hidden: true,
    meta: { title: 'login' },
    children: [
      //  CN LBS Login
      {
        path: '/cn/lbs/login',
        component: () => import('@/views/CNLBS/loginLayout/login/index'),
        hidden: true,
        meta: { title: 'login' }
      }
    ]
  },
  {
    path: '/cn/lbs/init',
    component: CNLBSLoginLayout,
    redirect: '/cn/lbs/init',
    hidden: true,
    meta: { title: 'login' },
    children: [
      //  CN LBS Init Data
      {
        path: '/cn/lbs/init',
        component: () => import('@/views/CNLBS/loginLayout/init/index'),
        hidden: true,
        meta: { title: 'login' }
      }
    ]
  },

  ...lbsRouter,

  {
    path: '/404',
    name: '404',
    meta: { title: '404' },
    hidden: true,
    component: () => import('@/views/404.vue')
  },

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  mode: 'history', // require service support
  base: process.env.NODE_ENV === 'development' ? '/' : '/vbsfrontend',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
