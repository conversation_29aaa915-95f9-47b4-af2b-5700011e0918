<template>
  <div v-loading.fullscreen.lock="loading" class="fund-order">
    <el-row>
      <el-col :span="24">
        <div class="text-center">
          <el-select v-model="fundAccount" style="width: 270px" @change="getFundOrderList">
            <el-option v-for="(item,index) in fundAccountOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
          <el-date-picker
            v-model="date"
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            :clearable="false"
            unlink-panels
            :start-placeholder="$t('lbs.FundOrder.StartDate')"
            :end-placeholder="$t('lbs.FundOrder.EndDate')"
            @change="getFundOrderList"
          />
        </div>
        <p class="fund-order-title">{{ $t('lbs.FundOrder.FundOrder') }}</p>
        <el-card>
          <el-table :data="fundOrderList" height="500">
            <el-table-column :label="$t('lbs.FundOrder.Date')" width="150" prop="requestTime" align="center" />
            <el-table-column :label="$t('lbs.FundOrder.FundCode')" prop="fundCode" align="center" />
            <el-table-column :label="$t('lbs.FundOrder.FundName')" prop="fundName" align="center" />
            <el-table-column :label="$t('lbs.FundOrder.TradingOption')" prop="tradingOption" align="center" />
            <el-table-column :label="$t('lbs.FundOrder.TradingAmount')" prop="totalAmount" align="center" />
            <el-table-column :label="$t('lbs.FundOrder.SharingNo')" prop="sharingNo" align="center" />
            <el-table-column :label="$t('lbs.FundOrder.Status')" prop="status" align="center" />
            <el-table-column align="center">
              <template slot-scope="scope">
                <span
                  class="view-more"
                  @click="viewFundOrderDetail(scope.row.id)"
                >{{ $t('lbs.FundOrder.ViewMore') }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    fundAccountOption: [],
    fundAccount: '',
    date: [new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30), new Date().getTime()],
    fundOrderList: []
  }),
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.getFundAccountOption()
    this.getFundOrderList()
  },
  methods: {
    // 查看订单详情
    viewFundOrderDetail(id) {
      this.$router.push({ path: `/cn/lbs/investment/fundtrading/fundorderdetail?id=${id}` })
    },
    // 获取基金订单列表
    getFundOrderList() {
      const _this = this
      if (!_this.fundAccount) return
      _this.loading = true
      const requestData = {
        accountNumber: _this.fundAccount,
        fromdate: _this.date[0],
        fundCode: '',
        index: 0,
        items: 999,
        todate: _this.date[1]
      }
      axios.post(`${_this.CNLBSGateway}/fund-experience/order/orderRetrieval`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          _this.fundOrderList = []
          if (response.data.code === '200') {
            const result = response.data.data
            for (let i = 0; i < result.length; i++) {
              result[i].requestTime = _this.$moment(result[i].requestTime).format('YYYY-MM-DD HH:mm:ss')
              result[i].tradingOption = result[i].tradingOption.toUpperCase()
              result[i].status = result[i].status.toUpperCase()
              result[i]['totalAmount'] = `${result[i].currencyCode} ${result[i].tradingAmount + result[i].tradingCommission}`
            }
            _this.fundOrderList = result
          } else if (response.data.code === '404010') {
            // _this.$message.success(_this.$t('lbs.FundOrder.RecordNotFound'), _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为A的基金账户列表
    getFundAccountOption() {
      const tempMutualFundAccountList = JSON.parse(window.sessionStorage.getItem('mutualFundaccountlist'))
      for (const item of tempMutualFundAccountList) {
        if (item.accountStatus === 'A') {
          this.fundAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      if (this.fundAccountOption && this.fundAccountOption[0]) {
        this.fundAccount = this.fundAccountOption[0].value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .fund-order {
    margin: 0 50px;

    .fund-order-title {
      font-size: 20px;
      color: #22C1E6;
    }

    .view-more {
      color: #22C1E6;
      text-decoration: underline;
      cursor: pointer;
    }

    .view-more:hover {
      color: #315efb;
    }

  }
</style>
