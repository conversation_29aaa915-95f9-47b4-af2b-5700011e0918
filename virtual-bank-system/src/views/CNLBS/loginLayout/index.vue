<template>
  <div class="outContainer">
    <div class="header" style="padding-top:50px">
      <img alt="" class="logo" :src="logo" style="cursor:pointer;" @click="jump2()">
    </div>
    <el-row class="content">
      <el-col :span="10" :xs="24" class="content_lf">
        <img alt="" class="login_img" src="../../../assets/images/login.jpg">
        <p>{{ $t('lbs.login.introLBS') }}</p>
        <div class="by" style="cursor:pointer;" @click="jump2">{{ $t('lbs.login.copyright') }}</div>
      </el-col>
      <el-col :span="14" :xs="24" class="content_rt">
        <router-view />
      </el-col>
    </el-row>
    <div class="footer">
      <a href="/" target="_blank">@2023 SIMNECTZ Inc.</a>
      <a rel="noopener noreferrer" href="https://beian.miit.gov.cn/" target="_blank">陕ICP备20000279号-2</a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoginLayout',
  data() {
    return {
      logo: require(`@/assets/images/logo-${process.env.VUE_APP_TEMPORARY_PERMISSION}.png`)
    }
  },
  methods: {
    jump2() {
      this.$router.push({ path: '/guest/introduction' })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
$bg: #fff;
$width: 1100px;
$height: 100%;
$light_gray: #333;
$cursor: #333;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
    .login-container .el-input input {
        color: $cursor;
    }
}

@mixin outContainer-px($width) {
    .outContainer {
        height: 100%;
        width: 100%;
        background: $bg url("../../../assets/images/bg.jpg") no-repeat center;
        background-size: cover;
        .header {
            width: $width;
            margin: 0 auto 20px;
            .logo {
                width: 200px;
                height: auto;
            }
        }
        .title {
            position: relative;
            &::before,
            &::after {
                position: absolute;
                top: 46%;
                content: "";
                display: block;
                width: 100px;
                height: 1px;
                background: #333;
            }
            &::before {
                left: 0;
            }
            &::after {
                right: 0;
            }
        }
        .content {
            width: $width;
            margin: 0 auto;
            .content_lf {
                font-size: 16px;
                font-family: "Open Sans", sans-serif;
                text-align: center;
                margin-bottom: 40px;
                .login_img {
                    text-align: center;
                    width: 95%;
                    border: 1px solid #999;
                    border-radius: 10px;
                    box-shadow: 10px 10px 10px #999;
                    margin-bottom: 10px;
                }
                .by {
                    border-top: 1px solid #999;
                    text-align: right;
                    font-weight: 400;
                    padding-top: 20px;
                    img {
                        width: 25px;
                        height: 25px;
                        vertical-align: middle;
                    }
                }
            }
        }
        .footer {
            width: $width;
            margin: 0 auto;
            text-align: center;
            padding: 80px 0;
        }
        .el-input {
            display: inline-block;
            height: 47px;
            width: 85%;
            input {
                background: transparent;
                border: 0px;
                -webkit-appearance: none;
                border-radius: 0px;
                padding: 12px 5px 12px 15px;
                color: $light_gray;
                height: 47px;
                caret-color: $cursor;
                &:-webkit-autofill {
                    -webkit-text-fill-color: $cursor !important;
                    box-shadow: 0 0 0px 1000px $bg inset !important;
                }
            }
            input::-webkit-input-placeholder {
                color: #ccc;
            }
            input::-moz-placeholder {
                color: #ccc;
            }
            input:-moz-placeholder {
                color: #ccc;
            }
            input:-ms-input-placeholder {
                color: #ccc;
            }
        }
        .el-form-item {
            border: 1px solid rgba(0, 0, 0, 0.3);
            background: #fff;
            border-radius: 5px;
            color: #454545;
            margin-bottom: 35px;
        }
    }
}
// <= 768
@media screen and (max-width: 768px) {
    @include outContainer-px($width: 95%);
}
// > 768
@media screen and (min-width: 768px) {
    @include outContainer-px($width: 1100px);
}
</style>
