<template>
  <div v-loading.fullscreen.lock="loading" class="applymortgage">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.MortgageApplication') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="main">
          <el-card>
            <div class="information">
              <p class="title2">{{ $t('lbs.PersonalInformation') }}</p>
              <div>
                <span class="left">{{ $t('lbs.Name') }}</span><span class="right">{{ `${personalInformation.firstname} ${personalInformation.lastname}` }}</span>
              </div>
              <div>
                <span class="left">{{ $t('lbs.Email') }}</span><span
                  class="right"
                >{{ personalInformation.emailaddress }}</span>
              </div>
              <div>
                <span class="left">{{ $t('lbs.MobileNumber') }}</span><span class="right">{{ personalInformation.mobilephonenumber }}</span>
              </div>
            </div>
            <div class="information">
              <el-form
                ref="MortgageForm"
                size="mini"
                :model="MortgageForm"
                :rules="MortgageFormRules"
                label-position="left"
              >
                <p class="title2">{{ $t('lbs.LoanInformation') }}</p>
                <div class="info-item">
                  <div class="mortgage-input-select">
                    <el-form-item :label="$t(&quot;lbs.monthlysalary&quot;)" prop="monthlysalary" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.monthlysalary"
                        autocomplete="off"
                        :placeholder="$t(&quot;lbs.monthlysalary&quot;)"
                        style="width: 80%;"
                      >
                        <el-select
                          slot="prepend"
                          v-model="MortgageForm.ccyCode"
                          placeholder=""
                          style="width: 80px"
                        >
                          <el-option
                            v-for="(item,index) in CurrencyList"
                            :key="index"
                            :label="item.ccycode"
                            :value="item.ccycode"
                            :disabled="item.ccycode !== 'CNY'"
                          />
                        </el-select>
                      </el-input>
                    </el-form-item>
                  </div>
                  <el-form-item :label="$t(&quot;lbs.repaymentPeriod&quot;)" prop="repaymentPeriod" :label-width="formLabelWidth">
                    <el-slider
                      v-model="MortgageForm.repaymentPeriod"
                      :max="30"
                      :min="1"
                      show-input
                    />
                  </el-form-item>
                  <div class="mortgage-input-select">
                    <el-form-item :label="$t(&quot;lbs.borringneeds&quot;)" prop="borringneeds" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.borringneeds"
                        autocomplete="off"
                        :placeholder="$t(&quot;lbs.borringneeds&quot;)"
                        style="width: 80%;"
                        maxlength="18"
                      >
                        <el-select
                          slot="prepend"
                          v-model="MortgageForm.ccyCode"
                          placeholder=""
                          style="width: 80px"
                        >
                          <el-option
                            v-for="(item,index) in CurrencyList"
                            :key="index"
                            :label="item.ccycode"
                            :value="item.ccycode"
                            :disabled="item.ccycode !== 'CNY'"
                          />
                        </el-select>
                      </el-input>
                      <p style="margin-top: 10px;color: green;font-size: 12px">{{ `(${$t('lbs.maxLoanAmount')}：
                        ${maxLoanAmount})` }}</p>
                    </el-form-item>
                  </div>
                  <el-form-item :label="$t(&quot;lbs.loanScheme&quot;)" prop="loanScheme" :label-width="formLabelWidth">
                    <el-select
                      v-model="MortgageForm.loanScheme"
                      :placeholder="$t(&quot;lbs.loanScheme&quot;)"
                      style="width: 80%"
                    >
                      <el-option
                        v-for="(item,index) in loanSchemeOptions"
                        :key="index"
                        :label="item.label"
                        :disabled="item.value !== 'F'"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t(&quot;lbs.repaymentPlan&quot;)" prop="repaymentPlan" :label-width="formLabelWidth">
                    <el-select
                      v-model="MortgageForm.repaymentPlan"
                      :placeholder="$t(&quot;lbs.repaymentPlan&quot;)"
                      style="width: 80%"
                    >
                      <el-option
                        v-for="(item,index) in RepaymentPlanOptions"
                        :key="index"
                        :label="item.label"
                        :disabled="item.value !== 'L'"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t(&quot;lbs.repaymentPlan&quot;)" prop="repaymentPlan" :label-width="formLabelWidth">
                    <el-radio-group v-model="MortgageForm.repaymentCycle">
                      <el-radio v-for="item in RepaymentCycleOptions" :key="item.value" :label="item.value">
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item
                    :label="$t(&quot;lbs.loanAccountNumber&quot;)"
                    prop="accountnumber"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="MortgageForm.accountnumber"
                      :placeholder="$t(&quot;lbs.loanAccountNumber&quot;)"
                      style="width: 80%;"
                    >
                      <el-option
                        v-for="item in loanAccountList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="$t(&quot;lbs.debitAccountNumber&quot;)"
                    prop="debitaccountnumber"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="MortgageForm.debitaccountnumber"
                      :placeholder="$t(&quot;lbs.debitAccountNumber&quot;)"
                      style="width: 80%;"
                    >
                      <el-option-group
                        v-for="group in debitAccountList"
                        :key="group.label"
                        :label="group.label"
                      >
                        <el-option
                          v-for="item in group.options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-option-group>
                    </el-select>
                  </el-form-item>
                </div>
                <hr style="background-color: #cccccc">
                <div class="information">
                  <p class="title2">{{ $t('lbs.MortgagedPropertyInformation') }}</p>
                  <div class="info-item">
                    <el-form-item
                      :label="$t(&quot;lbs.propertyClassification&quot;)"
                      prop="propertyClassification"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyClassification">
                        <el-radio v-for="item in ClassificationOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t(&quot;lbs.propertyType&quot;)" prop="propertyType" :label-width="formLabelWidth">
                      <el-select v-model="MortgageForm.propertyType" :placeholder="$t(&quot;lbs.propertyType&quot;)">
                        <el-option
                          v-for="item in TypeOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyTransactionStatus&quot;)"
                      prop="propertyTransactionStatus"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyTransactionStatus">
                        <el-radio v-for="item in TransactionStatusOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyMonthlyRental&quot;)"
                      prop="propertyMonthlyRental"
                      :rules="this.MortgageForm.propertyTransactionStatus === 'T' ? [{required: true, message: $t('lbs.MortgageTip1'), trigger: 'change'},{pattern: /^\+?[1-9]\d*$/, message: $t('lbs.MortgageTip2'), trigger: 'change'},] : []"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyMonthlyRental"
                        maxlength="18"
                        :disabled="MortgageForm.propertyTransactionStatus === 'V'"
                        :placeholder="$t(&quot;lbs.propertyMonthlyRental&quot;)"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyWithCarpark&quot;)"
                      prop="propertyWithCarpark"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyWithCarpark">
                        <el-radio v-for="item in BooleanOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyCarparkNumber&quot;)"
                      prop="propertyCarparkNumber"
                      :rules="MortgageForm.propertyWithCarpark === 'Y' ? [{required: true, message: $t('lbs.MortgageTip3'), trigger: 'change'}] : []"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyCarparkNumber"
                        autocomplete="off"
                        maxlength="10"
                        :placeholder="$t(&quot;lbs.propertyCarparkNumber&quot;)"
                        :disabled="MortgageForm.propertyWithCarpark !== 'Y'"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyWithGarden&quot;)"
                      prop="propertyWithGarden"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyWithGarden">
                        <el-radio v-for="item in BooleanOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyWithRoof&quot;)"
                      prop="propertyWithRoof"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyWithRoof">
                        <el-radio v-for="item in BooleanOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t(&quot;lbs.purchasePrice&quot;)" prop="purchasePrice" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.purchasePrice"
                        autocomplete="off"
                        maxlength="20"
                        :placeholder="$t(&quot;lbs.purchasePrice&quot;)"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.PropertyAddressFormat&quot;)"
                      prop="propertyAddressFormat"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyAddressFormat">
                        <el-radio
                          v-for="item in AddressFormatOptions"
                          :key="item.value"
                          :disabled="item.value === 'F'"
                          :label="item.value"
                        >{{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <div v-show="MortgageForm.propertyAddressFormat === 'F'">
                      <el-form-item
                        :label="$t(&quot;lbs.propertyAddressLine1&quot;)"
                        prop="propertyAddressLine1"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine1"
                          autocomplete="off"
                          :placeholder="$t(&quot;lbs.propertyAddressLine1&quot;)"
                        />
                      </el-form-item>
                      <el-form-item
                        :label="$t(&quot;lbs.propertyAddressLine2&quot;)"
                        prop="propertyAddressLine2"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine2"
                          autocomplete="off"
                          :placeholder="$t(&quot;lbs.propertyAddressLine2&quot;)"
                        />
                      </el-form-item>
                      <el-form-item
                        :label="$t(&quot;lbs.propertyAddressLine3&quot;)"
                        prop="propertyAddressLine3"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine3"
                          autocomplete="off"
                          :placeholder="$t(&quot;lbs.propertyAddressLine3&quot;)"
                        />
                      </el-form-item>
                      <el-form-item
                        :label="$t(&quot;lbs.propertyAddressLine4&quot;)"
                        prop="propertyAddressLine4"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine4"
                          autocomplete="off"
                          :placeholder="$t(&quot;lbs.propertyAddressLine4&quot;)"
                        />
                      </el-form-item>
                    </div>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyCountry&quot;)"
                      prop="propertyCountry"
                      :label-width="formLabelWidth"
                    >
                      <el-select
                        v-model="MortgageForm.propertyCountry"
                        filterable
                        :placeholder="$t(&quot;lbs.propertyCountry&quot;)"
                        style="width: 80%"
                      >
                        <!--                        <el-option v-if="language === 'en'" v-for="(item,index) in options" :key="index"-->
                        <!--                                   :label="item.en" :value="item.short"/>-->
                        <el-option
                          v-for="(item,index) in options"
                          :key="index"
                          :label="language === 'en' ? item.en : item.name"
                          :value="item.short"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyAreaCode&quot;)"
                      prop="propertyAreaCode"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyAreaCode"
                        disabled
                        autocomplete="off"
                        style="width: 40%;"
                        :placeholder="$t(&quot;lbs.propertyAreaCode&quot;)"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyPostCode&quot;)"
                      prop="propertyPostCode"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyPostCode"
                        autocomplete="off"
                        maxlength="8"
                        :placeholder="$t(&quot;lbs.propertyPostCode&quot;)"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyStreetName&quot;)"
                      prop="propertyStreetName"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyStreetName"
                        autocomplete="off"
                        maxlength="35"
                        :placeholder="$t(&quot;lbs.propertyStreetName&quot;)"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyStreetNumber&quot;)"
                      prop="propertyStreetNumber"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyStreetNumber"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t(&quot;lbs.propertyStreetNumber&quot;)"
                      />
                    </el-form-item>
                    <el-form-item :label="$t(&quot;lbs.propertyEstate&quot;)" prop="propertyEstate" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyEstate"
                        autocomplete="off"
                        maxlength="35"
                        :placeholder="$t(&quot;lbs.propertyEstate&quot;)"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t(&quot;lbs.propertyBuilding&quot;)"
                      prop="propertyBuilding"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyBuilding"
                        autocomplete="off"
                        maxlength="35"
                        :placeholder="$t(&quot;lbs.propertyBuilding&quot;)"
                      />
                    </el-form-item>
                    <el-form-item :label="$t(&quot;lbs.propertyBlock&quot;)" prop="propertyBlock" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyBlock"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t(&quot;lbs.propertyBlock&quot;)"
                      />
                    </el-form-item>
                    <el-form-item :label="$t(&quot;lbs.propertyFloor&quot;)" prop="propertyFloor" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyFloor"
                        autocomplete="off"
                        maxlength="3"
                        :placeholder="$t(&quot;lbs.propertyFloor&quot;)"
                      />
                    </el-form-item>
                    <el-form-item :label="$t(&quot;lbs.propertyRoom&quot;)" prop="propertyRoom" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyRoom"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t(&quot;lbs.propertyRoom&quot;)"
                      />
                    </el-form-item>
                    <el-form-item :label="$t(&quot;lbs.propertyFlat&quot;)" prop="propertyFlat" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyFlat"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t(&quot;lbs.propertyFlat&quot;)"
                      />
                    </el-form-item>
                  </div>
                </div>
                <p class="title2">{{ $t('lbs.SolicitorsInformation') }}</p>
                <div class="info-item">
                  <el-form-item :label="$t(&quot;lbs.solicitorsFirm&quot;)" prop="solicitorsFirm" :label-width="formLabelWidth">
                    <el-input
                      v-model="MortgageForm.solicitorsFirm"
                      autocomplete="off"
                      maxlength="140"
                      :placeholder="$t(&quot;lbs.solicitorsFirm&quot;)"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t(&quot;lbs.solicitorsContactPerson&quot;)"
                    prop="solicitorsContactPerson"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="MortgageForm.solicitorsContactPerson"
                      autocomplete="off"
                      maxlength="140"
                      :placeholder="$t(&quot;lbs.solicitorsContactPerson&quot;)"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t(&quot;lbs.solicitorsPhoneNumber&quot;)"
                    prop="solicitorsPhoneNumber"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="MortgageForm.solicitorsPhoneNumber"
                      autocomplete="off"
                      maxlength="34"
                      :placeholder="$t(&quot;lbs.solicitorsPhoneNumber&quot;)"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t(&quot;lbs.solicitorsFaxNumber&quot;)"
                    prop="solicitorsFaxNumber"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="MortgageForm.solicitorsFaxNumber"
                      autocomplete="off"
                      maxlength="34"
                      :placeholder="$t(&quot;lbs.solicitorsFaxNumber&quot;)"
                    />
                  </el-form-item>
                </div>
              </el-form>
            </div>
            <el-button type="primary" style="float: right;margin-bottom: 20px" @click="NextStep">
              {{ $t('lbs.Submit') }}
            </el-button>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import options from './addressJson'
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    options,
    maxLoanAmount: '',
    loanAccount: '',
    loanAccountList: [],
    debitAccountList: [
      { label: 'Saving Account', options: [] },
      { label: 'Current Account', options: [] }
    ],
    personalInformation: {
      firstname: '',
      lastname: '',
      emailaddress: '',
      mobilephonenumber: '',
      monthlysalary: ''
    },
    CurrencyList: [],
    formLabelWidth: '260px',
    MortgageForm: {
      accountnumber: '',
      borringneeds: 0,
      ccyCode: 'CNY',
      debitaccountnumber: '',
      loanScheme: 'F',
      monthlysalary: 0,
      propertyAddressFormat: 'S',
      propertyAddressLine1: '',
      propertyAddressLine2: '',
      propertyAddressLine3: '',
      propertyAddressLine4: '',
      propertyAreaCode: '852',
      propertyBlock: '',
      propertyBuilding: '',
      propertyCarparkNumber: '',
      propertyClassification: 'F',
      propertyCountry: 'HK',
      propertyEstate: '',
      propertyFlat: '',
      propertyFloor: '',
      propertyMonthlyRental: '',
      propertyPostCode: '',
      propertyRoom: '',
      propertyStreetName: '',
      propertyStreetNumber: '',
      propertyTransactionStatus: 'V',
      propertyType: 'R',
      propertyWithCarpark: 'N',
      propertyWithGarden: 'N',
      propertyWithRoof: 'N',
      purchasePrice: 0,
      repaymentCycle: 'M',
      repaymentPeriod: 0,
      repaymentPlan: 'L',
      solicitorsContactPerson: '',
      solicitorsFaxNumber: '',
      solicitorsFirm: '',
      solicitorsPhoneNumber: ''
    },
    MortgageFormRules: {},
    AddressFormatOptions: [],
    ClassificationOptions: [],
    TypeOptions: [],
    TransactionStatusOptions: [],
    BooleanOptions: [],
    loanSchemeOptions: [],
    RepaymentPlanOptions: [],
    RepaymentCycleOptions: [],
    debitAccountNumberOptions: []
  }),
  computed: {
    language() {
      return this.$store.state.app.language
    }
  },
  watch: {
    'MortgageForm.propertyTransactionStatus'(newValue, oldValue) {
      if (newValue === 'V') {
        this.MortgageForm.propertyMonthlyRental = ''
      }
    },
    'MortgageForm.propertyWithCarpark'(newValue, oldValue) {
      if (newValue !== 'Y') {
        this.MortgageForm.propertyCarparkNumber = ''
      }
    },
    'MortgageForm.propertyCountry'(newValue, oldValue) {
      for (let i = 0; i < this.options.length; i++) {
        if (newValue === this.options[i].short) {
          this.MortgageForm.propertyAreaCode = this.options[i].tel
        }
      }
    },
    'MortgageForm.repaymentPeriod'(newValue, oldValue) {
      if (this.MortgageForm.monthlysalary > 0) { this.loanCalculator() }
    },
    'MortgageForm.borringneeds'(newValue) {
      const maxLoanAmount = this.maxLoanAmount.substring(4).replace(/,/g, '')
      if (Number(newValue) > Number(maxLoanAmount)) {
        this.MortgageForm.borringneeds = parseInt(maxLoanAmount).toString()
      }
    },
    'language'() {
      this.initOptions()
    }
  },
  created() {
    this.token = window.sessionStorage.getItem('token')
    const loanAccountList = JSON.parse(window.sessionStorage.getItem('loanaccountlist'))
    const savingAccountList = JSON.parse(
      window.sessionStorage.getItem('savingaccountlist')
    )
    const currentaccountlist = JSON.parse(
      window.sessionStorage.getItem('currentaccountlist')
    )
    for (let i = 0; i < loanAccountList.length; i++) {
      if (loanAccountList[i].accountStatus === 'A') {
        this.loanAccountList.push({
          label: loanAccountList[i].accountNumber,
          value: loanAccountList[i].accountNumber
        })
      }
    }
    for (var i = 0; i < savingAccountList.length; i++) {
      if (savingAccountList[i].accountStatus === 'A') {
        this.debitAccountList[0].options.push({
          label: savingAccountList[i].accountNumber,
          value: savingAccountList[i].accountNumber
        })
      }
    }
    for (var i = 0; i < currentaccountlist.length; i++) {
      if (currentaccountlist[i].accountStatus === 'A') {
        this.debitAccountList[1].options.push({
          label: currentaccountlist[i].accountNumber,
          value: currentaccountlist[i].accountNumber
        })
      }
    }
    if (this.debitAccountList && this.debitAccountList[0] && this.debitAccountList[0].options && this.debitAccountList[0].options[0]) {
      this.MortgageForm.debitaccountnumber = this.debitAccountList[0].options[0].value
    }
    if (this.loanAccountList && this.loanAccountList[0]) {
      this.MortgageForm.accountnumber = this.loanAccountList[0].value
    }
    this.initOptions()
    this.getPersonalInformation()
    this.getCurrencyList()
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') == 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupapplymortgage'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
  },
  methods: {
    initOptions() {
      this.AddressFormatOptions = [
        { label: this.$t('lbs.StructureFormat'), value: 'S' },
        { label: this.$t('lbs.FreeFormat'), value: 'F' }
      ]
      this.ClassificationOptions = [
        { label: this.$t('lbs.FirstHand'), value: 'F' },
        { label: this.$t('lbs.SecondHand'), value: 'S' }
      ]
      this.TypeOptions = [
        { label: this.$t('lbs.Residential'), value: 'R' },
        { label: this.$t('lbs.CarPark'), value: 'C' },
        { label: this.$t('lbs.Villa'), value: 'V' },
        { label: this.$t('lbs.Village'), value: 'T' },
        { label: this.$t('lbs.Office'), value: 'O' },
        { label: this.$t('lbs.Shop'), value: 'S' },
        { label: this.$t('lbs.Industrial'), value: 'I' },
        { label: this.$t('lbs.House'), value: 'H' }
      ]
      this.TransactionStatusOptions = [
        { label: this.$t('lbs.Vacant'), value: 'V' },
        { label: this.$t('lbs.WithTenancyAgreement'), value: 'T' }
      ]
      this.BooleanOptions = [
        { label: this.$t('lbs.Yes'), value: 'Y' },
        { label: this.$t('lbs.No'), value: 'N' }
      ]
      this.loanSchemeOptions = [
        { label: this.$t('lbs.FixedRatePlan'), value: 'F' },
        { label: this.$t('lbs.HiborMortgagePlan'), value: 'H' },
        { label: this.$t('lbs.HKDPrimeRateMortgagePlan'), value: 'P' }
      ]
      this.RepaymentPlanOptions = [
        { label: this.$t('lbs.StraightLineRepaymentPlan'), value: 'L' },
        { label: this.$t('lbs.EqualPrincipalRepaymentPlan'), value: 'P' },
        { label: this.$t('lbs.StepUpRepaymentPlan'), value: 'U' }
      ]
      this.RepaymentCycleOptions = [
        { label: this.$t('lbs.Monthly'), value: 'M' },
        { label: this.$t('lbs.BiWeekly'), value: 'B' }
      ]
      this.MortgageFormRules = {
        borringneeds: [
          { required: true, message: this.$t('lbs.MortgageTip4'), trigger: 'change' },
          {
            pattern: /^\+?[1-9]\d*$/,
            message: this.$t('lbs.MortgageTip2'),
            trigger: 'change'
          }
        ],
        purchasePrice: [
          { required: true, message: this.$t('lbs.MortgageTip5'), trigger: 'change' },
          {
            pattern: /^\+?[1-9]\d*$/,
            message: this.$t('lbs.MortgageTip2'),
            trigger: 'change'
          }
        ],
        ccyCode: [
          { required: true, message: this.$t('lbs.MortgageTip6'), trigger: 'change' }
        ],
        loanScheme: [
          { required: true, message: this.$t('lbs.MortgageTip7'), trigger: 'change' }
        ],
        debitaccountnumber: [
          { required: true, message: this.$t('lbs.MortgageTip8'), trigger: 'change' }
        ],
        accountnumber: [
          { required: true, message: this.$t('lbs.MortgageTip9'), trigger: 'change' }
        ],
        monthlysalary: [
          { required: true, message: this.$t('lbs.MortgageTip10'), trigger: 'change' },
          {
            pattern: /^\+?[1-9]\d*$/,
            message: this.$t('lbs.MortgageTip2'),
            trigger: 'change'
          }
        ],
        propertyAddressFormat: [
          { required: true, message: this.$t('lbs.MortgageTip11'), trigger: 'change' }
        ],
        propertyAreaCode: [
          { required: true, message: this.$t('lbs.MortgageTip12'), trigger: 'change' }
        ],
        propertyBlock: [
          { required: true, message: this.$t('lbs.MortgageTip13'), trigger: 'change' }
        ],
        propertyBuilding: [
          { required: true, message: this.$t('lbs.MortgageTip14'), trigger: 'change' }
        ],
        propertyClassification: [
          { required: true, message: this.$t('lbs.MortgageTip15'), trigger: 'change' }
        ],
        propertyCountry: [
          { required: true, message: this.$t('lbs.MortgageTip16'), trigger: 'change' }
        ],
        propertyEstate: [
          { required: true, message: this.$t('lbs.MortgageTip17'), trigger: 'change' }
        ],
        propertyFlat: [
          { required: true, message: this.$t('lbs.MortgageTip18'), trigger: 'change' }
        ],
        propertyFloor: [
          { required: true, message: this.$t('lbs.MortgageTip19'), trigger: 'change' }
        ],
        propertyPostCode: [
          { required: true, message: this.$t('lbs.MortgageTip20'), trigger: 'change' }
        ],
        propertyRoom: [
          { required: true, message: this.$t('lbs.MortgageTip21'), trigger: 'change' }
        ],
        propertyStreetName: [
          { required: true, message: this.$t('lbs.MortgageTip22'), trigger: 'change' }
        ],
        propertyStreetNumber: [
          { required: true, message: this.$t('lbs.MortgageTip23'), trigger: 'change' },
          { pattern: /^[0-9]*$/, message: this.$t('lbs.MortgageTip24'), trigger: 'change' }
        ],
        propertyTransactionStatus: [
          { required: true, message: this.$t('lbs.MortgageTip25'), trigger: 'change' }
        ],
        propertyType: [
          { required: true, message: this.$t('lbs.MortgageTip26'), trigger: 'change' }
        ],
        propertyWithCarpark: [
          { required: true, message: this.$t('lbs.MortgageTip27'), trigger: 'change' }
        ],
        propertyWithGarden: [
          { required: true, message: this.$t('lbs.MortgageTip27'), trigger: 'change' }
        ],
        propertyWithRoof: [
          { required: true, message: this.$t('lbs.MortgageTip27'), trigger: 'change' }
        ],
        repaymentCycle: [
          { required: true, message: this.$t('lbs.MortgageTip30'), trigger: 'change' }
        ],
        repaymentPeriod: [
          { required: true, message: this.$t('lbs.MortgageTip31'), trigger: 'change' }
        ],
        repaymentPlan: [
          { required: true, message: this.$t('lbs.MortgageTip32'), trigger: 'change' }
        ],
        solicitorsContactPerson: [
          { required: true, message: this.$t('lbs.MortgageTip33'), trigger: 'change' }
        ],
        solicitorsFaxNumber: [
          { required: true, message: this.$t('lbs.MortgageTip34'), trigger: 'change' }
        ],
        solicitorsFirm: [
          { required: true, message: this.$t('lbs.MortgageTip35'), trigger: 'change' }
        ],
        solicitorsPhoneNumber: [
          { required: true, message: this.$t('lbs.MortgageTip36'), trigger: 'change' }
        ]
      }
    },
    //  贷款账号的获取个人信息
    getPersonalInformation() {
      const _this = this
      if (_this.loanAccountList.length == 0 || !_this.loanAccountList[0].value) return
      _this.loading = true
      axios.post(`${_this.CNLBSGateway}/loan-experience/mortgage/accountDetailEnquiry`, { accountnumber: _this.loanAccountList[0].value }, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.personalInformation = response.data.data.customerInfo
            _this.MortgageForm.monthlysalary = _this.personalInformation.monthlysalary
            _this.loanCalculator()
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    getCurrencyList() {
      const vue = this
      vue.loading = true
      axios.get(
        `${vue.CNLBSGateway}/domestic-sysadmin-process/sysadmin/currency/currencyTypeRetrieval`, { headers: { token: vue.token }})
        .then(response => {
          vue.loading = false
          if (response.data.code === '200') {
            vue.CurrencyList = response.data.data
          }
        })
        .catch(error => {
          vue.loading = false
          vue.$mui.alert(error.message, vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
        })
    },
    loanCalculator() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.CNLBSGateway}/loan-experience/mortgage/loanCalculator`, {
        ccyCode: _this.MortgageForm.ccyCode,
        loanPeriod: _this.MortgageForm.repaymentPeriod,
        monthlysalary: _this.MortgageForm.monthlysalary
      }, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.maxLoanAmount = response.data.data.ccyCode + ' ' + _this.$lbs.decimal_format(parseInt(response.data.data.maxLoanAmount))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    NextStep() {
      const _this = this
      _this.$refs['MortgageForm'].validate((valid) => {
        if (valid) {
          _this.loading = true
          axios.post(`${_this.CNLBSGateway}/loan-experience/mortgage/mortgageLoanApplication`, _this.MortgageForm, { headers: { token: _this.token }})
            .then(response => {
              _this.loading = false
              if (response.data.code === '200') {
                _this.$mui.alert(_this.$t('lbs.TransactionSuccess'), _this.$t('lbs.common.success'), _this.$t('lbs.common.confirm'), function() {
                  _this.$router.push({ path: '/cn/lbs/mortgage/finishmortgage' })
                })
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
            })
        } else {
          _this.$message.warning(_this.$t('lbs.MortgageTip0'), _this.$t('lbs.common.warning'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

  .applymortgage {

    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

    .title2 {
      font-size: 25px;
      color: #22C1E6;
      margin-bottom: 20px;
    }

    .main {
      padding: 20px 100px 60px 60px;
      width: 65%;
      line-height: 40px;
      margin: 0 auto;

      .left, .right {
        display: inline-block;
        font-size: 14px;
        color: #606266;
        font-weight: 700;
        width: 280px;
      }

      .left {
        margin-left: 40px;
      }

      .right {
        float: right;
      }

      .information {
        margin-bottom: 20px;
        border-bottom: 1px #cccccc solid;
      }

      .info-item {
        margin: 0 36px;
      }
    }

  }

</style>

<style>
  .applymortgage .el-radio {
    margin-top: 8px;
  }

  .applymortgage .mortgage-input-select .el-input__icon {
    height: 30px;
  }

  .applymortgage .mortgage-input-select .el-input__inner {
    margin-bottom: 0;
    height: 30px;
  }
</style>
