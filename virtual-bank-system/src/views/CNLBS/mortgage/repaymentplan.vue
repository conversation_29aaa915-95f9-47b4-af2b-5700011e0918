<template>
  <div v-loading.fullscreen.lock="loading" class="repaymentplan">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.MortgageRepaymentPlan') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-card class="main">
          <div style="text-align: center">
            <p style="line-height: 40px;letter-spacing: 2px">{{ language === 'en' ? `Phase
              ${nextRepayment.phaseno}，Payable in this period(${nextRepayment.currencycode})` :
              `第${nextRepayment.phaseno}期，本期应还(${nextRepayment.currencycode})` }}</p>
            <p style="line-height: 40px;font-size: 25px">{{ nextRepayment.totalpayment }}</p>
            <p style="line-height: 40px;letter-spacing: 2px">
              {{ $t('lbs.LastRepaymentDate') }}：{{ $moment(nextRepayment.repaymentduedate).format('YYYY-MM-DD') }}</p>
            <div style="text-align: center">
              <el-button type="primary" style="margin-bottom: 20px" @click="openPaymentPage">{{ $t('lbs.Repayment') }}
              </el-button>
            </div>
          </div>
          <el-tabs v-model="activeName">
            <el-tab-pane :label="$t('lbs.PendingRepayment')" name="first">
              <el-collapse accordion style="width: 75%;margin: 0 auto">
                <el-collapse-item v-for="(item,index) in contractPlan1" :key="index">
                  <template slot="title">
                    <p style="margin-left: 20px">{{ language === 'en' ? `Phase
                      ${item.phaseno}，${$t('lbs.ShouldRepayment')}
                      ${item.totalpayment} ${item.currencycode}` : `第 ${item.phaseno} 期，${$t('lbs.ShouldRepayment')}
                      ${item.totalpayment} ${item.currencycode}` }}</p>
                  </template>
                  <span class="item-prop">{{ `${$t('lbs.PhaseNo')}：${item.phaseno}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.LoanAmountPerPhase')}：${item.loanamountperphase}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.LoanInterestPerPhase')}：${item.loaninterestperphase}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.OutstandingBalance')}：${item.outstandingbalance}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.PenaltyAmount')}：${item.penaltyamount}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.TotalPayment')}：${item.totalpayment}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.CurrencyCode')}：${item.currencycode}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.RepaymentDueDate')}：${$moment(item.repaymentduedate).format('YYYY-MM-DD')}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.RepaymentStatus')}：${item.repaymentstatus}` }}</span>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
            <el-tab-pane :label="$t('lbs.Repaid')" name="second">
              <el-collapse accordion style="width: 75%;margin: 0 auto">
                <el-collapse-item v-for="(item,index) in contractPlan2" :key="index">
                  <template slot="title">
                    <p style="margin-left: 20px">{{ language === 'en' ? `Phase
                      ${item.phaseno}，${$t('lbs.ShouldRepayment')}
                      ${item.totalpayment} ${item.currencycode}` : `第 ${item.phaseno} 期，${$t('lbs.ShouldRepayment')}
                      ${item.totalpayment} ${item.currencycode}` }}</p>
                  </template>
                  <span class="item-prop">{{ `${$t('lbs.PhaseNo')}：${item.phaseno}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.LoanAmountPerPhase')}：${item.loanamountperphase}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.LoanInterestPerPhase')}：${item.loaninterestperphase}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.OutstandingBalance')}：${item.outstandingbalance}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.PenaltyAmount')}：${item.penaltyamount}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.TotalPayment')}：${item.totalpayment}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.CurrencyCode')}：${item.currencycode}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.RepaymentDueDate')}：${$moment(item.repaymentduedate).format('YYYY-MM-DD')}` }}</span>
                  <span class="item-prop">{{ `${$t('lbs.RepaymentStatus')}：${item.repaymentstatus}` }}</span>
                </el-collapse-item>
              </el-collapse>
            </el-tab-pane>
          </el-tabs>
        </el-card>
        <el-dialog
          :title="$t('lbs.Repayment')"
          :visible.sync="paymentDialogVisible"
          width="50%"
          center
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <div style="width: 60%;margin: 0 auto">
            <span style="display: inline-block;width: 33%">{{ $t('lbs.LoanAccount') }}：</span>
            <el-input v-model="nextRepayment.accountnumber" disabled size="small" style="width: 66%;" />
            <span style="display: inline-block;width: 33%">{{ $t('lbs.debitAccountNumber') }}：</span>
            <el-select
              v-model="debitaccountnumber"
              :placeholder="$t(&quot;lbs.debitAccountNumber&quot;)"
              style="width: 66%;margin-bottom: 15px"
            >
              <el-option-group
                v-for="group in debitAccountList"
                :key="group.label"
                :label="group.label"
              >
                <el-option
                  v-for="item in group.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-option-group>
            </el-select>
            <span style="display: inline-block;width: 33%">{{ $t('lbs.ContractNumber') }}：</span>
            <el-input v-model="nextRepayment.contractnumber" disabled size="small" style="width: 66%;" />
            <span style="display: inline-block;width: 33%">{{ $t('lbs.PhaseNo') }}：</span>
            <el-input v-model="nextRepayment.phaseno" disabled size="small" style="width: 66%;" />
            <span style="display: inline-block;width: 33%">{{ $t('lbs.TotalPayment') }}：</span>
            <el-input v-model="nextRepayment.totalpayment" disabled size="small" style="width: 66%;" />
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="paymentDialogVisible = false">{{ $t('lbs.common.cancel') }}</el-button>
            <el-button type="primary" @click="Repayment()">{{ $t('lbs.common.confirm') }}</el-button>
          </span>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    repaymentPlan: {},
    contractPlan1: [],
    contractPlan2: [],
    nextRepayment: {},
    paymentDialogVisible: false,
    debitaccountnumber: '',
    debitAccountList: [
      { label: 'Saving Account', options: [] },
      { label: 'Current Account', options: [] }
    ],
    activeName: 'first'
  }),
  computed: {
    language() {
      return this.$store.state.app.language
    }
  },
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.repaymentPlan = JSON.parse(window.sessionStorage.getItem('RepaymentPlan'))
    if (this.repaymentPlan) {
      this.RepaymentPlanDetails()
      this.NextRepaymentDetails()
    }
  },
  mounted() {
    const savingAccountList = JSON.parse(
      window.sessionStorage.getItem('savingaccountlist')
    )
    const currentaccountlist = JSON.parse(
      window.sessionStorage.getItem('currentaccountlist')
    )
    for (var i = 0; i < savingAccountList.length; i++) {
      if (savingAccountList[i].accountStatus === 'A') {
        this.debitAccountList[0].options.push({
          label: savingAccountList[i].accountNumber,
          value: savingAccountList[i].accountNumber
        })
      }
    }
    for (var i = 0; i < currentaccountlist.length; i++) {
      if (currentaccountlist[i].accountStatus === 'A') {
        this.debitAccountList[1].options.push({
          label: currentaccountlist[i].accountNumber,
          value: currentaccountlist[i].accountNumber
        })
      }
    }
    this.debitaccountnumber = this.debitAccountList[0].options[0].value
  },
  methods: {
    RepaymentPlanDetails() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.CNLBSGateway}/loan-experience/mortgage/repaymentPlanRetrieval`, _this.repaymentPlan, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            const contractPlan = response.data.data.contractPlan
            _this.contractPlan1 = []
            _this.contractPlan2 = []
            for (let i = 0; i < contractPlan.length; i++) {
              if (contractPlan[i].repaymentstatus === 'paid off') {
                _this.contractPlan2.push(contractPlan[i])
              } else {
                _this.contractPlan1.push(contractPlan[i])
              }
            }
          } else {
            _this.$mui.alert(response.data.msg, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    NextRepaymentDetails() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.CNLBSGateway}/loan-experience/mortgage/nextRepaymentEnquiry`, _this.repaymentPlan, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            this.nextRepayment = response.data.data
            // console.log(this.nextRepayment)
          } else {
            _this.$mui.alert(response.data.msg, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    openPaymentPage() {
      this.paymentDialogVisible = true
    },
    Repayment() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.CNLBSGateway}/loan-experience/mortgage/repayment`,
        {
          accountnumber: _this.nextRepayment.accountnumber,
          contractnumber: _this.nextRepayment.contractnumber,
          repaymentaccountnumber: _this.debitaccountnumber,
          repaymentamount: _this.nextRepayment.totalpayment
        }
        , { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.paymentDialogVisible = false
            _this.$mui.alert(_this.$t('lbs.TransactionSuccess'), _this.$t('lbs.common.success'), _this.$t('lbs.common.confirm'), function() {
              _this.NextRepaymentDetails()
              _this.RepaymentPlanDetails()
            })
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    }
  }
}
</script>

<style lang="scss" scoped>

  .repaymentplan {

    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

    .main {
      margin: 20px 60px;
    }

    .loan-account {
      padding-top: 3px;
      font-size: 20px;
      margin-bottom: 0;
    }

    .item-prop {
      font-size: 15px;
      display: inline-block;
      width: 30%;
      margin-left: 20px;
    }

  }

</style>
