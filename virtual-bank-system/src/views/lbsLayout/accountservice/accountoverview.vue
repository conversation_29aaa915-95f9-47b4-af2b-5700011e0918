<template>
  <div class="accountoverview">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="title-1">{{ $t('lbs.accountoverview') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col v-for="(item,index) in accountList" :key="index" class="el-col el-col-24 el-col-xs-24 el-col-md-12 el-col-lg-8 el-col-xl-6">
        <div v-if="item.accountType === 'Credit Card'" class="card-item" style="position:relative;" @click="accountDetail(item.accountNumber,item.accountType)">
          <svg-icon icon-class="visa" style="width: 80px; height: 60px;position: absolute;right: 30px;top: 60px" />
          <p>{{ $t('lbsTips.dashboardTip.CreditCardtitile') }}</p>
          <span>{{ item.ccy + ' ' + $lbs.decimal_format(item.availablebalance) }}</span>
          <hr>
          <span class="span-number">{{ item.accountNumber }}</span>
        </div>
        <div v-else class="card-item" @click="accountDetail(item.accountNumber,item.accountType)">
          <p v-if="item.accountType === 'Saving'">{{ $t('lbsTips.dashboardTip.Savingtitile') }}</p>
          <p v-else-if="item.accountType === 'Current'">{{ $t('lbsTips.dashboardTip.Currenttitile') }}</p>
          <span>{{ item.ccy + ' ' + $lbs.decimal_format(item.availablebalance) }}</span>
          <hr>
          <span class="span-number">{{ item.accountNumber }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Accountoverview',
  data() {
    return {
      accountList: []
    }
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupoverview'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.accountList = JSON.parse(window.sessionStorage.getItem('accountList'))
    this.cardSort()
  },
  methods: {
    accountDetail(accountNumber, accountType) {
      var url = ''
      if (accountType === 'Credit Card') {
        url = '/lbsaccountservice/creditcarddetail'
        window.sessionStorage.setItem('creditcardNumber', accountNumber)
        window.sessionStorage.setItem('type', accountType)
      } else {
        url = '/lbsaccountservice/accountdetail'
        window.sessionStorage.setItem('accountNumber', accountNumber)
        window.sessionStorage.setItem('accountType', accountType)
      }
      this.$router.push({ path: url })
    },
    cardSort() {
      const savingList = []
      const currentList = []
      const creditCardList = []
      for (let i = 0; i < this.accountList.length; i++) {
        if (this.accountList[i].accountType === 'Saving') {
          savingList.push(this.accountList[i])
        }
        if (this.accountList[i].accountType === 'Current') {
          currentList.push(this.accountList[i])
        }
        if (this.accountList[i].accountType === 'Credit Card') {
          creditCardList.push(this.accountList[i])
        }
      }
      this.accountList = savingList.concat(currentList).concat(creditCardList)
    }
  }
}
</script>

<style scoped>
  .accountoverview .title {
    padding: 18px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .accountoverview .card-item {
    margin: 40px 20px 0 20px;
    padding: 20px 20px;
    background-color: #FFFFFF;
    border-radius: 40px;
    box-shadow: 5px 5px 7px #bbbbbb;
    cursor: pointer;
  }

  .accountoverview .title .title-1{
    font-size: 50px;
    margin-top: 25px;
    margin-bottom: 15px;
    color: #22C1E6;
    line-height: 1;
  }

  .accountoverview .card-item p {
    font-size: 35px;
    color: #707070;
    margin: 0 0 80px 20px;
    line-height: 1;
  }

  .accountoverview span {
    font-size: 30px;
    color: #707070;
    line-height: 1;
  }

  .accountoverview .card-item span {
    font-size: 20px;
    color: #707070;
    margin: 0 0 45px 15px;
  }

  .accountoverview .card-item .span-number {
    color: #707070;
    font-size: 16px;
    margin-left: 20px;
  }

  hr {
    margin: 20px 0 15px;
  }
</style>
<style>

.el-message-box {
    width: 800px;
}
</style>
