<template>
  <div v-loading.fullscreen.lock="loading" class="index">
    <el-row>
      <el-col :span="24">
        <p style="font-size: 23px;line-height: 50px">{{ $t('lbs.changeLimitTip1') }}</p>
        <p style="color: #000000;font-size: 23px;line-height: 70px">{{ $t('lbs.changeLimitTip2') }}</p>
        <p style="font-size: 23px;line-height: 50px">{{ $t('lbs.changeLimitTip3') }}</p>
        <hr>
        <p style="color: #22C1E6;margin: 20px 0 20px;font-size: 30px">{{ $t('lbs.changeLimitTip4') }}</p>
        <el-form ref="form" :model="form" style="padding: 40px 0 0 200px;">
          <el-form-item label="">
            <el-select v-model="form.region" placeholder="" style="width: 85%;" @change="getCreditCardDetails()">
              <el-option v-for="(item,index) in creditCardaccountlist" :key="index" :label="item.value" :value="item.value" @change="getCreditCardDetails(item.value)" />
            </el-select>
          </el-form-item>
          <p>{{ $t('lbs.existingLimit') }}{{ accountDetail.ccyCode + ' ' + $lbs.decimal_format(accountDetail.approvedLimit) }}</p>
          <el-form-item style="margin-top: 60px">
            <el-button type="primary" style="float: right" @click="onSubmit('continue')">{{ $t('lbs.Continue') }}</el-button>
            <el-button type="danger" style="float: right;margin-right: 20px" @click="onSubmit('back')">{{ $t('lbs.cancel') }}</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {
      loading: false,
      token: null,
      creditCardaccountlist: [],
      accountDetail: {},
      form: {
        region: ''
      }
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.creditCardaccountlist = this.getcreditcardnumberpickerdata(window.sessionStorage.getItem('creditCardaccountlist'))
    this.form.region = this.creditCardaccountlist[0].value
    this.getCreditCardDetails()
  },
  methods: {
    getCreditCardDetails() {
      var vue = this
      var requestdata = {
        'creditcardnumber': this.form.region
      }

      this.$mui.ajax(this.LBSGateway + '/creditcard-experience/creditcard/creditLimitDetails', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json'
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            vue.accountDetail = data.data
            vue.accountDetail['accountNumber'] = vue.form.region
            console.log(JSON.stringify(vue.accountDetail))
            window.sessionStorage.setItem('changeLimitAccount', JSON.stringify(vue.accountDetail))
          } else {
            vue.$mui.alert('Get account info failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get account info failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get account info failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    getcreditcardnumberpickerdata(list) {
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      return pickerdata
    },
    onSubmit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/lbsaccountservice/creditcarddetail' })
      } else {
        this.$router.push({ path: '/lbsaccountservice/changecreditlimit/selectchangetype' })
      }
    }
  }
}
</script>

<style>
  .index input[type=text] {
    margin-bottom: 0;
  }

  .index .el-input--medium .el-input__inner {
    height: 60px;
    font-size: 18px;
    color: #707070;
  }
</style>
