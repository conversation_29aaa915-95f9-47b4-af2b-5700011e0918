<template>
  <div class="cancellationinfo">
    <el-row>
      <el-col :span="24">
        <p class="info">{{ $t('lbs.provideInformation') }}</p>
        <hr style="margin: 0">
        <p class="title">{{ $t('lbs.selectedCard') }}</p>
        <div class="main">
          <div class="div-select-info">
            <el-row>
              <el-col :xs="24" :lg="8">
                <p class="p-select-info">
                  {{ $t('lbs.visaCard') }}
                  <span class="min-text">{{ cancellationAccount.accountNumber }}</span>
                </p>
              </el-col>
              <el-col :xs="24" :lg="8">
                <p class="p-select-info">Chan <PERSON> Ming</p>
              </el-col>
              <el-col :xs="24" :lg="8">
                <p class="p-select-info min-text">
                  {{ $t('lbs.existingLimit') }}
                  {{ cancellationAccount.ccyCode + ' ' + $lbs.decimal_format(cancellationAccount.approvedLimit) }}
                </p>
              </el-col>
            </el-row>
          </div>
          <p class="title-2">{{ $t('lbs.moreInformation') }}</p>
          <el-row>
            <el-col :lg="{span: 10,offset: 2}" :xs="24">
              <p class="text-select">{{ $t('lbs.whyCancel') }}</p>
            </el-col>
            <el-col :lg="12" :xs="24">
              <el-select v-model="reason" :placeholder="$t('lbs.selectReason')" style="width: 400px">
                <el-option label="Many Cards" value="Many Cards" />
              </el-select>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="button-group">
          <el-button type="danger" @click="submit('back')">{{ $t('lbs.cancel') }}</el-button>
          <el-button type="primary" @click="submit('continue')">{{ $t('lbs.Continue') }}</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Cancellationinfo',
  data() {
    return {
      token: null,
      cancellationAccount: {},
      reason: ''
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.cancellationAccount = JSON.parse(
      window.sessionStorage.getItem('cancellationAccount')
    )
    if (this.cancellationAccount.reason) {
      this.reason = this.cancellationAccount.reason
    }
  },
  methods: {
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/lbsaccountservice/creditcarddetail' })
      } else {
        if (this.reason === '') {
          this.$mui.alert(
            this.$t('lbs.item4'),
            'Error',
            'OK'
          )
        } else {
          this.cancellationAccount['reason'] = this.reason
          window.sessionStorage.setItem(
            'cancellationAccount',
            JSON.stringify(this.cancellationAccount)
          )
          this.$router.push({
            path: '/lbsaccountservice/creditcardcancellation/cancellationform'
          })
        }
      }
    }
  }
}
</script>

<style scoped>
.cancellationinfo p {
  color: #707070;
}

.cancellationinfo .info {
  font-size: 18px;
  line-height: 60px;
}

.cancellationinfo .title {
  font-size: 30px;
  color: #22c1e6;
  margin: 30px 0 30px;
}

.cancellationinfo .div-select-info {
  width: 100%;
  height: auto;
  border: 1px #22c1e6 solid;
  padding: 0 0 30px;
}

.cancellationinfo .p-select-info {
  margin: 30px 0 0 0;
  font-size: 20px;
  text-align: center;
}

.cancellationinfo .min-text {
  font-size: 17px;
}

.cancellationinfo .title-2 {
  line-height: 80px;
  font-size: 22px;
}

.cancellationinfo .main {
  padding: 20px;
}

.cancellationinfo .text-select {
  font-size: 20px;
}

.cancellationinfo .button-group {
  margin: 30px 20px 20px 0;
  float: right;
  min-width: 200px;
}
</style>

<style>
.cancellationinfo .el-input__icon {
  height: 36px;
}

.cancellationinfo .el-input__inner {
  margin-bottom: 0;
}
</style>
