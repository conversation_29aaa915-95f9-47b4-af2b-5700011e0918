<template>
  <div v-loading.fullscreen.lock="loading" class="creditcarddetail">
    <el-row>
      <el-col :span="24">
        <div class="title" style="position: relative">
          <el-tooltip effect="dark" placement="bottom">
            <div
              slot="content"
              style="width: 300px"
            >{{ $t('lbsTips.creditcardTips.tip1') }}
            </div>
            <p>{{ $t('lbsTips.creditcardTips.tip2') }}</p>
          </el-tooltip>
          <div style="position: absolute;top:0;right: 40px;">
            <p style="text-align: center">{{ $t('lbs.switchAccounts') }}</p>
            <div
              v-if="saving"
              class="top-card"
              style="margin-right: 10px;background-color: #FFFF93;position: relative;padding:10px"
              @click="accountDetails(saving.accountNumber,saving.accountType)"
            >
              <p
                style="position: absolute;left: 25px;top: 10px;line-height: 1;font-size: 18px"
              >{{ $t('lbsTips.dashboardTip.Savingtitile') }}</p>
              <hr style="margin-top: 60px">
              <span
                style="font-size: 12px;color: #707070;position: absolute;bottom: 8px;left: 25px"
              >{{ saving.accountNumber }}</span>
            </div>
            <div
              v-if="current"
              class="top-card"
              style="margin-right: 10px;background-color: #CCE4FF;position: relative;padding:10px"
              @click="accountDetails(current.accountNumber,current.accountType)"
            >
              <p
                style="position: absolute;left: 25px;top: 10px;line-height: 1;font-size: 18px"
              >{{ $t('lbsTips.dashboardTip.Currenttitile') }}</p>
              <hr style="margin-top: 60px">
              <span
                style="font-size: 12px;color: #707070;position: absolute;bottom: 8px;left: 25px"
              >{{ current.accountNumber }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" :lg="7" :xs="24" :md="24">
        <div class="card-item" style="background-color: #DEFFC6;position: relative">
          <svg-icon
            icon-class="visa"
            style="width: 80px;height: 80px;position: absolute;right: 40px;top: 60px"
          />
          <p
            style="margin-bottom: 60px;margin-left: 12px;font-size: 40px;color: #707070;line-height: 1"
          >{{ $t('lbsTips.dashboardTip.CreditCardtitile') }}</p>
          <p style="margin-bottom: 50px;font-size: 22px;line-height: 1">
            {{ accountDetail.ccyCode + ' ' +
              $lbs.decimal_format(Number(accountDetail.availableLimit)) }}
          </p>
          <hr>
          <p
            style="margin-left: 10px;margin-top: 15px;font-size: 18px;line-height: 1"
          >{{ accountNumber }}</p>
        </div>
      </el-col>
      <el-col :span="17">
        <div style="padding-left: 30px">
          <el-row>
            <el-tooltip effect="dark" placement="right">
              <div
                slot="content"
                style="width: 300px"
              >{{ $t('lbsTips.creditcardTips.tip3') }}
              </div>
              <p
                style="margin: 40px 0 10px;color: #22C1E6;font-size: 25px;line-height: 1;width: 180px"
              >{{ $t('lbs.accountDetails') }}</p>
            </el-tooltip>
            <table
              style="border: 1px #E2E2E2 solid;height: 160px;width: 97%;color: #707070;font-size: 15px;box-shadow: 3px 3px 3px #bbbbbb;"
            >
              <tr style="border-bottom:1px #E2E2E2 solid">
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.AccountNo') }}</div>
                  <td style="padding-left: 10px">{{ $t('lbs.accountNo') }}</td>
                </el-tooltip>
                <td>{{ accountNumber }}</td>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.AvailableLimit') }}</div>
                  <td
                    style="border-left:1px #E2E2E2 solid;padding-left: 20px"
                  >{{ $t('lbs.availableLimit') }}
                  </td>
                </el-tooltip>
                <td>{{ $lbs.decimal_format(Number(accountDetail.availableLimit)) }}</td>
              </tr>
              <tr style="border-bottom:1px #E2E2E2 solid">
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.AccountType') }}</div>
                  <td style="padding-left: 10px">{{ $t('lbs.accountType') }}</td>
                </el-tooltip>
                <td>{{ $t('lbsTips.creditcardTips.tip4') }}</td>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.UsedLimit') }}</div>
                  <td
                    style="border-left:1px #E2E2E2 solid;padding-left: 20px"
                  >{{ $t('lbs.usedLimit') }}
                  </td>
                </el-tooltip>
                <td>{{ $lbs.decimal_format(Number(accountDetail.usedLimit)) }}</td>
              </tr>
              <tr>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.Currency') }}</div>
                  <td style="border-bottom:1px #E2E2E2 solid;padding-left: 10px">{{ $t('lbs.currency') }}</td>
                </el-tooltip>
                <td style="border-bottom:1px #E2E2E2 solid;">{{ accountDetail.ccyCode }}</td>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.ApprovedLimit') }}</div>
                  <td
                    style="border-bottom:1px #E2E2E2 solid;border-left:1px #E2E2E2 solid;padding-left: 20px"
                  >{{ $t('lbs.approvedLimit') }}
                  </td>
                </el-tooltip>
                <td style="border-bottom:1px #E2E2E2 solid;width: 240px">
                  {{ $lbs.decimal_format(Number(accountDetail.approvedLimit)) }}
                  <el-tooltip effect="dark" placement="top">
                    <div slot="content" style="width: 220px">{{ $t('lbsTips.creditcardTips.EditLimit') }}</div>
                    <el-button
                      size="mini"
                      type="primary"
                      style="height: 20px;line-height: 5px;margin-left: 10px"
                      @click="operations('editLimit')"
                    >{{ $t('lbs.editLimit') }}
                    </el-button>
                  </el-tooltip>
                </td>
              </tr>
              <tr>
                <td />
                <td />
                <td style="padding-left: 20px;border-left:1px #E2E2E2 solid;">{{ $t('lbs.CashAdvanceLimit') }}</td>
                <td>{{ $lbs.decimal_format(Number(accountDetail.cashAdvanceLimit)) }}</td>
              </tr>
            </table>
          </el-row>
          <el-row>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 200px">{{ $t('lbsTips.creditcardTips.CardLossReporting') }}</div>
              <el-button
                style="margin-top: 20px;float: right;margin-right: 50px;width: 240px"
                type="info"
                @click="operations('cardLossReporting')"
              >{{ $t('lbs.cardLossReporting') }}
              </el-button>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 200px">{{ $t('lbsTips.creditcardTips.CreditCardCancellation') }}</div>
              <el-button
                style="margin-top: 20px;float: right;margin-right: 20px;width: 240px"
                type="danger"
                @click="operations('creditCardCancellation')"
              >{{ $t('lbs.creditCardCancellation') }}
              </el-button>
            </el-tooltip>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="7">
        <p
          style="margin: 20px 0 10px 30px;color: #22C1E6;font-size: 25px;line-height: 1;width: 180px"
        >{{ $t('lbs.cardRewards') }}</p>
      </el-col>
      <el-tooltip effect="dark" placement="right">
        <div
          slot="content"
          style="width: 300px"
        >{{ $t('lbsTips.creditcardTips.tip7') }}
        </div>
        <el-col :span="17">
          <p
            style="margin: 20px 0 0 30px;color: #22C1E6;font-size: 25px;line-height: 1;display: inline-block"
          >{{ $t('lbs.statementDetails') }}</p>
        </el-col>
      </el-tooltip>
    </el-row>
    <el-row>
      <el-col :span="24" :lg="6" :xs="24" :md="6">
        <div
          style="margin: 0 0px 10px 60px;border: 1px #22C1E6 solid;padding: 15px;box-shadow: 3px 3px 3px #bbbbbb;"
        >
          <div style="width: 30px;height: 30px;margin: 0 auto">
            <svg-icon icon-class="awesome-gift" style="width: 30px;height: 30px;" />
          </div>
          <el-tooltip class="item" effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.RewardPoints') }}</div>
            <p
              style="color: #22C1E6;margin-top: 10px;line-height: 1;font-size: 20px"
            >{{ $t('lbs.rewardPoints') }}</p>
          </el-tooltip>
          <p
            style="width: 100%;height: 50px;font-size: 18px;background-color: #6A6A6A;border: 1px solid #22C1E6;text-align: center;line-height: 50px;color: #22C1E6;"
          >{{ $t('lbsTips.creditcardTips.tip5') }}</p>
          <el-tooltip class="item" effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.ClosestExpiryDate') }}</div>
            <p
              style="color: #22C1E6;margin-top: 10px;line-height: 1;font-size: 20px"
            >{{ $t('lbs.closestExpiryDate') }}</p>
          </el-tooltip>
          <p
            style="width: 100%;height: 50px;font-size: 18px;background-color: #6A6A6A;border: 1px solid #22C1E6;text-align: center;line-height: 50px;color: #22C1E6;"
          >{{ $t('lbsTips.creditcardTips.tip6') }}</p>
          <u style="color: #22C1E6">
            <p
              style="color: #22C1E6;margin-top: 10px;text-align: right;cursor: pointer"
              href="#"
            >{{ $t('lbs.viewMoreAboutPoints') }}</p>
          </u>
        </div>
      </el-col>
      <el-col :span="18">
        <div style="margin: 0 0 10px 95px">

          <table
            style="border: 1px #E2E2E2 solid;height: 160px;width: 97%;color: #707070;font-size: 15px;box-shadow: 3px 3px 3px #bbbbbb;"
          >
            <tr style="border-bottom:1px #E2E2E2 solid">
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.LastStatementDate') }}</div>
                <td style="padding-left: 10px">{{ $t('lbs.lastStatementDate') }}</td>
              </el-tooltip>
              <td>{{ $moment.parseZone(repaymentDetail.statementdate).local().format('YYYY-MM-DD') }}</td>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.PaymentDueDate') }}</div>
                <td
                  style="border-left:1px #E2E2E2 solid;padding-left: 20px"
                >{{ $t('lbs.paymentDueDate') }}
                </td>
              </el-tooltip>
              <td>{{ $moment.parseZone(repaymentDetail.repaymentduedate).local().format('YYYY-MM-DD') }}</td>
            </tr>
            <tr style="border-bottom:1px #E2E2E2 solid">
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.StatementBalance') }}</div>
                <td style="padding-left: 10px">{{ $t('lbs.statementBalance') }}:</td>
              </el-tooltip>
              <td>{{ 'HKD ' + $lbs.decimal_format(repaymentDetail.repaymentamount) }}</td>
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.PayMinimumAmount') }}</div>
                <td
                  style="border-left:1px #E2E2E2 solid;padding-left: 20px"
                >{{ $t('lbs.minimumPaymentAmount') }}:
                </td>
              </el-tooltip>
              <td>{{ 'HKD ' + $lbs.decimal_format(repaymentDetail.minimumpayment) }}</td>
            </tr>
            <tr>
              <td colspan="4">
                <el-tooltip effect="dark" placement="bottom">
                  <div
                    slot="content"
                    style="width: 220px"
                  >{{ $t('lbsTips.creditcardTips.tip8') }}
                  </div>
                  <el-button
                    style="float: right;margin-right: 10px"
                    type="primary"
                    @click="operations('repayment')"
                  >{{ $t('lbs.repayment') }}
                  </el-button>
                </el-tooltip>
              </td>
            </tr>
          </table>
        </div>
      </el-col>
      <el-col :span="18">
        <el-tooltip effect="dark" placement="right">
          <div
            slot="content"
            style="width: 300px"
          >{{ $t('lbsTips.creditcardTips.tip9') }}
          </div>
          <p
            style="margin: 20px 0 10px 95px;color: #22C1E6;font-size: 25px;line-height: 1;width: 220px"
          >{{ $t('lbs.transactionHistory') }}</p>
        </el-tooltip>
        <div
          style="border: 1px #E2E2E2 solid;padding: 20px;margin: 0 40px 0 100px;background-color: #ffffff"
        >
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.creditcardTips.Period') }}</div>
            <span style="margin-right: 30px;color: #707070;font-size: 15px">{{ $t('lbs.period') }}:</span>
          </el-tooltip>
          <el-date-picker
            id="date1"
            v-model="value1"
            style="margin-bottom: 10px"
            align="right"
            type="date"
            :picker-options="pickerOptions1"
            :default-time="'00:00:00'"
            @change="getaccounthistory()"
          />
          <span style="margin: 0 30px 0;color: #707070;font-size: 15px">{{ $t('lbs.to') }}</span>
          <el-date-picker
            id="date2"
            v-model="value2"
            align="right"
            type="date"
            :picker-options="pickerOptions2"
            :default-time="'23:59:59'"
            @change="getaccounthistory()"
          />
          <el-table
            :data="transactionHistory"
            border
            height="230"
            style="width: 100%;height: 260px"
          >
            <el-table-column prop="transactiontime" :label="$t('lbs.date')" />
            <el-table-column prop="type" :label="$t('lbs.consumptionTypes')" />
            <el-table-column prop="bookingamount" :label="$t('lbs.transactionAmount')" />
            <el-table-column prop="merchantname" :label="$t('lbs.merchantname')" />
          </el-table>
        </div>

      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Creditcarddetail',
  data() {
    return {
      loading: false,
      token: null,
      accountNumber: '',
      accountType: '',
      accountList: [],
      current: [],
      saving: [],
      accountDetail: {},
      repaymentDetail: {},
      transactionHistory: [],
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: this.$t('lbs.aWeekAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            }
          },
          {
            text: this.$t('lbs.aMonthAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', date)
            }
          },
          {
            text: this.$t('lbs.threeMonthsAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', date)
            }
          }
        ]
      },
      pickerOptions2: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: this.$t('lbs.today'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime())
              picker.$emit('pick', date)
            }
          }
        ]
      },
      value1: this.$moment
        .parseZone(new Date().getTime() - 1000 * 60 * 60 * 24 * 30)
        .local()
        .format('YYYY-MM-DD'),
      value2: this.$moment
        .parseZone(new Date().getTime())
        .local()
        .format('YYYY-MM-DD')
    }
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupCreditCard'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.token = window.sessionStorage.getItem('token')
    this.accountNumber = window.sessionStorage.getItem('creditcardNumber')
    this.accountType = window.sessionStorage.getItem('type')
    this.accountList = JSON.parse(window.sessionStorage.getItem('accountList'))
    this.topCard()
    this.getaccountbalance()
    this.getacreditcardRepayment()
    this.getaccounthistory()
  },
  methods: {
    accountDetails(accountNumber, accountType) {
      window.sessionStorage.setItem('accountNumber', accountNumber)
      window.sessionStorage.setItem('accountType', accountType)
      this.$router.push({ path: '/lbsaccountservice/accountdetail' })
    },
    topCard() {
      this.current = []
      this.saving = []
      var temp = []
      for (var i = 0; i < this.accountList.length; i++) {
        if (this.accountList[i].accountType !== this.accountType) {
          temp.push(this.accountList[i])
        }
      }

      for (var j = 0; j < temp.length; j++) {
        if (temp[j].accountType === 'Saving') {
          this.saving.push(temp[j])
        } else {
          this.current.push(temp[j])
        }
      }
      this.saving = this.saving[0]
      this.current = this.current[0]
    },
    getaccountbalance() {
      var vue = this
      var requestdata = {
        creditcardnumber: this.accountNumber
      }

      this.$mui.ajax(
        this.LBSGateway +
          '/creditcard-experience/creditcard/creditLimitDetails',
        {
          data: requestdata,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json'
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              vue.accountDetail = data.data
              console.log(JSON.stringify(vue.accountDetail))
            } else {
              vue.$mui.alert(
                'Get account info failed! The response is: \n' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get account info failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get account info failed. Time out!'
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      )
    },
    getacreditcardRepayment() {
      var vue = this
      var requestdata4 = {
        creditcardnumber: this.accountNumber
      }
      console.log(JSON.stringify(requestdata4))
      this.$mui.ajax(
        this.LBSGateway +
          '/creditcard-experience/creditcard/outstandingPayment',
        {
          data: requestdata4,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json'
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            // mui.alert(JSON.stringify(data))
            if (data.code === '200') {
              vue.accountinfo = data.data
              var response4 = data.data
              // Account Info
              if (response4) {
                vue.repaymentDetail = response4
                console.log(vue.repaymentDetail)
                // document.getElementById("repaymentamount").innerHTML = response4.repaymentamount + " HKD"
                // document.getElementById("minimumpayment").innerHTML = response4.minimumpayment + " HKD"
                // document.getElementById("repaymentduedate").innerHTML = vue.$moment.parseZone(response4.repaymentduedate).local().format('YYYY-MM-DD')
                // document.getElementById("statementdate").innerHTML = vue.$moment.parseZone(response4.statementdate).local().format('YYYY-MM-DD')
              } else {
                vue.$mui.alert(
                  'Get account info failed! The response is: \n' +
                    JSON.stringify(data) +
                    '.',
                  'Error',
                  'OK'
                )
              }
            } else {
              vue.$mui.alert(
                'Get account info failed! The response is: \n' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get account info failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get account info failed. Time out!'
            }

            if (
              typeof JSON.parse(xhr.responseText) === 'object' &&
                JSON.parse(xhr.responseText).code === '404003'
            ) {
              document.getElementById('transactionlog').innerHTML =
                  "<li class='mui-table-view-cell mui-text-center'><b>Transaction History</b></li><li class='mui-table-view-cell mui-text-center'>No Data</li>"
              document
                .getElementById('transactionlog_area')
                .classList.remove('hidden')
            } else {
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        }
      )
    },
    getaccounthistory() {
      var vue = this
      var fromdate = new Date(this.value1).getTime()
      var todate = new Date(this.value2).getTime() + ********
      var requestdata4 = {
        creditcardnumber: this.accountNumber,
        index: 0,
        items: 9999,
        transFromDate: fromdate,
        transToDate: todate
      }
      console.log(JSON.stringify(requestdata4))
      this.$mui.ajax(
        this.LBSGateway +
          '/creditcard-experience/creditcard/transactionDetails',
        {
          data: requestdata4,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json'
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            // mui.alert(JSON.stringify(data))
            if (data.code === '200') {
              var response4 = data.data
              // Account Info
              if (response4) {
                var account4 = response4
                account4.sort(function(a, b) {
                  return (
                    Date.parse(Number(b.transactiontime)) -
                      Date.parse(Number(a.transactiontime))
                  ) // 时间正序
                })

                var requesttimedata = {
                  item: 'Systemdate'
                }
                vue.$mui.ajax(
                  vue.LBSGateway +
                    '/sysadmin-experience/sysadmin/systemParameterRetrieval',
                  {
                    data: requesttimedata,
                    dataType: 'json', // 服务器返回json格式数据
                    type: 'post', // HTTP请求类型
                    timeout: 60000,
                    headers: {
                      accept: '*/*',
                      token: vue.token,
                      'Content-Type': 'application/json'
                    },
                    beforeSend: function() {
                      vue.loading = true
                    },
                    complete: function() {
                      vue.loading = false
                    },
                    success: function(data1) {
                      account4.sort(function(a, b) {
                        return (
                          Date.parse(Number(b.transactiontime)) -
                            Date.parse(Number(a.transactiontime))
                        ) // 时间正序
                      })
                      vue.transactionHistory = account4
                      console.log(vue.transactionHistory)
                      for (var i = 0; i < vue.transactionHistory.length; i++) {
                        vue.transactionHistory[i].transactiontime = vue.$moment.parseZone(Number(vue.transactionHistory[i].transactiontime)).local().format('YYYY-MM-DD')
                        vue.transactionHistory[i].bookingamount = vue.transactionHistory[i].bookingccy + ' ' + vue.$lbs.decimal_format(vue.transactionHistory[i].bookingamount)
                        if (vue.transactionHistory[i].merchantname === null) {
                          vue.transactionHistory[i]['type'] = 'Repayment'
                          vue.transactionHistory[i].bookingamount = '- ' + vue.transactionHistory[i].bookingamount
                        } else {
                          vue.transactionHistory[i]['type'] = 'Expenditure'
                        }
                      }
                    },
                    error: function(xhr, type, errorThrown) {
                      var msg =
                          'Get System Date failed! The response is: \n' +
                          xhr.responseText +
                          '.'
                      if (type === 'timeout') {
                        msg = 'Get System Date failed. Time out!'
                      }
                      vue.$mui.alert(msg, 'Error', 'OK')
                    }
                  }
                )
              }
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get account info failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get account info failed. Time out!'
            }

            if (
              typeof JSON.parse(xhr.responseText) === 'object' &&
                JSON.parse(xhr.responseText).code === '404003'
            ) {
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        }
      )
    },
    operations(type) {
      if (type === 'editLimit') {
        this.$router.push({ path: '/lbsaccountservice/changecreditlimit' })
      } else if (type === 'cardLossReporting') {
        this.$router.push({ path: '/lbsaccountservice/cardlossreporting' })
      } else if (type === 'creditCardCancellation') {
        this.$router.push({ path: '/lbsaccountservice/creditcardcancellation' })
      } else if (type === 'repayment') {
        this.$router.push({ path: '/lbsaccountservice/repayment' })
      }
    }
  }
}
</script>

<style scoped>
  .creditcarddetail .title {
    padding: 18px 28px;
    height: 140px;
    background-color: #ffffff;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .creditcarddetail .title > p {
    font-size: 50px;
    color: #22c1e6;
    margin-bottom: 20px;
    margin-top: 25px;
    line-height: 1;
    display: inline-block;
    /*line-height: normal;*/
  }

  .creditcarddetail .title span {
    font-size: 30px;
    line-height: 1;
    color: #707070;
  }

  .creditcarddetail h4 {
    color: #707070;
  }

  .creditcarddetail p {
    color: #707070;
    margin: 10px 0 0 0;
  }

  .creditcarddetail .top-card {
    border-radius: 50px;
    width: 250px;
    height: 100px;
    display: inline-block;
    cursor: pointer;
  }

  .creditcarddetail .card-item {
    margin: 40px 20px 0 20px;
    padding: 20px 20px;
    background-color: #ffffff;
    border-radius: 40px;
    box-shadow: 3px 3px 3px #bbbbbb;
  }
</style>

<style>
  .creditcarddetail .el-input--medium .el-input__inner {
    height: 36px;
    font-size: 15px;
    color: #707070;
  }

  .creditcarddetail input[type="text"] {
    margin-bottom: 0;
  }

  .el-message-box {
    width: 800px;
  }
</style>
