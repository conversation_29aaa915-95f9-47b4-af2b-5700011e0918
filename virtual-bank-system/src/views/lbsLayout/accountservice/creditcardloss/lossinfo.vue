<template>
  <div v-loading.fullscreen.lock="loading" class="lossinfo">
    <el-row>
      <el-col :span="24">
        <p class="info">{{ $t('lbs.lossInformation') }}</p>
        <hr style="margin: 0">
        <div class="main">
          <p class="title">{{ $t('lbs.selectedCard') }}</p>
          <div class="div-select-info">
            <el-row>
              <el-col :xs="24" :lg="8">
                <p class="p-select-info">
                  {{ $t('lbs.visaCard') }}
                  <span class="min-text">{{ lossAccount.accountNumber }}</span>
                </p>
              </el-col>
              <el-col :xs="24" :lg="8">
                <p class="p-select-info"><PERSON></p>
              </el-col>
              <el-col :xs="24" :lg="8">
                <p class="p-select-info min-text">
                  {{ $t('lbs.existingLimit') }}
                  {{ lossAccount.ccyCode + ' ' + $lbs.decimal_format(lossAccount.approvedLimit) }}
                </p>
              </el-col>
            </el-row>
          </div>
          <p class="title-2">{{ $t('lbs.moreInformation') }}</p>
          <div style="text-align: center;margin-bottom: 30px">
            <div style="display: inline-block;width: 33%">
              <p class="text-select">{{ $t('lbs.lossReason') }}</p>
            </div>
            <div style="display: inline-block;width: 66%">
              <el-select
                v-model="reason"
                :placeholder="$t('lbs.selectLossReason')"
                class="select-picker"
              >
                <el-option label="Stolen" value="Stolen" />
              </el-select>
            </div>
          </div>
          <div style="text-align: center;margin-bottom: 15px">
            <div style="display: inline-block;width: 33%">
              <p class="text-select">{{ $t('lbs.happenDid') }}</p>
            </div>
            <div style="display: inline-block;width: 66%">
              <el-select
                v-model="happenwhere"
                :placeholder="$t('lbs.selectLossSite')"
                class="select-picker"
              >
                <el-option label="Other" value="Other" />
              </el-select>
            </div>
          </div>
          <div style="text-align: center">
            <div style="display: inline-block;width: 33%">
              <p class="text-select">{{ $t('lbs.happenTime') }}</p>
            </div>
            <div style="display: inline-block;width: 66%">
              <div class="select-item">
                <el-date-picker
                  id="date"
                  v-model="happendate"
                  :placeholder="$t('lbs.selectLossDate')"
                  class="select-picker"
                  align="right"
                  type="date"
                  :picker-options="pickerOptions"
                />
              </div>
            </div>
          </div>
          <!--<el-row>
            <el-col :lg="{span: 10,offset: 2}" :xs="24">
              <div class="select-item">
                <p class="text-select">{{$t('lbs.lossReason')}}</p>
              </div>
            </el-col>
            <el-col :lg="11" :xs="24">
              <div class="select-item">
                <el-select
                  :placeholder="$t('lbs.selectLossReason')"
                  v-model="reason"
                  class="select-picker"
                >
                  <el-option label="Stolen" value="Stolen"></el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :lg="{span: 10,offset: 2}" :xs="24">
              <div class="select-item">
                <p class="text-select">{{$t('lbs.happenDid')}}</p>
              </div>
            </el-col>
            <el-col :lg="12" :xs="24">
              <div class="select-item">
                <el-select
                  :placeholder="$t('lbs.selectLossSite')"
                  v-model="happenwhere"
                  class="select-picker"
                >
                  <el-option label="Other" value="Other"></el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :lg="{span: 10,offset: 2}" :xs="24">
              <div class="select-item">
                <p class="text-select">{{$t('lbs.happenDid')}}</p>
              </div>
            </el-col>
            <el-col :lg="12" :xs="24">
              <div class="select-item">
                <el-date-picker
                  :placeholder="$t('lbs.selectLossDate')"
                  class="select-picker"
                  id="date"
                  v-model="happendate"
                  align="right"
                  type="date"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </div>
            </el-col>
          </el-row>-->
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="button-group">
          <el-button type="danger" @click="submit('back')">{{ $t('lbs.cancel') }}</el-button>
          <el-button type="primary" @click="submit('continue')">{{ $t('lbs.Continue') }}</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Lossinfo',
  data() {
    return {
      loading: false,
      token: null,
      lossAccount: {},
      reason: null,
      happenwhere: null,
      happendate: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.lossAccount = JSON.parse(window.sessionStorage.getItem('lossAccount'))
    if (
      this.lossAccount.reason &&
        this.lossAccount.happenwhere &&
        this.lossAccount.happendate
    ) {
      this.reason = this.lossAccount.reason
      this.happenwhere = this.lossAccount.happenwhere
      this.happendate = this.lossAccount.happendate
    }
  },
  methods: {
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/lbsaccountservice/creditcarddetail' })
      } else {
        if (this.reason === null) {
          this.$mui.alert(
            this.$t('lbs.selectLossReason'),
            'Error',
            'OK'
          )
        } else if (this.happenwhere === null) {
          this.$mui.alert(
            this.$t('lbs.selectLossSite'),
            'Error',
            'OK'
          )
        } else if (this.happendate === null) {
          this.$mui.alert(
            this.$t('lbs.selectLossDate'),
            'Error',
            'OK'
          )
        } else {
          this.lossAccount['reason'] = this.reason
          this.lossAccount['happenwhere'] = this.happenwhere
          this.lossAccount['happendate'] = document.getElementById(
            'date'
          ).value
          window.sessionStorage.setItem(
            'lossAccount',
            JSON.stringify(this.lossAccount)
          )
          this.$router.push({
            path: '/lbsaccountservice/cardlossreporting/lossform'
          })
        }
      }
    }
  }
}
</script>

<style scoped>
  .lossinfo p {
    color: #707070;
  }

  .lossinfo .info {
    font-size: 18px;
    line-height: 60px;
  }

  .lossinfo .title {
    font-size: 30px;
    color: #22c1e6;
    margin: 30px 0 30px;
  }

  .lossinfo .div-select-info {
    width: 100%;
    height: auto;
    border: 1px #22c1e6 solid;
    padding: 0 0 30px;
  }

  .lossinfo .p-select-info {
    margin: 30px 0 0 0;
    font-size: 20px;
    text-align: center;
  }

  .lossinfo .min-text {
    font-size: 17px;
  }

  .lossinfo .title-2 {
    line-height: 80px;
    font-size: 22px;
  }

  .lossinfo .main {
    padding: 20px;
  }

  .lossinfo .text-select {
    font-size: 20px;
  }

  .lossinfo .button-group {
    margin: 30px 20px 20px 0;
    float: right;
    min-width: 200px;
  }

  .lossinfo .select-item {
    margin-top: 20px;
  }

  .lossinfo .select-picker {
    width: 400px;
  }
</style>

<style>
  .lossinfo .el-input__icon {
    height: 36px;
  }

  .lossinfo .el-input__inner {
    margin-bottom: 0;
  }
</style>
