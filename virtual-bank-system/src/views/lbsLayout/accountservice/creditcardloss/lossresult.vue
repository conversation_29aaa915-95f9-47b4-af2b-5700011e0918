<template>
  <div class="lossresult">
    <el-row>
      <el-col :lg="{span:18,offset:6}" :xs="24">
        <p class="successful successful-item">{{ $t("lbs.repostSuccessful") }}</p>
        <p class="successful">{{ $t("lbs.newCard") }}</p>
      </el-col>
    </el-row>
    <el-row>
      <el-col :lg="{span:1,offset:16}" :xs="{span:16,offset:8}">
        <el-button class="lbs-button" type="primary" @click="submit()">{{ $t("lbs.Done") }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Lossresult',
  methods: {
    submit() {
      this.$router.push({ path: '/lbsIndex' })
    }
  }
}
</script>

<style scoped>
  .lossresult .successful{
    font-size: 40px;
    line-height: 80px;
  }
  .lossresult .lbs-button{
    float: right;
  }

  .lossresult .successful-item{
    margin-top: 80px;
  }
</style>
