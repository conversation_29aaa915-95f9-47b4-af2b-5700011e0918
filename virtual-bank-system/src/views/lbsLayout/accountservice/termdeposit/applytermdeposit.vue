<template>
  <div v-loading.fullscreen.lock="loading" class="applytermdeposit">
    <el-row>
      <el-col :lg="10" :xs="24">
        <div class="main-left">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
            <p class="title">{{ $t('lbsTips.accountDetailTip.tip37') }}</p>
          </el-tooltip>
          <div class="div-info">
            <el-input
              v-model="amount"
              placeholder="Enter amount for term deposit"
              class="input-with-select"
              :maxlength="40"
            >
              <el-select slot="prepend" v-model="currency" placeholder style="width: 80px">
                <el-option label="HKD" value="HKD" />
              </el-select>
            </el-input>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DebitAccount') }}</div>
              <p class="title">{{ $t('lbsTips.accountDetailTip.tip38') }}</p>
            </el-tooltip>
            <el-select
              v-model="debitAccountNumber"
              :placeholder="$t('lbsTips.accountDetailTip.tip42')"
              class="el-input"
              @change="getBalance()"
            >
              <el-option
                v-for="(item,index) in debitList"
                :key="index"
                :label="item.type + ' ' + item.label"
                :value="item.value"
              />
            </el-select>
            <p style="color: #707070;font-size: 12px;margin-top: 3px">
              {{ `(${$t('lbs.AvailableBalance')}: ${settlementBalance})` }}</p>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.TermDepositAccount') }}</div>
              <p class="title">{{ $t('lbsTips.accountDetailTip.tip39') }}</p>
            </el-tooltip>
            <el-select
              v-model="termDepositAccountNumber"
              :placeholder="$t('lbsTips.accountDetailTip.tip46')"
              class="el-input"
            >
              <el-option
                v-for="(item,index) in termDepositList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DepositMaturity') }}</div>
              <p class="title">{{ $t('lbsTips.accountDetailTip.tip40') }}</p>
            </el-tooltip>
            <el-select
              v-model="depositMaturity"
              :placeholder="$t('lbsTips.accountDetailTip.tip47')"
              class="el-input"
            >
              <el-option
                v-for="(item,index) in PeriodPicker"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>
      </el-col>
      <el-col :lg="14" :xs="24">
        <div class="main-right">
          <p class="summary">{{ $t('lbsTips.accountDetailTip.tip41') }}</p>
          <el-row>
            <div style="height:120px;">
              <el-col :lg="9" :xs="24">
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
                  <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip37') }}:</p>
                </el-tooltip>
              </el-col>
              <el-col :lg="15" :xs="24">
                <p
                  v-if="amount.length === 0"
                  class="select-result"
                >{{ currency + ' ' + $lbs.decimal_format(0) }}</p>
                <p v-else class="select-result">{{ currency + ' ' + $lbs.decimal_format(amount) }}</p>
              </el-col>
            </div>
            <div style="height:120px;">
              <el-col :lg="9" :xs="24">
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DebitAccount') }}</div>
                  <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip38') }}:</p>
                </el-tooltip>
              </el-col>
              <el-col :lg="15" :xs="24">
                <p v-if="debitAccountNumber.length === 0" class="select-result">{{ $t('lbsTips.accountDetailTip.tip43') }}</p>
                <p v-else class="select-result">{{ debitAccountNumber }}</p>
              </el-col>
            </div>
            <div style="height:120px;">
              <el-col :lg="9" :xs="24">
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.TermDepositAccount') }}</div>
                  <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip39') }}:</p>
                </el-tooltip>
              </el-col>
              <el-col :lg="15" :xs="24">
                <p v-if="termDepositAccountNumber.length === 0" class="select-result">{{ $t('lbsTips.accountDetailTip.tip43') }}</p>
                <p v-else class="select-result">{{ termDepositAccountNumber }}</p>
              </el-col>
            </div>
            <div style="height:120px;">
              <el-col :lg="9" :xs="24">
                <el-tooltip effect="dark" placement="bottom">
                  <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DepositMaturity') }}</div>
                  <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip40') }}:</p>
                </el-tooltip>
              </el-col>
              <el-col :lg="15" :xs="24">
                <p class="select-result">{{ depositMaturity }}</p>
              </el-col>
            </div>
          </el-row>
          <el-col :span="24">
            <el-button class="button btn1" type="primary" @click="submit('next')">{{ $t('lbs.Next') }}</el-button>
            <el-button class="button btn2" type="danger" @click="submit('back')">{{ $t('lbs.Cancel') }}</el-button>
          </el-col>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Applytermdeposit',
  data() {
    return {
      loading: false,
      token: null,
      amount: '10000',
      currency: 'HKD',
      accountNumber: '',
      debitAccountNumber: '',
      termDepositAccountNumber: '',
      depositMaturity: '6months',
      PeriodPicker: [
        {
          label: '1 Day',
          value: '1day'
        },
        {
          value: '1week',
          label: '1 Week'
        },
        {
          value: '2weeks',
          label: '2 Weeks'
        },
        {
          value: '1month',
          label: '1 Month'
        },
        {
          value: '2months',
          label: '2 Months'
        },
        {
          value: '3months',
          label: '3 Months'
        },
        {
          value: '6months',
          label: '6 Months'
        },
        {
          value: '9months',
          label: '9 Months'
        },
        {
          value: '12months',
          label: '12 Months'
        }
      ],
      savingList: [],
      currentList: [],
      debitList: [],
      termDepositList: [],
      settlementBalance: 0
    }
  },
  watch: {
    amount: {
      handler() {
        this.clearNoNum()
      }
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupTermDeposit2'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.initdata()
  },
  methods: {
    // 获取结算账户余额
    getBalance() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.debitAccountNumber}`, {}, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          _this.settlementBalance = 0
          if (response.data.code === '200') {
            _this.settlementBalance = response.data.data.account.currencycode + ' ' + response.data.data.account.availablebalance
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    initdata() {
      const savingAccountList = JSON.parse(
        window.sessionStorage.getItem('savingaccountlist')
      )
      const currentaccountlist = JSON.parse(
        window.sessionStorage.getItem('currentaccountlist')
      )
      const termDepositaccountlist = JSON.parse(
        window.sessionStorage.getItem('termDepositaccountlist')
      )
      for (var i = 0; i < savingAccountList.length; i++) {
        if (savingAccountList[i].accountStatus === 'A') {
          this.savingList.push({
            label: savingAccountList[i].accountNumber,
            value: savingAccountList[i].accountNumber,
            type: 'Saving'
          })
        }
      }

      for (var k = 0; k < currentaccountlist.length; k++) {
        if (currentaccountlist[k].accountStatus === 'A') {
          this.currentList.push({
            label: currentaccountlist[k].accountNumber,
            value: currentaccountlist[k].accountNumber,
            type: 'Current'
          })
        }
      }

      for (var j = 0; j < termDepositaccountlist.length; j++) {
        if (termDepositaccountlist[j].accountStatus === 'A') {
          this.termDepositList.push({
            label: termDepositaccountlist[j].accountNumber,
            value: termDepositaccountlist[j].accountNumber
          })
        }
      }
      this.debitList = this.savingList.concat(this.currentList)
    },
    clearNoNum() {
      this.amount = this.amount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.amount = this.amount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.amount = this.amount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.amount = this.amount
        .replace('.', '$#$')
        .replace(/\./g, '')
        .replace('$#$', '.')
      this.amount = this.amount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    },
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/lbsaccountservice/termdeposit' })
      } else if (type === 'next') {
        if (
          !this.amount ||
          this.amount.length === 0 ||
          !(typeof Number(this.amount) && !isNaN(this.amount)) ||
          Number(this.amount) < 10000
        ) {
          this.$mui.alert(
            'Please enter the TD Amount, at least 10000.',
            'Warning',
            'OK'
          )
        } else if (
          !this.debitAccountNumber ||
          this.debitAccountNumber.length === 0
        ) {
          this.$mui.alert(
            'Please enter the Debit Account Number.',
            'Warning',
            'OK'
          )
        } else if (
          !this.termDepositAccountNumber ||
          this.termDepositAccountNumber.length === 0
        ) {
          this.$mui.alert('Please enter the TD Account Number.')
        } else if (!this.currency || this.currency.length === 0) {
          this.$mui.alert('Please enter the TD Ccy.', 'Warning', 'OK')
        } else if (!this.depositMaturity || this.depositMaturity.length === 0) {
          this.$mui.alert(
            'Please enter the TD Contract Period.',
            'Warning',
            'OK'
          )
        } else {
          const requestData = {
            debitAccountNumber: this.debitAccountNumber,
            tdAccountNumber: this.termDepositAccountNumber,
            tdAmount: this.amount,
            tdCcy: this.currency,
            tdContractPeriod: this.depositMaturity
          }
          window.sessionStorage.setItem(
            'termDepositInfo',
            JSON.stringify(requestData)
          )
          this.$router.push({
            path: '/lbsaccountservice/termdeposit/termdepositform'
          })
        }
      }
    }
  }
}
</script>

<style scoped>
.applytermdeposit p {
  line-height: 1;
}

.applytermdeposit .main-left {
  padding: 0 50px 30px;
  border-right: 1px #cccccc solid;
}

.applytermdeposit .title {
  font-size: 30px;
  color: #707070;
  margin: 40px 0 40px;
}

.applytermdeposit .div-currency {
  display: inline-block;
}

.applytermdeposit .div-select {
  display: inline-block;
}

.applytermdeposit .summary {
  color: #707070;
  font-size: 42px;
  margin-bottom: 60px;
}

.applytermdeposit .main-right {
  padding: 30px 50px 30px;
}

.applytermdeposit .select-result {
  color: #707070;
  font-size: 30px;
  margin-bottom: 60px;
}

.applytermdeposit .button {
  float: right;
  width: 100px;
  margin-bottom: 30px;
}

.applytermdeposit .btn2 {
  margin-right: 20px;
}
</style>

<style>
.applytermdeposit .el-input__icon {
  height: 40px;
}

.applytermdeposit .el-input__inner {
  margin-bottom: 0;
  height: 40px;
}
</style>

