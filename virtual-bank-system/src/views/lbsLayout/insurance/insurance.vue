<template>
  <div v-loading.fullscreen.lock="loading" class="insurance">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.TravelInsurance') }}</p>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="insurance-main">
          <div style="margin: 0 40px 0 100px">
            <el-form ref="form" :model="form" label-width="80px" label-position="left">
              <el-form-item label-width="100px" style="margin-bottom: 60px">
                <el-radio-group v-model="form.coverageType" @change="singleTrip()">
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuranceQuote.SingleTrip') }}</div>
                    <el-radio-button label="S">{{ $t('lbs.SingleTrip') }}</el-radio-button>
                  </el-tooltip>
                  <!--                  <el-tooltip effect="dark" placement="top">-->
                  <!--                    <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuranceQuote.AnnualChinaTrip') }}</div>-->
                  <el-radio-button label="AN">{{ $t('lbs.AnnualChinaTrip') }}</el-radio-button>
                  <!--                  </el-tooltip>-->
                </el-radio-group>
              </el-form-item>
              <el-tooltip effect="dark" placement="top">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuranceQuote.Date') }}</div>
                <el-form-item :label="$t('lbs.Date')" label-width="150px">
                  <el-date-picker
                    v-model="form.startDate"
                    :clearable="false"
                    :picker-options="startPickerOptions"
                    :placeholder="$t('lbs.StartDate')"
                    value-format="timestamp"
                    type="date"
                  />
                  <span style="display: inline-block;width: 60px;text-align: center"> {{ $t('lbs.To') }} </span>
                  <el-date-picker
                    v-model="form.endDate"
                    :clearable="false"
                    :disabled="form.coverageType === 'AN'"
                    :picker-options="endPickerOptions"
                    :placeholder="$t('lbs.EndDate')"
                    value-format="timestamp"
                    type="date"
                  />
                </el-form-item>
              </el-tooltip>
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuranceQuote.Destination') }}</div>
                <el-form-item :label="$t('lbs.Destination')" label-width="150px">
                  <el-select
                    v-model="form.destination"
                    :placeholder="$t('lbs.Destination')"
                    style="width: 300px"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-tooltip>
              <el-form-item :label="$t('lbs.Insured')" label-width="150px">
                <el-checkbox
                  v-model="Self"
                  :disabled="(Spouse && Number(MyChildrenCount) + Number(FriendsOrRelativesCount) >= 9) || Number(MyChildrenCount) + Number(FriendsOrRelativesCount) === 10"
                  @change="handlerSelfChange"
                >
                  {{ $t('lbs.Self') }}
                </el-checkbox>
                <el-checkbox
                  v-model="Spouse"
                  :disabled="(Self && Number(MyChildrenCount) + Number(FriendsOrRelativesCount) >= 9) || Number(MyChildrenCount) + Number(FriendsOrRelativesCount) === 10"
                  @change="handlerSpouseChange"
                >
                  {{ $t('lbs.Spouse') }}
                </el-checkbox>
                <br>
                <el-checkbox v-model="MyChildren" @change="handlerMyChildrenChange">{{ $t('lbs.MyChildren') }}</el-checkbox>
                <el-select
                  v-show="MyChildren"
                  v-model="MyChildrenCount"
                  placeholder="Children Count"
                  size="mini"
                  style="width: 200px;float: right;margin-right: 160px"
                >
                  <el-option
                    v-for="item in 6"
                    :key="item"
                    :disabled="item > (total - (Number(FriendsOrRelativesCount) + Number(ParentsCount)))"
                    :label="item"
                    :value="item"
                  />
                </el-select>
                <br>
                <el-checkbox v-model="Parents" @change="handlerParentsChange">{{ $t('lbs.Parents') }}
                </el-checkbox>
                <el-select
                  v-show="Parents"
                  v-model="ParentsCount"
                  placeholder="Parents Count"
                  size="mini"
                  style="width: 200px;float: right;margin-right: 160px"
                >
                  <el-option
                    v-for="item in 2"
                    :key="item"
                    :disabled="item > (total - (Number(FriendsOrRelativesCount) + Number(MyChildrenCount)))"
                    :label="item"
                    :value="item"
                  />
                </el-select>
                <br>
                <el-checkbox
                  v-model="FriendsOrRelatives"
                  @change="handlerFriendsOrRelativesChange"
                >
                  {{ $t('lbs.FriendsOrRelatives') }}
                </el-checkbox>
                <el-select
                  v-show="FriendsOrRelatives"
                  v-model="FriendsOrRelativesCount"
                  placeholder="Friends Or Relatives Count"
                  size="mini"
                  style="width: 200px;float: right;margin-right: 160px"
                >
                  <el-option
                    v-for="item in 6"
                    :key="item"
                    :disabled="item > (total - (Number(MyChildrenCount) + Number(ParentsCount)))"
                    :label="item"
                    :value="item"
                  />
                </el-select>
                <p style="width: 560px;line-height: 20px;font-size: 10px">{{ $t('lbs.insuranceTip1') }}</p>
              </el-form-item>
              <el-form-item :label="$t('lbs.DateOfBirth')" label-width="150px">
                <el-date-picker
                  v-model="form.dateOfBirth"
                  :picker-options="birthdayPickerOptions"
                  :placeholder="$t('lbs.DateOfBirth')"
                  type="date"
                  value-format="timestamp"
                  style="width: 180px"
                />
              </el-form-item>
              <el-form-item :label="$t('lbs.IdInfo')" label-width="150px">
                <el-input
                  v-model="form.idNumber"
                  :placeholder="$t('lbs.IdNumber')"
                  style="width: 450px"
                  class="input-with-select"
                >
                  <el-select
                    slot="prepend"
                    v-model="form.idType"
                    :placeholder="$t('lbs.IdType')"
                    style="width: 120px;"
                  >
                    <el-option :label="$t('lbs.IDCard')" value="I" />
                    <el-option :label="$t('lbs.Passport')" value="P" />
                  </el-select>
                </el-input>
              </el-form-item>
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuranceQuote.PromotionCode') }}</div>
                <el-form-item :label="$t('lbs.PromotionCode')" label-width="150px">
                  <el-input
                    v-model="form.promotionCode"
                    :placeholder="$t('lbs.EnterPromotionCode')"
                    style="width: 300px;"
                  />
                </el-form-item>
              </el-tooltip>
            </el-form>
            <div style="text-align: center">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 150px;text-align: center">{{ $t('lbsTips.InsuranceQuote.GetAQuote') }}
                </div>
                <el-button type="primary" style="width: 280px" @click="goToPage()">{{ $t('lbs.GETAQUOTE') }}</el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
        <svg-icon icon-class="insurance" style="width: 300px;height: 300px;vertical-align: 120px" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  data() {
    return {
      token: null,
      stateDataName: 'INSURANCE_TYPE',
      loading: false,
      total: 10,
      form: {
        coverageType: 'S',
        startDate: '',
        endDate: '',
        destination: 'Mainland China and Macau',
        dateOfBirth: '',
        idType: '',
        idNumber: '',
        promotionCode: 'G456q2wDf234'
      },
      PlanTypeList: ['Family', 'Individual'],
      options: [],
      startPickerOptions: {
        disabledDate: (time) => {
          if (this.form.endDate !== '') {
            return time.getTime() < Date.now() || time.getTime() > this.form.endDate
          } else {
            return time.getTime() < Date.now()
          }
        }
      },
      endPickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < this.form.startDate + 1000 * 60 * 60 * 24 || time.getTime() < Date.now()
        }
      },
      Self: '',
      Spouse: '',
      MyChildren: '',
      MyChildrenCount: '0',
      Parents: '',
      ParentsCount: '0',
      FriendsOrRelatives: '',
      FriendsOrRelativesCount: '0',
      birthdayPickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    language() {
      return this.$store.state.app.language
    }
  },
  watch: {
    form: {
      handler(newValue) {
        this.consolidateStateData(newValue)
      },
      deep: true
    },
    Self: {
      handler(newValue) {
        this.consolidateStateData({ Self: newValue })
      }
    },
    Spouse: {
      handler(newValue) {
        this.consolidateStateData({ Spouse: newValue })
      }
    },
    MyChildren: {
      handler(newValue) {
        this.consolidateStateData({ MyChildren: newValue })
      }
    },
    MyChildrenCount: {
      handler(newValue) {
        this.consolidateStateData({ MyChildrenCount: newValue })
      }
    },
    Parents: {
      handler(newValue) {
        this.consolidateStateData({ Parents: newValue })
      }
    },
    ParentsCount: {
      handler(newValue) {
        this.consolidateStateData({ ParentsCount: newValue })
      }
    },
    FriendsOrRelatives: {
      handler(newValue) {
        this.consolidateStateData({ FriendsOrRelatives: newValue })
      }
    },
    FriendsOrRelativesCount: {
      handler(newValue) {
        this.consolidateStateData({ FriendsOrRelativesCount: newValue })
      }
    },
    total: {
      handler(newValue) {
        this.consolidateStateData({ total: newValue })
      }
    },
    language: {
      handler() {
        this.options = [
          {
            label: this.$t('lbs.MainlandChinaandMacau'),
            value: 'Mainland China and Macau'
          },
          {
            label: this.$t('lbs.Asia'),
            value: 'Asia'
          },
          {
            label: this.$t('lbs.Worldwide'),
            value: 'Worldwide'
          }
        ]
      }
    }
  },
  created() {
    this.token = window.sessionStorage.getItem('token')
  },
  mounted() {
    // 导航提示
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupinsurancequote'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.options = [
      {
        label: this.$t('lbs.MainlandChinaandMacau'),
        value: 'Mainland China and Macau'
      },
      {
        label: this.$t('lbs.Asia'),
        value: 'Asia'
      },
      {
        label: this.$t('lbs.Worldwide'),
        value: 'Worldwide'
      }
    ]
    // 还原状态数据
    this.restoringStateData()
  },
  methods: {
    // 合并状态数据
    consolidateStateData(value) {
      let jsonData
      const item = sessionStorage.getItem(this.stateDataName)
      if (item) {
        jsonData = JSON.parse(item)
      }
      sessionStorage.setItem(this.stateDataName, JSON.stringify({ ...jsonData, ...value }))
    },
    // 还原状态数据
    restoringStateData() {
      const stateData = JSON.parse(sessionStorage.getItem(this.stateDataName))
      if (stateData) {
        if (stateData.coverageType) this.form.coverageType = stateData.coverageType
        if (stateData.startDate) this.form.startDate = stateData.startDate
        if (stateData.endDate) this.form.endDate = stateData.endDate
        if (stateData.destination) this.form.destination = stateData.destination
        if (stateData.dateOfBirth) this.form.dateOfBirth = stateData.dateOfBirth
        if (stateData.idType) this.form.idType = stateData.idType
        if (stateData.idNumber) this.form.idNumber = stateData.idNumber
        if (stateData.promotionCode) this.form.promotionCode = stateData.promotionCode

        if (stateData.total) this.total = stateData.total

        if (stateData.Self) this.Self = stateData.Self
        if (stateData.Spouse) this.Spouse = stateData.Spouse
        if (stateData.MyChildren) this.MyChildren = stateData.MyChildren
        if (stateData.MyChildrenCount) this.MyChildrenCount = stateData.MyChildrenCount
        if (stateData.Parents) this.Parents = stateData.Parents
        if (stateData.ParentsCount) this.ParentsCount = stateData.ParentsCount
        if (stateData.FriendsOrRelatives) this.FriendsOrRelatives = stateData.FriendsOrRelatives
        if (stateData.FriendsOrRelativesCount) this.FriendsOrRelativesCount = stateData.FriendsOrRelativesCount
      }
    },
    goToPage: function() {
      // 判断填写内容是否为空
      if (this.form.startDate.length < 1) {
        this.$message.warning(this.$t('lbs.SelectStartDate'))
        return
      }
      if (this.form.coverageType === 'S' && this.form.endDate.length < 1) {
        this.$message.warning(this.$t('lbs.SelectEndDate'))
        return
      }
      if (this.form.coverageType === 'S' && this.form.destination.length < 1) {
        this.$message.warning(this.$t('lbs.SelectDestination'))
        return
      }

      if (!(this.Self || this.Spouse || this.MyChildren || this.Parents || this.FriendsOrRelatives)) {
        this.$message.warning(this.$t('lbs.SelectPolicyholder'))
        return
      }

      if (this.form.dateOfBirth !== '' || this.form.idType !== '' || this.form.idNumber !== '') {
        if (this.form.dateOfBirth === '' || this.form.dateOfBirth === null || this.form.dateOfBirth === undefined) {
          this.$message.warning(this.$t('lbs.SelectDateOfBirth'))
          return
        }
        if (this.form.idType === '') {
          this.$message.warning(this.$t('lbs.SelectIdType'))
          return
        }
        if (this.form.idNumber === '') {
          this.$message.warning(this.$t('lbs.InputIdNumber'))
          return
        }
      }

      if (this.form.idType !== '') {
        let reg

        if (this.form.idType === 'P') {
          // 校验护照
          reg = /^[a-zA-Z]\d*$|^\d*$/
        } else {
          // 校验香港身份证
          reg = /^[A-Z]{1,2}[0-9]{6}\(?[0-9A]\)?$/
        }
        if (!reg.test(this.form.idNumber)) {
          this.$message.warning(this.$t('lbs.ErrorIdNumber'), this.$t('lbs.common.warning'))
          return
        }
      }

      // 转换为时间戳
      // this.form.startDate = new Date(this.form.startDate).getTime()
      // this.form.endDate = new Date(this.form.endDate).getTime()

      let peopleCount = 0

      const form = { ...this.form }

      if (this.Self) {
        form['Self'] = 'Y'
        peopleCount++
      } else {
        form['Self'] = ''
      }

      if (this.Spouse) {
        peopleCount++
        form['Spouse'] = 'Y'
      } else {
        form['Spouse'] = ''
      }

      if (this.MyChildren) {
        form['MyChildren'] = 'Y'
        form['ChildrenNo'] = this.MyChildrenCount
        peopleCount += this.MyChildrenCount
      } else {
        this.form['MyChildren'] = ''
        this.form['ChildrenNo'] = 0
      }

      if (this.Parents) {
        form['Parents'] = 'Y'
        form['ParentsNo'] = this.ParentsCount
        peopleCount += this.ParentsCount
      } else {
        form['Parents'] = ''
        form['ParentsNo'] = 0
      }

      if (this.FriendsOrRelatives) {
        form['FriendsOrRelatives'] = 'Y'
        form['friendsOrRelativesNo'] = this.FriendsOrRelativesCount
        peopleCount += this.FriendsOrRelativesCount
      } else {
        form['FriendsOrRelatives'] = ''
        form['friendsOrRelativesNo'] = 0
      }

      form['peopleCount'] = peopleCount
      this.consolidateStateData({ peopleCount: peopleCount })

      // 存入session
      // window.sessionStorage.setItem('INSURANCE_TYPE', JSON.stringify(this.form))

      this.$router.push({ path: '/lbsinsurance/getaquote' })
    },
    singleTrip() {
      this.form.destination = 'Mainland China and Macau'
      if (this.form.coverageType === 'S') {
      //   this.Parents = false
      //   this.ParentsCount = 0
      //   this.form.destination = 'Mainland China and Macau'
      } else {
      //   this.FriendsOrRelatives = false
      //   this.FriendsOrRelativesCount = 0
        this.form.endDate = ''
      //   this.form.destination = ''
      }
    },
    handlerSelfChange() {
      if (this.Self) {
        this.total -= 1
      } else {
        this.total += 1
      }
    },
    handlerSpouseChange() {
      if (this.Spouse) {
        this.total -= 1
      } else {
        this.total += 1
      }
    },
    handlerMyChildrenChange() {
      if (!this.MyChildren) {
        this.MyChildrenCount = 0
      } else {
        this.MyChildrenCount = 1
      }
    },
    handlerParentsChange() {
      if (!this.Parents) {
        this.ParentsCount = 0
      } else {
        this.ParentsCount = 2
      }
    },
    handlerFriendsOrRelativesChange() {
      if (!this.FriendsOrRelatives) {
        this.FriendsOrRelativesCount = 0
      } else {
        this.FriendsOrRelativesCount = 1
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

  .insurance {
    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

    .insurance-main {
      display: inline-block;
      margin: 40px 25px 0 25px;
      padding: 0 20px 0 20px;
    }
  }
</style>

<style>
  .insurance .el-input__icon {
    height: 40px;
  }

  .insurance .el-input__inner {
    margin-bottom: 0;
    height: 30px;
  }

  .insurance .el-input-group__append button.el-button, .el-input-group__append div.el-select .el-input__inner, .el-input-group__append div.el-select:hover .el-input__inner, .el-input-group__prepend button.el-button, .el-input-group__prepend div.el-select .el-input__inner, .el-input-group__prepend div.el-select:hover .el-input__inner {
    background-color: #ffffff;
    border-top: 1px #cccccc solid;
    border-bottom: 1px #cccccc solid;
    border-left: 1px #cccccc solid;
    border-right: 1px #cccccc solid;
  }

  .el-select .el-input.is-focus .el-input__inner {
    border-color: #109eae!important;
  }
</style>
