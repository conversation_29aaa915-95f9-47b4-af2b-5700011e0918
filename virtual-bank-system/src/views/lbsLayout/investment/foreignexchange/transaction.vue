<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="fx-transaction"
  >
    <el-col
      :span="12"
      class="fx left-content"
    >
      <p
        class="title2"
      >
        {{ $t('lbs.transaction') }}
      </p>
      <el-tooltip effect="dark" placement="right">
        <div slot="content" style="width: 300px">{{ $t('lbsTips.FxTransaction.Amount') }}</div>
        <p
          class="title3"
          style="display: inline-block;width: 150px;"
        >
          {{ $t('lbs.Amount') }}
        </p>
      </el-tooltip>
      <el-tooltip effect="dark" placement="bottom">
        <div slot="content" style="width: 300px">{{ $t('lbsTips.FxTransaction.EURUSD') }}</div>
        <el-input
          ref="FxAmount"
          v-model="FxAmount"
          :placeholder="$t('lbs.InputAmount')"
          class="input-with-select"
          maxlength="30"
          @change="countFx()"
        >
          <el-select
            slot="prepend"
            v-model="FxCurrency"
            placeholder=""
            class="input-select"
          >
            <el-option
              v-for="(item,index) in CurrencyList"
              :key="index"
              :label="item.ccycode"
              :value="item.ccycode"
            />
          </el-select>
        </el-input>
      </el-tooltip>
      <el-tooltip effect="dark" placement="bottom">
        <div slot="content" style="width: 300px">{{ $t('lbsTips.FxTransaction.Fee') }}</div>
        <p
          class="center-down"
        >
          {{ `${$t('lbs.Fee')}：${CurrentCurrency} ${'0.00'}` }}
        </p>
      </el-tooltip>
      <svg-icon
        icon-class="Icon awesome-arrow-down"
        class="svg-down"
      />
      <el-tooltip effect="dark" placement="bottom">
        <div slot="content" style="width: 300px">{{ $t('lbsTips.FxTransaction.Rate') }}</div>
        <p
          class="center-down"
        >
          {{ `${$t('lbs.Rate')}：${FxCurrency} ${'0.00'}` }}
        </p>
      </el-tooltip>
      <el-tooltip effect="dark" placement="bottom">
        <div slot="content" style="width: 300px">{{ $t('lbsTips.FxTransaction.EURUSD') }}</div>
        <el-input
          v-model="CurrentAmount"
          placeholder=""
          class="input-with-select"
          maxlength="30"
          disabled
        >
          <el-select
            slot="prepend"
            v-model="CurrentCurrency"
            placeholder=""
            class="input-select"
            disabled
          >
            <el-option
              v-for="(item,index) in CurrencyList"
              :key="index"
              :label="item.ccycode"
              :value="item.ccycode"
            />
          </el-select>
        </el-input>
      </el-tooltip>
    </el-col>
    <el-col :span="11">
      <div class="div-right">
        <el-tooltip effect="dark" placement="right">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.FxTransaction.From') }}</div>
          <p
            class="title3"
          >
            {{ transactionType === 'buy' ? $t('lbs.AssociatedDepositAccount') : $t('lbs.SavingAccount') }}
          </p>
        </el-tooltip>
        <el-select
          v-model="CurrentAccount"
          placeholder=""
          class="right-select"
        >
          <el-option-group
            v-for="group in CurrentAccountOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="item in group.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-option-group>
        </el-select>
        <p style="margin-left: 80px;color: #707070;margin-bottom: 40px">{{ `( ${$t('lbs.Balance')} (${CurrentCurrency})：${CurrentBalanceAmount} )` }}</p>
        <el-tooltip effect="dark" placement="right">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.FxTransaction.To') }}</div>
          <p
            class="title3"
            style="margin-top: 50px"
          >
            {{ $t('lbs.ForeignCurrencyExchangeAccount') }}
          </p>
        </el-tooltip>
        <el-select
          v-model="FxAccount"
          placeholder=""
          class="right-select"
        >
          <el-option-group
            v-for="group in FxAccountOptions"
            :key="group.label"
            :label="group.label"
          >
            <el-option
              v-for="item in group.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-option-group>
        </el-select>
        <p style="margin-left: 80px;color: #707070;margin-bottom: 40px">{{ `( ${$t('lbs.Balance')} (${FxCurrency})：${FxBalanceAmount} )` }}</p>
        <div
          class="button-group"
        >
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 150px;text-align: center">{{ $t('lbsTips.FxTransaction.Cancel') }}</div>
            <el-button
              type="danger"
              class="lbs-button button-cancel"
              @click="$router.go(-1)"
            >
              {{ $t('lbs.Cancel') }}
            </el-button>
          </el-tooltip>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 150px;text-align: center">{{ $t('lbsTips.FxTransaction.Next') }}</div>
            <el-button
              type="primary"
              class="lbs-button button-next"
              @click="goToPage()"
            >
              {{ $t('lbs.Next') }}
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </el-col>
  </div>
</template>
<script>

import axios from 'axios'

export default {
  data: () => ({
    loading: false,
    token: null,
    transactionType: null,
    CurrentAmount: '',
    FxAmount: '',
    xbuy: 0,
    xsell: 0,
    ysell: 0,
    CurrentCurrency: 'HKD',
    FxCurrency: 'USD',
    CurrencyList: [],
    CurrentAccount: '',
    FxAccount: '',
    CurrentAccountOptions: [
      {
        label: 'Saving Account',
        options: []
      },
      {
        label: 'Current Account',
        options: []
      }
    ],
    FxAccountOptions: [
      {
        label: 'Fx Account',
        options: []
      }
    ],
    FxBalanceAmount: 0,
    CurrentBalanceAmount: 0
  }),
  computed: {},
  watch: {
    'FxAmount'() {
      this.clearNoNum()
    },
    'FxCurrency'() {
      this.selectFxBalance()
    },
    'FxAccount'() {
      this.selectFxBalance()
    }
  },
  created() {
    this.transactionType = this.$route.query.type
    this.FxCurrency = this.$route.query.code
    this.token = window.sessionStorage.getItem('token')
    this.getCurrencyList()
    const savingaccountlist = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
    const currentaccountlist = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
    const fxaccountlist = JSON.parse(window.sessionStorage.getItem('fexaccountlist'))

    for (let i = 0; i < savingaccountlist.length; i++) {
      if (savingaccountlist[i].accountStatus === 'A') {
        this.CurrentAccountOptions[0].options.push({
          label: savingaccountlist[i].accountNumber,
          value: savingaccountlist[i].accountNumber
        })
      }
    }
    for (let i = 0; i < currentaccountlist.length; i++) {
      if (currentaccountlist[i].accountStatus === 'A') {
        this.CurrentAccountOptions[1].options.push(
          {
            label: currentaccountlist[i].accountNumber,
            value: currentaccountlist[i].accountNumber
          }
        )
      }
    }
    for (let i = 0; i < fxaccountlist.length; i++) {
      if (fxaccountlist[i].accountStatus === 'A') {
        this.FxAccountOptions[0].options.push(
          {
            label: fxaccountlist[i].accountNumber,
            value: fxaccountlist[i].accountNumber
          }
        )
      }
    }
    if (savingaccountlist && savingaccountlist[0]) {
      this.CurrentAccount = savingaccountlist[0].accountNumber
    }
    if (fxaccountlist && fxaccountlist[0]) {
      this.FxAccount = fxaccountlist[0].accountNumber
    }
    this.selectCurrentBalance()
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupfxtransaction'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
  },
  methods: {
    selectFxBalance() {
      const _this = this
      if (!_this.FxAccount) return
      this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.FxAccount}`, {}, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.FxBalanceAmount = 0
            if (response.data.data.account.fexbalance !== null && response.data.data.account.fexbalance !== undefined && response.data.data.account.fexbalance.length > 0) {
              for (let i = 0; i < response.data.data.account.fexbalance.length; i++) {
                if (response.data.data.account.fexbalance[i].ccy === _this.FxCurrency) {
                  _this.FxBalanceAmount = response.data.data.account.fexbalance[i].balance
                }
              }
            } else {
              _this.FxBalanceAmount = 0
            }
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    selectCurrentBalance() {
      const _this = this
      if (!_this.CurrentAccount) return
      this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.CurrentAccount}`, {}, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.CurrentBalanceAmount = response.data.data.account.availablebalance
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    goToPage() {
      if (!this.CurrentAccount || !this.FxAccount) return
      if (Number(this.FxAmount) > 0) {
        window.sessionStorage.setItem('fxTrading', JSON.stringify(
          {
            actionCode: this.transactionType,
            ccycode: this.FxCurrency,
            debitaccountnumber: this.CurrentAccount,
            exchangeAmount: this.FxAmount,
            fexAccountNumber: this.FxAccount,
            currentAmount: this.CurrentAmount
          }
        ))
        this.$router.push({ path: '/lbsinvestment/foreignexchange/summary' })
      } else {
        this.$message.warning(this.$t('lbs.AmountError'), this.$t('lbs.common.error'))
        this.$refs['FxAmount'].focus()
      }
    },
    getCurrencyList() {
      const vue = this
      vue.loading = true
      axios.get(
        `${vue.LBSGateway}/sysadmin-experience/sysadmin/currency/currencyTypeRetrieval`, { headers: { token: vue.token }})
        .then(response => {
          vue.loading = false
          if (response.data.code === '200') {
            vue.CurrencyList = response.data.data
            for (let i = 0; i < vue.CurrencyList.length; i++) {
              if (vue.CurrencyList[i].ccycode === 'JPY') {
                const bankbuy = vue.$lbs.decimal_format(1 / vue.CurrencyList[i].banksell, 4)
                const banksell = vue.$lbs.decimal_format(1 / vue.CurrencyList[i].bankbuy, 4)
                vue.CurrencyList[i].bankbuy = bankbuy
                vue.CurrencyList[i].banksell = banksell
              }
            }
          }
        })
        .catch(error => {
          vue.loading = false
          vue.$mui.alert(error.message, vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
        })
    },
    countFx() {
      if (this.transactionType === 'sell') {
        for (let i = 0; i < this.CurrencyList.length; i++) {
          if (this.CurrencyList[i].ccycode === this.FxCurrency) {
            this.xbuy = this.CurrencyList[i].bankbuy
          }

          if (this.CurrencyList[i].ccycode === this.CurrentCurrency) {
            this.ysell = this.CurrencyList[i].banksell
          }
        }
        this.CurrentAmount = this.$lbs.decimal_format(Number(this.FxAmount) * this.xbuy / this.ysell, 5)
      } else {
        for (let i = 0; i < this.CurrencyList.length; i++) {
          if (this.CurrencyList[i].ccycode === this.FxCurrency) {
            this.xsell = this.CurrencyList[i].banksell
          }
        }
        this.CurrentAmount = this.$lbs.decimal_format(Number(this.FxAmount) * this.xsell, 5)
      }
    },
    clearNoNum() {
      this.FxAmount = this.FxAmount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.FxAmount = this.FxAmount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.FxAmount = this.FxAmount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.FxAmount = this.FxAmount.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      this.FxAmount = this.FxAmount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    }
  }
}
</script>

<style scoped>

  .fx-transaction .fx {
    padding: 0 20px 150px 3%;
    margin: 40px 20px 20px;
    border-right: 1px #ccc solid;
  }

  .fx-transaction .title2 {
    font-size: 25px;
    color: #22C1E6;
  }

  .fx-transaction .title3 {
    font-size: 18px;
    color: #707070;
    padding-left: 60px;
    margin-bottom: 20px;
  }

  .fx-transaction .input-with-select {
    width: 85%;
    margin: 0 0 0 55px;
  }

  .fx-transaction .input-select {
    width: 100px;
  }

  .fx-transaction .svg-down {
    height: 250px;
    width: 10%;
    vertical-align: middle;
  }

  .fx-transaction .center-down {
    width: 44.2%;
    font-size: 18px;
    color: #707070;
    display: inline-block;
    text-align: center;
  }

  .fx-transaction .div-right {
    padding: 95px 0 0 0;
  }

  .fx-transaction .right-select {
    width: 70%;
    margin: 30px 0 5px 80px;
  }

  .fx-transaction .button-cancel {
    margin-right: 50px;
  }

  .fx-transaction .lbs-button {
    width: 120px;
  }

  .fx-transaction .button-group {
    float: right;
    margin: 20px 100px 0 0;
  }

</style>

<style>
  .fx-transaction .el-input__icon {
    height: 40px;
  }

  .fx-transaction .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }
</style>
