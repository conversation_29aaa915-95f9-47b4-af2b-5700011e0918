<template>
  <div v-loading.fullscreen.lock="loading" class="fund-detail-quotation">
    <!-- 已选择基金 Selected Fund -->
    <el-row>
      <el-col :span="16">
        <div class="selected-fund">
          <p class="fund-quote-title">{{ $t('lbs.FundQuote.SelectedFund') }}</p>
          <el-card>
            <div>
              <span class="fund-name">{{ fundInformation.fundName }}</span>
              <span class="fund-code">{{ fundInformation.fundCode }}</span>
            </div>
            <div class="selected-fund-info">
              <div class="text-center half-width float-left">
                <p class="label color-info">{{ $t('lbs.FundQuote.lastNAV') }}</p>
                <p class="value" style="color: #34A761">{{ fundInformation.lastNAV }}</p>
              </div>
              <div class="text-center half-width float-left">
                <p class="label color-info">{{ $t('lbs.FundQuote.ValuationDate') }}</p>
                <p class="value">{{ fundInformation.valuationDate }}</p>
              </div>
            </div>
            <div class="fund-info-footer">
              <span class="fund-info-footer-item">{{ $t('lbs.FundQuote.ManagementFee') }}： </span>
              <span class="fund-info-footer-item">{{ fundInformation.managementFee }}</span>
              <span class="fund-info-footer-item">{{ $t('lbs.FundQuote.FundCurrency') }}： </span>
              <span class="fund-info-footer-item">{{ fundInformation.fundCurrency }}</span>
            </div>
          </el-card>
        </div>
      </el-col>
      <el-col :span="7" :offset="1">
        <el-card style="margin-top: 45px">
          <p class="text-center color-info" style="font-size: 18px">{{ $t('lbs.FundQuote.Transaction') }}</p>
          <div class="text-center" style="margin-top: 10px;margin-bottom: 25px">
            <el-button
              type="primary"
              style="width: 250px"
              plain
              @click="showSubscribe = !showSubscribe,showRedeem = false"
            >
              {{ $t('lbs.FundQuote.Subscribe') }}
            </el-button>
            <el-button
              type="danger"
              style="width: 250px;margin: 20px 0 0 0"
              plain
              @click="sell()"
            >{{ $t('lbs.FundQuote.Redeem') }}
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- Invested 10k Return And Fund Document -->
    <transition name="el-zoom-in-center">
      <el-row v-show="!showSubscribe && !showRedeem" style="margin-top: 50px">
        <el-col :span="10">
          <el-card>
            <p class="color-info" style="text-align: center;font-size: 18px">
              {{ $t('lbs.FundQuote.Invested10kReturn') }}</p>
            <svg-icon style="width: 400px;height: 300px" icon-class="1000return" />
          </el-card>
        </el-col>
        <el-col :span="13" :offset="1">
          <el-card>
            <el-tabs v-model="activeName">
              <el-tab-pane :label="$t('lbs.FundQuote.FundDocument')" name="first">
                <el-form label-position="left" inline class="demo-table-expand">
                  <el-form-item :label="$t('lbs.FundText.FundCode')" :label-width="labelWidth">
                    <span>{{ fundInformation.fundCode }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.FundName')" :label-width="labelWidth">
                    <span>{{ fundInformation.fundName }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.FundType')" :label-width="labelWidth">
                    <span>{{ fundInformation.fundType }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.Geographic')" :label-width="labelWidth">
                    <span>{{ fundInformation.geographic }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.LastNAV')" :label-width="labelWidth">
                    <span>{{ fundInformation.lastNAV }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.ManagementFee')" :label-width="labelWidth">
                    <span>{{ fundInformation.managementFee }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.Sector')" :label-width="labelWidth">
                    <span>{{ fundInformation.sector }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.ValuationDate')" :label-width="labelWidth">
                    <span>{{ fundInformation.valuationDate }}</span>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
    </transition>
    <!-- Subscription 买入 -->
    <transition name="el-zoom-in-center">
      <el-row v-show="showSubscribe" style="margin: 50px auto 0;">
        <!--        <p class="fund-quote-title">{{$t('lbs.FundQuote.Subscription')}}</p>-->
        <el-card style="position: relative">
          <div style="text-align: center;width: 50%;display: inline-block">
            <p style="font-size: 16px">{{ fundInformation.fundName }}</p>
            <p class="color-info">{{ fundInformation.fundCode }}</p>
          </div>
          <div class="float-right" style="text-align: center;width: 50%;display: inline-block">
            <span
              class="color-info"
              style="line-height: 70px;margin-right: 80px"
            >{{ `${$t('lbs.FundQuote.lastNAV')}` }}</span>
            <span style="line-height: 70px;font-size: 25px;color: rgb(52, 167, 97);">{{ fundInformation.lastNAV }}</span>
          </div>
          <span
            class="color-info"
            style="position: absolute;top: 5px;right: 20px;font-size: 12px;text-decoration: underline;cursor: pointer"
            @click="showSubscribe = !showSubscribe"
          >{{ $t('lbs.FundQuote.ViewMore') }}</span>
          <div style="border-top: 3px #22C1E6 solid">
            <div style="width: 660px;margin: 50px auto 0;">
              <p style="display: inline-block;color: #22C1E6;font-size: 16px;width: 315px;">
                {{ $t('lbs.FundQuote.FundAccount') }}</p>
              <el-select
                v-model="fundAccount"
                size="small"
                :placeholder="$t('lbs.FundQuote.PlaceholderTip2')"
                style="width: 340px"
                @change="getFundSettingAccount()"
              >
                <el-option
                  v-for="(item,index) in fundAccountOption"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div style="width: 660px;margin: 0 auto;padding-top: 10px;">
              <p
                style="display: inline-block;color: #22C1E6;font-size: 16px;width: 315px;margin-bottom: 0;"
              >
                {{ `${$t('lbs.FundAccount.SettlementAccount')}` }}</p>
              <el-select
                v-model="settlementAccount"
                size="small"
                :placeholder="$t('lbs.FundQuote.PlaceholderTip1')"
                style="width: 340px;"
                @change="setSettingAccount()"
              >
                <el-option
                  v-for="(item,index) in settlementAccountOption"
                  :key="index"
                  :label="item.label.split('HK')[0]+(item.label.split('HK')[1]?item.label.split('HK')[1].substr(6,15):'')"
                  :value="item.value"
                />
              </el-select>
              <p style="text-align: left;padding-left: 320px;color: #707070;font-size: 12px">
                {{ `(${$t('lbs.AvailableBalance')}: ${settlementBalance})` }}</p>

            </div>
            <div style="width: 660px;margin: 0 auto;padding-top: 0;">
              <p style="display: inline-block;color: #22C1E6;font-size: 16px;width: 315px;">
                {{ `${$t('lbs.FundQuote.SubscriptionAmount')} (${fundInformation.fundCurrency})` }}</p>
              <el-input
                v-model="subscriptionAmount"
                size="small"
                :placeholder="$t('lbs.FundQuote.PlaceholderTip1')"
                style="width: 340px;"
              />
            </div>
          </div>
          <div style="text-align: center;margin-top: 35px;padding-bottom: 35px">
            <el-button style="width: 300px;font-size: 16px" type="primary" @click="openDialog()">
              {{ $t('lbs.FundQuote.ConfirmOrder') }}
            </el-button>
          </div>
        </el-card>
      </el-row>
    </transition>
    <!-- Redeem 卖出 -->
    <transition name="el-zoom-in-center">
      <el-row v-show="showRedeem" style="margin: 50px auto 0;">
        <!--        <p class="fund-quote-title">{{$t('lbs.FundQuote.Subscription')}}</p>-->
        <el-card style="position: relative">
          <div style="text-align: center;width: 50%;display: inline-block">
            <p style="font-size: 16px">{{ fundInformation.fundName }}</p>
            <p class="color-info">{{ fundInformation.fundCode }}</p>
          </div>
          <div class="float-right" style="text-align: center;width: 50%;display: inline-block">
            <span
              class="color-info"
              style="line-height: 70px;margin-right: 80px"
            >{{ `${$t('lbs.FundQuote.lastNAV')}` }}</span>
            <span style="line-height: 70px;font-size: 25px;color: rgb(52, 167, 97);">{{ fundInformation.lastNAV }}</span>
          </div>
          <span
            class="color-info"
            style="position: absolute;top: 5px;right: 20px;font-size: 12px;text-decoration: underline;cursor: pointer"
            @click="showRedeem = !showRedeem"
          >{{ $t('lbs.FundQuote.ViewMore') }}</span>
          <div style="border-top: 3px #F56C6C solid">
            <div style="width: 660px;margin: 50px auto 0;">
              <p
                style="display: inline-block;color: #F56C6C;font-size: 16px;width: 315px;margin-bottom: 0;"
              >
                {{ $t('lbs.FundQuote.FundAccount') }}</p>
              <el-select
                v-model="redeemFundAccount"
                size="small"
                :placeholder="$t('lbs.FundQuote.PlaceholderTip2')"
                style="width: 340px;"
                @change="getFundHoldingShareNo()"
              >
                <el-option
                  v-for="(item,index) in fundAccountOption"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <p style="text-align: left;padding-left: 320px;color: #707070;font-size: 12px">
                {{ `(${$t('lbs.FundQuote.AvailableHoldingNo')}：${fundHoldingShareNo})` }}</p>
            </div>
            <div style="width: 660px;margin: 0 auto;padding-top: 0;">
              <p style="display: inline-block;color: #F56C6C;font-size: 16px;width: 315px;">
                {{ `${$t('lbs.FundAccount.SettlementAccount')}` }}</p>
              <el-select
                v-model="settlementAccount"
                size="small"
                :placeholder="$t('lbs.FundQuote.PlaceholderTip1')"
                style="width: 340px;"
                @change="setSettingAccount()"
              >
                <el-option
                  v-for="(item,index) in settlementAccountOption"
                  :key="index"
                  :label="item.label.split('HK')[0]+(item.label.split('HK')[1]?item.label.split('HK')[1].substr(6,15):'')"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div style="width: 660px;margin: 0 auto;padding-top: 10px;">
              <p style="display: inline-block;color: #F56C6C;font-size: 16px;width: 315px;">
                {{ `${$t('lbs.FundQuote.SharingNo')}` }}</p>
              <el-input
                v-model="sharingNo"
                size="small"
                :placeholder="$t('lbs.FundQuote.PlaceholderTip3')"
                style="width: 340px;"
              />
            </div>
          </div>
          <div style="text-align: center;margin-top: 35px;padding-bottom: 35px">
            <el-button
              style="width: 300px;font-size: 16px"
              type="danger"
              :disabled="Number(fundHoldingShareNo) <= 0"
              @click="openRedeemDialog"
            >
              {{ $t('lbs.FundQuote.ConfirmOrder') }}
            </el-button>
          </div>
        </el-card>
      </el-row>
    </transition>
    <!-- ConfirmOrder 买入 -->
    <el-dialog
      style="margin-top: 12vh"
      :title="$t('lbs.FundQuote.ConfirmOrder')"
      :visible.sync="transactionDialog"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="confirm-order">
        <el-form label-position="left" inline class="demo-table-expand">
          <el-form-item :label="$t('lbs.FundText.FundCode')" :label-width="labelWidth">
            <span>{{ fundInformation.fundCode }}</span>
          </el-form-item>
          <el-form-item :label="$t('lbs.FundText.FundName')" :label-width="labelWidth">
            <span>{{ fundInformation.fundName }}</span>
          </el-form-item>
          <el-form-item :label="$t('lbs.FundQuote.FundAccount')" :label-width="labelWidth">
            <span>{{ fundAccount }}</span>
          </el-form-item>
          <el-form-item :label="$t('lbs.FundQuote.SubscriptionAmount')" :label-width="labelWidth">
            <span>{{ `${subscriptionAmount} (${fundInformation.fundCurrency})` }}</span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="transactionDialog = false">{{ $t('lbs.common.cancel') }}</el-button>
        <el-button type="primary" @click="placeSubscriptionOrder()">{{ $t('lbs.common.confirm') }}</el-button>
      </span>
    </el-dialog>
    <!-- ConfirmOrder 卖出 -->
    <el-dialog
      style="margin-top: 12vh"
      :title="$t('lbs.FundQuote.ConfirmOrder')"
      :visible.sync="redeemDialog"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="confirm-order">
        <el-form label-position="left" inline class="demo-table-expand">
          <el-form-item :label="$t('lbs.FundText.FundCode')" :label-width="labelWidth">
            <span>{{ fundInformation.fundCode }}</span>
          </el-form-item>
          <el-form-item :label="$t('lbs.FundText.FundName')" :label-width="labelWidth">
            <span>{{ fundInformation.fundName }}</span>
          </el-form-item>
          <el-form-item :label="$t('lbs.FundQuote.FundAccount')" :label-width="labelWidth">
            <span>{{ redeemFundAccount }}</span>
          </el-form-item>
          <el-form-item :label="$t('lbs.FundQuote.SharingNo')" :label-width="labelWidth">
            <span>{{ `${sharingNo}` }}</span>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="redeemDialog = false">{{ $t('lbs.common.cancel') }}</el-button>
        <el-button type="primary" @click="placeRedeemOrder()">{{ $t('lbs.common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    fundCode: '',
    fundInformation: {},
    activeName: 'first',
    labelWidth: '150px',
    showSubscribe: false,
    showRedeem: false,
    subscriptionAmount: '',
    settlementAccount: '',
    settlementAccountOption: [],
    fundAccount: '',
    redeemFundAccount: '',
    sharingNo: '',
    fundAccountOption: [],
    transactionDialog: false,
    redeemDialog: false,
    fundHoldingShareNo: 0,
    settlementBalance: 0,
    isInit: false
  }),
  watch: {
    'subscriptionAmount'() {
      this.clearNoNum(1)
    },
    'sharingNo'() {
      this.clearNoNum(2)
    },
    'settlementAccount'() {
      this.getBalance()
    }
  },
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.fundCode = this.$route.query.fundCode
    this.getFundInformation()
    this.getSettingAccountOption()
    this.getFundAccount()
    this.getFundSettingAccount()
  },
  methods: {
    // 获取结算账户余额
    getBalance() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.settlementAccount}`, {}, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          _this.settlementBalance = 0
          if (response.data.code === '200') {
            _this.settlementBalance = response.data.data.account.currencycode + ' ' + response.data.data.account.availablebalance
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取基金账户设置的结算账户
    getFundSettingAccount() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.fundAccount}`, {}, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.settlementAccount = response.data.data.account.relaccountnumber
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取基金持有份额
    getFundHoldingShareNo() {
      const _this = this
      _this.loading = true
      const requestData = { fundAccountNumber: _this.redeemFundAccount, fundCode: _this.fundInformation.fundCode }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/fundHoldingEnquiry`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.fundHoldingShareNo = response.data.data.avaliableholdingno
            _this.getFundSettingAccount()
          } else if (response.data.code === '404010') {
            _this.fundHoldingShareNo = 0
          } else {
            _this.fundHoldingShareNo = 0
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 购买下订单
    placeSubscriptionOrder() {
      const _this = this
      _this.loading = true
      const requestData = {
        fundAccountNumber: _this.fundAccount,
        fundCode: _this.fundInformation.fundCode,
        tradingAmount: _this.subscriptionAmount
      }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/order/subscriptionOrderPlacing`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.$message.success(_this.$t('lbs.FundQuote.OrderPlaced'), _this.$t('lbs.common.success'))
            setTimeout(function() {
              _this.showSubscribe = false
              _this.showRedeem = false
              _this.subscriptionAmount = ''
              _this.transactionDialog = false
            }, 1000)
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    sell() {
      this.showRedeem = !this.showRedeem
      this.showSubscribe = false

      if (!this.isInit) {
        this.getFundHoldingShareNo()
      }

      this.isInit = true
    },
    // 卖出下订单
    placeRedeemOrder() {
      const _this = this
      _this.loading = true
      const requestData = {
        fundAccountNumber: _this.redeemFundAccount,
        fundCode: _this.fundInformation.fundCode,
        sharingNo: _this.sharingNo
      }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/order/redemptionOrderPlacing`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.$message.success(_this.$t('lbs.FundQuote.OrderPlaced'), _this.$t('lbs.common.success'))
            setTimeout(function() {
              _this.fundHoldingShareNo = _this.fundHoldingShareNo - _this.sharingNo
              _this.showSubscribe = false
              _this.showRedeem = false
              _this.sharingNo = ''
              _this.redeemDialog = false
            }, 1000)
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 确认订单信息(买入)
    openDialog() {
      const _this = this
      if (_this.subscriptionAmount.length <= 0) {
        _this.$message.warning(_this.$t('lbs.FundQuote.EnterAmountTip1'), _this.$t('lbs.common.warning'))
        return
      }
      if (Number(_this.subscriptionAmount) <= 0) {
        _this.$message.warning(_this.$t('lbs.FundQuote.EnterAmountTip2'), _this.$t('lbs.common.warning'))
        return
      }
      _this.transactionDialog = true
    },
    // 确认订单信息(卖出)
    openRedeemDialog() {
      const _this = this
      if (_this.sharingNo.length <= 0) {
        _this.$message.warning(_this.$t('lbs.FundQuote.EnterSharingNoTip1'), _this.$t('lbs.common.warning'))
        return
      }
      if (Number(_this.sharingNo) <= 0) {
        _this.$message.warning(_this.$t('lbs.FundQuote.EnterSharingNoTip2'), _this.$t('lbs.common.warning'))
        return
      }
      _this.redeemDialog = true
    },
    // 设置结算账户
    setSettingAccount() {
      const _this = this
      _this.loading = true
      const requestData = { fundAccountNumber: _this.fundAccount, newSettleAccountNumber: _this.settlementAccount }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/settlementAccountUpdate`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.$message.success(_this.$t('lbs.settlementTip'), _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取结算账户列表(current / saving | Status: A)
    getSettingAccountOption() {
      const savingList = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
      const currentList = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
      for (const item of savingList) {
        if (item.accountStatus === 'A') {
          this.settlementAccountOption.push(
            {
              label: `Saving Account ${item.accountNumber}`,
              value: item.accountNumber
            }
          )
        }
      }
      for (const item of currentList) {
        if (item.accountStatus === 'A') {
          this.settlementAccountOption.push(
            {
              label: `Current Account ${item.accountNumber}`,
              value: item.accountNumber
            }
          )
        }
      }
      this.settlementAccount = this.settlementAccountOption[0].value
    },
    // 获取基金详情信息
    getFundInformation() {
      const _this = this
      _this.loading = true
      const requestData = { fundCode: _this.fundCode }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/fundQuotation`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.fundInformation = response.data.data
            _this.fundInformation.valuationDate = _this.$moment(_this.fundInformation.valuationDate).format('YYYY-MM-DD HH:mm:ss')
            // _this.getFundHoldingShareNo()
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为A的基金账户
    getFundAccount() {
      const tempMutualFundAccountList = JSON.parse(window.sessionStorage.getItem('mutualFundaccountlist'))
      for (const item of tempMutualFundAccountList) {
        if (item.accountStatus === 'A') {
          this.fundAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      this.fundAccount = this.fundAccountOption[0].value
      this.redeemFundAccount = this.fundAccountOption[0].value
    },
    // 保留五位小数
    clearNoNum(type) {
      if (type === 1) {
        this.subscriptionAmount = this.subscriptionAmount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
        this.subscriptionAmount = this.subscriptionAmount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
        this.subscriptionAmount = this.subscriptionAmount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
        this.subscriptionAmount = this.subscriptionAmount.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        this.subscriptionAmount = this.subscriptionAmount.replace(/^(\-)*(\d+)\.(\d\d\d\d\d).*$/, '$1$2.$3') // 只能输入五个小数
      } else if (type === 2) {
        if (this.sharingNo > this.fundHoldingShareNo) {
          this.sharingNo = this.fundHoldingShareNo
        } else {
          this.sharingNo = this.sharingNo.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
          this.sharingNo = this.sharingNo.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
          this.sharingNo = this.sharingNo.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
          this.sharingNo = this.sharingNo.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
          this.sharingNo = this.sharingNo.replace(/^(\-)*(\d+)\.(\d\d\d\d\d).*$/, '$1$2.$3') // 只能输入五个小数
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .fund-detail-quotation {
    padding: 0 130px 30px;

    .color-info {
      color: #707070;
    }

    .half-width {
      width: 50%;
    }

    .fund-quote-title {
      font-size: 20px;
      color: #22C1E6;
    }

    .fund-name {
      font-size: 18px;
      display: inline-block;
      margin-right: 40px;
    }

    .fund-code {
      font-size: 15px;
      color: #999999;
      display: inline-block;
    }

    .selected-fund-info {
      margin: 20px 0;
    }

    .label {
      font-size: 19px;
    }

    .value {
      font-size: 23px;
    }

    .fund-info-footer {
      margin: 20px 0 0;
    }

    .fund-info-footer-item {
      font-size: 13px;
      margin-right: 15px;
    }

    .confirm-order {
      margin: 50px 70px 0;
    }
  }
</style>

<style lang="scss">
  .fund-detail-quotation {
    .el-form-item {
      margin-bottom: 0;
    }

    label {
      font-weight: 400;
      color: #99a9bf;
    }

    .el-form-item--medium .el-form-item__content, .el-form-item--medium .el-form-item__label {
      line-height: 28.8px;
    }

    .el-table__expanded-cell[class*=cell] {
      padding: 5px 68px;
    }

    .el-table--medium td, .el-table--medium th {
      padding: 5px 0;
    }

    .el-form--inline .el-form-item {
      display: block;
    }

    input[type=color], input[type=date], input[type=datetime-local], input[type=datetime], input[type=email], input[type=month], input[type=number], input[type=password], input[type=search], input[type=tel], input[type=text], input[type=time], input[type=url], input[type=week], select, textarea {
      margin-bottom: 0;
    }

    .el-form-item__content, .el-form-item__label {
      font-size: 12px;
    }

    .confirm-order .el-form-item__content, .el-form-item__label {
      font-size: 14px;
    }
  }
</style>
