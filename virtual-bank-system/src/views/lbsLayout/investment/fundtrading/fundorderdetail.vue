<template>
  <div v-loading.fullscreen.lock="loading" class="fund-order-detail">
    <el-row>
      <el-col :span="18">
        <p class="fund-order-detail-title">{{ $t('lbs.FundOrder.FundOrderDetail') }}</p>
        <el-card>
          <div style="padding: 0 50px">
            <div style="margin-bottom: 20px">
              <span class="fund-name">{{ orderDetail.fundName }}</span>
              <span class="fund-code">{{ orderDetail.fundCode }}</span>
            </div>
            <div>
              <el-form label-position="left" inline class="order-info">
                <el-form-item
                  :label="`${$t('lbs.FundOrder.TradingPrice')}(${orderDetail.currencyCode})`"
                  :label-width="labelWidth"
                >
                  <span>{{ orderDetail.tradingPrice }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.FundAccountNumber')" :label-width="labelWidth">
                  <span>{{ orderDetail.fundAccountNumber }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.SharingNo')" :label-width="labelWidth">
                  <span>{{ orderDetail.sharingNo }}</span>
                </el-form-item>
                <el-form-item
                  :label="`${$t('lbs.FundOrder.TradingAmount')}(${orderDetail.currencyCode})`"
                  :label-width="labelWidth"
                >
                  <span>{{ orderDetail.tradingAmount }}</span>
                </el-form-item>
                <el-form-item
                  :label="`${$t('lbs.FundOrder.TradingCommission')}(${orderDetail.currencyCode})`"
                  :label-width="labelWidth"
                >
                  <span>{{ orderDetail.tradingCommission }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.CurrencyCode')" :label-width="labelWidth">
                  <span>{{ orderDetail.currencyCode }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.TradingOption')" :label-width="labelWidth">
                  <span>{{ orderDetail.tradingOption }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.LastUpdateDate')" :label-width="labelWidth">
                  <span>{{ orderDetail.lastupdateDate }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.RequestTime')" :label-width="labelWidth">
                  <span>{{ orderDetail.requestTime }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.SettlementAccount')" :label-width="labelWidth">
                  <span>{{ orderDetail.settlementAccount }}</span>
                </el-form-item>
                <el-form-item :label="`${$t('lbs.FundOrder.TransactionAmount')}(HKD)`" :label-width="labelWidth">
                  <span>{{ orderDetail.transactionAmount }}</span>
                </el-form-item>
                <el-form-item :label="$t('lbs.FundOrder.Status')" :label-width="labelWidth">
                  <span style="color: #22C1E6">{{ orderDetail.status }}</span>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6" style="padding-left: 20px">
        <p class="fund-order-detail-title">{{ $t('lbs.FundOrder.FundOrderManagement') }}</p>
        <el-card>
          <el-button
            type="warning"
            :disabled="orderDetail.status !== 'PENDING'"
            style="width: 100%"
            @click="openUpdateOrderDialog()"
          >
            {{ $t('lbs.FundOrder.Change') }}
          </el-button>
          <el-button
            type="danger"
            :disabled="orderDetail.status !== 'PENDING'"
            style="width: 100%;margin: 10px 0 0 0"
            @click="cancelOrder()"
          >
            {{ $t('lbs.FundOrder.Cancel') }}
          </el-button>
          <el-button
            type="primary"
            :disabled="orderDetail.status !== 'PENDING'"
            style="width: 100%;margin: 10px 0 0 0"
            @click="approveOrder()"
          >
            {{ $t('lbs.FundOrder.StrikeABargain') }}
          </el-button>
        </el-card>
      </el-col>
      <el-dialog
        :title="$t('lbs.FundOrder.ChangeOrder')"
        :visible.sync="updateOrderDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        style="margin-top: 12vh"
        width="50%"
      >
        <div style="width: 60%;margin: 10px auto 0">
          <el-input v-model="changeFrom.id" hidden />
          <el-form label-position="left" label-width="200px" size="mini" :model="changeFrom">
            <el-form-item :label="$t('lbs.FundOrder.FundCode')">
              <el-input v-model="orderDetail.fundCode" disabled />
            </el-form-item>
            <el-form-item :label="$t('lbs.FundOrder.FundName')">
              <el-input v-model="orderDetail.fundName" disabled />
            </el-form-item>
            <el-form-item v-if="orderDetail.tradingOption === 'SELL'" :label="$t('lbs.FundOrder.SharingNo')">
              <el-input v-model="changeFrom.sharingNo" />
            </el-form-item>
            <el-form-item v-if="orderDetail.tradingOption === 'BUY'" :label="$t('lbs.FundOrder.TradingAmount')">
              <el-input v-model="changeFrom.tradingAmount" />
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="updateOrderDialog = false">{{ $t('lbs.common.cancel') }}</el-button>
          <el-button type="primary" @click="updateOrder()">{{ $t('lbs.common.confirm') }}</el-button>
        </span>
      </el-dialog>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    id: '',
    orderDetail: {},
    labelWidth: '600px',
    updateOrderDialog: false,
    changeFrom: {
      id: '',
      sharingNo: '',
      tradingAmount: ''
    }
  }),
  created() {
    this.id = this.$route.query.id
    this.token = window.sessionStorage.getItem('token')
    this.getFundOrderDetail()
  },
  methods: {
    // 批准订单(修改订单状态)
    approveOrder() {
      const _this = this
      _this.$confirm(_this.$t('lbs.FundOrder.ApproveTip'), _this.$t('lbs.common.tip'), {
        confirmButtonText: _this.$t('lbs.common.confirm'),
        showCancelButton: false,
        type: _this.$t('lbs.common.warning')
      }).then(() => {
        _this.loading = true
        const requestData = { id: _this.orderDetail.id, status: 1 }
        axios.post(`${_this.LBSGateway}/fund-experience/fund/order/simulatorUpdate`, requestData, { headers: { token: _this.token }})
          .then(response => {
            _this.loading = false
            if (response.data.code === '200') {
              _this.$message.success(_this.$t('lbs.FundOrder.SuccessfulTransaction'), _this.$t('lbs.common.success'))
              _this.getFundOrderDetail()
              _this.$router.go(-1)
            } else {
              _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
            }
          })
          .catch(error => {
            _this.loading = false
            _this.$message.error(error.message, _this.$t('lbs.common.error'))
          })
      })
    },
    // 修改订单
    updateOrder() {
      const _this = this
      _this.$confirm(_this.$t('lbs.FundOrder.ChangeTip'), _this.$t('lbs.common.tip'), {
        confirmButtonText: _this.$t('lbs.common.confirm'),
        showCancelButton: false,
        type: _this.$t('lbs.common.warning')
      }).then(() => {
        if (_this.orderDetail.tradingOption === 'BUY') {
          _this.changeFrom.sharingNo = ''
        } else {
          _this.changeFrom.tradingAmount = ''
        }
        _this.loading = true
        axios.post(`${_this.LBSGateway}/fund-experience/fund/order/orderChange`, _this.changeFrom, { headers: { token: _this.token }})
          .then(response => {
            _this.loading = false
            if (response.data.code === '200') {
              _this.$message.success(_this.$t('lbs.FundOrder.OrderChangeSuccess'), _this.$t('lbs.common.success'))
              _this.updateOrderDialog = false
              _this.getFundOrderDetail()
            } else {
              _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
            }
          })
          .catch(error => {
            _this.loading = false
            _this.$message.error(error.message, _this.$t('lbs.common.error'))
          })
      })
    },
    // 打开修改订单的对话框
    openUpdateOrderDialog() {
      this.changeFrom.id = this.orderDetail.id
      this.changeFrom.sharingNo = this.orderDetail.sharingNo
      this.changeFrom.tradingAmount = this.orderDetail.tradingAmount + this.orderDetail.tradingCommission
      this.updateOrderDialog = true
    },
    // 取消订单
    cancelOrder() {
      const _this = this
      _this.$confirm(_this.$t('lbs.FundOrder.CancelTip'), _this.$t('lbs.common.tip'), {
        confirmButtonText: _this.$t('lbs.common.confirm'),
        showCancelButton: false,
        type: _this.$t('lbs.common.warning')
      }).then(() => {
        _this.loading = true
        const requestDate = { id: _this.id }
        axios.post(`${_this.LBSGateway}/fund-experience/fund/order/cancellation`, requestDate, { headers: { token: _this.token }})
          .then(response => {
            _this.loading = false
            if (response.data.code === '200') {
              _this.$message.success(_this.$t('lbs.FundOrder.CancelTip2'), _this.$t('lbs.common.success'))
              _this.$router.go(-1)
            } else {
              _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
            }
          })
          .catch(error => {
            _this.loading = false
            _this.$message.error(error.message, _this.$t('lbs.common.error'))
          })
      })
    },
    // 获取基金订单详情
    getFundOrderDetail() {
      const _this = this
      _this.loading = true
      const requestDate = { id: _this.id }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/order/orderDetailRetrievalById`, requestDate, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            const resutl = response.data.data
            resutl.lastupdateDate = _this.$moment(resutl.lastupdateDate).format('YYYY-MM-DD HH:mm:ss')
            resutl.requestTime = _this.$moment(resutl.requestTime).format('YYYY-MM-DD HH:mm:ss')
            resutl.tradingOption = resutl.tradingOption.toUpperCase()
            resutl.status = resutl.status.toUpperCase()
            _this.orderDetail = resutl
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    }
  }
}
</script>

<style lang="scss" scoped>
  .fund-order-detail {
    margin: 0 50px;

    .fund-order-detail-title {
      font-size: 20px;
      color: #22C1E6;
    }

    .fund-code {
      display: inline-block;
    }

    .fund-name {
      display: inline-block;
      width: 595px;
    }
  }
</style>

<style lang="scss">
  .fund-order-detail {
    .el-form-item {
      margin-bottom: 0;
    }

    .order-info .el-form-item__label {
      color: #22C1E6;
    }
  }
</style>
