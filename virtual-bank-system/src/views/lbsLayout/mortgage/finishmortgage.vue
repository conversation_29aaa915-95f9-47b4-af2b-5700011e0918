<template>
  <div class="finishmortgage">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.MortgageApplication') }}</p>
        </div>
      </el-col>
      <el-col :span="24">
        <div style="width: 49%;margin: 80px auto 0">
          <p style="font-size: 40px;line-height: 80px">{{ $t('lbs.Wereceivedyourapplication') }}</p>
          <p style="font-size: 30px;line-height: 60px">{{ $t('lbs.TipInfo') }}</p>
          <el-button style="float: right" type="primary" @click="$router.push({path: '/lbsmortgage'})">{{ $t('lbs.Done') }}</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {}
</script>

<style lang="scss" scoped>
  .finishmortgage {
    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }
  }
</style>
