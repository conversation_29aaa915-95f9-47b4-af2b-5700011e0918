<template>
  <div v-loading.fullscreen.lock="loading" class="mortgagecalculator">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.MortgageCalculator') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div style="text-align: center;margin-top: 40px;margin-bottom: 40px">
          <el-radio-group v-model="radio">
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.Borrow') }}</div>
              <el-radio-button label="Borrow">{{ $t('lbs.Borrow') }}</el-radio-button>
            </el-tooltip>
          </el-radio-group>
        </div>
        <el-row>
          <el-col :span="12" style="border-right: 1px #ccc solid">
            <div style="padding: 40px 0 0 120px">
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.YourMonthlyIncome') }}</div>
                <p style="font-size: 18px;margin-bottom: 30px;display: inline-block">{{ $t('lbs.Yourmonthlyincome') }}</p>
              </el-tooltip>
              <br>
              <el-input
                v-model="monthlyIncome"
                :placeholder="$t('lbs.InputAmount')"
                class="input-with-select"
                maxlength="13"
                style="width: 370px;margin-left: 15px;margin-bottom: 130px"
              >
                <el-select
                  slot="prepend"
                  v-model="CurrentCurrency"
                  placeholder=""
                  style="width: 100px"
                >
                  <el-option
                    v-for="(item,index) in CurrencyList"
                    :key="index"
                    :label="item.ccycode"
                    :value="item.ccycode"
                    :disabled="item.ccycode !== 'HKD'"
                  />
                </el-select>
              </el-input>
              <br>
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.LoanPeriod') }}</div>
                <p style="font-size: 18px;margin-bottom: 30px;display: inline-block">{{ $t('lbs.loanPeriod') }}</p>
              </el-tooltip>

              <el-slider
                v-model="loanPeriod"
                :min="1"
                :max="30"
                style="width: 360px;margin-left: 15px;margin-bottom: 40px"
              />
              <el-button type="success" style="margin-left: 150px" @click="loanCalculator()">
                {{ `${$t('lbs.Confirm')}` }}
              </el-button>
            </div>
          </el-col>
          <el-col :span="12">
            <el-card style="width: 60%;height: 340px;margin: 20px auto 0">
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.MaxLoanAmount') }}</div>
                <p style="text-align: center;line-height: 25px">{{ $t('lbs.MaxLoanAmount') }}</p>
              </el-tooltip>
              <p style="text-align: center;line-height: 25px">{{ `${loanInfo.ccyCode === undefined ? 'HKD' :
                loanInfo.ccyCode} ${loanInfo.maxLoanAmount === undefined ? 0.00 :
                $lbs.decimal_format(loanInfo.maxLoanAmount)}` }}</p>
              <el-tabs v-model="activeName" type="card">
                <el-tab-pane :label="$t('lbs.RepaymentByFortnight')" name="first">
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.LoanPeriod') }}</div>
                      <span class="labels">{{ $t('lbs.LoanPeriod') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.repaymentByFortnight === undefined ? 0 : loanInfo.repaymentByFortnight.loanPeriod}` }}</span>
                  </div>
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.RepaymentPerPhase') }}
                      </div>
                      <span class="labels">{{ $t('lbs.RepaymentPerPhase') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.ccyCode === undefined ? 'HKD' : loanInfo.ccyCode} ${loanInfo.repaymentByFortnight === undefined ? '' : $lbs.decimal_format(loanInfo.repaymentByFortnight.repaymentPerPhase)}` }}</span>
                  </div>
                  <div>
                    <span class="labels">{{ $t('lbs.TotalPhase') }}</span>
                    <span class="values">{{ `${loanInfo.repaymentByFortnight === undefined ? 0 : loanInfo.repaymentByFortnight.totalPhase}` }}</span>
                  </div>
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.MortgageCalculator.TotalInterestPayable') }}
                      </div>
                      <span class="labels">{{ $t('lbs.TotalInterestPayable') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.ccyCode === undefined ? 'HKD' : loanInfo.ccyCode} ${loanInfo.repaymentByFortnight === undefined ? '' : $lbs.decimal_format(loanInfo.repaymentByFortnight.totalInterestPayable)}` }}</span>
                  </div>
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.TotalRepayment') }}</div>
                      <span class="labels">{{ $t('lbs.TotalRepaymentAmount') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.ccyCode === undefined ? 'HKD' : loanInfo.ccyCode} ${loanInfo.repaymentByFortnight === undefined ? '' : $lbs.decimal_format(loanInfo.repaymentByFortnight.totalRepaymentAmount)}` }}</span>
                  </div>
                </el-tab-pane>
                <el-tab-pane :label="$t('lbs.RepaymentByMonth')" name="second">
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.LoanPeriod') }}</div>
                      <span class="labels">{{ $t('lbs.LoanPeriod') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.repaymentByMonth === undefined ? 0 : loanInfo.repaymentByMonth.loanPeriod}` }}</span>
                  </div>
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.RepaymentPerPhase') }}
                      </div>
                      <span class="labels">{{ $t('lbs.RepaymentPerPhase') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.ccyCode === undefined ? 'HKD' : loanInfo.ccyCode} ${loanInfo.repaymentByMonth === undefined ? '' : $lbs.decimal_format(loanInfo.repaymentByMonth.repaymentPerPhase)}` }}</span>
                  </div>
                  <div>
                    <span class="labels">{{ $t('lbs.TotalPhase') }}</span>
                    <span class="values">{{ `${loanInfo.repaymentByMonth === undefined ? 0 : loanInfo.repaymentByMonth.totalPhase}` }}</span>
                  </div>
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.MortgageCalculator.TotalInterestPayable') }}
                      </div>
                      <span class="labels">{{ $t('lbs.TotalInterestPayable') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.ccyCode === undefined ? 'HKD' : loanInfo.ccyCode} ${loanInfo.repaymentByMonth === undefined ? '' : $lbs.decimal_format(loanInfo.repaymentByMonth.totalInterestPayable)}` }}</span>
                  </div>
                  <div>
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageCalculator.TotalRepayment') }}</div>
                      <span class="labels">{{ $t('lbs.TotalRepaymentAmount') }}</span>
                    </el-tooltip>
                    <span class="values">{{ `${loanInfo.ccyCode === undefined ? 'HKD' : loanInfo.ccyCode} ${loanInfo.repaymentByMonth === undefined ? '' : $lbs.decimal_format(loanInfo.repaymentByMonth.totalRepaymentAmount)}` }}</span>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
          </el-col>
        </el-row>
        <div style="text-align: center;margin-top: 40px">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 150px">{{ $t('lbsTips.MortgageOverview.ApplyMortgage') }}</div>
            <el-button
              type="primary"
              class="mortgage-button"
              @click="$router.push({path: '/lbsmortgage/applymortgage'})"
            >
              {{ $t('lbs.ApplyMortgage') }}
            </el-button>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    radio: 'Borrow',
    monthlyIncome: '',
    CurrentCurrency: 'HKD',
    CurrencyList: '',
    loanPeriod: 3,
    loanInfo: {},
    activeName: 'first'
  }),
  computed: {},
  watch: {
    'monthlyIncome'() {
      this.clearNoNum()
    }
  },
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.getCurrencyList()
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupmortgagecalculator'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
  },
  methods: {
    getCurrencyList() {
      const vue = this
      vue.loading = true
      axios.get(
        `${vue.LBSGateway}/sysadmin-experience/sysadmin/currency/currencyTypeRetrieval`, { headers: { token: vue.token }})
        .then(response => {
          vue.loading = false
          if (response.data.code === '200') {
            vue.CurrencyList = response.data.data
          }
        })
        .catch(error => {
          vue.loading = false
          vue.$mui.alert(error.message, vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
        })
    },
    loanCalculator() {
      if (this.monthlyIncome.length < 1 || Number(this.monthlyIncome) <= 0) {
        this.$message.warning(this.$t('lbs.monthlyIncomeTip1'))
        return
      }
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/loan-experience/mortgage/loanCalculator`, {
        ccyCode: _this.CurrentCurrency,
        loanPeriod: _this.loanPeriod,
        monthlysalary: _this.monthlyIncome
      }, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.loanInfo = response.data.data
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    clearNoNum() {
      this.monthlyIncome = this.monthlyIncome.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.monthlyIncome = this.monthlyIncome.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.monthlyIncome = this.monthlyIncome.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.monthlyIncome = this.monthlyIncome.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      this.monthlyIncome = this.monthlyIncome.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    }
  }
}
</script>

<style lang="scss" scoped>

  .mortgagecalculator {
    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

    .main {
      padding: 50px 60px;
    }

    .labels, .values {
      font-size: 15px;
      line-height: 36px;
    }

    .values {
      float: right;
    }
  }

</style>

<style>
  .mortgagecalculator .el-input__icon {
    height: 36px;
  }

  .mortgagecalculator .el-input__inner {
    margin-bottom: 0;
    height: 36px;
  }
</style>
