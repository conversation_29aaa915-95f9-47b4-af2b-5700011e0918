<template>
  <div v-loading.fullscreen.lock="loading" class="mortgagevaluation">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.MortgageOverview') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <p>Mortgage Valuation</p>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  data: () => ({
    token: null,
    loading: false
  }),
  computed: {},
  watch: {},
  created() {
    this.token = window.sessionStorage.getItem('token')
  },
  mounted() {
  },
  methods: {}
}
</script>

<style lang="scss" scoped>

  .mortgagevaluation {

    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

  }

</style>
