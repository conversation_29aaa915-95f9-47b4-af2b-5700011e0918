<template>
  <div class="stepfour">
    <el-row>
      <el-col :span="24" :offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-finish" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 140px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 130px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 200px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right">
          <p class="text-title is-finish">Move Money From</p>
          <div style="padding: 43px 0 43px">
            <p class="text-account">Account</p>
            <p style="display: inline-block;width: 400px">{{ paymentInfo.accountNumber }}</p>
          </div>
          <p class="text-title is-finish">Move Money To</p>
          <div style="padding: 20px 0 20px">
            <p class="text-account" style="width: 170px;margin: 20px 0">Payee Number</p>
            <p style="display: inline-block;width: 400px">{{ paymentInfo.payeenumber }}</p>
          </div>
          <p class="text-title is-finish">Transfer Details</p>
          <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px">Amount</p>
          <p style="display: inline-block;width: 400px">{{ paymentInfo.currency + ' ' + $lbs.decimal_format(paymentInfo.amount) }}</p>
          <br>
          <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px">Effective Date</p>
          <p style="display: inline-block;width: 400px">{{ paymentInfo.effectiveDay }}</p>
          <br>
          <div style="width: 620px">
            <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px">
              Reference</p>
            <p style="display: inline-block;width: 400px">{{ paymentInfo.reference }}</p>
          </div>
          <p class="text-title is-finish">Confirmation</p>
          <div style="width: 800px">
            <el-button
              class="button"
              style="float: right;background-color: #109eae;color: #ffffff"
              icon="el-icon-check"
              @click="submit('submit')"
            >CONFIRM
            </el-button>
            <el-button
              class="button"
              style="float: right;background-color: #ED1B1B;color: #ffffff;margin-right: 20px"
              icon="el-icon-close"
              @click="submit('back')"
            >CANCEL
            </el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Steptwo',
  data() {
    return {
      currency: 'HKD',
      accountNumber: '',
      savingList: [],
      currentList: [],
      creditCardList: [],
      accountList: [],
      paymentInfo: {},
      checkList: [' ', '  ', '   '],
      creditCardAccountList: [],
      category: '',
      payeename: '',
      payeenumber: '',
      categoryList: [],
      payeeList: [],
      payeeNumberList: [],
      amount: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > Date.now() + 1000 * 60 * 60 * 24 * 365
        }
      },
      value: '',
      reference: ''
    }
  },
  watch: {
    amount: {
      handler() {
        this.clearNoNum()
      }
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentDetail'))
    this.initdata()
  },
  methods: {
    initdata() {
      this.accountNumber = this.paymentInfo.accountNumber
      this.category = this.paymentInfo.categoryid
      this.payeename = this.paymentInfo.payeeid
      this.payeenumber = this.paymentInfo.payeenumber
    },
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/lbspayment/makepayment' })
      } else {
        this.submitPayment()
      }
    },
    submitPayment() {
      var vue = this
      // 如果时间是当天将effectiveday置为空
      var nowDate = vue.$moment.parseZone(new Date().getTime()).local().format('YYYY-MM-DD')
      if (this.paymentInfo.effectiveDay === nowDate) {
        this.paymentInfo.effectiveDay = ''
      }
      if (this.paymentInfo.effectiveDay.length > 0) {
        this.paymentInfo.effectiveDay = new Date(this.paymentInfo.effectiveDay.replace(/[\r-]/g, '/')).getTime()
      }

      var accountAndType = this.paymentInfo.accountNumber.split(' ')
      var account = accountAndType[1]
      var type = ''
      if (accountAndType[0] === 'Saving') {
        type = 'SAVI'
      } else if (accountAndType[0] === 'Current') {
        type = 'CURR'
      } else if (accountAndType[0] === 'CreditCard') {
        type = 'CRED'
      }

      var requesttimedata = {
        'customerAccountNumber': account,
        'customerAccountType': type,
        'payeeId': this.paymentInfo.payeeid,
        'payeeNumber': this.paymentInfo.payeenumber,
        'paymentAmount': this.paymentInfo.amount,
        'paymentEffectiveDay': this.paymentInfo.effectiveDay,
        'remarks': this.paymentInfo.reference,
        'transactionCurrency': this.paymentInfo.currency
      }
      console.log(requesttimedata)
      var btnArray = ['cancel', 'ok']
      this.$mui.confirm('Do you confirm payment?', 'Prompt', btnArray, function(e) {
        if (e.index === 1) {
          vue.$mui.ajax(vue.LBSGateway + '/payment-experience/payment/paymentTransaction', {
            data: requesttimedata,
            dataType: 'json', // 服务器返回json格式数据
            type: 'post', // HTTP请求类型
            timeout: 60000,
            headers: {
              'accept': '*/*',
              'token': vue.token,
              'clientid': vue.$parent.clientid,
              'messageid': vue.$parent.messageid,
              'Content-Type': 'application/json'
            },
            beforeSend: function() {
              // document.getElementById("load").style.display = "inline";
            },
            complete: function() {
              // document.getElementById("load").style.display = "none";
            },
            success: function(data) {
              console.log('Sell stock response data:' + JSON.stringify(data))
              if (data.code === '200') {
                window.sessionStorage.removeItem('paymentDetail')
                vue.$mui.alert('Payment Succeeds.', 'Success', 'OK', function() {
                  vue.$router.push({ path: '/lbspayment/makepayment' })
                })
              } else {
                vue.$mui.alert('Payment failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg = 'Payment failed! The response is: \n' + xhr.responseText + '.'
              if (type === 'timeout') {
                msg = 'Payment failed. Time out!'
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          })
        }
      })
    },
    clearNoNum() {
      this.amount = this.amount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.amount = this.amount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.amount = this.amount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.amount = this.amount.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      this.amount = this.amount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    }
  }
}
</script>

<style scoped>
  .stepfour p {
    line-height: 1;
    color: #707070;
  }

  .stepfour .text-title {
    font-size: 40px;
    color: #707070;
  }

  .stepfour .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .stepfour .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .stepfour .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .stepfour .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .stepfour .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .stepfour .el-input__icon {
    height: 40px;
  }

  .stepfour .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .stepfour .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .stepfour .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .stepfour .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .stepfour .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .stepfour .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .stepfour .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .stepfour .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }

  .stepfour .el-textarea__inner {
    height: 100px;
  }
</style>
