<template>
  <div v-loading.fullscreen.lock="loading" class="accountdetail">
    <el-row>
      <el-col :span="24">
        <div class="title" style="position: relative">
          <el-tooltip effect="dark" placement="bottom">
            <div
              v-if="accountType === 'Saving'"
              slot="content"
              style="width: 300px"
            >{{ $t('lbsTips.accountDetailTip.accountdetailIntro') }}
            </div>
            <div
              v-else-if="accountType === 'Current'"
              slot="content"
              style="width: 300px"
            >{{ $t('lbsTips.accountDetailTip.accountdetailIntro3') }}
            </div>
            <p v-if="accountType === 'Saving'" style="display: inline-block">
              {{ $t('lbsTips.dashboardTip.Savingtitile') }}</p>
            <p v-else style="display: inline-block">{{ $t('lbsTips.dashboardTip.Currenttitile') }}</p>
          </el-tooltip>
          <div style="position: absolute;top:0;right: 40px;">
            <p style="text-align: center">{{ $t('lbs.switchAccounts') }}</p>
            <div
              v-if="creditCard !== undefined"
              class="top-card"
              style="margin-right: 10px;background-color: #DEFFC6;position: relative;padding:10px"
              @click="accountDetail(creditCard.accountNumber,creditCard.accountType)"
            >
              <svg-icon
                icon-class="visa"
                style="width: 30px;height: 30px;position: absolute;right: 25px;top: 30px"
              />
              <p
                style="position: absolute;left: 25px;top: 10px;line-height: 1;font-size: 18px"
              >{{ $t('lbsTips.dashboardTip.CreditCardtitile') }}</p>
              <hr style="margin-top: 60px">
              <span
                style="font-size: 12px;color: #707070;position: absolute;bottom: 8px;left: 25px"
              >{{ creditCard.accountNumber }}</span>
            </div>
            <div
              v-if="saving"
              class="top-card"
              style="margin-right: 10px;background-color: #FFF9CC;position: relative;padding:10px"
              @click="accountDetail(saving.accountNumber,saving.accountType)"
            >
              <p
                v-if="saving.accountType === 'Saving'"
                style="position: absolute;left: 25px;top: 10px;line-height: 1;font-size: 18px"
              >{{ $t('lbsTips.dashboardTip.Savingtitile') }}</p>
              <p
                v-else-if="saving.accountType === 'Current'"
                style="position: absolute;left: 25px;top: 10px;line-height: 1;font-size: 18px"
              >{{ $t('lbsTips.dashboardTip.Currenttitile') }}</p>
              <hr style="margin-top: 60px">
              <span
                style="font-size: 12px;color: #707070;position: absolute;bottom: 8px;left: 25px"
              >{{ saving.accountNumber }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="7" class="el-col el-col-24 el-col-xs-24 el-col-md-24 el-col-lg-7 el-col-xl-7">
        <div class="card-item" style="background-color: #FFFF93">
          <p
            v-if="accountType === 'Saving'"
            style="margin-bottom: 60px;margin-left: 12px;font-size: 40px;color: #707070;line-height: 1"
          >{{ $t('lbsTips.dashboardTip.Savingtitile') }}</p>
          <p
            v-else-if="accountType === 'Current'"
            style="margin-bottom: 60px;margin-left: 12px;font-size: 40px;color: #707070;line-height: 1"
          >{{ $t('lbsTips.dashboardTip.Currenttitile') }}</p>
          <p style="margin-bottom: 50px;font-size: 22px;line-height: 1">
            {{ accountDetails.currencycode + ' ' +
              $lbs.decimal_format(accountDetails.availablebalance) }}
          </p>
          <hr>
          <p
            style="margin-left: 10px;margin-top: 15px;font-size: 18px;line-height: 1"
          >{{ accountDetails.accountnumber }}</p>
        </div>
      </el-col>
      <el-col
        :span="17"
        class="el-col el-col-24 el-col-xs-24 el-col-md-24 el-col-lg-17 el-col-xl-17"
      >
        <div style="padding-left: 30px">
          <el-row>
            <el-tooltip effect="dark" placement="right">
              <div
                slot="content"
                style="width: 300px"
              >{{ $t('lbsTips.accountDetailTip.accountdetailIntro2') }}
              </div>
              <p
                style="margin: 40px 0 10px;line-height: 1;font-size: 25px;color: #22C1E6;display: inline-block"
              >{{ $t('lbs.accountDetails') }}</p>
            </el-tooltip>
            <table
              style="border: 1px #E2E2E2 solid;height: 160px;width: 97%;color: #707070;font-size: 15px;box-shadow: 3px 3px 3px #bbbbbb;"
            >
              <tr style="border-bottom:1px #E2E2E2 solid">
                <el-tooltip class="item" effect="dark" placement="right">
                  <div
                    slot="content"
                    style="width: 300px"
                  >{{ $t('lbsTips.accountDetailTip.AccountNo') }}
                  </div>
                  <td style="padding-left: 10px">{{ $t('lbs.accountNo') }}</td>
                </el-tooltip>
                <td>{{ accountDetails.accountnumber }}</td>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div
                    slot="content"
                    style="width: 300px"
                  >{{ $t('lbsTips.accountDetailTip.AccountBalanceTip') }}
                  </div>
                  <td
                    style="border-left:1px #E2E2E2 solid;padding-left: 20px"
                  >{{ $t('lbs.ledgerbalance') }}
                  </td>
                </el-tooltip>
                <td>{{ $lbs.decimal_format(accountDetails.ledgerbalance) }}</td>
              </tr>
              <tr style="border-bottom:1px #E2E2E2 solid">
                <el-tooltip class="item" effect="dark" placement="right">
                  <div
                    slot="content"
                    style="width: 300px"
                  >{{ $t('lbsTips.accountDetailTip.AccountType') }}
                  </div>
                  <td style="padding-left: 10px">{{ $t('lbs.accountType') }}</td>
                </el-tooltip>
                <td>{{ accountType }}</td>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div
                    slot="content"
                    style="width: 300px"
                  >{{ $t('lbsTips.accountDetailTip.AvailableBalance') }}
                  </div>
                  <td
                    style="border-left:1px #E2E2E2 solid;padding-left: 20px"
                  >{{ $t('lbs.availableBalance') }}
                  </td>
                </el-tooltip>
                <td>{{ $lbs.decimal_format(accountDetails.availablebalance) }}</td>
              </tr>
              <tr>
                <el-tooltip class="item" effect="dark" placement="right">
                  <div
                    slot="content"
                    style="width: 300px"
                  >{{ $t('lbsTips.accountDetailTip.Currency1') }}
                  </div>
                  <td style="padding-left: 10px">{{ $t('lbs.currency') }}</td>
                </el-tooltip>
                <td>{{ accountDetails.currencycode }}</td>
                <td
                  style="border-left:1px #E2E2E2 solid;padding-left: 20px"
                >{{ $t('lbs.holdingbalance') }}
                </td>
                <td>{{ $lbs.decimal_format(accountDetails.holdingbalance) }}</td>
              </tr>
            </table>

          </el-row>
          <el-row>
            <p>{{ $t('lbs.lastUpdatedTime') }}: {{ accountDetails.lastupdateddate }}</p>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div
                slot="content"
                style="width: 300px"
              >{{ $t('lbsTips.accountDetailTip.PayorTransfer') }}
              </div>
              <el-button
                style="margin-top: 10px"
                type="primary"
                @click="pageJump('payment')"
              >{{ $t('lbsTips.accountDetailTip.PayBtn') }}
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div
                slot="content"
                style="width: 300px"
              >{{ $t('lbsTips.accountDetailTip.PayorTransfer') }}
              </div>
              <el-button
                style="margin-top: 10px"
                type="primary"
                @click="pageJump('transfer')"
              >{{ $t('lbsTips.accountDetailTip.TransferBtn') }}
              </el-button>
            </el-tooltip>
            <el-tooltip
              v-if="accountType !== 'Saving'"
              class="item"
              effect="dark"
              placement="bottom"
            >
              <div
                slot="content"
                style="width: 300px"
              >{{ $t('lbsTips.accountDetailTip.ChequeBookRequest') }}
              </div>
              <el-button
                style="margin-top: 10px"
                type="primary"
                @click="chequeBookDialogVisible = true"
              >{{ $t('lbsTips.accountDetailTip.ChequeBookRequestBtn') }}
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div
                slot="content"
                style="width: 300px"
              >{{ $t('lbsTips.accountDetailTip.MyTermDepositTip') }}
              </div>
              <el-button
                style="margin-top: 10px"
                type="primary"
                @click="pageJump('termdeposit')"
              >{{ $t('lbsTips.accountDetailTip.DepositMyTermDepositBtn') }}
              </el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.accountDetailTip.DepositTip') }}</div>
              <el-button
                style="margin-top: 10px"
                type="primary"
                @click="pageJump('deposit')"
              >{{ $t('lbsTips.accountDetailTip.DepositBtn') }}
              </el-button>
            </el-tooltip>
            <!--            <el-tooltip class="item" effect="dark" placement="bottom">-->
            <!--              <div slot="content" style="width: 300px">{{$t('lbsTips.accountDetailTip.Estatement')}}</div>-->
            <!--              <el-button-->
            <!--                style="margin-top: 10px"-->
            <!--                type="primary"-->
            <!--              >{{$t('lbsTips.accountDetailTip.EStatementBtn')}}-->
            <!--              </el-button>-->
            <!--            </el-tooltip>-->
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div style="padding: 0 50px 0 20px">
          <el-tooltip effect="dark" placement="top">
            <div
              slot="content"
              style="width: 300px"
            >{{ $t('lbsTips.accountDetailTip.transactionHistory') }}
            </div>
            <p
              style="line-height: 1;font-size: 25px;color: #22C1E6;width: 400px;"
            >{{ $t('lbs.transactionHistory') }}</p>
          </el-tooltip>
          <div
            style="margin: 30px 0 0 30px;border: 1px #E2E2E2 solid;padding: 20px;background-color: #ffffff"
          >
            <el-tooltip class="item" effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.accountDetailTip.Period') }}</div>
              <span
                style="margin-right: 30px;color: #707070;font-size: 15px"
              >{{ $t('lbsTips.accountDetailTip.PeriodBtn') }}:</span>
            </el-tooltip>
            <el-date-picker
              id="date1"
              v-model="value1"
              style="margin-bottom: 10px"
              align="right"
              type="date"
              :picker-options="pickerOptions1"
              :default-time="'00:00:00'"
              @change="getcurrentsavingaccounthistory()"
            />
            <span style="margin: 0 30px 0;color: #707070;font-size: 15px">To</span>
            <el-date-picker
              id="date2"
              v-model="value2"
              align="right"
              type="date"
              :picker-options="pickerOptions2"
              :default-time="'23:59:59'"
              @change="getcurrentsavingaccounthistory()"
            />
            <table
              cellspacing="0"
              cellpadding="0"
              border="0"
              class="el-table__header el-table"
              style="width: 100%;line-height: 40px;"
            >
              <colgroup />
              <thead class="has-gutter">
                <tr class>
                  <th colspan="1" rowspan="1" class="el-table_2_column_5 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div
                        slot="content"
                        style="width: 300px"
                      >{{ $t('lbsTips.accountDetailTip.TransactionDate') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.Date') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_2_column_6 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div
                        slot="content"
                        style="width: 300px"
                      >{{ $t('lbsTips.accountDetailTip.TransactionDetails') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.Details') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_2_column_7 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div
                        slot="content"
                        style="width: 300px"
                      >{{ $t('lbsTips.accountDetailTip.TransactionAmount') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.TransactionAmountTh') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_2_column_8 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div
                        slot="content"
                        style="width: 300px"
                      >{{ $t('lbsTips.accountDetailTip.AccountBalance') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.AccountBalanceTh') }}</div>
                    </el-tooltip>
                  </th>
                  <th class="gutter" style="width: 15px;" />
                </tr>
              </thead>
            </table>
            <el-table
              :data="currentsavingaccounthistory"
              border
              height="230"
              :show-header="false"
              style="width: 100%;height: 260px"
            >
              <el-table-column prop="trandate" :label="$t('lbsTips.accountDetailTip.Date')" />
              <el-table-column prop="trandesc" :label="$t('lbsTips.accountDetailTip.Details')" />
              <el-table-column
                prop="tranamt"
                :label="$t('lbsTips.accountDetailTip.TransactionAmountTh')"
              />
              <el-table-column
                prop="accountbalance"
                :label="$t('lbsTips.accountDetailTip.AccountBalanceTh')"
              />
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      :title="$t('lbs.chequeBook')"
      :visible.sync="chequeBookDialogVisible"
      width="50%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <span slot="footer" class="dialog-footer">
        <el-form :model="chequeBookForm" label-width="300px" style="margin: 100px 0 150px 0">
          <el-form-item :label="$t('lbs.CurrentAccountNumber')">
            <el-select v-model="chequeBookForm.accountNumber" placeholder="" style="width: 300px">
              <el-option v-for="(item,index) in currentAccountList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lbs.ChequeBookType')">
            <el-radio-group v-model="chequeBookForm.chequeBookType">
              <el-radio label="S">Short (50页)</el-radio>
              <el-radio label="L">Long (100页)</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-button @click="chequeBookDialogVisible = false">{{ $t('lbs.cancel') }}</el-button>
        <el-button type="primary" @click="applyChequeBook">{{ $t('lbs.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Accountdetail',
  data() {
    return {
      loading: false,
      token: null,
      accountNumber: '',
      accountType: '',
      accountDetails: {},
      accountList: [],
      creditCard: [],
      saving: [],
      currentAccountList: [],
      currentsavingaccounthistory: [],
      pickerOptions1: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: this.$t('lbs.aWeekAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', date)
            }
          },
          {
            text: this.$t('lbs.aMonthAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', date)
            }
          },
          {
            text: this.$t('lbs.threeMonthsAgo'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', date)
            }
          }
        ]
      },
      pickerOptions2: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: this.$t('lbs.today'),
            onClick(picker) {
              const date = new Date()
              date.setTime(date.getTime())
              picker.$emit('pick', date)
            }
          }
        ]
      },
      value1: this.$moment
        .parseZone(new Date().getTime() - 1000 * 60 * 60 * 24 * 30)
        .local()
        .format('YYYY-MM-DD'),
      value2: this.$moment
        .parseZone(new Date().getTime())
        .local()
        .format('YYYY-MM-DD'),
      chequeBookDialogVisible: false,
      chequeBookForm: {
        accountNumber: '',
        chequeBookSize: 50,
        chequeBookType: 'S'
      },
      customernumber: ''
    }
  },
  watch: {
    'chequeBookForm.chequeBookType'(newValue) {
      if (newValue === 'S') {
        this.chequeBookForm.chequeBookSize = 50
      } else {
        this.chequeBookForm.chequeBookSize = 100
      }
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.accountNumber = window.sessionStorage.getItem('accountNumber')
    this.accountType = window.sessionStorage.getItem('accountType')
    if (
      window.sessionStorage.getItem('showTips') === 'true' &&
        window.sessionStorage.getItem('token')
    ) {
      if (this.accountType === 'Saving') {
        this.$alert(this.$t('lbs.popupSaving'), this.$t('lbs.Tip'), {
          dangerouslyUseHTMLString: true
        })
      } else {
        this.$alert(this.$t('lbs.popupCurrent'), this.$t('lbs.Tip'), {
          dangerouslyUseHTMLString: true
        })
      }
    }
    const currentAccountList = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
    for (let i = 0; i < currentAccountList.length; i++) {
      if (currentAccountList[i].accountStatus === 'A') {
        this.currentAccountList.push(
          {
            label: currentAccountList[i].accountNumber,
            value: currentAccountList[i].accountNumber
          }
        )
      }
    }
    if (this.currentAccountList && this.currentAccountList[0]) this.chequeBookForm.accountNumber = this.currentAccountList[0].value
    this.accountList = JSON.parse(window.sessionStorage.getItem('accountList'))
    this.getAccountDetail()
    this.getcurrentsavingaccounthistory()
    this.topCard()
  },
  methods: {
    applyChequeBook() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/chequeBook/creation`, _this.chequeBookForm, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.chequeBookDialogVisible = false
            _this.$message.success(_this.$t('lbs.ApplyChequeBookSuccess'), _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    getAccountDetail() {
      var vue = this
      this.$mui.ajax(
        this.LBSGateway +
          '/deposit-experience/deposit/account/accountDetails/' +
          this.accountNumber,
        {
          dataType: 'json',
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            customerNumber: vue.customernumber
          },
          type: 'post',
          timeout: 60000,
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(result1) {
            console.log('result1: ' + JSON.stringify(result1))
            if (result1 && result1.code === '200') {
              var data1 = result1.data.account
              if (!data1.availablebalance) data1.availablebalance = 0.0
              if (typeof data1.availablebalance) {
                data1.availablebalance = Number(
                  data1.availablebalance
                    .toString()
                    .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
                )
              }
              data1.lastupdateddate = new Date(
                Number(data1.lastupdateddate)
              ).toUTCString()
              vue.accountDetails = data1
              console.log('--------------' + JSON.stringify(vue.accountDetails))
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get saving account detail failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get saving account detail failed. Time out!'
            }
            if (
              typeof JSON.parse(xhr.responseText) === 'object' &&
                JSON.parse(xhr.responseText).code === '202001'
            ) {
              msg = JSON.parse(xhr.responseText).error
            } else if (
              typeof JSON.parse(xhr.responseText) === 'object' &&
                JSON.parse(xhr.responseText).code === '404004'
            ) {
              msg = JSON.parse(xhr.responseText).error
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      )
    },
    getcurrentsavingaccounthistory() {
      var vue = this

      var fromdate = new Date(this.value1).getTime()
      var todate = new Date(this.value2).getTime() + ********

      var requestdata7 = {
        accountnumber: this.accountNumber,
        index: 1,
        items: 9999,
        transFromDate: fromdate,
        transToDate: todate,
        trantype: ''
      }
      console.log(JSON.stringify(requestdata7))
      this.$mui.ajax(
        this.LBSGateway + '/deposit-experience/transactionLog/enquiry',
        {
          data: requestdata7,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json',
            customerNumber: vue.customernumber
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              var response7 = data.data
              var str7 = ''
              // Account Info
              if (response7) {
                var account7 = response7
                account7.sort(function(a, b) {
                  return (
                    Date.parse(Number(b.trandate)) -
                      Date.parse(Number(a.trandate))
                  ) // 时间正序
                })
                account7.length >= 50
                  ? (vue.currentsavingaccounthistory = account7.slice(0, 50))
                  : (vue.currentsavingaccounthistory = account7)
                for (
                  var i = 0;
                  i < vue.currentsavingaccounthistory.length;
                  i++
                ) {
                  vue.currentsavingaccounthistory[
                    i
                  ].trandate = vue.$moment
                    .parseZone(
                      Number(vue.currentsavingaccounthistory[i].trandate)
                    )
                    .local()
                    .format('YYYY-MM-DD HH:mm:ss')
                  if (
                    vue.currentsavingaccounthistory[i].trandesc ==
                      'tranfer in' ||
                      vue.currentsavingaccounthistory[i].trandesc === 'deposit' ||
                      vue.currentsavingaccounthistory[i].trandesc === 'stock sell' ||

                      vue.currentsavingaccounthistory[i].trandesc === 'foreign sell' ||
                      vue.currentsavingaccounthistory[i].trandesc === 'mortgage loan application'
                  ) {
                    vue.currentsavingaccounthistory[i].tranamt =
                        '+ ' +
                        vue.currentsavingaccounthistory[i].ccy +
                        ' ' +
                        vue.$lbs.decimal_format(
                          vue.currentsavingaccounthistory[i].tranamt
                        )
                  } else if (
                    vue.currentsavingaccounthistory[i].trandesc ==
                      'transfer out' ||
                      vue.currentsavingaccounthistory[i].trandesc ==
                      'stock buy' ||
                      vue.currentsavingaccounthistory[i].trandesc === 'fund buy' ||
                      vue.currentsavingaccounthistory[i].trandesc ==
                      'foreign buy' ||
                      vue.currentsavingaccounthistory[i].trandesc ==
                      'withdrawal' ||
                      vue.currentsavingaccounthistory[i].trandesc ==
                      'Term Deposit Application' ||
                      vue.currentsavingaccounthistory[i].trandesc ==
                      'mortgage loan repayment'
                  ) {
                    vue.currentsavingaccounthistory[i].tranamt =
                        '- ' +
                        vue.currentsavingaccounthistory[i].ccy +
                        ' ' +
                        vue.$lbs.decimal_format(
                          vue.currentsavingaccounthistory[i].tranamt
                        )
                  }
                  vue.currentsavingaccounthistory[i].accountbalance =
                      vue.currentsavingaccounthistory[i].ccy +
                      ' ' +
                      vue.$lbs.decimal_format(
                        vue.currentsavingaccounthistory[i].actualbalamt
                      )
                  vue.currentsavingaccounthistory[
                    i
                  ].trandesc = vue.currentsavingaccounthistory[
                    i
                  ].trandesc.toUpperCase()
                }

                // vue.currentsavingaccounthistory.push({
                //   trandate:
                //     vue.currentsavingaccounthistory[
                //       vue.currentsavingaccounthistory.length - 1
                //     ].trandate,
                //   accountbalance:
                //     vue.currentsavingaccounthistory[
                //       vue.currentsavingaccounthistory.length - 1
                //     ].ccy +
                //     " " +
                //     vue.$lbs.decimal_format(
                //       vue.currentsavingaccounthistory[
                //         vue.currentsavingaccounthistory.length - 2
                //       ].previousbalamt
                //     ),
                //   trandesc: "Opening Balance"
                // });

                // vue.currentsavingaccounthistory.unshift({
                //   trandate: vue.currentsavingaccounthistory[0].trandate,
                //   accountbalance:
                //     vue.currentsavingaccounthistory[0].ccy +
                //     " " +
                //     vue.$lbs.decimal_format(
                //       vue.currentsavingaccounthistory[0].actualbalamt
                //     ),
                //   trandesc: "Closing Balance"
                // });

                console.log(vue.currentsavingaccounthistory)
              }
            } else if (data.code === '404003') {
              // No transaction record
            } else {
              vue.$mui.alert(
                'Get history info failed! The response is: \n' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get history info failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get history info failed. Time out!'
            }
            if (
              typeof JSON.parse(xhr.responseText) === 'object' &&
                JSON.parse(xhr.responseText).code === '404003'
            ) {
            } else {
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        }
      )
    },
    topCard() {
      this.creditCard = []
      this.saving = []
      var temp = []
      for (var i = 0; i < this.accountList.length; i++) {
        if (this.accountList[i].accountType != this.accountType) {
          temp.push(this.accountList[i])
        }
      }

      for (var i = 0; i < temp.length; i++) {
        if (temp[i].accountType === 'Credit Card') {
          this.creditCard.push(temp[i])
        } else {
          this.saving.push(temp[i])
        }
      }
      this.saving = this.saving[0]
      this.creditCard = this.creditCard[0]
    },
    accountDetail(accountNumber, accountType) {
      if (accountType === 'Credit Card') {
        window.sessionStorage.setItem('creditcardNumber', accountNumber)
        window.sessionStorage.setItem('type', accountType)
        this.$router.push({ path: '/tellerlbsaccountservice/creditcarddetail' })
      } else {
        window.sessionStorage.setItem('accountNumber', accountNumber)
        window.sessionStorage.setItem('accountType', accountType)
        this.accountNumber = accountNumber
        this.accountType = accountType
        this.value1 = this.$moment
          .parseZone(new Date().getTime() - 1000 * 60 * 60 * 24 * 30)
          .local()
          .format('YYYY-MM-DD')
        this.value2 = this.$moment
          .parseZone(new Date().getTime())
          .local()
          .format('YYYY-MM-DD')
        this.getAccountDetail()
        this.getcurrentsavingaccounthistory()
        this.topCard()
      }
    },
    pageJump(page) {
      if (page === 'termdeposit') {
        window.sessionStorage.setItem(
          'customerNumber',
          this.$parent.customernumber
        )
        this.$router.push({ path: '/tellerlbsaccountservice/termdeposit' })
      } else if (page === 'deposit') {
        window.sessionStorage.setItem(
          'customerNumber',
          this.$parent.customernumber
        )
        this.$router.push({ path: '/tellerlbsaccountservice/termdeposit/applydeposit' })
      } else if (page === 'payment') {
        this.$router.push({ path: '/tellerlbspayment/makepayment' })
      } else if (page === 'transfer') {
        this.$router.push({ path: '/tellerlbstransaction/transfer' })
      }
    }
  }
}
</script>

<style scoped>
  .accountdetail .title {
    padding: 18px 28px;
    height: 140px;
    background-color: #ffffff;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .accountdetail .title > p {
    font-size: 50px;
    margin-bottom: 20px;
    margin-top: 25px;
    line-height: 1;
    color: #22c1e6;
    /*line-height: normal;*/
  }

  .accountdetail .title span {
    font-size: 30px;
    line-height: 1;
    color: #707070;
  }

  .accountdetail h4 {
    color: #707070;
  }

  .accountdetail p {
    color: #707070;
    margin: 10px 0 0 0;
  }

  .accountdetail .top-card {
    border-radius: 50px;
    width: 250px;
    height: 100px;
    display: inline-block;
    cursor: pointer;
  }

  .accountdetail .card-item {
    margin: 40px 20px 0 20px;
    padding: 20px 20px;
    background-color: #ffffff;
    border-radius: 40px;
    box-shadow: 3px 3px 3px #bbbbbb;
  }
</style>

<style>
  .accountdetail input[type="text"] {
    margin-bottom: 0;
  }

  .accountdetail .accountdetail .el-input--medium .el-input__inner {
    height: 36px;
    font-size: 15px;
    color: #707070;
  }

  .accountdetail .el-radio {
    margin-top: 8px;
  }
</style>
