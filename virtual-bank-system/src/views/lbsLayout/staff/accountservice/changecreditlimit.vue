<template>
  <div class="changecreditlimit">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 350px">
              Limit decrease: This is designed to decrease a credit card limit.<br/>
              Limit increase: This is designed to increase a credit card limit.
            </div>
          <p>Change Credit Limit</p>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div style="padding: 30px">
          <router-view/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    name: "changecreditlimit"
  }
</script>

<style scoped>
  .changecreditlimit .title {
    padding: 18px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }
  .changecreditlimit .title > p {
    font-size: 50px;
    color: #22C1E6;
    margin-bottom: 20px;
    margin-top: 25px;
    line-height: 1;
    display: inline-block;
  }

  .changecreditlimit .title > span {
    font-size: 30px;
    color: #707070;
    margin-bottom: 20px;
    line-height: 1;
  }
</style>
