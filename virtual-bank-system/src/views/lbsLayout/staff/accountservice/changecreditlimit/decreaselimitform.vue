<template>
  <el-row v-loading.fullscreen.lock="loading" class="selectchangetype">
    <el-col :span="24">
      <p style="font-size: 23px;line-height: 50px">{{ $t('lbs.decreaseLimitTip5') }}</p>
      <hr style="margin-left: 0">
      <p style="color: #707070;font-size: 25px;line-height: 50px">{{ $t('lbs.youSelected') }}</p>
      <div style="width: 100%;height: 80px;border: 1px #22C1E6 solid">
        <el-col :span="8">
          <p style="margin: 0;font-size: 20px;text-align: center;line-height: 80px">{{ $t('lbs.visaCard') }}<span
            style="font-size: 17px"
          >{{ changeLimitAccount.accountNumber }}</span></p>
        </el-col>
        <el-col :span="8">
          <p style="margin: 0;font-size: 20px;text-align: center;line-height: 80px"><PERSON> <PERSON><PERSON></p>
        </el-col>
        <el-col :span="8">
          <p style="margin: 0;font-size: 17px;text-align: center;line-height: 80px">{{ $t('lbs.existingLimit') }}
            {{ changeLimitAccount.ccyCode + ' ' + $lbs.decimal_format(changeLimitAccount.approvedLimit) }}</p>
        </el-col>
      </div>
    </el-col>
    <el-row>
      <el-col :span="12">
        <span style="color: #707070;font-size: 25px;line-height: 50px;">{{ $t('lbs.increaseTip8') }}</span>
      </el-col>
      <el-col :span="12">
        <div style="cursor: pointer;width: 100px" @click="onSubmit('edit')">
          <u style="color: #707070;margin-left: 5px"><span
            style="color: #707070;font-size: 25px;line-height: 50px"
          >EDIT</span></u>
          <svg-icon icon-class="awesome-edit" style="width: 30px; height: 23px" />
        </div>
      </el-col>
      <el-col :span="12" style="padding-left: 150px">
        <p style="margin-top: 30px;font-size: 16px">{{ $t('lbs.existingLimit') }}</p>
      </el-col>
      <el-col :span="12">
        <p style="margin-top: 30px;font-size: 16px">{{ changeLimitAccount.ccyCode + ' ' + $lbs.decimal_format(changeLimitAccount.approvedLimit) }}</p>
      </el-col>
      <el-col :span="12" style="padding-left: 150px">
        <p style="margin-top: 20px;font-size: 16px">{{ $t('lbs.decreaseLimitTip3') }}</p>
      </el-col>
      <el-col :span="12">
        <p style="margin-top: 20px;font-size: 16px">{{ changeLimitAccount.ccyCode + ' ' + $lbs.decimal_format(changeLimitAccount.decreaseLimit)
        }}</p>
      </el-col>
      <el-col :span="12" style="padding-left: 150px">
        <p style="margin-top: 20px;font-size: 16px">{{ $t('lbs.decreaseLimitTip4') }}</p>
      </el-col>
      <el-col :span="12">
        <p style="margin-top: 20px;font-size: 16px">{{ changeLimitAccount.reason2 }}</p>
      </el-col>
      <el-col :span="24">
        <div style="width: 100%;height: 50px;padding: 13px 5px 5px 60px;margin-top: 10px;border: 1px #707070 solid">
          <el-checkbox-group v-model="isSelect">
            <el-checkbox name="type" label="" />
            <p style="display: inline-block;color: #707070;margin: 0 0 0 40px">{{ $t('lbs.increaseTip9') }}</p>
          </el-checkbox-group>

        </div>
      </el-col>
    </el-row>
    <el-col :span="24">
      <div style="margin: 20px 20px 20px 0;float: right;width: 240px">
        <el-button type="danger" @click="onSubmit('back')">{{ $t('lbs.cancel') }}</el-button>
        <el-button id="continue" type="primary" @click="onSubmit('continue')">{{ $t('lbs.common.confirm') }}</el-button>
      </div>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: 'Decreaselimitform',
  data() {
    return {
      loading: false,
      token: null,
      changeLimitAccount: {},
      isSelect: [],
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.changeLimitAccount = JSON.parse(window.sessionStorage.getItem('changeLimitAccount'))
  },
  methods: {
    onSubmit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/tellerlbsaccountservice/creditcarddetail' })
      } else if (type === 'edit') {
        this.$router.push({ path: '/tellerlbsaccountservice/changecreditlimit/selectchangetype' })
      } else {
        if (this.isSelect.length === 1) {
          this.getaccountbalance()
        } else {
          this.$mui.alert(this.$t('lbs.increaseTip10'), 'Error', 'OK')
        }
      }
    },
    getaccountbalance() {
      var vue = this
      var requestdata = {
        'changeAmount': this.changeLimitAccount.approvedLimit - this.changeLimitAccount.decreaseLimit,
        'creditcardnumber': this.changeLimitAccount.accountNumber
      }
      this.$mui.ajax(this.LBSGateway + '/creditcard-experience/creditcard/limitDecrease', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            vue.$router.push({ path: '/tellerlbsaccountservice/changecreditlimit/changeresult' })
          } else {
            vue.$mui.alert('Transaction failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          // plus.nativeUI.closeWaiting();
          mask.close()
          console.log(type)
          var msg = 'Transaction failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Transaction failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    }
  }
}
</script>
