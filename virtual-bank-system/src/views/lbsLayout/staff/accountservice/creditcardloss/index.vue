<template>
  <div v-loading.fullscreen.lock="loading" class="loss-index">
    <el-row>
      <el-col :span="24">
        <p class="info">{{ $t('lbs.lostStolen') }}</p>
        <p class="info">{{ $t('lbs.if1') }}</p>
        <p class="info">{{ $t('lbs.if2') }}</p>
        <hr style="margin: 0">
        <div class="div-select">
          <p class="title">{{ $t('lbs.lossCard') }}</p>
          <el-form ref="form" :model="form" class="form">
            <el-form-item label="">
              <el-select v-model="form.region" placeholder="" class="select-item" @change="getCreditCardDetails()">
                <el-option
                  v-for="(item,index) in creditCardaccountlist"
                  :key="index"
                  :label="item.value"
                  :value="item.value"
                  @change="getCreditCardDetails(item.value)"
                />
              </el-select>
            </el-form-item>
            <p>{{ $t('lbs.existingLimit') }}{{ accountDetail.ccyCode + ' ' + $lbs.decimal_format(accountDetail.approvedLimit) }}</p>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <el-col :span="24">
      <div class="clause">
        <p class="clause-p">{{ $t('lbs.lossItem1') }}</p>
        <p class="clause-p">{{ $t('lbs.lossItem2') }}</p>
        <p class="clause-p">{{ $t('lbs.lossItem3') }}</p>
        <p class="clause-p">{{ $t('lbs.lossItem4') }}</p>
      </div>
    </el-col>
    <el-row>
      <el-col :span="24">
        <div class="button-group">
          <el-button type="danger" @click="submit('back')">{{ $t('lbs.cancel') }}</el-button>
          <el-button type="primary" @click="submit('continue')">{{ $t('lbs.Continue') }}</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {
      loading: false,
      token: null,
      creditCardaccountlist: [],
      accountDetail: {},
      form: {
        region: ''
      },
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupCreditCardLoss'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.token = window.sessionStorage.getItem('token')
    this.creditCardaccountlist = this.getcreditcardnumberpickerdata(window.sessionStorage.getItem('creditCardaccountlist'))
    this.form.region = this.creditCardaccountlist[0].value
    this.getCreditCardDetails()
  },
  methods: {
    getCreditCardDetails() {
      var vue = this
      var requestdata = {
        'creditcardnumber': this.form.region
      }

      this.$mui.ajax(this.LBSGateway + '/creditcard-experience/creditcard/creditLimitDetails', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            vue.accountDetail = data.data
            vue.accountDetail['accountNumber'] = vue.form.region
            console.log(JSON.stringify(vue.accountDetail))
            window.sessionStorage.setItem('lossAccount', JSON.stringify(vue.accountDetail))
          } else {
            vue.$mui.alert('Get account info failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get account info failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get account info failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    getcreditcardnumberpickerdata(list) {
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      return pickerdata
    },
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/tellerlbsaccountservice/creditcarddetail' })
      } else {
        this.$router.push({ path: '/tellerlbsaccountservice/cardlossreporting/lossinfo' })
      }
    }
  }
}
</script>

<style scoped>
  .loss-index{
    margin: 30px;
  }
  .loss-index .info {
    font-size: 23px;
    line-height: 40px
  }

  .loss-index .title {
    font-size: 30px;
    color: #22C1E6;
    margin: 30px 0 10px;
  }

  .loss-index .form {
    padding: 20px 0 0 0;
  }

  .loss-index .div-select{
    padding: 0 15px 0;
  }

  .loss-index .select-item {
    width: 100%;
  }

  .loss-index .btn-parent {
    margin-top: 30px;
  }

  .loss-index .button {
    float: right;
  }

  .loss-index .btn-back {
    margin-right: 20px;
  }

  .loss-index .clause {
    width: 100%;
    background-color: #E2E2E2;
    padding: 15px;
    margin-top: 30px
  }

  .loss-index .clause-p {
    color: #000000;
  }

  .loss-index .button-group{
    margin-top: 30px;
    float: right;
  }
</style>

<style>
  .loss-index input[type=text] {
    margin-bottom: 0;
  }

  .loss-index .el-input--medium .el-input__inner {
    height: 60px;
    font-size: 18px;
    color: #707070;
  }
</style>
