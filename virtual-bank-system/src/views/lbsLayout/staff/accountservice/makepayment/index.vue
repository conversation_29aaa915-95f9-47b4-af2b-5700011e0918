<template>
  <div v-loading.fullscreen.lock="loading" class="makepaymentindex">
    <el-row>
      <el-col :span="24" :offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-ing" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 170px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right">
          <p class="text-title is-ing">Move Money From</p>
          <div style="padding: 43px 0 43px">
            <p class="text-account">Account</p>
            <el-select slot="prepend" v-model="accountNumber" placeholder="" style="width: 400px;margin-right: 100px">
              <el-option
                v-for="(item,index) in accountList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div style="display: inline-block" @click="submit()">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label=" " />
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title" style="margin-bottom: 130px">Move Money To</p>

          <p class="text-title" style="margin-bottom: 155px">Transfer Details</p>
          <p class="text-title">Confirmation</p>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Makepaymentindex',
  data() {
    return {
      loading: false,
      currency: 'HKD',
      accountNumber: '',
      savingList: [],
      currentList: [],
      accountList: [],
      paymentInfo: {},
      checkList: []
    }
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupRepayment'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    if (JSON.parse(window.sessionStorage.getItem('paymentInfo')) != null) {
      this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentInfo'))
    }
    this.initdata()
  },
  methods: {
    initdata() {
      const savingAccountList = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
      const currentaccountlist = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
      for (var i = 0; i < savingAccountList.length; i++) {
        if (savingAccountList[i].accountStatus === 'A') {
          this.savingList.push({
            label: savingAccountList[i].accountNumber,
            value: savingAccountList[i].accountNumber
          })
        }
      }

      for (var i = 0; i < currentaccountlist.length; i++) {
        if (currentaccountlist[i].accountStatus === 'A') {
          this.currentList.push({
            label: currentaccountlist[i].accountNumber,
            value: currentaccountlist[i].accountNumber
          })
        }
      }
      this.accountList = this.savingList.concat(this.currentList)
      if (!this.paymentInfo.accountNumber) {
        this.accountNumber = this.accountList[0].label
      } else {
        this.accountNumber = this.paymentInfo.accountNumber
      }
    },
    submit() {
      this.paymentInfo['accountNumber'] = this.accountNumber
      window.sessionStorage.setItem('paymentInfo', JSON.stringify(this.paymentInfo))
      this.$router.push({ path: '/tellerlbsaccountservice/repayment/steptwo' })
    }
  }
}
</script>

<style scoped>
  .makepaymentindex p {
    line-height: 1;
    color: #707070;
  }

  .makepaymentindex .text-title {
    font-size: 40px;
    color: #707070;
  }

  .makepaymentindex .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .makepaymentindex .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .makepaymentindex .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .makepaymentindex .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .makepaymentindex .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .makepaymentindex .el-input__icon {
    height: 40px;
  }

  .makepaymentindex .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .makepaymentindex .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }

  .makepaymentindex .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }
</style>
