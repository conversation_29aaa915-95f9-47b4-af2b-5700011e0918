<template>
  <div class="steofour">
    <el-row>
      <el-col :span="24" offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-finish" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 190px;width: 13px" />
          <div class="el-step__icon is-text is-ing">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right">
          <p class="text-title is-finish">Move Money From</p>
          <div style="padding: 43px 0 43px">
            <p class="text-account">Account</p>
            <el-select
              slot="prepend"
              v-model="accountNumber"
              disabled
              placeholder=""
              style="width: 400px;margin-right: 200px"
            >
              <el-option
                v-for="(item,index) in accountList"
                :key="index"
                :label="item.type + ' ' + item.label"
                :value="item.value"
              />
            </el-select>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label=" " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit()">EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-finish">Move Money To</p>
          <div style="padding: 40px 0 40px">
            <el-select slot="prepend" v-model="type" disabled placeholder="" style="width: 150px;margin-right: 100px">
              <el-option label="My Payee" value="My Payee" />
            </el-select>
            <el-select
              slot="prepend"
              v-model="creditCardAccount"
              disabled
              placeholder=""
              style="width: 400px;margin-right: 117px"
            >
              <el-option
                v-for="(item,index) in creditCardAccountList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label="  " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit('two')">
                  EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-finish">Transfer Details</p>
          <div style="padding: 20px;position: relative">
            <p style="display: inline-block;font-size: 20px;margin: 7px 60px 30px 0">Payment Type</p>
            <el-button type="primary">Full statement amount</el-button>
            <el-button type="primary">Total Minimum Amount</el-button>
            <el-button type="primary">Other Amount</el-button>
            <br>
            <p style="display: inline-block;font-size: 20px;margin: 20px 120px 30px 0">Amount</p>
            <el-input
              v-model="amount"
              disabled
              placeholder="Enter amount"
              class="input-with-select"
              style="width: 400px;margin-right: 175px"
            >
              <el-select slot="prepend" v-model="currency" disabled placeholder="" style="width: 80px">
                <el-option label="HKD" value="HKD" />
              </el-select>
            </el-input>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label="   " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit('three')">
                  EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-ing">Confirmation</p>
          <div style="width: 800px">
            <el-button class="button" style="float: right;background-color: #109eae;color: #ffffff" icon="el-icon-check" @click="submit('next')">CONFIRM</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Steofour',
  data() {
    return {
      currency: 'HKD',
      accountNumber: '',
      creditCardAccount: '',
      savingList: [],
      currentList: [],
      accountList: [],
      paymentInfo: {},
      checkList: [' ', '  ', '   '],
      type: 'My Payee',
      creditCardAccountList: [],
      amount: '',
      reference: ''

    }
  },
  watch: {
    amount: {
      handler() {
        this.clearNoNum()
      }
    }
  },
  mounted() {
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentInfo'))
    this.creditCardAccountList = this.getcreditcardnumberpickerdata(window.sessionStorage.getItem('creditCardaccountlist'))
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentInfo'))
    this.initdata()
  },
  methods: {
    initdata() {
      this.accountNumber = this.paymentInfo.accountNumber
      this.creditCardAccount = this.paymentInfo.creditCardAccount
      this.amount = this.paymentInfo.amount
      this.reference = this.paymentInfo.reference
    },
    submit(type) {
      if (type === 'next') {
        this.$router.push({ path: '/tellerlbsaccountservice/repayment/paymentform' })
      } else if (type === 'two') {
        this.$router.push({ path: '/tellerlbsaccountservice/repayment/steptwo' })
      } else if (type === 'three') {
        this.$router.push({ path: '/tellerlbsaccountservice/repayment/stepthree' })
      } else {
        this.$router.push({ path: '/tellerlbsaccountservice/repayment' })
      }
    },
    getcreditcardnumberpickerdata(list) {
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      return pickerdata
    },
    clearNoNum() {
      this.amount = this.amount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.amount = this.amount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.amount = this.amount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.amount = this.amount.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      this.amount = this.amount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    }
  }
}
</script>

<style scoped>
  .steofour p {
    line-height: 1;
    color: #707070;
  }

  .steofour .text-title {
    font-size: 40px;
    color: #707070;
  }

  .steofour .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .steofour .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .steofour .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .steofour .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .steofour .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .steofour .el-input__icon {
    height: 40px;
  }

  .steofour .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .steofour .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .steofour .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .steofour .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .steofour .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .steofour .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .steofour .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .steofour .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }

  .steofour .el-textarea__inner {
    height: 150px;
  }
</style>
