<template>
  <div v-loading.fullscreen.lock="loading">
    <el-row class="depositdetail">
      <el-col :span="24">
        <div class="div-title">
          <p class="title">{{ $t('lbsTips.accountDetailTip.tip24') }}</p>
        </div>
      </el-col>
      <el-col :span="20" offset="1">
        <div class="depositInfo">
          <table>
            <tr>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.EffectiveDate') }}</div>
                <td>{{ $t('lbsTips.accountDetailTip.tip18') }} :</td>
              </el-tooltip>
              <td>{{ depositDetail.createdate }}</td>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DepositType') }}</div>
                <td>{{ $t('lbsTips.accountDetailTip.tip25') }} :</td>
              </el-tooltip>
              <td>{{ $t('lbsTips.accountDetailTip.tip26') }}</td>
            </tr>
            <tr>
              <td>{{ $t('lbsTips.accountDetailTip.tip27') }} :</td>
              <td>{{ depositDetail.maturitydate }}</td>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Tenor') }}</div>
                <td>{{ $t('lbsTips.accountDetailTip.tip19') }} :</td>
              </el-tooltip>
              <td>{{ depositDetail.termperiod }}</td>
            </tr>
            <tr>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Status') }}</div>
                <td>{{ $t('lbsTips.accountDetailTip.tip23') }} :</td>
              </el-tooltip>
              <td>{{ depositDetail.maturitystatus }}</td>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Currency') }}</div>
                <td>{{ $t('lbsTips.accountDetailTip.tip20') }} :</td>
              </el-tooltip>
              <td>{{ depositDetail.currencycode }}</td>
            </tr>
          </table>
        </div>
      </el-col>
      <el-col :span="20" offset="1">
        <div class="depositInfo2">
          <table>
            <tr>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Principal') }}</div>
                <td class="show-line show-line-rigth">{{ $t('lbsTips.accountDetailTip.tip22') }}</td>
              </el-tooltip>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Interest') }}</div>
                <td class="show-line show-line-rigth">{{ $t('lbsTips.accountDetailTip.tip21') }}</td>
              </el-tooltip>
              <td class="show-line show-line-rigth">{{ $t('lbsTips.accountDetailTip.tip28') }}</td>
              <td class="show-line">{{ $t('lbsTips.accountDetailTip.tip29') }}</td>
            </tr>
            <tr>
              <td
                class="td-bottom show-line-rigth"
              >{{ depositDetail.currencycode + ' ' + depositDetail.depositamount }}</td>
              <td class="td-bottom show-line-rigth">{{ depositDetail.terminterestrate }}</td>
              <td
                class="td-bottom show-line-rigth"
              >{{ depositDetail.currencycode + ' ' + $lbs.decimal_format(depositDetail.maturityinterest) }}</td>
              <td
                class="td-bottom"
              >{{ depositDetail.currencycode + ' ' + $lbs.decimal_format(depositDetail.maturityamount) }}</td>
            </tr>
          </table>
        </div>
      </el-col>
      <el-col :span="5" offset="1">
        <div class="div-bottom">
          <p class="title">{{ $t('lbsTips.accountDetailTip.tip30') }}</p>
        </div>
      </el-col>
      <el-col :span="15" offset="1">
        <div class="div-bottom">
          <el-checkbox-group v-model="checkList">
            <el-checkbox label="Full Renewal" disabled style />
            <p class="checkbox-form">(Tenor : __________ )</p>
            <br>
            <el-checkbox label="Renew principal and repay interest to" disabled />
            <p
              class="checkbox-form"
            >( A/C No : _______________ A/C Name : _______________ Tenor : ____________)</p>
            <br>
            <el-checkbox label="Full withdrawal and repay to account below" disabled />
            <p class="checkbox-form">( A/C No : 1235 1466 5652 A/C Name : Chan Tai Man )</p>
          </el-checkbox-group>
        </div>
      </el-col>
      <el-col :span="20" :offset="1">
        <el-button class="button" type="danger" @click="submit()">{{ $t('lbs.common.back') }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Depositdetail',
  data() {
    return {
      loading: false,
      depositDetail: {},
      checkList: ['Full Renewal'],
      backPath: ''
    }
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupTermDepositDetail'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    const depositDetailList = JSON.parse(
      window.sessionStorage.getItem('depositDetailList')
    )
    const index = window.sessionStorage.getItem('index')
    this.backPath = window.sessionStorage.getItem('path')
    this.depositDetail = depositDetailList[index]
  },
  methods: {
    submit() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.depositdetail p {
  line-height: 1;
}

.depositdetail .div-title {
  padding: 40px;
}

.depositdetail .title {
  font-size: 30px;
  color: #22c1e6;
}

.depositdetail .depositInfo {
  margin-left: 40px;
  padding: 15px;
  border-radius: 50px;
  border: 1px solid #22c1e6;
  box-shadow: 3px 3px 3px #bbbbbb;
}

.depositdetail .depositInfo table {
  width: 100%;
}

.depositdetail .depositInfo td {
  width: 25%;
  height: 40px;
  padding-left: 20px;
  color: #707070;
  font-size: 20px;
}

.depositdetail .depositInfo2 {
  margin-left: 40px;
  padding: 15px;
  border: 1px solid #22c1e6;
  box-shadow: 3px 3px 3px #bbbbbb;
  margin-top: 60px;
}

.depositdetail .depositInfo2 table {
  text-align: center;
  width: 100%;
}

.depositdetail .depositInfo2 td {
  width: 25%;
  height: 60px;
  padding-left: 20px;
  color: #707070;
  font-size: 20px;
}

.depositdetail .depositInfo2 .td-bottom {
  font-size: 15px;
}

.depositdetail .depositInfo2 .show-line {
  border-bottom: 1px #cccccc solid;
}

.depositdetail .depositInfo2 .show-line-rigth {
  border-right: 1px #cccccc solid;
}

.depositdetail .div-bottom {
  margin: 50px 0 0 40px;
}

.depositdetail .div-bottom .title {
  color: #707070;
  font-size: 25px;
}
.depositdetail .checkbox-form {
  font-size: 12px;
  margin-left: 25px;
}

.depositdetail .button {
  margin-top: 30px;
  width: 100px;
  float: right;
}
</style>

<style>
.depositdetail .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #919191;
  font-size: 16px;
}
</style>
