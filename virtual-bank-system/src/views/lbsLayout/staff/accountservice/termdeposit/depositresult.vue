<template>
  <div>
    <el-row class="termdeposit">
      <el-col :psan="24">
        <el-row>
          <el-col :span="24">
            <div class="title">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.accountDetailTip.deposit') }}</div>
                <p style="display: inline-block" class="max-title">Deposit</p>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row class="depositresult">
      <el-col :xs="24" :lg="16">
        <p class="successful">{{ $t('lbsTips.accountDetailTip.tip57') }}</p>
        <el-button class="button" type="primary" @click="submit()">{{ $t('lbsTips.accountDetailTip.tip53') }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Depositresult',
  methods: {
    submit() {
      this.$router.push({ path: '/tellerlbsaccountservice/termdeposit/applydeposit' })
    }
  }
}
</script>

<style scoped>
  .termdeposit .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .termdeposit p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .termdeposit .max-title {
    font-size: 50px;
    margin-bottom: 40px;
    font-weight: 500;
    color: #22C1E6;
    margin-top: 15px;
  }

  .termdeposit .little-title {
    font-size: 30px;
  }
  .depositresult .successful{
    font-size: 45px;
    color: #707070;
    line-height: 400px;
    text-align: center;
  }

  .depositresult .button {
    float: right;
    width: 100px;
  }
</style>
