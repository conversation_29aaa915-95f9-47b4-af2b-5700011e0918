<template>
  <div v-loading.fullscreen.lock="loading" class="trade">
    <el-col :span="24">
      <div class="lbs-icon-group1">
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink()">
            <svg-icon icon-class="ionic-md-information-circle-outline" class="lbs-icon" />
          </div>
          <p @click="goToLink()">{{ $t('lbs.marketInformation') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockportfolio')">
            <svg-icon icon-class="Icon material-favorite-border" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockportfolio')">{{ $t('lbs.stockPortfolio') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockholding')">
            <svg-icon icon-class="Icon awesome-wpforms" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockholding')">{{ $t('lbs.StockHolding') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('transactionhistory')">
            <svg-icon icon-class="Icon awesome-history" class="lbs-icon" />
          </div>
          <p @click="goToLink('transactionhistory')">{{ $t('lbs.TransactionHistory') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockaccount')">
            <svg-icon icon-class="Icon material-person-pin" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockaccount')">{{ $t('lbs.MyStockAccount') }}</p>
        </div>
      </div>
    </el-col>
    <el-col>
      <div class="lbs-div-portfolio">
        <el-tooltip effect="dark" placement="bottom">
          <div slot="content" style="width: 300px;text-align: center">{{ $t('lbsTips.StockTrade.HongKongButton') }}</div>
          <el-select v-model="market" clearable placeholder="" class="lbs-input">
            <el-option
              v-for="item in marketList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-tooltip>
        <el-select v-model="search" filterable placeholder="" style="width: 330px;">
          <el-option
            v-for="item in stockCodeList"
            :key="item.stockCode"
            :label="item.stockName + ' ' + item.stockCode"
            :value="item.stockCode"
          >
            <span style="float: left">{{ item.stockName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stockCode }}</span>
          </el-option>
        </el-select>
        <!--        <el-tooltip effect="dark" placement="bottom">-->
        <!--          <div slot="content" style="width: 100px;text-align: center">{{$t('lbsTips.StockTrade.Search')}}</div>-->
        <!--          <el-button type="primary" class="lbs-button">{{$t('lbs.common.search')}}</el-button>-->
        <!--        </el-tooltip>-->
      </div>
    </el-col>
    <el-col :span="24">
      <div class="codenametime">
        <el-tooltip effect="dark" placement="bottom">
          <div slot="content" style="width: 300px;text-align: center">{{ $t('lbsTips.StockTrade.stockCode') }}</div>
          <p class="stockinfo stock-code">{{ stockDetail.stockcode }}</p>
        </el-tooltip>
        <el-tooltip effect="dark" placement="bottom">
          <div slot="content" style="width: 300px;text-align: center">{{ $t('lbsTips.StockTrade.stockName') }}</div>
          <p class="stockinfo stock-name">{{ stockDetail.stockname }}</p>
        </el-tooltip>
        <el-tooltip effect="dark" placement="bottom">
          <div slot="content" style="width: 300px;text-align: center">{{ $t('lbsTips.StockTrade.UpdateTime') }}</div>
          <p class="stockinfo stock-time">{{ $t('lbs.updateTime') + ': ' + $moment.parseZone(new
            Date()).local().format('YYYY-MM-DD HH:mm:ss') }}</p>
        </el-tooltip>
      </div>
    </el-col>
    <el-col :span="9">
      <div id="echarts" style="width: 500px;height: 300px" />
    </el-col>
    <el-col :span="15">
      <div class="stock-info">
        <table style="width: 100%;text-align: center;font-size: 18px;">
          <tr>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.Bid') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.bid') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.Ask') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.ask') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.Ask') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.open') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.High') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.high') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.Low') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.low') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.PrevClose') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.prevClose') }}</p></td>
            </el-tooltip>
          </tr>
          <tr>
            <td rowspan="2"><p>{{ Number(stockDetail.buyprice).toFixed(2) }}</p></td>
            <td rowspan="2"><p>{{ Number(stockDetail.sellprice).toFixed(2) }}</p></td>
            <td><p>{{ Number(stockDetail.open).toFixed(2) }}</p></td>
            <td><p>{{ Number(stockDetail.high).toFixed(2) }}</p></td>
            <td><p>{{ Number(stockDetail.low).toFixed(2) }}</p></td>
            <td><p>{{ Number(stockDetail.previousclose).toFixed(2) }}</p></td>
          </tr>
          <tr>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.Currency') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.currency') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.Change') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.change') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.Turnover') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.turnover') }}</p></td>
            </el-tooltip>
            <td><p class="lbs-storage">{{ $t('lbs.volume') }}</p></td>
          </tr>
          <tr>
            <td colspan="2"><p class="lbs-storage">{{ $t('lbs.lastTradingDay') }}</p></td>
            <td><p>HKD</p></td>
            <td>
              <p style="line-height: 1;margin-top: 10px">{{ stockDetail.changed }}</p>
              <p style="line-height: 1;margin-bottom: 10px;font-size: 10px">{{ '(' + stockDetail.changedpercent +
                ')' }}</p>
            </td>
            <td><p>{{ stockDetail.turnover }}</p></td>
            <td><p>{{ stockDetail.volume }}</p></td>
          </tr>
          <tr>
            <td colspan="2" rowspan="2"><p>
              {{ $moment.parseZone(stockDetail.lasttradingday).local().format('YYYY-MM-DD') }}</p></td>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.BoardLot') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.boardLot') }}</p></td>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.StockTrade.PeRatio') }}</div>
              <td><p class="lbs-storage">{{ $t('lbs.ratio') }}</p></td>
            </el-tooltip>
            <td><p class="lbs-storage">{{ $t('lbs.eps') }}</p></td>
            <td><p class="lbs-storage">{{ $t('lbs.tradingPoint') }}</p></td>
          </tr>
          <tr>
            <td><p>{{ stockDetail.lotsize }}</p></td>
            <td><p>{{ stockDetail.ratio }}</p></td>
            <td><p>{{ stockDetail.eps }}</p></td>
            <td><p>{{ stockDetail.tradingpoint }}</p></td>
          </tr>
        </table>
      </div>
      <div style="float: right;padding: 30px 30px 50px 0">
        <el-tooltip effect="dark" placement="bottom">
          <div slot="content" style="width: 100px;text-align: center">{{ $t('lbsTips.StockTrade.Back') }}</div>
          <el-button type="danger" @click="$router.go(-1)">{{ $t('lbs.common.back') }}</el-button>
        </el-tooltip>
        <el-tooltip effect="dark" placement="bottom">
          <div slot="content" style="width: 100px;text-align: center">{{ $t('lbsTips.StockTrade.MadeATransaction') }}
          </div>
          <el-button type="primary" @click="madeTransaction('Sell')">{{ $t('lbs.sell') }}
          </el-button>
        </el-tooltip>
        <el-tooltip effect="dark" placement="bottom">
          <div slot="content" style="width: 100px;text-align: center">{{ $t('lbsTips.StockTrade.MadeATransaction') }}
          </div>
          <el-button type="primary" @click="madeTransaction('Buy')">{{ $t('lbs.madeTransaction') }}</el-button>
        </el-tooltip>
        <el-dialog
          :title="$t('lbs.transaction')"
          :visible.sync="dialogFormVisible"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <div class="buy-stock-form">
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <p>{{ $t('lbs.market') }}</p>
              </el-col>
              <el-col :span="12">
                <el-input
                  v-model="market"
                  disabled
                  style="width: 300px"
                  placeholder=""
                  clearable
                />
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <p>{{ $t('lbs.stockCode') }}</p>
              </el-col>
              <el-col :span="12">
                <el-input
                  v-model="stockDetail.stockcode"
                  disabled
                  style="width: 300px"
                  placeholder=""
                  clearable
                />
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <p>{{ $t('lbs.stockName') }}</p>
              </el-col>
              <el-col :span="12">
                <el-input
                  v-model="stockDetail.stockname"
                  disabled
                  style="width: 300px"
                  placeholder=""
                  clearable
                />
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <p>{{ $t('lbs.orderType') }}</p>
              </el-col>
              <el-col :span="12">
                <el-select v-model="orderType" placeholder="" class="lbs-input">
                  <el-option
                    v-for="item in orderTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-col>
            </el-row>
            <div id="isHidden">
              <el-row style="margin-bottom: 10px">
                <el-col :span="12">
                  <p>{{ $t('lbs.expiryDate') }}</p>
                </el-col>
                <el-col :span="12">
                  <el-date-picker
                    id="date"
                    v-model="value"
                    type="datetime"
                    placeholder=""
                    default-time="23:59:00"
                    :picker-options="pickerOptions"
                    :clearable="false"
                  />
                </el-col>
              </el-row>
              <el-row style="margin-bottom: 10px">
                <el-col :span="12">
                  <p>{{ $t('lbs.price') }}</p>
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-model="price"
                    style="width: 150px"
                    placeholder=""
                    clearable
                  />
                  <el-button size="mini" style="margin: 3px 0 0 10px" @click="priceChange('plus')">+
                    {{ stockDetail.tradingpoint }}
                  </el-button>
                  <el-button size="mini" style="margin: 3px 0 0 10px" @click="priceChange('reduce')">-
                    {{ stockDetail.tradingpoint }}
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <p>{{ $t('lbs.quantity') }}</p>
              </el-col>
              <el-col :span="12">
                <el-input
                  v-model="quantity"
                  style="width: 150px"
                  placeholder=""
                  clearable
                />
                <el-button size="mini" style="margin: 3px 0 0 10px" @click="quantityChange('plus')">+
                  {{ stockDetail.lotsize }}
                </el-button>
                <el-button size="mini" style="margin: 3px 0 0 10px" @click="quantityChange('reduce')">-
                  {{ stockDetail.lotsize }}
                </el-button>
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <p>{{ $t('lbs.StockAccount') }}</p>
              </el-col>
              <el-col :span="12">
                <el-select v-model="stockAccountNumber" placeholder="" style="width: 300px" @change="getStockHolding()">
                  <el-option
                    v-for="(item,index) in stockAccountOption"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <p v-show="tradingType === 'Sell'" style="color: #707070;margin-bottom: 0">( {{ $t('lbs.AvailableNo') }}：{{ availableshare }} )</p>
              </el-col>
            </el-row>
            <el-row style="margin-bottom: 10px">
              <el-col :span="12">
                <p>{{ $t('lbs.settlementAccount') }}</p>
              </el-col>
              <el-col :span="12">
                <el-select v-model="savingaccount" placeholder="" style="width: 300px" @change="updateSettlementAccount()">
                  <el-option
                    v-for="item in savingaccountlist"
                    :key="item.accountNumber"
                    :label="item.accountNumber"
                    :value="item.accountNumber"
                  />
                </el-select>
                <p v-show="tradingType === 'Buy'" style="font-size: 12px;color: #707070">{{ `(${$t('lbs.AvailableBalance')}: ${settlementBalance})` }}</p>
              </el-col>
            </el-row>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">{{ $t('lbs.common.cancel') }}</el-button>
            <el-button type="primary" :disabled="tradingType === 'Sell' && availableshare <= 0" @click="buyStock(token)">{{ $t('lbs.common.confirm') }}</el-button>
          </div>
        </el-dialog>
      </div>
    </el-col>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data() {
    return {
      loading: false,
      token: null,
      search: '',
      market: 'Hong Kong',
      marketList: [
        {
          label: 'Hong Kong',
          value: 'Hong Kong'
        }
      ],
      stockCode: '',
      stockDetail: {},
      dialogFormVisible: false,
      accountNumber: 'A123456789',
      accountType: 'CashAccount',
      orderType: 'Fix Price',
      orderTypeList: [
        {
          label: 'Fix Price',
          value: 'Fix Price'
        },
        {
          label: 'Market Price',
          value: 'Market Price'
        }
      ],
      // value: this.$moment.parseZone(new Date().getTime()).local().format('YYYY-MM-DD') + " 23:59:59",
      value: this.$moment.parseZone(new Date().getTime()).local().format('YYYY-MM-DD') + ' 23:59:59',
      pickerOptions: {
        disabledDate(time) {
          // 选择的时间范围2014.1.1 00:00:00 - 2020.12.31 23:59:59
          return time.getTime() < new Date('2014-1-1'.replace(/[\r-]/g, '/')).getTime() || time.getTime() > new Date('2020-12-31'.replace(/[\r-]/g, '/')).getTime() + 1000 * 60 * 60 * 23 + 1000 * 60 * 59 + 59
        }
      },
      price: '',
      quantity: '',
      stockAccount: {},
      savingaccountlist: [],
      savingaccount: '',
      availableshare: 0,
      tradingType: null,
      stockCodeList: [],
      stockAccountNumber: '',
      stockAccountOption: [],
      settlementBalance: 0,
      customernumber: ''
    }
  },
  watch: {
    orderType: {
      handler() {
        if (this.orderType === 'Market Price') {
          document.getElementById('isHidden').style.display = 'none'
        } else {
          document.getElementById('isHidden').style.display = 'inline'
        }
      }
    },
    'search'(newValue, oldValue) {
      this.stockCode = newValue
      // this.getStockHolding()
      this.generateTable()
      this.getStockList()
    },
    'savingaccount'() {
      this.getBalance()
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.stockCode = window.sessionStorage.getItem('stockCode')
    this.getStockAccountOption()
    this.stockCodeList = JSON.parse(window.sessionStorage.getItem('stockCodeList'))
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupStockTrade'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.stockAccount = JSON.parse(window.sessionStorage.getItem('stockAccount'))
    const savingaccountlist = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
    const currentaccountlist = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))

    for (let i = 0; i < savingaccountlist.length; i++) {
      if (savingaccountlist[i].accountStatus === 'A') {
        this.savingaccountlist.push(savingaccountlist[i])
      }
    }
    for (let i = 0; i < currentaccountlist.length; i++) {
      if (currentaccountlist[i].accountStatus === 'A') {
        this.savingaccountlist.push(currentaccountlist[i])
      }
    }
    // this.savingaccount = this.savingaccountlist[0].accountNumber
    this.getStockSettingAccount()

    if (this.stockCode === undefined) {
      this.search = this.stockCodeList[0].stockCode
    } else {
      this.search = this.stockCode
    }
    // this.getStockHolding()
    // this.generateTable()
    // this.getStockList()
  },
  methods: {
    retry(param) {
      this.buyStock(param)
    },
    hideAuth() {
      this.showAuth = false
    },
    // 获取结算账户余额
    getBalance() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.savingaccount}`, {}, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          _this.settlementBalance = 0
          if (response.data.code === '200') {
            _this.settlementBalance = response.data.data.account.currencycode + ' ' + response.data.data.account.availablebalance
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取股票账户设置的结算账户
    getStockSettingAccount() {
      const _this = this
      if (!_this.stockAccountNumber) return
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.stockAccountNumber}`, {}, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.savingaccount = response.data.data.account.relaccountnumber
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    getStockAccountOption() {
      const stockAccountOption = JSON.parse(window.sessionStorage.getItem('stockaccountlist'))
      for (const item of stockAccountOption) {
        if (item.accountStatus === 'A') {
          this.stockAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      if (this.stockAccountOption && this.stockAccountOption[0]) {
        this.stockAccountNumber = this.stockAccountOption[0].value
      }
    },
    getStockHolding() {
      const vue = this
      if (!vue.stockAccountNumber) return
      const requestdata = {
        stkaccountnumber: vue.stockAccountNumber,
        stockcode: vue.stockCode
      }
      vue.$mui.ajax(this.LBSGateway + '/stock-experience/stock/stockHoldingEnquiry', {
        data: requestdata,
        dataType: 'json',
        type: 'post',
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(response) {
          if (response.code === '200') {
            vue.availableshare = response.data.availableshare
            vue.getStockSettingAccount()
          } else if (response.code === '404015') {
            vue.getStockSettingAccount()
            vue.availableshare = 0
          } else {
            vue.availableshare = 0
            vue.$mui.alert(vue.$t('lbs.GetStockHoldingFail'), vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          let msg = 'Get stock holding information failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get stock holding information failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    buyStock(token) {
      const vue = this
      if (!vue.stockAccountNumber) {
        vue.$alert('No Stock Account')
        return
      }
      let price = vue.price
      let date = new Date(vue.value).getTime()
      if (vue.orderType === 'Market Price') {
        date = ''
        price = ''
      } else {
        // 校验有效日期
        if (vue.value === '' || vue.value === null) {
          vue.$message({ message: vue.$t('lbs.buyStockTip1'), type: 'warning' })
          return
        }
        // 校验输入价格
        if (vue.price === '' || vue.price === null) {
          vue.$message({ message: vue.$t('lbs.buyStockTip2'), type: 'warning' })
          return
        }
        if (Math.abs(Number(vue.price * 100 - this.stockDetail.buyprice * 100)) % (this.stockDetail.tradingpoint * 100) !== 0) {
          vue.$message({ message: vue.$t('lbs.buyStockTip3'), type: 'warning' })
          return
        }
        // if (vue.price < vue.stockDetail.low) {
        //   vue.$message({message: vue.$t('lbs.buyStockTip3'), type: 'warning'});
        //   return;
        // }
        // if (vue.price > vue.stockDetail.high) {
        //   vue.$message({message: vue.$t('lbs.buyStockTip4'), type: 'warning'});
        //   return;
        // }
      }
      // 校验数量
      if (vue.quantity === '' || vue.quantity === null) {
        vue.$message({ message: vue.$t('lbs.buyStockTip5'), type: 'warning' })
        return
      }
      if (vue.quantity < vue.stockDetail.lotsize || vue.quantity % vue.stockDetail.lotsize !== 0) {
        vue.$message({ message: vue.$t('lbs.buyStockTip6'), type: 'warning' })
        return
      }
      const requestdata = {
        'expiryDate': date,
        'orderType': vue.orderType,
        'sharingNo': vue.quantity,
        'stkaccountnumber': vue.stockAccountNumber,
        'stockCode': vue.stockCode,
        'tradingOption': vue.tradingType,
        'tradingPrice': price
      }
      console.log('Buy stock request data:' + JSON.stringify(requestdata))
      this.$mui.ajax(this.LBSGateway + '/stock-experience/stock/order/orderPlacing', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          console.log('Buy stock response data:' + JSON.stringify(data))
          if (data.code === '200') {
            vue.dialogFormVisible = false
            vue.$mui.alert(vue.$t('lbs.settlementTip2'), 'Success', 'OK', function() {
              if (vue.tradingType === 'Sell') {
                vue.availableshare = vue.availableshare - vue.quantity
              }
            })
          } else {
            vue.$mui.alert('Transaction failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          if (JSON.parse(xhr.response).code === '403005') {
            vue.$mui.alert(vue.$t('lbs.common.limitTip'), vue.$t('lbs.common.warning'), vue.$t('lbs.common.confirm'), function() {
              vue.showAuth = true
            })
          } else {
            console.log(type)
            var msg = 'Transaction failed! The response is: \n' + xhr.responseText + '.'
            if (type === 'timeout') {
              msg = 'Transaction failed. Time out!'
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      })
    },
    madeTransaction(type) {
      this.tradingType = type
      this.price = this.stockDetail.buyprice
      this.quantity = this.stockDetail.lotsize
      this.getStockHolding()
      this.dialogFormVisible = true
    },
    updateSettlementAccount() {
      var vue = this
      if (!vue.stockAccountNumber) {
        vue.$alert('No Stock Account')
        return
      }
      var requestdata = {
        'newsettleaccountnumber': this.savingaccount,
        'stkaccountnumber': vue.stockAccountNumber
      }
      this.$mui.ajax(this.LBSGateway + '/stock-experience/stock/settlementAccountUpdate', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            // vue.$mui.alert("Update the settlement account Accepted.", "Success", "OK")
            vue.$message({ message: vue.$t('lbs.settlementTip'), type: 'success' })
          } else {
            // vue.$mui.alert("Update the settlement account failed! The response is: \n" + JSON.stringify(data) + ".", "Error", "OK")
            vue.$message({
              message: 'Update the settlement account failed! The response is: \n' + JSON.stringify(data.msg) + '.',
              type: 'error'
            })
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Update the settlement account failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Update the settlement account failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    priceChange(type) {
      if (type === 'plus') {
        // if (this.price >= this.stockDetail.low && this.price < this.stockDetail.high)
        this.price = parseFloat(this.price + this.stockDetail.tradingpoint).toFixed(2) - 0
      }
      if (type === 'reduce') {
        if (Number(this.price) > 0) { this.price = parseFloat(this.price - this.stockDetail.tradingpoint).toFixed(2) - 0 }
      }
    },
    quantityChange(type) {
      if (type === 'plus') {
        this.quantity = Number(this.quantity) + Number(this.stockDetail.lotsize)
      }
      if (type === 'reduce') {
        if (this.quantity >= this.stockDetail.lotsize) {
          this.quantity = this.quantity - this.stockDetail.lotsize
        }

        if (this.quantity > 0 && this.quantity < this.stockDetail.lotsize) {
          this.quantity = 0
        }
      }
    },
    getStockList() {
      const vue = this
      this.$mui.ajax(vue.LBSGateway + '/stock-experience/stock/stockQuotation', {
        data: {
          'stockcode': vue.stockCode
        },
        dataType: 'json',
        type: 'post',
        timeout: 60000,
        headers: {
          accept: '*/*',
          token: vue.token,
          clientid: vue.$parent.clientid,
          messageid: vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            console.log(data)
            vue.stockDetail = data.data
          } else {
            vue.$mui.alert('Get Stock Information failed! The response is:' + JSON.stringify(data.msg) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          var msg = 'Get Stock Information failed!'
          if (type === 'timeout') {
            msg = 'Get Stock Information failed!. Time out!'
          } else {
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      })
    },
    generateTable() {
      // creat echart object
      var myChart = this.$echarts.init(document.getElementById('echarts'))
      var upColor = '#008F28'
      var downColor = '#8A0000'

      var dataCount = 200
      var data = this.generateOHLC(dataCount)

      var option = {
        dataset: {
          source: data
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line'
          }
        },
        grid: [{
          left: '15%',
          top: '5%',
          right: '5%',
          bottom: 200
        },
        {
          left: '5%',
          right: '5%',
          height: 80,
          bottom: 10
        }
        ],
        xAxis: [{
          type: 'category',
          axisLine: {
            onZero: false
          },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax'
        },
        {
          type: 'category',
          gridIndex: 1,
          scale: true,
          boundaryGap: false,
          axisLine: {
            onZero: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitNumber: 20,
          min: 'dataMin',
          max: 'dataMax'
        }
        ],
        yAxis: [{
          scale: true,
          splitArea: {
            show: true
          }
        },
        {
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        }
        ],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: [0, 1],
            start: 10,
            end: 100
          },
          {
            show: false,
            xAxisIndex: [0, 1],
            type: 'slider',
            bottom: 10,
            start: 10,
            end: 100,
            handleIcon: 'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '105%'
          }
        ],
        visualMap: {
          show: false,
          seriesIndex: 1,
          dimension: 6,
          pieces: [{
            value: 1,
            color: upColor
          }, {
            value: -1,
            color: downColor
          }]
        },
        series: [
          {
            type: 'candlestick',
            itemStyle: {
              // color: upColor,
              // color0: downColor,
              // borderColor: upBorderColor,
              // borderColor0: downBorderColor
            },
            encode: {
              x: 0,
              y: [1, 4, 3, 2]
            }
          },
          {
            name: 'Volumn',
            type: 'bar',
            xAxisIndex: 1,
            yAxisIndex: 1,
            itemStyle: {
              color: '#ff0000'
            },
            large: true,
            encode: {
              x: 0,
              y: 5
            }
          }
        ]
      }
      // init data
      myChart.setOption(option)
    },
    generateOHLC(count) {
      var data = []
      var Day = new Date().getDate() - 1
      var Month = new Date().getMonth()
      var Year = new Date().getFullYear()
      var xValue = +new Date(Year, Month, Day)
      var minute = 60 * 10000
      var baseValue = Math.random() * 12000
      var boxVals = new Array(4)
      var dayRange = 24

      for (var i = 0; i < count; i++) {
        baseValue = baseValue + Math.random() * 20 - 10

        for (var j = 0; j < 4; j++) {
          boxVals[j] = (Math.random() - 0.5) * dayRange + baseValue
        }
        boxVals.sort()

        var openIdx = Math.round(Math.random() * 3)
        var closeIdx = Math.round(Math.random() * 2)
        if (closeIdx === openIdx) {
          closeIdx++
        }
        var volumn = boxVals[3] * (1000 + Math.random() * 500)

        // ['open', 'close', 'lowest', 'highest', 'volumn']
        // [1, 4, 3, 2]
        data[i] = [
          this.$echarts.format.formatTime('yyyy-MM-dd\nhh:mm:ss', xValue += minute),
          // echarts.format.formatTime('hh:mm:ss', xValue += minute),
          +boxVals[openIdx].toFixed(2), // open
          +boxVals[3].toFixed(2), // highest
          +boxVals[0].toFixed(2), // lowest
          +boxVals[closeIdx].toFixed(2), // close
          volumn.toFixed(0),
          this.getSign(data, i, +boxVals[openIdx], +boxVals[closeIdx], 4) // sign
        ]
      }
      return data
    },
    getSign(data, dataIndex, openVal, closeVal, closeDimIdx) {
      var sign
      if (openVal > closeVal) {
        sign = -1
      } else if (openVal < closeVal) {
        sign = 1
      } else {
        sign = dataIndex > 0
        // If close === open, compare with close of last record
          ? (data[dataIndex - 1][closeDimIdx] <= closeVal ? 1 : -1)
        // No record of previous, set to be positive
          : 1
      }
      return sign
    },
    goToLink(path) {
      if (path === undefined) {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading` })
      } else {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading/${path}` })
      }
    }
  }
}
</script>

<style scoped>
  .trade {
    padding: 50px 30px 30px;
  }

  .trade .lbs-icon {
    height: 35px;
    width: 35px;
  }

  .trade .lbs-icon-item {
    width: 200px;
    text-align: center;
    display: inline-block;
  }

  .trade .lbs-div-icon {
    width: 35px;
    margin: 0 auto;
    cursor: pointer;
  }

  .trade .lbs-icon-group1 {
    width: 1020px;
    margin: 0 auto;
  }

  .trade .lbs-icon-group2 {
    width: 816px;
    margin: 30px auto 0;
  }

  .trade .lbs-icon-group1 .lbs-icon-item p {
    display: inline-block;
    cursor: pointer;
  }

  .trade .lbs-icon-group1 .lbs-icon-item .lbs-div-icon.lbs-current {
    cursor: default;
  }

  .trade .lbs-icon-group1 .lbs-icon-item p.lbs-current {
    cursor: default;
  }

  .trade .lbs-icon-group2 .lbs-icon-item p {
    display: inline-block;
    cursor: pointer;
  }

  .trade .lbs-icon-group2 .lbs-icon-item .lbs-div-icon.lbs-current {
    cursor: default;
  }

  .trade .lbs-icon-group2 .lbs-icon-item p.lbs-current {
    cursor: default;
  }

  .trade .lbs-input {
    width: 200px;
    margin-right: 50px;
  }

  .trade .lbs-div-portfolio {
    width: 630px;
    margin: 40px auto 40px;
  }

  .trade .lbs-button {
    margin-left: 30px;
  }

  .trade .codenametime {
    width: 100%;
    border-top: 2px #cccccc solid;
    border-bottom: 2px #cccccc solid;
    padding: 0 30px;
    margin-bottom: 40px;
  }

  .trade .stockinfo {
    line-height: 60px;
    margin-bottom: 0;
    display: inline-block;
    color: #707070;
  }

  .trade .stock-code {
    margin-right: 30px;
  }

  .trade .stock-code, .stock-name {
    font-size: 20px;
  }

  .trade .stock-time {
    float: right;
  }

  .trade .stock-info {
    padding: 20px;
    background-color: #ffffff;
  }

  .trade .stock-info table td {
    border: 1px solid #888;
  }

  .trade .stock-info table td p {
    margin-bottom: 0;
    line-height: 45px;
  }

  .trade .stock-info table td .lbs-storage {
    font-weight: 500;
  }

  .trade .accountInfo {
    width: 80%;
    margin: 0 auto;
  }

  .trade .info {
    color: #707070;
    width: 49%;
    text-align: center;
    display: inline-block;
    font-size: 20px;
    margin: 30px 0;
  }

  .trade .buy-stock-form {
    width: 80%;
    margin: 0 auto;
  }
</style>

<style>
  .trade .el-input__inner {
    margin-bottom: 10px;
  }

  .trade div.el-select-dropdown.el-popper {
    margin-top: 180px;
  }
</style>
