<template>
  <div v-loading.fullscreen.lock="loading" class="mortgage">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.MortgageOverview') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <div class="main">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.MyLatestMortgageStatus') }}</div>
            <p class="title2">{{ $t('lbs.MyLatestMortgageStatus') }}</p>
          </el-tooltip>
          <el-card class="mortgage-status">
            <div style="margin-top: 20px">
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.ProcessingMortgage') }}</div>
                <span>{{ $t('lbs.ProcessingMortgage') }}</span>
              </el-tooltip>
              <span class="right">{{ count }}</span>
            </div>
            <div style="margin: 60px 20px 0">
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.Status') }}</div>
                <span>{{ $t('lbs.Status') }}</span>
              </el-tooltip>
              <span class="right">{{ count>0?"Verifying":"" }}</span>
            </div>
          </el-card>
        </div>
      </el-col>
      <el-col :span="6" :offset="3">
        <div class="main-right">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.HelpingTool') }}</div>
            <p class="title3">{{ $t('lbs.HelpingTool') }}</p>
          </el-tooltip>
          <div class="helping-tool">
            <div
              style="width: 220px;margin: 0 auto;cursor: pointer"
              @click="$router.push({path: '/tellerlbsmortgage/mortgagecalculator'})"
            >
              <span style="display: inline-block;margin-right: 10px"><svg-icon
                icon-class="Icon awesome-calculator"
              /></span>
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.MortgageCalculator') }}</div>
                <span class="helping-tool-item" style="padding-top: 20px">{{ $t('lbs.MortgageCalculator') }}</span>
              </el-tooltip>
            </div>
            <!--@click="$router.push({path: '/tellerlbsmortgage/mortgagevaluation'})"-->
            <div style="width: 220px;margin: 0 auto;cursor: pointer">
              <span style="display: inline-block;margin-right: 10px"><svg-icon icon-class="Icon material-attach-money" /></span>
              <el-tooltip effect="dark" placement="right">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.InstantValuation') }}</div>
                <span class="helping-tool-item">{{ $t('lbs.InstantValuation ') }}</span>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="button-group">
          <!--          <el-tooltip effect="dark" placement="right">-->
          <!--            <div slot="content" style="width: 300px">{{$t('lbsTips.MortgageOverview.PlanEnquiry')}}</div>-->
          <!--            <el-button type="danger" class="mortgage-button">{{$t('lbs.PlanEnquiry')}}-->
          <!--            </el-button>-->
          <!--          </el-tooltip>-->
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.ApplyMortgage') }}</div>
            <el-button
              type="primary"
              class="mortgage-button"
              @click="$router.push({path: '/tellerlbsmortgage/applymortgage'})"
            >
              {{ $t('lbs.ApplyMortgage') }}
            </el-button>
          </el-tooltip>
          <!--          <el-tooltip effect="dark" placement="right">-->
          <!--            <div slot="content" style="width: 300px">{{$t('lbsTips.MortgageOverview.ViewMyALLMortgage')}}</div>-->
          <!--            <el-button type="success" class="mortgage-button">{{$t('lbs.ViewMyALLMortgage')}}</el-button>-->
          <!--          </el-tooltip>-->
        </div>
      </el-col>
      <el-col :span="24">
        <div class="mortgage-table">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.MortgageOverview.MortgageRecord') }}</div>
            <p class="title2" style="margin-bottom: 20px">{{ $t('lbs.MortgageRecord') }}</p>
          </el-tooltip>
          <el-card>
            <span style="display: inline-block;margin-bottom: 20px">{{ $t('lbs.LoanAccount') }}： </span>
            <el-select v-model="loanAccount" placeholder="" style="width: 250px;" size="small">
              <el-option
                v-for="item in loanAccountList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-table
              :data="tableData"
              height="400px"
              :default-sort="{prop: 'contracttime', order: 'descending'}"
              style="width: 100%"
            >
              <el-table-column
                prop="contractnumber"
                :label="$t('lbs.ContractNumber')"
              />
              <el-table-column
                prop="contracttime"
                width="180"
                sortable
                :label="$t('lbs.ContractTime')"
              />
              <el-table-column
                prop="loanamount"
                :label="$t('lbs.LoanAmount')"
              />
              <el-table-column
                prop="currencycode"
                :label="$t('lbs.Currency')"
              />
              <el-table-column
                prop="loanannualrate"
                :label="$t('lbs.LoanAnnualRate')"
              />
              <el-table-column
                prop="repaymentperiod"
                :label="$t('lbs.RepaymentPeriod')"
              />
              <el-table-column
                prop="totalphase"
                :label="$t('lbs.TotalPhase')"
              />
              <el-table-column
                prop="interestamount"
                :label="$t('lbs.InterestAmount')"
              />
              <el-table-column
                prop="contractstatus"
                :label="$t('lbs.ContractStatus')"
              />
              <el-table-column
                width="80"
                prop="View"
                :label="$t('lbs.view')"
              >
                <template slot-scope="scope">
                  <i
                    class="el-icon-view"
                    style="cursor: pointer"
                    @click="viewRepaymentPlan(scope.row.accountnumber,scope.row.contractnumber)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    count: 0,
    loanAccount: '',
    loanAccountList: [],
    tableData: [],
    customernumber: ''
  }),
  watch: {
    'loanAccount'() {
      this.mortgageRecord()
    }
  },
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    const loanAccountList = JSON.parse(window.sessionStorage.getItem('loanaccountlist'))
    for (let i = 0; i < loanAccountList.length; i++) {
      if (loanAccountList[i].accountStatus === 'A') {
        this.loanAccountList.push({
          label: loanAccountList[i].accountNumber,
          value: loanAccountList[i].accountNumber
        })
      }
    }
    if (this.loanAccountList && this.loanAccountList[0]) {
      this.loanAccount = this.loanAccountList[0].value
    }
    this.mortgageRecord()
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupmortgageoverview'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色房屋贷款菜单'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(res.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
  },
  methods: {
    mortgageRecord() {
      const _this = this
      if (!_this.loanAccount) return
      _this.loading = true
      axios.post(`${_this.LBSGateway}/loan-experience/mortgage/allContractsRetrieval`, { accountnumber: _this.loanAccount }, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            if (response.data.data !== null) {
              const result = response.data.data
              _this.count = 0
              for (let i = 0; i < result.length; i++) {
                if (result[i].contractstatus === 'Active') {
                  _this.count++
                }
                result[i].contracttime = _this.$moment(result[i].contracttime).format('YYYY-MM-DD HH:mm:ss')
                result[i].interestamount = _this.$lbs.decimal_format(result[i].interestamount)
              }
              _this.tableData = response.data.data
            } else {
              _this.tableData = null
            }
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    // 查看还款计划详情
    viewRepaymentPlan(accountnumber, contractnumber) {
      window.sessionStorage.setItem('RepaymentPlan', JSON.stringify({
        accountnumber: accountnumber,
        contractnumber: contractnumber
      }))
      this.$router.push({ path: '/tellerlbsmortgage/repaymentplan' })
    }
  }
}
</script>

<style lang="scss" scoped>

  .mortgage {
    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

    .main {
      padding: 50px 15px 50px 60px;
    }

    .title2 {
      font-size: 25px;
      color: #22C1E6;
      display: inline-block;
    }

    .title3 {
      font-size: 18px;
      text-align: center;
      color: #22C1E6;
    }

    .mortgage-status {
      margin: 20px 0 0;
      height: 190px;
      background-color: #ACEFFF;
    }

    .right {
      display: inline-block;
      margin-right: 100px;
      float: right;
    }

    .button-group {
      text-align: center;
      margin-top: 20px;
    }

    .mortgage-button {
      width: 250px;
      margin: 20px 0 0 0;
    }

    .main-right {
      margin-top: 60px;
    }

    .helping-tool {
      width: 90%;
      height: 120px;
      margin: 20px auto 0;
      background-color: #C6F4FF;
    }

    .helping-tool-item {
      font-size: 18px;
      line-height: 40px;
      text-align: center;
      display: inline-block;
      color: #1e1e1e;
    }

    .mortgage-table {
      margin: 0 60px;
    }

  }

</style>
