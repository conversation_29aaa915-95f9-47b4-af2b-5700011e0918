<template>
  <div v-loading.fullscreen.lock="loading" class="makepaymentindex">
    <el-row>
      <el-col :span="24" :offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-ing" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 190px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 130px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 250px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right" style="width:630px;">
          <p class="text-title is-ing">{{ $t('lbsTips.paymentTip.tip1') }}</p>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Account') }}</div>
            <div style="padding: 43px 0 43px">
              <p class="text-account">{{ $t('lbsTips.paymentTip.tip13') }}</p>
              <el-select slot="prepend" v-model="accountNumber" placeholder="" style="width: 400px;" @change="getAccountBalance">
                <el-option
                  v-for="(item,index) in accountList"
                  :key="index"
                  :label="item.type + ' ' + ((item.type=='CreditCard')?item.label:item.label.substr(8,15))"
                  :value="item.type + ' ' + item.label"
                />
              </el-select>
              <div style="padding-left: 220px;color: #666;font-size: 14px;line-height: 40px;">{{ fromAccountBalance }}</div>
            </div>
          </el-tooltip>
          <p class="text-title is-ing">{{ $t('lbsTips.paymentTip.tip2') }}</p>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.payee') }}</div>
            <div style="padding: 20px 0 20px">
              <p class="text-account" style="width: 170px;margin: 20px 0">{{ $t('lbsTips.paymentTip.tip17') }}</p>
              <el-select
                slot="prepend"
                v-model="payeenumber"
                filterable
                :placeholder="$t('lbsTips.paymentTip.tip12')"
                style="width: 400px;"
              >
                <el-option
                  v-for="(item,index) in payeelist"
                  :key="index"
                  :label="(index+1)+', '+item.payeecategoryname+', '+item.payeename+', '+item.payeenumber"
                  :value="item.payeename+', '+item.payeenumber"
                />
              </el-select>
            </div>
          </el-tooltip>
          <p class="text-title is-ing">{{ $t('lbsTips.paymentTip.tip4') }}</p>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Amount') }}</div>
            <div>
              <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px">{{ $t('lbsTips.paymentTip.tip5') }}</p>
              <el-input v-model="amount" :placeholder="$t('lbsTips.paymentTip.tip11')" class="input-with-select" style="width: 400px" maxlength="50">
                <el-select slot="prepend" v-model="currency" placeholder="" style="width: 80px">
                  <el-option label="HKD" value="HKD" />
                </el-select>
              </el-input>
            </div>
          </el-tooltip>
          <br>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Effectivedate') }}</div>
            <div>
              <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px">{{ $t('lbsTips.paymentTip.tip6') }}</p>
              <el-date-picker
                id="effectiveDay"
                v-model="value"
                style="width: 400px;"
                type="date"
                placeholder=""
                :clearable="false"
                value-format="timestamp"
                :picker-options="pickerOptions"
              />
            </div>
          </el-tooltip>
          <br>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Reference') }}</div>
            <div style="width: 620px;position: relative">
              <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px;">{{ $t('lbsTips.paymentTip.tip7') }}</p>
              <el-input
                v-model="reference"
                type="textarea"
                style="width: 400px;float: right;resize: none;"
                :placeholder="$t('lbsTips.paymentTip.tip10')"
                maxlength="100"
              />
            </div>
          </el-tooltip>
          <p class="text-title">{{ $t('lbsTips.paymentTip.tip8') }}</p>
          <div style="width: 800px;text-align: center;">
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.tip47') }}</div>
              <el-button class="button" style="background-color: #109eae;color: #ffffff" icon="el-icon-check" @click="submit('next')">{{ $t('lbsTips.paymentTip.tip9') }}</el-button>

            </el-tooltip></div>
        </div>
      </el-col>
    </el-row>
    <auth v-if="showAuth" @redirect="retry" @hideAuth="hideAuth" />
  </div>
</template>

<script>
import auth from '../../auth.vue'
export default {
  name: 'Makepaymentindex',
  components: {
    auth
  },
  data() {
    return {
      showAuth: false,
      loading: false,
      currency: 'HKD',
      accountNumber: '',
      savingList: [],
      currentList: [],
      creditCardList: [],
      accountList: [],
      paymentInfo: {},
      checkList: [],
      payeelist: [],
      creditCardAccountList: [],
      payeenumber: '',
      payeeNumberList: [],
      amount: '',
      payeeCategoryIds: [],
      payeeInfo: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 1000 * 60 * 60 * 24 || time.getTime() > Date.now() + 1000 * 60 * 60 * 24 * 365
        }
      },
      value: new Date(new Date().getTime()),
      reference: '',
      fromAccountBalance: '',
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupMakeaPayment'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色支付菜单付款'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(res.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
    this.token = window.sessionStorage.getItem('token')
    this.initdata()
    // this.initCustomerPayeeList()
    this.initPayeeCategoryList()
  },
  methods: {
    retry(param) {
      this.submitPayment(param)
    },
    hideAuth() {
      this.showAuth = false
    },
    initPayeeCategoryList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeCategoryList', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          console.log(data.data)
          // Page show
          if (data.code === '200') {
            vue.payeeCategoryList = data.data
            vue.initCustomerPayeeList()
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeCategoryList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeCategoryList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initdata() {
      const savingAccountList = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
      const currentaccountlist = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
      const creditCardaccountlist = JSON.parse(window.sessionStorage.getItem('creditCardaccountlist'))
      for (var i = 0; i < savingAccountList.length; i++) {
        if (savingAccountList[i].accountStatus === 'A') {
          this.savingList.push({
            label: savingAccountList[i].accountNumber,
            value: savingAccountList[i].accountNumber,
            type: 'Saving'
          })
        }
      }

      for (var j = 0; j < currentaccountlist.length; j++) {
        if (currentaccountlist[j].accountStatus === 'A') {
          this.currentList.push({
            label: currentaccountlist[j].accountNumber,
            value: currentaccountlist[j].accountNumber,
            type: 'Current'
          })
        }
      }

      for (var k = 0; k < creditCardaccountlist.length; k++) {
        if (creditCardaccountlist[k].accountStatus === 'A') {
          this.creditCardList.push({
            label: creditCardaccountlist[k].accountNumber,
            value: creditCardaccountlist[k].accountNumber,
            type: 'CreditCard'
          })
        }
      }
      this.accountList = this.savingList.concat(this.currentList).concat(this.creditCardList)
      this.accountNumber = this.accountList[0].type + ' ' + this.accountList[0].label
      this.getAccountBalance()
    },
    getAccountBalance() {
      var vue = this
      var type = vue.accountNumber.split(' ')[0]
      var accountNumber = vue.accountNumber.split(' ')[1]

      if (type === 'CreditCard') {
        var requestdata = {
          creditcardnumber: accountNumber
        }

        this.$mui.ajax(
          this.LBSGateway +
              '/creditcard-experience/creditcard/creditLimitDetails',
          {
            data: requestdata,
            dataType: 'json', // 服务器返回json格式数据
            type: 'post', // HTTP请求类型
            timeout: 60000,
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid,
              'Content-Type': 'application/json',
              customerNumber: vue.customernumber
            },
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(data) {
              if (data.code === '200') {
                data.data.availableLimit = Number(
                  data.data.availableLimit
                    .toString()
                    .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
                )
                vue.fromAccountBalance = '( ' + vue.$t('lbs.Balance') + ' (' + data.data.ccyCode + ')：' + data.data.availableLimit + ' )'
              } else {
                vue.$mui.alert(
                  'Get account info failed! The response is: \n' +
                      JSON.stringify(data) +
                      '.',
                  'Error',
                  'OK'
                )
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get account info failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get account info failed. Time out!'
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      } else {
        vue.$mui.ajax(vue.LBSGateway + '/deposit-experience/deposit/account/accountDetails/' + vue.accountNumber.split(' ')[1], {
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            'accept': '*/*',
            'token': vue.token,
            'clientid': vue.$parent.clientid,
            'messageid': vue.$parent.messageid,
            'Content-Type': 'application/json',
            'customerNumber': vue.customernumber
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              var data1 = data.data.account
              if (!data1.availablebalance) data1.availablebalance = 0.0
              if (data1.availablebalance) {
                data1.availablebalance = Number(
                  data1.availablebalance
                    .toString()
                    .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
                )
              }
              vue.fromAccountBalance = '( ' + vue.$t('lbs.Balance') + ' (' + data1.currencycode + ')：' + data1.availablebalance + ' )'
            } else {
              vue.fromAccountBalance = 0.0
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg = 'Transfer failed! The response is: \n' + xhr.responseText + '.'
            if (type === 'timeout') {
              msg = 'Transfer failed. Time out!'
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        })
      }
    },
    initCustomerPayeeList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/customerPayeeRetrieval', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          console.log(data)
          if (data.code === '200') {
            var response = data.data

            for (var i = 0; i < response.length; i++) {
              if (vue.payeeCategoryIds.indexOf(response[i].payeecategoryid) === -1) {
                vue.payeeCategoryIds.push(response[i].payeecategoryid)
              }
              for (var j = 0; j < vue.payeeCategoryList.length; j++) {
                if (response[i].payeecategoryid === vue.payeeCategoryList[j].payeecategoryid) {
                  response[i]['payeecategoryname'] = vue.payeeCategoryList[j].payeecategory
                }
              }
            }
            vue.getinitdata()
            vue.payeelistTemp = response
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get CustomerPayeeList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get CustomerPayeeList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    getinitdata() {
      for (var i = 0; i < this.payeeCategoryIds.length; i++) {
        this.initPayeeInfoList(this.payeeCategoryIds[i])
      }
    },
    initPayeeInfoList(categoryid) {
      var vue = this
      var requesttimedata = {
        'payeeCategoryId': categoryid
      }
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeInfoListRetrieval', {
        data: requesttimedata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          // Page show
          vue.payeeInfo = vue.payeeInfo.concat(data.data)
          for (var i = 0; i < vue.payeelistTemp.length; i++) {
            for (var j = 0; j < vue.payeeInfo.length; j++) {
              if (vue.payeelistTemp[i].payeeid === vue.payeeInfo[j].payeeid) {
                vue.payeelistTemp[i]['payeename'] = vue.payeeInfo[j].payeename
                break
              }
            }
          }
          console.log(vue.payeelistTemp)
          vue.payeelist = vue.payeelistTemp

          // 刷新表格数据
          vue.payeelist.push({})
          vue.payeelist.splice(vue.payeelist.length - 1, 1)
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeInfoList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeInfoList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    submit() {
      if (this.payeenumber.length === 0) {
        this.$mui.alert('Please select Payee Number', 'Error', 'OK')
        return false
      }
      if (this.currency.length === 0) {
        this.$mui.alert('Please select currency', 'Error', 'OK')
        return false
      }

      if (this.amount.length === 0) {
        this.$mui.alert('Please enter Amount', 'Error', 'OK')
        return false
      }

      if (document.getElementById('effectiveDay').value === 0) {
        this.$mui.alert('Please select Effective Date', 'Error', 'OK')
        return false
      }
      this.paymentInfo['accountNumber'] = this.accountNumber

      this.paymentInfo['payeenumber'] = this.payeenumber.split(', ')[1]
      this.paymentInfo['currency'] = this.currency
      this.paymentInfo['amount'] = this.amount
      this.paymentInfo['effectiveDay'] = this.value
      this.paymentInfo['reference'] = this.reference
      for (var i = 0; i < this.payeelist.length; i++) {
        if (this.payeelist[i].payeenumber === this.payeenumber.split(', ')[1] && this.payeelist[i].payeename === this.payeenumber.split(', ')[0]) {
          this.paymentInfo['payeeid'] = this.payeelist[i].payeeid
        }
      }
      // window.sessionStorage.setItem('paymentDetail', JSON.stringify(this.paymentInfo))
      // this.$router.push({path: '/tellerlbspayment/makepayment/stepform'});
      this.submitPayment(this.token)
    },
    submitPayment(token) {
      var vue = this
      // 如果时间是当天将effectiveday置为空
      var nowDate = vue.$moment.parseZone(new Date().getTime()).local().format('YYYY-MM-DD')
      if (vue.$moment.parseZone(this.paymentInfo.effectiveDay).format('YYYY-MM-DD') === nowDate) {
        this.paymentInfo.effectiveDay = ''
      }
      var accountAndType = this.paymentInfo.accountNumber.split(' ')
      var account = accountAndType[1]
      var type = ''
      if (accountAndType[0] === 'Saving') {
        type = 'SAVI'
      } else if (accountAndType[0] === 'Current') {
        type = 'CURR'
      } else if (accountAndType[0] === 'CreditCard') {
        type = 'CRED'
      }

      var requesttimedata = {
        'customerAccountNumber': account,
        'customerAccountType': type,
        'payeeId': this.paymentInfo.payeeid,
        'payeeNumber': this.paymentInfo.payeenumber,
        'paymentAmount': this.paymentInfo.amount,
        'paymentEffectiveDay': this.paymentInfo.effectiveDay,
        'remarks': this.paymentInfo.reference,
        'transactionCurrency': this.paymentInfo.currency
      }
      console.log(requesttimedata)
      var btnArray = ['cancel', 'ok']
      this.$mui.confirm('Do you confirm payment?', 'Prompt', btnArray, function(e) {
        if (e.index === 1) {
          vue.$mui.ajax(vue.LBSGateway + '/payment-experience/payment/paymentTransaction', {
            data: requesttimedata,
            dataType: 'json', // 服务器返回json格式数据
            type: 'post', // HTTP请求类型
            timeout: 60000,
            headers: {
              'accept': '*/*',
              'token': token,
              'clientid': vue.$parent.clientid,
              'messageid': vue.$parent.messageid,
              'Content-Type': 'application/json',
              'customerNumber': vue.customernumber
            },
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(data) {
              console.log('Sell stock response data:' + JSON.stringify(data))
              if (data.code === '200') {
                window.sessionStorage.removeItem('paymentDetail')
                vue.$mui.alert('Payment Succeeds.', 'Success', 'OK', function() {
                  vue.$router.push({ path: '/tellerlbspayment/makepayment' })
                  vue.getAccountBalance()
                })
              } else {
                vue.$mui.alert('Payment failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
              }
            },
            error: function(xhr, type, errorThrown) {
              if (JSON.parse(xhr.response).code === '403005') {
                vue.$mui.alert(vue.$t('lbs.common.limitTip'), vue.$t('lbs.common.warning'), vue.$t('lbs.common.confirm'), function() {
                  vue.showAuth = true
                })
              } else {
                console.log(type)
                var msg = 'Payment failed! The response is: \n' + xhr.responseText + '.'
                if (type === 'timeout') {
                  msg = 'Payment failed. Time out!'
                }
                vue.$mui.alert(msg, 'Error', 'OK')
              }
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped>
  .makepaymentindex p {
    line-height: 1;
    color: #707070;
  }

  .makepaymentindex .text-title {
    font-size: 40px;
    color: #707070;
  }

  .makepaymentindex .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .makepaymentindex .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .makepaymentindex .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .makepaymentindex .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .el-message-box {
      width: 800px;
  }
  .makepaymentindex .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .makepaymentindex .el-input__icon {
    height: 40px;
  }

  .makepaymentindex .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .makepaymentindex .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }

  .makepaymentindex .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }
</style>
<style scoped>
  .makepaymentindex p {
    line-height: 1;
    color: #707070;
  }

  .makepaymentindex .text-title {
    font-size: 40px;
    color: #707070;
  }

  .makepaymentindex .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .makepaymentindex .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .makepaymentindex .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .makepaymentindex .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .makepaymentindex .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .makepaymentindex .el-input__icon {
    height: 40px;
  }

  .makepaymentindex .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .makepaymentindex .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .makepaymentindex .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .makepaymentindex .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .makepaymentindex .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .makepaymentindex .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .makepaymentindex .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .makepaymentindex .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }
</style>
