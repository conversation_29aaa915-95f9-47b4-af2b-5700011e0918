<template>
  <div v-loading.fullscreen.lock="loading">
    <el-row class="payeelist">
      <el-col :span="24">
        <div class="title">
          <p style="display: inline-block" class="max-title">{{ $t("lbsTips.paymentTip.tip17") }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row class="mypayment">
      <el-col :span="24">
        <div class="top-card">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.MerchantCharity') }}</div>
            <div class="card-item" style="position: relative" @click="jumpToMarchantpage()">
              <svg-icon
                icon-class="awesome-warehouse"
                style="width: 30px; height: 30px;position: absolute;top: 18px;left: 20px"
              />
              <p style="margin: 25px 0 26px;padding-left: 40px">{{ $t('lbsTips.paymentTip.tip18') }}</p>
            </div>
          </el-tooltip>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Mypayee') }}</div>
            <div class="card-item" style="position: relative;background-color: #AFF3FF;cursor: default">
              <svg-icon
                icon-class="material-group"
                style="width: 30px; height: 30px;position: absolute;top: 20px;left: 20px"
              />
              <p style="margin: 25px 0 26px;padding-left: 40px">{{ $t('lbsTips.paymentTip.tip17') }}</p>
            </div>
          </el-tooltip>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px">
              {{ $t('lbsTips.paymentTip.Addpayee') }}
            </div>
            <div class="card-item" style="position: relative;margin-right: 0" @click="jumppage()">
              <svg-icon
                icon-class="material-group-add"
                style="width: 30px; height: 30px;position: absolute;top: 20px;left: 20px"
              />
              <p style="margin: 25px 0 26px;padding-left: 40px">{{ $t('lbsTips.paymentTip.tip20') }}</p>
            </div>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <el-row class="mypayment">
      <el-col :span="6">
        <div style="padding-right: 10px">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 180px">
              {{ $t('lbsTips.paymentTip.Selectcategories') }}
            </div>
            <div style="padding: 20px;background-color: #ffffff;margin-top: 70px">
              <p class="title">{{ $t('lbsTips.paymentTip.tip24') }}</p>
              <el-table
                ref="multipleTable"
                height="400"
                :data="payeeCategoryList"
                tooltip-effect="dark"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  type="selection"
                  width="55"
                />
                <el-table-column
                  prop="payeecategory"
                  :label="$t('lbsTips.paymentTip.tip23')"
                />
              </el-table>
            </div>
          </el-tooltip>
        </div>
      </el-col>
      <el-col :span="18">
        <div style="padding-left: 10px">
          <div style="padding: 20px;background-color: #ffffff;margin-top: 70px">
            <div
              style="width: 160px;height: 50px;"
            >
              <el-tooltip effect="dark" placement="top">
                <div slot="content" style="width: 300px">
                  {{ $t('lbsTips.paymentTip.Allpayee') }}
                </div>
                <p style="font-size: 20px;text-align: center;line-height: 50px"> {{ $t('lbsTips.paymentTip.tip25') }}</p>
              </el-tooltip>
            </div>
            <div class="el-table__header-wrapper">
              <table
                cellspacing="0"
                cellpadding="0"
                border="0"
                class="el-table el-table__header"
                style="width: 100%;"
              >
                <colgroup />
                <thead class="has-gutter">
                  <tr class="">
                    <th colspan="1" rowspan="1" class="el-table_10_column_45 is-leaf" width="50">
                      <el-tooltip effect="dark" placement="top">
                        <div slot="content" style="width: 300px">
                          {{ $t('lbsTips.paymentTip.SequenceNumber') }}
                        </div>
                        <div class="cell">No.</div>
                      </el-tooltip>
                    </th>
                    <th colspan="1" rowspan="1" class="el-table_10_column_46 is-leaf">
                      <el-tooltip effect="dark" placement="top">
                        <div slot="content" style="width: 300px">
                          {{ $t('lbsTips.paymentTip.Categoryname') }}
                        </div>
                        <div class="cell">{{ $t('lbsTips.paymentTip.tip26') }}</div>
                      </el-tooltip>
                    </th>
                    <th colspan="1" rowspan="1" class="el-table_10_column_47 is-leaf">
                      <el-tooltip effect="dark" placement="top">
                        <div slot="content" style="width: 300px">
                          {{ $t('lbsTips.paymentTip.Payeename') }}
                        </div>
                        <div class="cell">{{ $t('lbsTips.paymentTip.tip27') }}</div>
                      </el-tooltip>
                    </th>
                    <th colspan="1" rowspan="1" class="el-table_10_column_48 is-leaf">
                      <el-tooltip effect="dark" placement="top">
                        <div slot="content" style="width: 300px">
                          {{ $t('lbsTips.paymentTip.Payeenumber') }}
                        </div>
                        <div class="cell">{{ $t('lbsTips.paymentTip.tip28') }}</div>
                      </el-tooltip>
                    </th>
                    <th colspan="1" rowspan="1" class="el-table_10_column_49 is-center is-leaf" width="190">
                      <div class="cell">
                        <div @click="jumppage()">
                          <svg-icon
                            icon-class="Icon ionic-ios-add-circle-outline"
                            style="width: 20px; height: 20px;cursor: pointer;margin-top: 10px"
                          />
                        </div>
                      </div>
                    </th>
                    <th class="gutter" style="width: 15px;" />
                  </tr>
                </thead>
              </table>
            </div>
            <el-table
              :data="payeelist"
              :show-header="false"
              style="width: 100%;"
              :default-sort="{prop: 'payeecategoryid', order: 'ascending'}"
              height="688px"
            >
              <el-table-column type="index" label="No." width="50" />
              <el-table-column prop="payeecategoryname" :label="$t('lbsTips.paymentTip.tip26')" />
              <el-table-column prop="payeename" :label="$t('lbsTips.paymentTip.tip27')" />
              <el-table-column prop="payeenumber" :label="$t('lbsTips.paymentTip.tip28')" />
              <el-table-column
                :label="$t('lbsTips.paymentTip.tip29')"
                align="center"
                width="190"
              >
                <template slot="header">
                  <div @click="jumppage()">
                    <svg-icon
                      icon-class="Icon ionic-ios-add-circle-outline"
                      style="width: 20px; height: 20px;cursor: pointer;margin-top: 10px"
                    />
                  </div>
                </template>
                <template slot-scope="scope">
                  <el-tooltip effect="dark" placement="right">
                    <div slot="content" style="width: 180px">
                      {{ $t('lbsTips.paymentTip.tip30') }}
                    </div>
                    <el-button
                      size="mini"
                      type="danger"
                      @click="handleDelete(scope.$index, scope.row)"
                    >{{ $t('lbsTips.paymentTip.tip31') }}
                    </el-button>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Mypayee',
  data() {
    return {
      loading: false,
      token: null,
      name: '',
      category: '',
      payeeCategoryList: [],
      multipleSelection: [],
      payeelist: [],
      payeeInfo: [],
      payeeCategoryIds: [],
      tempList: [],
      tempList2: [],
      flag: false,
      payeelistCopy: [],
      flag2: true,
      payeelistTemp: [],
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupPayeeList'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色支付菜单收款列表'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(res.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
    this.token = window.sessionStorage.getItem('token')
    this.initPayeeCategoryList()
  },
  methods: {
    initPayeeCategoryList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeCategoryList', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          console.log(data.data)
          // Page show
          if (data.code === '200') {
            vue.payeeCategoryList = data.data
            vue.initCustomerPayeeList()
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeCategoryList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeCategoryList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    handleSelectionChange(val) {
      if (this.payeelistCopy.length > 0) {
        this.payeelist = this.payeelistCopy
      }
      var ids = []
      if (val.length === 0) {
        this.payeelist = this.tempList.concat(this.tempList2)
      } else {
        if (this.flag) {
          this.payeelist = this.tempList.concat(this.tempList2)
          this.tempList = []
          this.tempList2 = []
        }
        for (var i = 0; i < val.length; i++) {
          ids.push(val[i].payeecategoryid)
        }
        for (var j = 0; j < this.payeelist.length; j++) {
          if (ids.indexOf(this.payeelist[j].payeecategoryid) > -1) {
            this.tempList.push(this.payeelist[j])
          } else {
            this.tempList2.push(this.payeelist[j])
          }
        }
        this.flag = true
        this.payeelist = this.tempList
      }
    },
    initCustomerPayeeList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/customerPayeeRetrieval', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          console.log(data)
          if (data.code === '200') {
            var response = data.data

            for (var i = 0; i < response.length; i++) {
              if (vue.payeeCategoryIds.indexOf(response[i].payeecategoryid) === -1) {
                vue.payeeCategoryIds.push(response[i].payeecategoryid)
              }
              for (var j = 0; j < vue.payeeCategoryList.length; j++) {
                if (response[i].payeecategoryid === vue.payeeCategoryList[j].payeecategoryid) {
                  response[i]['payeecategoryname'] = vue.payeeCategoryList[j].payeecategory
                }
              }
            }
            vue.initdata()
            vue.payeelistTemp = response
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get CustomerPayeeList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get CustomerPayeeList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initdata() {
      for (var i = 0; i < this.payeeCategoryIds.length; i++) {
        this.initPayeeInfoList(this.payeeCategoryIds[i])
      }
    },
    initPayeeInfoList(categoryid) {
      var vue = this
      var requesttimedata = {
        'payeeCategoryId': categoryid
      }
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeInfoListRetrieval', {
        data: requesttimedata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          // Page show
          vue.payeeInfo = vue.payeeInfo.concat(data.data)
          for (var i = 0; i < vue.payeelistTemp.length; i++) {
            for (var j = 0; j < vue.payeeInfo.length; j++) {
              if (vue.payeelistTemp[i].payeeid === vue.payeeInfo[j].payeeid) {
                vue.payeelistTemp[i]['payeename'] = vue.payeeInfo[j].payeename
                vue.payeelistTemp[i]['payeeaccountnumber'] = vue.payeeInfo[j].payeeaccountnumber
                break
              }
            }
          }
          console.log(vue.payeelistTemp)
          vue.payeelist = vue.payeelistTemp

          // 刷新表格数据
          vue.payeelist.push({})
          vue.payeelist.splice(vue.payeelist.length - 1, 1)
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeInfoList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeInfoList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    handleEdit(index, row) {
      console.log(index, row)
    },
    handleDelete(index, row, rows) {
      console.log(index, row)
      this.submitDelete(row.payeecategoryid, row.payeeid, row.payeenumber, index)
    },
    submitDelete(payeecategoryid, payeeid, payeenumber, index) {
      var vue = this
      var requesttimedata = {
        'payeeCategoryID': payeecategoryid,
        'payeeID': payeeid,
        'payeeNumber': payeenumber
      }
      var btnArray = ['cancel', 'ok']
      vue.$mui.confirm('Are you sure you want to delete it?', 'Delete Payee', btnArray, function(e) {
        if (e.index === 1) {
          vue.$mui.ajax(vue.LBSGateway + '/payment-experience/payment/billPayeeDelete', {
            data: requesttimedata,
            dataType: 'json', // 服务器返回json格式数据
            type: 'post', // HTTP请求类型
            timeout: 60000,
            headers: {
              'accept': '*/*',
              'token': vue.token,
              'clientid': vue.$parent.clientid,
              'messageid': vue.$parent.messageid,
              'Content-Type': 'application/json',
              'customerNumber': vue.customernumber
            },
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(data) {
              console.log('Delete Payee response data:' + JSON.stringify(data))
              if (data.code === '200') {
                vue.$mui.alert('Delete Payee Succeeds.', 'Success', 'OK', function() {
                  // window.location.reload();
                  // vue.initPayeeCategoryList();
                  vue.payeelist.splice(index, 1)
                })
              } else {
                vue.$mui.alert('Delete Payee failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg = 'Delete Payee failed! The response is: \n' + xhr.responseText + '.'
              if (type === 'timeout') {
                msg = 'Delete Payee failed. Time out!'
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          })
        }
      })
    },
    advancedSearch() {
      if (this.flag2) {
        this.payeelistCopy = this.payeelist
      }
      this.flag2 = false
      if (this.name.length > 0) {
        if (this.category.length > 0) {
          //  根据 name 和 category 搜索
          this.payeelist = []
          for (var i = 0; i < this.payeelistCopy.length; i++) {
            if (this.name.toLowerCase() === this.payeelistCopy[i].payeename.toLowerCase() && this.category.toLowerCase() === this.payeelistCopy[i].payeecategoryname.toLowerCase()) {
              this.payeelist.push(this.payeelistCopy[i])
            }
          }
        } else {
          //  根据 name 搜索
          this.payeelist = []
          for (var j = 0; j < this.payeelistCopy.length; j++) {
            if (this.name.toLowerCase() === this.payeelistCopy[j].payeename.toLowerCase()) {
              this.payeelist.push(this.payeelistCopy[j])
            }
          }
        }
      } else {
        if (this.category.length > 0) {
          //  根据 category 搜索
          this.payeelist = []
          for (var k = 0; k < this.payeelistCopy.length; k++) {
            if (this.category.toLowerCase() === this.payeelistCopy[k].payeecategoryname.toLowerCase()) {
              this.payeelist.push(this.payeelistCopy[k])
            }
          }
        }
      }
    },
    isNull() {
      if (this.name.length === 0 && this.category.length === 0) {
        this.payeelist = this.payeelistCopy
      }
    },
    jumppage() {
      this.$router.push({ path: '/tellerlbspayment/payeelist/addpayee' })
    },
    jumpToMarchantpage() {
      this.$router.push({ path: '/tellerlbspayment/payeelist/merchantlist' })
    }
  }
}
</script>

<style scoped>
  .mypayment .title {
    font-size: 25px;
    color: #707070;
    margin-bottom: 20px;
  }

  .mypayment .search-text {
    margin-top: 10px;
    font-size: 20px;
    color: #707070;
  }

  .mypayment .top-card {
    width: 1050px;
    margin: 0 auto;
  }

  .mypayment .top-card .card-item {
    float: left;
    width: 240px;
    border: 1px solid #22C1E6;
    margin-right: 150px;
    cursor: pointer;
    background-color: #ffffff;
  }

  .mypayment .top-card .card-item p {
    text-align: center;
    color: #707070;
    font-size: 20px;
  }
</style>

<style>
  .mypayment .el-input--medium .el-input__inner {
    margin-bottom: 0;
  }

  .el-message-box {
    width: 800px;
  }
</style>
<style scoped>
  .payeelist .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 40px 25px;
    border-radius: 50px;
    background-image: url("../../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .payeelist p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .payeelist .max-title {
    font-size: 50px;
    margin-top: 15px;
    font-weight: 500;
    color: #22C1E6;
    margin-bottom: 40px;
  }
</style>

