<template>
  <div>
    <el-row v-loading.fullscreen.lock="loading" class="transaction">
      <el-col :psan="24">
        <el-row>
          <el-col :span="24">
            <div class="title">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.transactionTips.WithDrawal') }}</div>
                <p style="display: inline-block" class="max-title">{{ $t("lbs.router.withdraw") }}</p>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row class="depositform">
      <el-col :lg="14" :xs="24">
        <div class="main-left">
          <p class="summary">{{ $t('lbsTips.accountDetailTip.tip48') }}</p>
          <el-row>
            <el-col :lg="12" :xs="24">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip37') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="12" :xs="24">
              <p
                class="select-result"
              >{{ depositInfo.currencycode + ' ' + $lbs.decimal_format(depositInfo.withDrawalAmount) }}</p>
            </el-col>
            <el-col :lg="12" :xs="24">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.transactionTips.AccountTip') }}</div>
                <p class="select-result">{{ $t('lbsTips.transactionTips.Account') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="12" :xs="24">
              <p class="select-result">{{ depositInfo.accountNumber }}</p>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="button-group">
          <el-button class="button btn1" type="primary" @click="submit('next')">{{ $t('lbs.Next') }}</el-button>
          <el-button class="button btn2" type="danger" @click="submit('back')">{{ $t('lbs.Cancel') }}</el-button>
        </div>
      </el-col>
    </el-row>
    <auth v-if="showAuth" :auth-list="authList" @redirect="retry" @hideAuth="hideAuth" />
  </div>
</template>

<script>
import auth from '../../auth.vue'

export default {
  name: 'Depositform',
  components: {
    auth
  },
  data() {
    return {
      showAuth: false,
      loading: false,
      token: null,
      depositInfo: {},
      password: '',
      customernumber: '',
      authList: []
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.depositInfo = JSON.parse(window.sessionStorage.getItem('depositInfo'))
    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色交易取现'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(res.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
  },
  methods: {
    retry(param) {
      this.submitDeposit(param)
    },
    hideAuth() {
      this.showAuth = false
    },
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/tellerlbstransaction/withdraw' })
      } else if (type === 'next') {
        this.submitDeposit(this.token)
      }
    },
    submitDeposit(token) {
      var vue = this
      this.$mui.ajax(
        this.LBSGateway + '/deposit-experience/deposit/account/withdrawal',
        {
          data: vue.depositInfo,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json',
            customerNumber: vue.customernumber
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            console.log('Operation response data:' + JSON.stringify(data))
            if (data.code === '200') {
              vue.$mui.alert(
                'Operation success!',
                'Success',
                'OK'
              )
              vue.$router.push({ path: '/tellerlbstransaction/withdraw' })
              this.showAuth = false
            } else if (data.code === '202002') {
              vue.$mui.alert(
                'Insufficient Fund!',
                'error',
                'OK'
              )
            } else {
              vue.$mui.alert(
                'Operation failed! The response is: \n' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            if (JSON.parse(xhr.response).code === '403005') {
              vue.$mui.alert(vue.$t('lbs.common.limitTip'), vue.$t('lbs.common.warning'), vue.$t('lbs.common.confirm'), function() {
                vue.authList = JSON.parse(xhr.response).data
                if (vue.authList) {
                  vue.showAuth = true
                } else {
                  vue.$mui.alert(vue.$t('lbsTips.accountDetailTip.overrideLimitMsg'), vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
                }
              })
            } else {
              console.log(type)
              var msg =
                'Operation failed! The response is: \n' + xhr.responseText + '.'
              if (type === 'timeout') {
                msg = 'Operation failed. Time out!'
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        }
      )
    }
  }
}
</script>

<style scoped>
  .transaction .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .transaction p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .transaction .max-title {
    font-size: 50px;
    margin-bottom: 40px;
    font-weight: 500;
    color: #22C1E6;
    margin-top: 15px;
  }

  .transaction .little-title {
    font-size: 30px;
  }
.depositform .summary {
  color: #707070;
  font-size: 42px;
  margin-bottom: 80px;
  margin-top: 100px;
}

.depositform .main-left {
  padding: 30px 50px 30px;
}

.depositform .select-result {
  color: #707070;
  font-size: 30px;
  margin-bottom: 80px;
}

.depositform p {
  line-height: 1;
}

.depositform .title {
  font-size: 30px;
  color: #22c1e6;
  margin-top: 100px;
  text-align: center;
  margin-bottom: 40px;
}

.depositform .title2 {
  font-size: 28px;
  color: #22c1e6;
  text-align: center;
  margin-bottom: 70px;
}

.depositform .verification {
  width: 400px;
  height: 280px;
  margin: 0 auto;
  border: 1px solid #999999;
  padding: 20px;
}

.depositform .button {
  float: right;
  width: 150px;
  margin-bottom: 30px;
  margin-top: 60px;
  height: 40px;
}

.depositform .btn2 {
  margin-right: 20px;
}

.depositform .button-group {
  min-width: 330px;
  margin: 0 auto;
}

.depositform .div-button-verify {
  width: 100px;
  margin: 0 auto;
}

.depositform .button-verify {
  margin-top: 40px;
  width: 100px;
}
</style>

<style>
.depositform .el-input__icon {
  height: 50px;
}

.depositform .el-input__inner {
  margin-bottom: 0;
  height: 50px;
}

.depositform .el-input-group__append {
  height: 50px;
}
</style>
