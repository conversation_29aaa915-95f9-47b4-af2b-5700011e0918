<template>
  <div>
    <el-card v-if="!dialogCaptureDemographic" v-loading.fullscreen.lock="loading" shadow="never" class="workflow">
      <div slot="header" class="clearfix">
        <span>{{ $t("Workflow.Application") }}</span>
      </div>

      <el-table
        class="params-table"
        :data="tableDataList.slice((currentPage-1)*pagesize,currentPage*pagesize)"
      >
        <el-table-column type="index" />
        <el-table-column
          prop="type"
          :label="$t('Workflow.type')"
          align="center"
        />
        <!--申请编号-->
        <el-table-column
          prop="applyNo"
          :label="$t('Workflow.ApplicationNo')"
          align="center"
        />
        <!--提交时间-->
        <el-table-column
          prop="createDate"
          :label="$t('Workflow.submitTime')"
          align="center"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.updateDate).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <!--更新时间-->
        <el-table-column
          prop="updateDate"
          :label="$t('Workflow.UpdateT')"
          align="center"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.updateDate).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <!--状态-->
        <el-table-column
          prop="status"
          :label="$t('Workflow.Status')"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.status === '0'">{{ $t('lbs.common.pending') }}</span>
            <span v-if="scope.row.status === '1'">{{ $t('lbs.common.agree') }}</span>
            <span v-if="scope.row.status === '2'">{{ $t('lbs.common.reject') }}</span>
            <span v-if="scope.row.status === '-1'">{{ $t('lbs.common.cancel') }}</span>
          </template>
        </el-table-column>
        <!--操作-->
        <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === '0'"
              size="mini"
              type="text"
              @click="gethandleFun(scope.row)"
            >
              {{ $t('Operate.handle') }}
            </el-button>
            <el-button
              v-else
              size="mini"
              type="text"
              @click="getLookerFun(scope.row)"
            >
              {{ $t('Operate.see') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pagesize"
          :total="tableDataList.length"
          layout="total, prev, pager, next"
          style="margin-top: 20px; text-align: right;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!--员工信息-->
      <el-dialog
        :title="dialogVisiblePlus.title === 'edit' ? $t('Operate.stafferInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="dialogVisiblePlus.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form ref="form" :model="fromList" label-width="150px" label-position="left" size="small">
          <!--类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>
          <!--员工编号-->
          <el-form-item :label="$t('lbs.common.stafferNumber')">
            <el-input v-model="MobilyJson.staffNumber" disabled />
          </el-form-item>
          <!--部门-->
          <el-form-item :label="$t('Workflow.department')">
            <el-select v-model="MobilyJson.department" :placeholder="$t('Workflow.pleaseSelect')" disabled style="width: 100%" @change="changeSelect">
              <el-option
                v-for="(item,index) in brandOptions"
                :key="index"
                :label="item.department"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <!--角色-->
          <el-form-item :label="$t('Workflow.role')">
            <el-select v-model="MobilyJson.roleId" :placeholder="$t('Workflow.pleaseSelect')" disabled style="width: 100%">
              <el-option
                v-for="(item,index) in typeOptions"
                :key="index"
                :label="item.remark"
                :value="item.role"
              />
            </el-select>
          </el-form-item>
          <!--用户名 UserName-->
          <el-form-item :label="$t('lbs.common.username')">
            <el-input v-model="MobilyJson.username" disabled />
          </el-form-item>
          <!--邮箱 Workflow.emailaddress-->
          <el-form-item :label="$t('Workflow.emailaddress')">
            <el-input v-model="MobilyJson.emailAddress" disabled />
          </el-form-item>
          <!--密码password-->
          <el-form-item :label="$t('lbs.common.password')">
            <el-input v-model="MobilyJson.pwd" disabled />
          </el-form-item>
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="dialogVisiblePlus.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="dialogVisiblePlus.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="dialogVisiblePlus.title === 'detail'" size="small" @click="dialogVisiblePlus.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝-->
          <el-button v-if="dialogVisiblePlus.title === 'edit'&& ListStatus === '0'" type="danger" size="small" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--确定处理-->
          <el-button v-if="dialogVisiblePlus.title === 'edit' && ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--员工权限-->
      <el-dialog
        :title="jurisdiction.title === 'edit' ? $t('Operate.stafferPermissionInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="jurisdiction.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <!--<p>员工权限</p>-->
        <el-form ref="ruleFormList" :model="ruleFormList" label-width="220px" label-position="left" size="small">

          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')" prop="roles">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>

          <!--操作对象-->
          <el-form-item :label="$t('Workflow.opearObject')" prop="roles">
            <el-input v-model="MobilyJson.roles" disabled />
          </el-form-item>
          <!--权限描述-->
          <el-form-item :label="$t('Workflow.description')" prop="name">
            <el-input v-model="MobilyJson.name" disabled />
          </el-form-item>
          <!--权限接口-->
          <el-form-item :label="$t('Workflow.interface')" prop="url">
            <el-input v-model="MobilyJson.url" disabled />
          </el-form-item>
          <!--接口额度审核字段-->
          <el-form-item :label="$t('Workflow.approvalfield')" prop="checkLimitAmtField">
            <el-input v-model="MobilyJson.checkLimitAmtField" disabled />
          </el-form-item>
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="jurisdiction.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="jurisdiction.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="jurisdiction.title === 'detail'" size="small" @click="jurisdiction.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="jurisdiction.title === 'edit'&& ListStatus === '0'" type="danger" size="small" @click="refuse()">{{ $t('Operate.Refuse') }} </el-button>
          <!--提交-->
          <el-button v-if="jurisdiction.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--角色管理-->
      <el-dialog
        :title="RoleQuota.title === 'edit' ? $t('Operate.roleInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="RoleQuota.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form ref="form" :model="MobilyJson" label-width="150px" label-position="left" size="small">
          <!--部门-->
          <el-form-item :label="$t('Workflow.department')">
            <el-select v-model="MobilyJson.departmentId" :placeholder="$t('Workflow.pleaseSelect')" disabled style="width: 100%" @change="changeSelect">
              <el-option
                v-for="(item,index) in brandOptions"
                :key="index"
                :label="item.department"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <!--角色-->
          <el-form-item :label="$t('Workflow.role')">
            <el-input v-model="MobilyJson.role" disabled />
          </el-form-item>
          <!--描述-->
          <el-form-item :label="$t('Workflow.describe')">
            <el-input v-model="MobilyJson.remark" disabled />
          </el-form-item>
          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="RoleQuota.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="RoleQuota.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="RoleQuota.title === 'detail'" size="small" @click="RoleQuota.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="RoleQuota.title === 'edit'&& ListStatus === '0'" type="danger" size="small" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="RoleQuota.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--参数配置-->
      <el-dialog
        :title="Parameterconfiguration.title === 'edit' ? $t('Operate.systemParamInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="Parameterconfiguration.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form ref="ruleFormList" :model="ruleFormUser" label-width="150px" label-position="left" size="small">
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>
          <!--参数-->
          <el-form-item :label="$t('Workflow.parameterN')">
            <el-input v-model="MobilyJson.item" disabled />
          </el-form-item>
          <!--值-->
          <el-form-item :label="$t('Workflow.value')">
            <el-input v-model="MobilyJson.value" disabled />
          </el-form-item>
          <!--序列-->
          <el-form-item :label="$t('Operate.sequence')">
            <el-input v-model="MobilyJson.sort" disabled />
          </el-form-item>
          <!--备注-->
          <el-form-item :label="$t('Workflow.remarks')">
            <el-input v-model="MobilyJson.remark" disabled />
          </el-form-item>
          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="Parameterconfiguration.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="Parameterconfiguration.title==='detail'" />
          </el-form-item>

        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="Parameterconfiguration.title === 'detail'" size="small" @click="Parameterconfiguration.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="Parameterconfiguration.title === 'edit'&& ListStatus === '0'" type="danger" size="small" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="Parameterconfiguration.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--定存利率-->
      <el-dialog
        :title="InterestDialog.title === 'edit' ? $t('Operate.depositRateInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="InterestDialog.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="ruleFormList"
          :model="ruleFormUser"
          label-width="150px"
          class="demo-ruleForm"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>

          <!--定存金额范围-->
          <el-form-item :label="$t('Workflow.FixedAmount')">
            <el-select
              v-model="MobilyJson.depositrange"
              disabled
              style="width: 100%"
              @change="changeSelect"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.amountrangemin < 1000000 ? item.amountrangemin + '-' + item.amountrangemax : '>' + item.amountrangemin"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <!--存款期限-->
          <el-form-item :label="$t('Workflow.Deposit')">
            <el-input v-model="MobilyJson.tdperiod" disabled />
          </el-form-item>
          <!--定存利率-->
          <el-form-item :label="$t('Workflow.depositrate')">
            <el-input v-model="MobilyJson.tdinterestrate" disabled />
          </el-form-item>
          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="InterestDialog.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark" :label="$t('Workflow.remarks')">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="InterestDialog.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="InterestDialog.title === 'detail'" size="small" @click="InterestDialog.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="InterestDialog.title === 'edit'&& ListStatus === '0'" type="danger" size="small" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="InterestDialog.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--外汇利率-->
      <el-dialog
        :title="CurrencyDialog.title === 'edit' ? $t('Operate.fexRateInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="CurrencyDialog.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="ruleFormList"
          :model="ruleFormUser"
          label-width="150px"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>

          <el-form-item :label="$t('Workflow.Currencyname')">
            <el-input v-model="MobilyJson.currency" disabled />
          </el-form-item>
          <!--货币简称-->
          <el-form-item :label="$t('Workflow.Currencyabbreviation')">
            <el-input v-model="MobilyJson.ccycode" disabled />
          </el-form-item>
          <!--小数位-->
          <el-form-item :label="$t('Workflow.Decimalplaces')">
            <el-input v-model="MobilyJson.ccyplaces" disabled />
          </el-form-item>
          <!--银行买入价-->
          <el-form-item :label="$t('Workflow.BnakBuy')">
            <el-input v-model="MobilyJson.bankbuy" disabled />
          </el-form-item>
          <!--银行卖出价-->
          <el-form-item :label="$t('Workflow.BankSell')">
            <el-input v-model="MobilyJson.banksell" disabled />
          </el-form-item>
          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="CurrencyDialog.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="CurrencyDialog.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="CurrencyDialog.title === 'detail'" size="small" @click="CurrencyDialog.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="CurrencyDialog.title === 'edit'&& ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="CurrencyDialog.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--节假日配置-->
      <el-dialog
        :title="HolidayDialog.title === 'edit' ? $t('Operate.holidayInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="HolidayDialog.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="ruleFormList"
          :model="ruleFormUser"
          label-width="150px"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>

          <!--国家-->
          <el-form-item :label="$t('Workflow.country')">
            <el-input v-model="MobilyJson.countrycode" disabled />
          </el-form-item>
          <!--日期-->
          <el-form-item :label="$t('Workflow.date')">
            <el-date-picker
              v-model="MobilyJson.day"
              type="date"
              align="center"
              disabled
              style="width: 100%;"
            />
          </el-form-item>

          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="HolidayDialog.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="HolidayDialog.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="HolidayDialog.title === 'detail'" size="small" @click="HolidayDialog.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="HolidayDialog.title === 'edit'&& ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="HolidayDialog.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--个人贷款金额配置-->
      <el-dialog
        :title="LoanDialog.title === 'edit' ? $t('Operate.personalLoanRateInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="LoanDialog.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="ruleFormList"
          :model="ruleFormUser"
          label-width="180px"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>

          <!--产品代码-->
          <el-form-item :label="$t('Workflow.proCode')" prop="productcode">
            <el-input v-model="MobilyJson.productcode" maxlength="10" disabled />
          </el-form-item>
          <!--货币类型-->
          <el-form-item :label="$t('Workflow.Currencytype')" prop="currencycode">
            <el-input v-model="MobilyJson.currencycode" maxlength="3" disabled />
          </el-form-item>
          <!--贷款最低金额-->
          <el-form-item :label="$t('Workflow.Minimum')">
            <el-input v-model="MobilyJson.amountrangemin" type="number" disabled />
          </el-form-item>
          <!--贷款最大金额-->
          <el-form-item :label="$t('Workflow.Maximum')">
            <el-input v-model="MobilyJson.amountrangemax" type="number" disabled />
          </el-form-item>
          <!--低利率-->
          <el-form-item :label="$t('Workflow.Lowinterest')">
            <el-input v-model="MobilyJson.lowrate" type="number" disabled />
          </el-form-item>
          <!--高利率-->
          <el-form-item :label="$t('Workflow.Highinteres')">
            <el-input v-model="MobilyJson.highrate" type="number" disabled />
          </el-form-item>
          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="LoanDialog.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="LoanDialog.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="LoanDialog.title === 'detail'" size="small" @click="LoanDialog.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="LoanDialog.title === 'edit'&& ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="LoanDialog.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--支付收款人信息配置-->
      <el-dialog
        :title="PaymentDialog.title === 'edit' ? $t('Operate.paymentPayeeInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="PaymentDialog.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="ruleFormList"
          :model="ruleFormUser"
          label-width="180px"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>

          <!--收款人行业编号-->
          <el-form-item :label="$t('Workflow.industryNum')">
            <el-input v-model="MobilyJson.payeecategoryid" disabled />
          </el-form-item>
          <!--收款人行业-->
          <el-form-item :label="$t('Workflow.Payeeindustry')">
            <el-input v-model="MobilyJson.payeecategory" disabled />
          </el-form-item>
          <!--更新时间-->
          <el-form-item :label="$t('Workflow.UpdateTime')">
            <div class="block">
              <el-date-picker
                v-model="MobilyJson.lastupdatedate"
                type="date"
                align="center"
                style="width: 100%"
                disabled
              />
            </div>
          </el-form-item>
          <!--创建时间-->
          <el-form-item :label="$t('Workflow.Cteatime')">
            <div class="block">
              <el-date-picker
                v-model="MobilyJson.createdate"
                type="date"
                align="center"
                style="width: 100%"
                disabled
              />
            </div>
          </el-form-item>

          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="PaymentDialog.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="PaymentDialog.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="PaymentDialog.title === 'detail'" size="small" @click="PaymentDialog.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="PaymentDialog.title === 'edit'&& ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="PaymentDialog.title === 'edit'&& ListStatus === '0'" type="primary" size="small" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!--定时跑批表配置-->
      <el-dialog
        :title="SchedulejobDialog.title === 'edit' ? $t('Operate.scheduleJobInfoChangeApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="SchedulejobDialog.show"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="ruleFormList"
          :model="ruleFormUser"
          label-width="150px"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="Mobily.type" disabled />
          </el-form-item>

          <el-form-item label="Name">
            <el-input v-model="MobilyJson.name" disabled />
          </el-form-item>
          <el-form-item label="Group name">
            <el-input v-model="MobilyJson.groupName" disabled />
          </el-form-item>
          <el-form-item label="Cron">
            <el-input v-model="MobilyJson.cron" disabled />
          </el-form-item>
          <el-form-item label="URL">
            <el-input v-model="MobilyJson.url" disabled />
          </el-form-item>
          <el-form-item label="Parameter">
            <el-input v-model="MobilyJson.parameter" disabled />
          </el-form-item>
          <el-form-item label="Description">
            <el-input v-model="MobilyJson.description" disabled />
          </el-form-item>
          <el-form-item label="VM param">
            <el-input v-model="MobilyJson.vmParam" disabled />
          </el-form-item>
          <el-form-item label="Jar path">
            <el-input v-model="MobilyJson.jarPath" disabled />
          </el-form-item>
          <el-form-item label="Status">
            <el-input v-model="MobilyJson.status" disabled />
          </el-form-item>

          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            {{ Mobily.applicant }}
          </el-form-item>
          <el-form-item :label="$t('Workflow.submitTime')">
            {{ Mobily.createDate }}
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="SchedulejobDialog.title==='detail'" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="SchedulejobDialog.title==='detail'" />
          </el-form-item>
        </el-form>

        <span slot="footer" class="dialog-footer">
          <el-button v-if="SchedulejobDialog.title === 'detail'" size="small" @click="SchedulejobDialog.show = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="SchedulejobDialog.title === 'edit'&& ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="SchedulejobDialog.title === 'edit'&& ListStatus === '0'" size="small" type="primary" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!-- 权限管理 -->
      <el-dialog
        :title="permissionManagementDialog.isUpdate ? $t('lbs.common.modify') : $t('lbs.common.view')"
        :visible.sync="permissionManagementDialog.dialogShow"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="permissionManagementForm"
          :model="permissionManagementFormData"
          :rules="permissionManagementRules"
          label-width="150px"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="permissionManagementFormData.type" disabled />
          </el-form-item>

          <!-- 上级权限 -->
          <el-form-item :label="$t('permission.parentId')" prop="parentId">
            <el-cascader
              v-model="permissionManagementFormData.parentId"
              :options="permissionOptions"
              :props="{ checkStrictly: true, value: 'id', label: 'permissionName', emitPath: false }"
              :placeholder="$t('permission.parentIdPlaceholder')"
              class="select-item"
              :show-all-levels="false"
              clearable
              disabled
            />
          </el-form-item>

          <!-- 权限名称 -->
          <el-form-item :label="$t('permission.permissionName')" prop="permissionName">
            <el-input v-model="permissionManagementFormData.permissionName" :placeholder="$t('permission.permissionNamePlaceholder')" maxlength="64" clearable disabled />
          </el-form-item>

          <!-- 权限标识 -->
          <el-form-item :label="$t('permission.accredit')" prop="accredit">
            <el-input v-model="permissionManagementFormData.accredit" :placeholder="$t('permission.accreditPlaceholder')" maxlength="128" clearable disabled />
          </el-form-item>

          <!-- 排序编号 -->
          <el-form-item :label="$t('permission.sortNumber')" prop="sortNumber">
            <el-input-number
              v-model="permissionManagementFormData.sortNumber"
              :placeholder="$t('permission.sortNumberPlaceholder')"
              :min="1"
              :max="99"
              :step="1"
              step-strictly
              class="input-number-item"
              disabled
            />
          </el-form-item>

          <!-- 是否支持限额 -->
          <el-form-item :label="$t('permission.isLimit')" prop="isLimit">
            <el-select v-model="permissionManagementFormData.isLimit" :placeholder="$t('permission.isLimitPlaceholder')" class="select-item" disabled>
              <el-option :label="$t('lbs.common.yes')" :value="1" />
              <el-option :label="$t('lbs.common.no')" :value="0" />
            </el-select>
          </el-form-item>

          <!-- 是否需要审批 -->
          <el-form-item :label="$t('permission.isApprove')" prop="isApprove">
            <el-select v-model="permissionManagementFormData.isApprove" :placeholder="$t('permission.isApprovePlaceholder')" class="select-item" disabled>
              <el-option :label="$t('lbs.common.yes')" :value="1" />
              <el-option :label="$t('lbs.common.no')" :value="0" />
            </el-select>
          </el-form-item>

          <!-- API URL -->
          <el-form-item :label="$t('permission.url')" prop="url">
            <el-input v-model="permissionManagementFormData.url" :placeholder="$t('permission.urlPlaceholder')" maxlength="255" disabled />
          </el-form-item>

          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            <el-input v-model="Mobily.applicant" />
          </el-form-item>
          <!--提交时间-->
          <el-form-item :label="$t('Workflow.submitTime')">
            <el-input v-model="Mobily.createDate" />
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="!permissionManagementDialog.isUpdate" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="!permissionManagementDialog.isUpdate" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="!permissionManagementDialog.isUpdate" size="small" @click="permissionManagementDialog.dialogShow = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="permissionManagementDialog.isUpdate && ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="permissionManagementDialog.isUpdate&& ListStatus === '0'" size="small" type="primary" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!-- 交易限额配置-->
      <el-dialog
        :title="transactionLimitConfigDialog.isUpdate ? $t('lbs.common.modify') : $t('lbs.common.view')"
        :visible.sync="transactionLimitConfigDialog.dialogShow"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="permissionManagementForm"
          :model="transactionLimitConfigFormData"
          :rules="transactionLimitConfigRules"
          label-width="150px"
          label-position="left"
          size="small"
        >
          <!--操作类型-->
          <el-form-item :label="$t('Workflow.OperationType')">
            <el-input v-model="transactionLimitConfigFormData.type" disabled />
          </el-form-item>
          <!-- 角色 -->
          <el-form-item :label="$t('permission.role')" prop="roleId">
            <el-select v-model="transactionLimitConfigFormData.roleId" clearable :placeholder="$t('permission.rolePlaceholder')" class="select-item" disabled>
              <el-option v-for="(item, index) in roleOptions" :key="index" :label="item.role" :value="item.id" />
            </el-select>
          </el-form-item>

          <!-- 权限功能 -->
          <el-form-item :label="$t('permission.function')" prop="funPermissionId">
            <el-select v-model="transactionLimitConfigFormData.funPermissionId" clearable :placeholder="$t('permission.functionPlaceholder')" class="select-item" disabled>
              <el-option v-for="(item, index) in permissionOptions" :key="index" :label="item.permissionName" :value="item.id" />
            </el-select>
          </el-form-item>

          <!-- 额度 -->
          <el-form-item :label="$t('permission.limit')" prop="limit">
            <el-input-number
              v-model="transactionLimitConfigFormData.limit"
              clearable
              :controls="false"
              :min="0"
              :step="1"
              step-strictly
              :placeholder="$t('permission.limitPlaceholder')"
              class="select-item"
              disabled
            />
          </el-form-item>

          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            <el-input v-model="Mobily.applicant" />
          </el-form-item>
          <!--提交时间-->
          <el-form-item :label="$t('Workflow.submitTime')">
            <el-input v-model="Mobily.createDate" />
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="!transactionLimitConfigDialog.isUpdate" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="!transactionLimitConfigDialog.isUpdate" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="!transactionLimitConfigDialog.isUpdate" size="small" @click="transactionLimitConfigDialog.dialogShow = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="transactionLimitConfigDialog.isUpdate && ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="transactionLimitConfigDialog.isUpdate && ListStatus === '0'" size="small" type="primary" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
      <!-- 分配权限 -->
      <el-dialog
        :title="assignPermissionDialog.title === 'edit' ? $t('Operate.permissionAssignApprove') : $t('Operate.viewApproveDetails')"
        :visible.sync="assignPermissionDialog.dialogShow"
        width="40%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          ref="roleForm"
          :model="assignPermissionFormData"
          size="small"
          :rules="assignPermissionRules"
          label-width="100px"
          label-position="left"
          :disabled="!assignPermissionDialog.isUpdate"
        >
          <el-form-item :label="$t('permission.role')" prop="roleId">
            <el-select v-model="assignPermissionFormData.roleId" style="width: 100%" disabled>
              <el-option v-for="(item, index) in roleOptions" :key="index" :label="item.role" :value="item.id" />
            </el-select>
          </el-form-item>
          <!-- 权限 -->
          <el-form-item :label="$t('permission.permission')" prop="permissionIds">
            <el-tree
              ref="permissionTree"
              :data="permissionList"
              show-checkbox
              default-expand-all
              node-key="id"
              highlight-current
              :props="defaultProps"
              disabled
              @check="permissionTreeCheck('roleForm')"
            />
          </el-form-item>

          <!--申请人-->
          <el-form-item :label="$t('Workflow.applicant')">
            <el-input v-model="Mobily.applicant" disabled />
          </el-form-item>
          <!--提交时间-->
          <el-form-item :label="$t('Workflow.submitTime')">
            <el-input v-model="Mobily.createDate" disabled />
          </el-form-item>
          <!--审批意见-->
          <el-form-item v-if="ListStatus !== '0'" :label="$t('Operate.ApprovalComment')">
            <el-select v-model="ListStatus" :disabled="!assignPermissionDialog.isUpdate" style="width: 100%">
              <el-option :label="$t('lbs.common.pending')" value="0" />
              <el-option :label="$t('lbs.common.agree')" value="1" />
              <el-option :label="$t('lbs.common.reject')" value="2" />
              <el-option :label="$t('lbs.common.cancel')" value="-1" />
            </el-select>
          </el-form-item>
          <!--备注-->
          <el-form-item prop="remark">
            <template #label>
              <span style="color: red;">*</span>
              {{ $t('Workflow.remarks') }}
            </template>
            <el-input v-model="Mobily.remark" :disabled="!assignPermissionDialog.isUpdate" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button v-if="!assignPermissionDialog.isUpdate" size="small" @click="assignPermissionDialog.dialogShow = false">{{ $t('lbs.common.close') }}</el-button>
          <!--拒绝 {{$t('Operate.Refuse')}} {{$t('Operate.Submit')}}-->
          <el-button v-if="assignPermissionDialog.isUpdate && ListStatus === '0'" size="small" type="danger" @click="refuse()">{{ $t('Operate.Refuse') }}</el-button>
          <!--提交-->
          <el-button v-if="assignPermissionDialog.isUpdate && ListStatus === '0'" size="small" type="primary" @click="CancelUserSure">{{ $t('Operate.Submit') }}</el-button>
        </span>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import moment from 'moment'
import axios from 'axios'

export default {
  props: [],
  data() {
    return {
      moment,
      name: '',
      dialogCaptureDemographic: false,
      tableData: [],
      options: [],
      loading: false,
      token: null,
      currentPage: 1,
      pagesize: 10,
      totalPages: 0,
      tableDataList: [],
      // 弹框
      dialogVisiblePlus: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 员工权限
      jurisdiction: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 参数配置
      Parameterconfiguration: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 定存利率配置
      InterestDialog: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 外汇利率配置
      CurrencyDialog: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 节假日配置
      HolidayDialog: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 个人贷款金额配置配置
      LoanDialog: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 支付收款人信息配置
      PaymentDialog: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 定时跑批表配置
      SchedulejobDialog: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      // 角色额度弹框
      RoleQuota: {
        show: false, // 是否显示
        title: 'edit' // 默认弹框类型-编辑
      },
      editform: {}, // 角色弹框里面的表单
      fromList: {},
      ruleFormUser: {}, // 用户
      ruleFormList: {
        start: 0,
        end: 10000,
        id: '',
        roles: '', // 操作对象
        name: '', // 操作描述
        url: '', // 权限接口
        checkLimitAmtField: '', // 接口额度审核字段
        remark: ''
      },
      // 第一个下拉内容
      brandOptions: [],
      // 第二个下拉内容
      typeOptions: [],

      MobilyJson: {

      },
      Mobily: {
        applicant: 'linjun',
        applyNo: 'AP1635333976507',
        approvalType: 'User_add',
        approver: null,
        comment: null,
        createDate: '2021-10-27T11:26:17.000+0000',
        end: 0,
        id: 2,
        json: '',
        permId: 4,
        remark: null,
        start: 0,
        status: '',
        type: '添加用户',
        updateDate: '2021-10-27T11:26:17.000+0000'
      },
      departmenSelect: [], // 部门下拉
      openEditList: {}, // 打开弹框，每条数据的所有值
      ListStatus: '',
      approve: '', // 打开查看按钮
      approveJson: '', // 查看按钮里面的Json具体数据
      permissionManagementDialog: {
        dialogShow: false,
        isUpdate: true
      },
      permissionManagementFormData: {
        type: '',
        parentId: '',
        permissionName: '',
        accredit: '',
        sortNumber: '',
        isLimit: '',
        isApprove: ''
      },
      permissionManagementRules: {
        parentId: [
          { required: true, trigger: 'change', message: this.$t('permission.parentIdIsRequired') }
        ],
        permissionName: [
          { required: true, trigger: 'blur', message: this.$t('permission.permissionNameIsRequired') }
        ],
        accredit: [
          { required: true, trigger: 'blur', message: this.$t('permission.accreditIsRequired') }
        ],
        sortNumber: [
          { required: true, trigger: 'blur', message: this.$t('permission.sortNumberIsRequired') }
        ],
        isApprove: [
          { required: true, trigger: 'blur' }
        ],
        isLimit: [
          { required: true, trigger: 'blur' }
        ]
      },
      permissionOptions: [],
      transactionLimitConfigDialog: {
        dialogShow: false,
        isUpdate: true
      },
      assignPermissionDialog: {
        dialogShow: false,
        isUpdate: true
      },
      assignPermissionFormData: {
        roleId: '',
        permissionIds: []
      },
      defaultProps: {
        children: 'children',
        label: 'permissionName'
      },
      permissionList: [],
      assignPermissionRules: {
        roleId: [
          { required: true, trigger: 'blur', message: this.$t('permission.roleIsRequired') }
        ],
        permissionIds: [
          { required: true, trigger: 'change', message: this.$t('permission.permissionIsRequired') }
        ]
      },
      transactionLimitConfigFormData: {
        type: '',
        roleId: '',
        funPermissionId: '',
        limit: 0
      },
      transactionLimitConfigRules: {
        roleId: [
          { required: true, trigger: 'change', message: this.$t('permission.roleIsRequired') }
        ],
        funPermissionId: [
          { required: true, trigger: 'blur', message: this.$t('permission.functionIsRequired') }
        ],
        limit: [
          { required: true, trigger: 'blur', message: this.$t('permission.limitIsRequired') },
          { type: 'number', min: 1, message: this.$t('permission.limitGTZero') }
        ]
      },
      roleOptions: []
    }
  },
  watch: {},
  created() {
    this.token = window.sessionStorage.getItem('token')
    var needReload = window.sessionStorage.getItem('needReload')
    if (needReload === '1') {
      window.sessionStorage.setItem('needReload', 0)
      window.location.reload()
    }
  },
  mounted() {
    this.getOpearFun()
  },
  beforeDestroy() {
  },
  methods: {
    // 提交
    CancelUserSure() {
      if (this.Mobily.remark === '' || this.Mobily.remark === null || this.Mobily.remark === undefined) {
        this.$message({
          message: this.$t('Operate.PleaseRemark'),
          type: 'warning'
        })
        return
      }
      const id = sessionStorage.getItem('handlle')
      const str = {
        start: 0,
        end: 10000,
        remark: this.Mobily.remark,
        id: +id
      }
      this.loading = true
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/approval/agree`, str, { headers: { token: this.token }}).then(res => {
        this.loading = false
        if (res.data.code === '200') {
          this.getOpearFun()
          this.dialogVisiblePlus.show = false
          this.jurisdiction.show = false // 权限
          this.RoleQuota.show = false
          this.Parameterconfiguration.show = false
          this.InterestDialog.show = false
          this.CurrencyDialog.show = false
          this.HolidayDialog.show = false
          this.LoanDialog.show = false
          this.PaymentDialog.show = false
          this.SchedulejobDialog.show = false
          this.permissionManagementDialog.dialogShow = false
          this.transactionLimitConfigDialog.dialogShow = false
          window.location.reload(true)
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },

    // 拒绝
    refuse() {
      if (this.Mobily.remark === '' || this.Mobily.remark === null || this.Mobily.remark === undefined) {
        this.$message({
          message: this.$t('Operate.PleaseRemark'),
          type: 'warning'
        })
        return
      }

      const str = {
        start: 0,
        end: 10000,
        remark: this.Mobily.remark,
        id: sessionStorage.getItem('handlle')
      }
      this.loading = true
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/approval/reject`, str, { headers: { token: this.token }}).then(res => {
        this.loading = false
        if (res.data.code === '200') {
          this.getOpearFun()
          this.dialogVisiblePlus.show = false
          this.jurisdiction.show = false // 权限
          this.RoleQuota.show = false
          this.Parameterconfiguration.show = false
          this.InterestDialog.show = false
          this.CurrencyDialog.show = false
          this.HolidayDialog.show = false
          this.LoanDialog.show = false
          this.PaymentDialog.show = false
          this.SchedulejobDialog.show = false
          this.permissionManagementDialog.dialogShow = false
          this.assignPermissionDialog.dialogShow = false
          this.transactionLimitConfigDialog.dialogShow = false
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 列表
    getOpearFun() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/sysadmin-process/sysadmin/approval/findMany`, { start: 0, end: 10000 }, { headers: { token: _this.token }}).then(res => {
        // 点击登录提交的时候，给本地储存资料
        _this.loading = false
        if (res.data.code === '200') {
          this.tableDataList = res.data.data.data
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 每点一次弹框就会调一下这个接口，然后弹框的数据都在json里面
    MobilyFun(row) {
      this.openEditList = row
      this.loading = true
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/approval/findOne`, { start: 0, end: 10000, id: row.id }, { headers: { token: this.token }}).then(res => {
        this.loading = false
        if (res.data.code === '200') {
          this.Mobily = res.data.data
          this.Mobily.createDate = moment(this.Mobily.createDate).format('YYYY-MM-DD HH:mm:ss')
          this.Mobily.updateDate = moment(this.Mobily.updateDate).format('YYYY-MM-DD HH:mm:ss')
          this.MobilyJson = JSON.parse(this.Mobily.json)
          if (this.MobilyJson.departmentId) {
            this.MobilyJson.department = this.MobilyJson.departmentId
          }
          if (this.Mobily.approvalType.includes('system:staffer')) {
            if (this.MobilyJson.department === '1' || this.MobilyJson.department === 1) {
              this.MobilyJson.department = 'Operations'
            } else if (this.MobilyJson.department === '2' || this.MobilyJson.department === 2) {
              this.MobilyJson.department = 'Credit Card'
            } else if (this.MobilyJson.department === '3' || this.MobilyJson.department === 3) {
              this.MobilyJson.department = 'Retail Branch'
            }
            this.getRoleInfoById(this.MobilyJson.roleId)
          }
          if (this.Mobily.approvalType.includes('system:parameter-configuration:term-deposit-interest-rate')) {
            this.FixedDepositFun()
          }

          if (this.MobilyJson.day) {
            this.MobilyJson.day = moment(this.MobilyJson.day).format('YYYY-MM-DD')
          }

          if (this.MobilyJson.depositrange) {
            if (this.MobilyJson.depositrange === '1' || this.MobilyJson.depositrange === 1) {
              this.MobilyJson.depositrange = '10000-99999'
            } else if (this.MobilyJson.depositrange === '2' || this.MobilyJson.depositrange === 2) {
              this.MobilyJson.depositrange = '100000-499999'
            } else if (this.MobilyJson.depositrange === '3' || this.MobilyJson.depositrange === 3) {
              this.MobilyJson.depositrange = '500000-999999'
            } else if (this.MobilyJson.depositrange === '4' || this.MobilyJson.depositrange === 4) {
              this.MobilyJson.depositrange = '>1000000'
            }
          }

          if (this.MobilyJson.departmentId) {
            if (this.MobilyJson.departmentId === '1' || this.MobilyJson.departmentId === 1) {
              this.MobilyJson.department = 'Operations'
            } else if (this.MobilyJson.departmentId === '2' || this.MobilyJson.departmentId === 2) {
              this.MobilyJson.department = 'Credit Card'
            } else if (this.MobilyJson.departmentId === '3' || this.MobilyJson.departmentId === 3) {
              this.MobilyJson.department = 'Retail Branch'
            }
          }
          if (this.MobilyJson.params) {
            this.MobilyJson.params = JSON.parse(this.MobilyJson.params)
          }

          if (row.approvalType.includes('system:permission') && row.approvalType !== ('system:permission:assign')) {
            this.permissionManagementFormData = { type: this.Mobily.type, ...this.MobilyJson }
          }

          if (row.approvalType === ('system:permission:assign')) {
            this.assignPermissionFormData = { type: this.Mobily.type, ...this.MobilyJson }
            this.getPermissionList()
          }

          if (row.approvalType.includes('system:limit')) {
            this.transactionLimitConfigFormData = { type: this.Mobily.type, ...this.MobilyJson }
          }
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    FixedDepositFun() {
      axios.post(`${this.LBSGateway}/interest-service/amount/range/findMany`, { start: 0, end: 1000 }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.options = JSON.parse(res.data.data)
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    getRoleInfoById(id) {
      axios.post(`${this.LBSGateway}/sysadmin-service/role/findOne`, { id: id }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          const roleInfo = JSON.parse(res.data.data)
          this.MobilyJson.roleId = roleInfo.role
          this.dialogVisiblePlus.show = true
        } else {
          this.$message({
            showClose: true,
            message: res.data.desc,
            type: 'error'
          })
        }
      })
    },
    // 点击第一个下拉
    changeSelect(val) {
      const item = this.brandOptions.filter(v => v.id === val)[0]
      this.MobilyJson.role = ''
      this.getTwoSel(item)
    },
    // 部门下拉框的内容 //获取第一个下拉数据 department/findMany
    getOneSel() {
      const _this = this
      _this.loading = true
      const requestData = {
        departmentId: '',
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/department/findMany`, requestData, { headers: { token: _this.token }}).then(res => {
        _this.loading = false
        if (res.data.code === '200') {
          this.brandOptions = JSON.parse(res.data.data)
          // this.MobilyJson.department = datas[0].id

          // 获取二级下拉-默认获取第一个
          // this.getTwoSel(datas[0])
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 获取第二个下拉数据
    getTwoSel(request) {
      sessionStorage.setItem('idSelect', request.id)
      const _this = this
      const requestData = {
        departmentId: request.id,
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/role/findMany`, requestData, { headers: { token: _this.token }}).then(res => {
        if (res.data.code === '200') {
          const datas = res.data.data
          if (datas) {
            this.typeOptions = JSON.parse(res.data.data)
          } else {
            this.typeOptions = []
            this.form.phoneType = ''
          }
        } else {
          this.$message({
            showClose: true,
            message: res.data.desc,
            type: 'error'
          })
        }
      })
    },

    // getDelFun 待处理 处理
    async gethandleFun(row) {
      this.getOneSel() // //部门下拉框的内容 //获取第一个下拉数据 department/findMany

      sessionStorage.setItem('approvalType', row.approvalType)
      sessionStorage.setItem('handlle', row.id)
      this.ListStatus = row.status
      if (row.approvalType.includes('system:staffer')) { // 用户修改
        this.dialogVisiblePlus.title = 'edit' // 弹框类型
        this.dialogVisiblePlus.show = true
        this.MobilyFun(row)// 每点一次弹框就会调一下这个接口，然后弹框的数据都在json里面
        return
      }

      if (row.approvalType.includes('system:role')) { // 角色
        this.RoleQuota.title = 'edit'
        this.RoleQuota.show = true
        this.MobilyFun(row)
        return
      }
      // Sysconfig_update
      if (row.approvalType.includes('system:parameter-configuration:system-parameter')) { // 参数配置
        this.Parameterconfiguration.title = 'edit'
        this.Parameterconfiguration.show = true
        this.MobilyFun(row)
        return
      }
      // Interest_update
      if (row.approvalType.includes('system:parameter-configuration:term-deposit-interest-rate')) { // 参数配置
        this.InterestDialog.show = true
        this.InterestDialog.title = 'edit'
        this.MobilyFun(row) // 列表回显
        return
      }
      // Currency_update
      if (row.approvalType.includes('system:parameter-configuration:currency')) { // 参数配置
        this.CurrencyDialog.show = true
        this.CurrencyDialog.title = 'edit'
        this.MobilyFun(row) // 列表回显
        return
      }
      // Holiday_update
      if (row.approvalType.includes('system:parameter-configuration:holiday')) { // 参数配置
        this.HolidayDialog.show = true
        this.HolidayDialog.title = 'edit'
        this.MobilyFun(row) // 列表回显
        return
      }
      // Loan_update
      if (row.approvalType.includes('system:parameter-configuration:personal-loan-interest-rate')) { // 参数配置
        this.LoanDialog.show = true
        this.LoanDialog.title = 'edit'
        this.MobilyFun(row) // 列表回显
        return
      }
      // Payment_update
      if (row.approvalType.includes('system:parameter-configuration:payee')) { // 参数配置
        this.PaymentDialog.show = true
        this.PaymentDialog.title = 'edit'
        this.MobilyFun(row) // 列表回显
        return
      }
      // Schedulejob_update
      if (row.approvalType.includes('system:parameter-configuration:schedule-job')) { // 参数配置
        this.SchedulejobDialog.show = true
        this.SchedulejobDialog.title = 'edit'
        this.MobilyFun(row) // 列表回显
        return
      }

      // edit permission management
      if (row.approvalType.includes('system:permission') && row.approvalType !== 'system:permission:assign') {
        // 加载权限列表
        this.permissionOptions = await this.getPermissionOptions('permission')
        this.MobilyFun(row) // 列表回显
        this.permissionManagementDialog.dialogShow = true
        this.permissionManagementDialog.isUpdate = true
        return
      }

      if (row.approvalType === 'system:permission:assign') {
        // 加载权限列表
        this.permissionOptions = await this.getPermissionOptions('permission')
        this.getRoleList()
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        this.assignPermissionDialog.dialogShow = true
        this.assignPermissionDialog.isUpdate = true
        return
      }

      // edit transaction limit config
      if (row.approvalType.includes('system:limit')) {
        // 加载额度列表
        this.permissionOptions = await this.getPermissionOptions('limit')
        this.getRoleList()
        this.MobilyFun(row) // 列表回显
        this.transactionLimitConfigDialog.dialogShow = true
        this.transactionLimitConfigDialog.isUpdate = true
        return
      }
    },
    // 查看处理
    async getLookerFun(row) {
      sessionStorage.setItem('approvalType', row.approvalType)
      sessionStorage.setItem('handlle', row.id)
      this.ListStatus = row.status
      if (row.approvalType.includes('system:staffer')) { // 用户修改
        this.dialogVisiblePlus.title = 'detail' // 弹框类型
        this.dialogVisiblePlus.show = true
        this.MobilyFun(row)// 每点一次弹框就会调一下这个接口，然后弹框的数据都在json里面
        this.getOneSel() // //部门下拉框的内容 //获取第一个下拉数据 department/findMany
        return
      }
      if (row.approvalType.includes('system:role')) { // 角色
        this.RoleQuota.title = 'detail'
        this.RoleQuota.show = true
        this.MobilyFun(row)
        this.getOneSel()
        return
      }
      // Sysconfig_update  Sysconfig_update
      if (row.approvalType.includes('system:parameter-configuration:system-parameter')) { // 参数配置
        this.Parameterconfiguration.title = 'detail'
        this.Parameterconfiguration.show = true
        this.MobilyFun(row)
        this.getOneSel()
        return
      }
      // Interest_update
      if (row.approvalType.includes('system:parameter-configuration:term-deposit-interest-rate')) { // 参数配置
        this.InterestDialog.show = true
        this.InterestDialog.title = 'detail'
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        return
      }
      // Currency_update
      if (row.approvalType.includes('system:parameter-configuration:currency')) { // 参数配置
        this.CurrencyDialog.show = true
        this.CurrencyDialog.title = 'detail'
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        return
      }
      // Holiday_update
      if (row.approvalType.includes('system:parameter-configuration:holiday')) { // 参数配置
        this.HolidayDialog.show = true
        this.HolidayDialog.title = 'detail'
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        return
      }
      // Loan_update
      if (row.approvalType.includes('system:parameter-configuration:personal-loan-interest-rate')) { // 参数配置
        this.LoanDialog.show = true
        this.LoanDialog.title = 'detail'
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        return
      }
      // Payment_update
      if (row.approvalType.includes('system:parameter-configuration:payee')) { // 参数配置
        this.PaymentDialog.show = true
        this.PaymentDialog.title = 'detail'
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        return
      }
      // Schedulejob_update
      if (row.approvalType.includes('system:parameter-configuration:schedule-job')) { // 参数配置
        this.SchedulejobDialog.show = true
        this.SchedulejobDialog.title = 'detail'
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        return
      }

      // edit permission management
      if (row.approvalType.includes('system:permission') && row.approvalType !== 'system:permission:assign') {
        // 加载权限列表
        this.permissionOptions = await this.getPermissionOptions('permission')
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        this.permissionManagementDialog.dialogShow = true
        this.permissionManagementDialog.isUpdate = false
        return
      }

      // edit permission management
      if (row.approvalType === 'system:permission:assign') {
        // 加载权限列表
        this.permissionOptions = await this.getPermissionOptions('permission')
        this.getRoleList()
        this.MobilyFun(row) // 列表回显
        this.assignPermissionDialog.dialogShow = true
        this.assignPermissionDialog.isUpdate = false
        return
      }

      // edit transaction limit config
      if (row.approvalType.includes('system:limit')) {
        // 加载额度列表
        this.permissionOptions = await this.getPermissionOptions('limit')
        this.getRoleList()
        this.MobilyFun(row) // 列表回显
        this.getOneSel() // 下拉
        this.transactionLimitConfigDialog.dialogShow = true
        this.transactionLimitConfigDialog.isUpdate = false
        return
      }
    },
    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    },
    async getPermissionOptions(type) {
      const isLimit = type === 'limit'
      const requestData = isLimit ? { isLimit: 1, type: 'list' } : {}
      const { data } = await axios.get(this.LBSGateway + '/sysadmin-process/sysadmin/fun-permission', { params: requestData, headers: { token: this.token }})
      const { data: permissionOptions } = data
      if (isLimit) {
        return permissionOptions
      }
      function clearEmptyChildren(nodes) {
        nodes.forEach(node => {
          if (!node.children.length) {
            delete node.children
          } else {
            clearEmptyChildren(node.children)
          }
        })
      }

      clearEmptyChildren(permissionOptions)

      return [
        {
          accredit: 'root',
          children: permissionOptions,
          id: 0,
          parentId: 0,
          permissionName: 'Root',
          sortNumber: 1,
          label: 'Root'
        }
      ]
    },
    getRoleList() {
      const requestData = {
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/sysadmin-service/role/findMany`, requestData, { headers: { token: this.token }})
        .then(res => {
          if (res.data.code === '200') {
            const datas = res.data.data
            if (datas) {
              this.roleOptions = JSON.parse(res.data.data)
            } else {
              this.roleOptions = []
            }
          } else {
            this.$message.error(res.data.msg)
          }
        })
    },
    permissionTreeCheck(formName) {
      this.assignPermissionFormData.permissionIds = this.$refs.permissionTree.getCheckedKeys().concat(this.$refs.permissionTree.getHalfCheckedKeys())
      this.$refs[formName].validateField('permissionIds')
    },
    handlerRoleIdChange() {
      this.$refs.permissionTree.setCheckedKeys([])
      if (this.assignPermissionFormData.permissionIds && this.assignPermissionFormData.permissionIds.length) {
        this.assignPermissionFormData.permissionIds.forEach((item) => {
          this.$nextTick(() => {
            const node = this.$refs.permissionTree.getNode(item)
            if (node && node.isLeaf) {
              this.$refs.permissionTree.setChecked(node, true)
            }
          })
        })
      }
    },
    getPermissionList() {
      const requestData = {
        permissionName: '',
        isApprove: null,
        isLimit: null
      }

      axios.get(this.LBSGateway + '/sysadmin-process/sysadmin/fun-permission', { params: requestData, headers: { token: this.token }})
        .then((res) => {
          const { code, msg, data: permissionList } = res.data
          if (code === '200') {
            this.permissionList = permissionList
            this.handlerRoleIdChange()
          } else {
            this.permissionList = []
            this.$message.error(msg)
          }
        })
    }
  }

}
</script>

<style lang='scss' scoped>
  .workflow {
    min-height: 840px;

    .list-group1 {
      display: inline-block;
      width: 100%;
      height: 66px;
    }

    .list-group2 {
      display: inline-block;
      width: 100%;
      height: 61px;
    }

    .w-group-item {
      width: 146px;
      margin: 0 5px 5px 0;
      text-align: center;
      cursor: move;
    }

    .select-item, .input-number-item {
      width: 100%;
      line-height: 32px;
    }
  }
  .content{
    width: 100%;
    position: relative;
    .content01{
      position: absolute;
      left: 30px;
      top: 15px;
      width: 60px;
      height: 30px;
      font-size: 14px;
      border: 2px solid seagreen;
      text-align: center;
      line-height: 26px;
      border-radius: 5px;
      cursor: pointer;
      color: white;
      background:seagreen ;
    }
  }
  .content_one{
    width: 100%;
    text-align: center;
  }

  .contenthex{
    width: 100%;
    .box{
      border: 3px solid white;
      background: #ffffff;
      width: 60%;
      margin: 0px auto;
      padding-top: 30px;
    }
  }
  .content_onehex{
    width: 100%;
    text-align: center;
    font-size: 20px;
  }
  .content_1{
    width: 80%;
    padding: 15px;
    margin: 10px auto;
    display: flex;
    justify-content: center;
    .Yuangong{
      line-height: 38px;
      font-size: 16px;
      margin-right: 20px;
    }
  }
  .content_2{
    width: 80%;
    display: flex;
    justify-content: center;
    margin-left: 15px;
    margin-top: 30px;
    .Yuangong{
      line-height: 38px;
      font-size: 13px;
    }
  }
  .box_two{
    position: relative;
    height: 60px;
    line-height: 60px;
    .box_two01{
      position: absolute;
      top:10px;
      left: -43px;
    }
  }
  .content01{
    width: 60px;
    height: 30px;
    font-size: 14px;
    border: 2px solid seagreen;
    text-align: center;
    line-height: 26px;
    border-radius: 5px;
    cursor: pointer;
    color: white;
    background:#109eae ;
    margin-left: 30px;
  }
  .departmentC{
    margin-top: 40px;
    margin-left: 160px;
    h4{
      font-weight: bold;
      color: #109eae ;
    }
  }
  .modalTitle{
    width: 100%;
    height: 43px;
    padding-left: 10px;
    font-weight: 600;

    h4{
      line-height: 43px;
      font-size: 15px;
    }
  }
  .shenpi{
    font-weight: bold;
    font-size: 18px;
  }
</style>
