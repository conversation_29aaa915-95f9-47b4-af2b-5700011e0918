<template>
  <div>
    <el-card v-if="!dialogCaptureDemographic" v-loading.fullscreen.lock="loading" shadow="never" class="workflow">
      <div slot="header" class="clearfix">
        <!--系统参数配置-->
        <span>{{ $t("Workflow.parameter") }}</span>
      </div>

      <el-form ref="form" :model="form" inline>
        <!--源数据库-->
        <el-form-item :label="$t('Workflow.SourcedataBase')" size="small">
          <el-select v-model="form.region" style="width: 300px" @change="selectNode(form.region)">
            <!--系统参数表-->
            <el-option :label="$t('Operate.Parameter')" value="1" />
            <!--定存利率表-->
            <el-option :label="$t('Operate.FixedTable')" value="2" />
            <!--外汇利率表-->
            <el-option :label="$t('Operate.ForeignTable')" value="3" />
            <!--节假日配置表-->
            <el-option :label="$t('Operate.HolidayTable')" value="4" />
            <!--个人贷款金额配置表-->
            <el-option :label="$t('Operate.PersonalTable')" value="5" />
            <!--支付收款人信息表-->
            <el-option :label="$t('Operate.PayeeTable')" value="6" />
            <!--定时跑批表-->
            <el-option :label="$t('Operate.RegularTable')" value="7" />
          </el-select>
        </el-form-item>
      </el-form>

      <div class="data-action-button-group">
        <div>
          <el-button v-if="system_one" size="small" icon="el-icon-plus" type="primary" plain @click="goclickplusOne()">{{ $t('permission.create') }}</el-button>
          <el-button v-if="HolidayTable" size="small" icon="el-icon-plus" type="primary" plain @click="goHolidayplus()">{{ $t('permission.create') }}</el-button>
          <el-button v-if="Fixeddeposit" size="small" icon="el-icon-plus" type="primary" plain @click="goFixedplus()">{{ $t('permission.create') }}</el-button>
          <el-button v-if="Foreigninterest_show" size="small" icon="el-icon-plus" type="primary" plain @click="$refs.Foreignexchange_ref.goExchangeplus()">{{ $t('permission.create') }}</el-button>
          <el-button v-if="Personalloanamount_show" size="small" icon="el-icon-plus" type="primary" plain @click="$refs.Personalloan_ref.goPersonalplus()">{{ $t('permission.create') }}</el-button>
          <el-button v-if="Paycollection_show" size="small" icon="el-icon-plus" type="primary" plain @click="$refs.Paycollection_ref.goPayplus()">{{ $t('permission.create') }}</el-button>
          <el-button v-if="Regular_show" size="small" icon="el-icon-plus" type="primary" plain @click="$refs.Regular_ref.Regularplus()">{{ $t('permission.create') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="selectNode(form.region)" />
      </div>

      <el-card shadow="never">
        <!--系统参数配置表-->
        <div v-if="system_one">
          <el-table
            :data="tableDataList_one.slice((currentPage-1)*pagesize,currentPage*pagesize)"
            stripe
            style="width: 100%"
          >
            <el-table-column
              type="index"
              :label="$t('lbs.common.number')"
              align="center"
            />

            <el-table-column
              prop="item"
              :label="$t('lbs.common.parameter')"
              align="center"
            />
            <!--值-->
            <el-table-column
              prop="value"
              :label="$t('Workflow.value')"
              align="center"
            />
            <!--备注 remarks-->
            <el-table-column
              prop="remark"
              :label="$t('Workflow.remarks')"
              align="center"
            />
            <!--序列-->
            <el-table-column
              prop="sort"
              :label="$t('Operate.sequence')"
              align="center"
            />
            <!--操作-->
            <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
              <template slot-scope="scope">
                <el-button
                  v-if="scope"
                  size="mini"
                  type="text"
                  @click="getEditFunOne(scope.row)"
                >
                  {{ $t("Workflow.Edit") }}
                </el-button>
                <el-button
                  v-if="scope"
                  size="mini"
                  type="text"
                  @click="getSysDelet(scope.row)"
                >
                  {{ $t("Workflow.Delete") }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="system_one" class="block">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 40]"
            :page-size="pagesize"
            :total="tableDataList_one.length"
            layout="total, prev, pager, next"
            style="margin-top: 20px; text-align: right;"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <!--系统参数弹框-->
        <el-dialog
          :title="dialogVisibleone.title === 'add' ? $t('Operate.Systemparameter') : $t('Operate.SystemEdit')"
          :visible.sync="dialogVisibleone.show"
          width="40%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <el-form ref="ruleFormList" :model="ruleFormUser" label-width="100px" label-position="left" size="small">
            <!--参数-->
            <el-form-item :label="$t('Workflow.parameterN')">
              <el-input v-model="ruleFormList.item" />
            </el-form-item>
            <!--值-->
            <el-form-item :label="$t('Workflow.value')">
              <el-input v-model="ruleFormList.value" />
            </el-form-item>
            <!--备注-->
            <el-form-item :label="$t('Workflow.remarks')">
              <el-input v-model="ruleFormList.remark" />
            </el-form-item>

            <el-form-item :label="$t('Operate.sequence')">
              <el-input v-model="ruleFormList.sort" />
            </el-form-item>

          </el-form>

          <span slot="footer" class="dialog-footer">
            <!--取消-->
            <el-button size="small" @click="dialogVisibleone.show = false">{{ $t('Workflow.Cancel') }}</el-button>
            <!--确定-->
            <!--系统参数确定编辑-->
            <el-button v-if="dialogVisibleone.title === 'edit'" type="primary" size="small" @click="sysParameter()">{{ $t('Operate.Okedit') }}</el-button>
            <!--系统参数确认添加-->
            <el-button v-if="dialogVisibleone.title === 'add'" type="primary" size="small" @click="sysAddParameter()">{{ $t('Operate.Determine') }}</el-button>
          </span>
        </el-dialog>
        <!--嵌套弹框start-->
        <el-dialog
          width="40%"
          :title="$t('Operate.applicationSuec')"
          :visible.sync="innerVisible"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          append-to-body
        >
          <p class="applicationSuccess">{{ $t('Operate.submitsuceess') }}</p>
          <!--取消-->
          <div style="width: 100%;text-align: right">
            <el-button @click="innerVisible = false">{{ $t('Operate.Continue') }}</el-button>
            <!--查看详情-->
            <el-button type="primary" @click="Individual">{{ $t('Operate.ViewDetails') }}</el-button>
          </div>
        </el-dialog>
        <!--嵌套弹框end-->

        <!--节假日参数配置表start-->
        <div v-if="HolidayTable">
          <el-table
            :data="tableDataList_four.slice((currentPage-1)*pagesize,currentPage*pagesize)"
            stripe
            style="width: 100%"
          >
            <!--序号-->
            <el-table-column
              type="index"
              :label="$t('lbs.common.number')"
              align="center"
            />
            <!--国家country-->
            <el-table-column
              prop="countrycode"
              :label="$t('lbs.common.country')"
              align="center"
            />
            <!--日期-->
            <el-table-column
              prop="day"
              :label="$t('Workflow.date')"
              align="center"
            />

            <!--节假日操作-->
            <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
              <template slot-scope="scope">
                <el-button
                  v-if="scope"
                  size="mini"
                  type="text"
                  @click="getHolidayEditFun(scope.row)"
                >
                  {{ $t("Workflow.Edit") }}
                </el-button>
                <el-button
                  v-if="scope"
                  size="mini"
                  type="text"
                  @click="getHolidayDelFun(scope.row)"
                >
                  {{ $t("Workflow.Delete") }}
                </el-button>

              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="HolidayTable" class="block">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 40]"
            :page-size="pagesize"
            :total="tableDataList_four.length"
            layout="total, prev, pager, next"
            style="margin-top: 20px; text-align: right;"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
        <!--节假日弹框start-->
        <el-dialog
          :title="dialogVisiblefour.title === 'add' ? $t('Operate.HolidayIncrease') : $t('Operate.HolidayEditL')"
          :visible.sync="dialogVisiblefour.show"
          width="40%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <el-form ref="ruleFormList" :model="holidayFormUser" label-width="100px" label-position="left" size="small">
            <!--国家-->
            <el-form-item :label="$t('Workflow.country')">
              <el-input v-model="holidayFormUser.countrycode" />
            </el-form-item>
            <!--日期-->
            <el-form-item :label="$t('Workflow.date')">
              <el-date-picker
                v-model="holidayFormUser.day"
                type="date"
                align="center"
                placeholder="选择日期"
                style="width: 100%;"
              />
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <!--取消-->
            <el-button size="small" @click="dialogVisiblefour.show = false">{{ $t('Workflow.Cancel') }}</el-button>
            <!--节假日增加确定-->
            <el-button v-if="dialogVisiblefour.title === 'add'" type="primary" size="small" @click="getHolidayAdd()">{{ $t('Operate.Determine') }}</el-button>
            <!--节假日编辑确定-->
            <el-button v-if="dialogVisiblefour.title === 'edit'" type="primary" size="small" @click="getHolidayEdit()">{{ $t('Operate.Okedit') }}</el-button>
          </span>
        </el-dialog>

        <!--定存利率表 Fixeddeposit-->
        <div v-if="Fixeddeposit">
          <el-table
            :data="tableDataList_two.slice((currentPage-1)*pagesize,currentPage*pagesize)"
            stripe
            style="width: 100%"
          >
            <!--序号-->
            <el-table-column
              type="index"
              :label="$t('lbs.common.number')"
              align="center"
            />

            <!--定存金额范围-->
            <el-table-column
              prop="depositrange"
              :label="$t('Workflow.FixedAmount')"
              align="center"
            />
            <!--存款期限-->
            <el-table-column
              prop="tdperiod"
              :label="$t('Workflow.Deposit')"
              align="center"
            />
            <!--定存利率-->
            <el-table-column
              prop="tdinterestrate"
              :label="$t('Workflow.depositrate')"
              align="center"
            />
            <!--定存利率操作-->
            <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
              <template slot-scope="scope">
                <el-button
                  v-if="scope"
                  size="mini"
                  type="text"
                  @click="getFixEditFun(scope.row)"
                >
                  {{ $t("Workflow.Edit") }}
                </el-button>
                <el-button
                  v-if="scope"
                  size="mini"
                  type="text"
                  @click="getFixDelFun(scope.row)"
                >
                  {{ $t("Workflow.Delete") }}
                </el-button>

              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="Fixeddeposit" class="block">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 40]"
            :page-size="pagesize"
            :total="tableDataList_two.length"
            layout="total, prev, pager, next"
            style="margin-top: 20px; text-align: right;"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
        <!--定存利率表弹框start-->
        <el-dialog
          :title="dialogVisibleTwo.title === 'add' ? $t('Operate.Increase') : $t('Operate.FixedDeposit')"
          :visible.sync="dialogVisibleTwo.show"
          width="40%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >
          <el-form ref="ruleFormList" :model="FixedFormUser" label-width="160px" label-position="left" size="small">
            <!--定存金额范围-->
            <el-form-item :label="$t('Workflow.FixedAmount')">
              <el-select v-model="FixedFormUser.depositrange" style="width: 100%" @change="changeSelect">
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.amountrangemin < 1000000 ? item.amountrangemin + '-' + item.amountrangemax : '>' + item.amountrangemin"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>

            <!--存款期限-->
            <el-form-item :label="$t('Workflow.Deposit')">
              <el-input v-model="FixedFormUser.tdperiod" />
            </el-form-item>
            <!--定存利率-->
            <el-form-item :label="$t('Workflow.depositrate')">
              <el-input v-model="FixedFormUser.tdinterestrate" />
            </el-form-item>

          </el-form>

          <span slot="footer" class="dialog-footer">
            <!--取消-->
            <el-button size="small" @click="dialogVisibleTwo.show = false">{{ $t('Workflow.Cancel') }}</el-button>
            <!--定存利率增加确定-->
            <el-button v-if="dialogVisibleTwo.title === 'add'" size="small" type="primary" @click="getFixDepositAdd()">{{ $t('Operate.Determine') }}</el-button>
            <!--定存利率编辑确定-->
            <el-button v-if="dialogVisibleTwo.title === 'edit'" size="small" type="primary" @click="getFixDepositEdit()">{{ $t('Operate.Okedit') }}</el-button>
          </span>
        </el-dialog>

        <!--外汇利率表-->
        <Foreignexchange v-if="Foreigninterest_show" ref="Foreignexchange_ref" />
        <!--个人贷款金额配置表-->
        <Personalloan v-if="Personalloanamount_show" ref="Personalloan_ref" />
        <!--支付收款人信息表-->
        <Payeeinformation v-if="Paycollection_show" ref="Paycollection_ref" />
        <!--定时跑批表-->
        <regular v-if="Regular_show" ref="Regular_ref" />

      </el-card>
    </el-card>
  </div>
</template>

<script>
import moment from 'moment'
import Foreignexchange from './Foreignexchange' // 外汇利率表
import Personalloan from './Personalloan' // 个人贷款金额配置表
import Payeeinformation from './Payeeinformation' // 支付收款人信息表
import regular from './regular'
import axios from 'axios' // regular 定时跑批表

export default {
  components: {
    Foreignexchange, // 外汇利率表
    Personalloan, // 个人贷款金额配置表
    Payeeinformation, // 支付收款人信息表
    regular // 定时跑批表
  },
  props: [],
  data() {
    return {
      role: '',
      options: [],
      innerVisible: false,
      token: null,
      name: '',
      dialogCaptureDemographic: false,
      loading: false,
      tableData: [],
      currentPage: 1,
      pagesize: 10,
      totalPages: 0,
      form: {
        phoneBrand: '',
        phoneType: '',
        region: ''
      },
      brandOptions: [],
      typeOptions: {},
      uploadTable: true, // 刷新列表
      tableHeadeList: [],
      tableList: [],
      tableDataList_one: [], // 系统参数配置
      dialogVisibleone: { // 系统参数弹框
        show: false,
        title: 'add'
      },
      dialogVisibleTwo: { // 定存利率表
        show: false,
        title: 'add'
      },
      dialogVisiblefour: { // 节假日弹框
        show: false,
        title: 'add'
      },

      holidayFormUser: {}, // 节假日弹框列表
      tableDataList_four: [], // 节假日参数配置
      ruleFormUser: {}, // 系统参数编辑弹框
      ruleFormList: {},
      system_one: false, // 是否展示系统参数配置
      HolidayTable: false, // 是否展示节假日
      Fixeddeposit: false, // 定存利率表
      FixedFormUser: {}, // 定存利率编辑弹框
      Foreigninterest_show: false, // 是否展示外汇利率表
      Personalloanamount_show: false, // 是否展示个人贷款金额配置表
      Paycollection_show: false, // 是否展示支付收款人信息表
      Regular_show: false, // 是否展示定时跑批表
      depositId: ''// 下拉id
    }
  },
  watch: {},
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.role = window.sessionStorage.getItem('role')
  },
  mounted() {
    this.form.region = '1'
    this.selectNode('1') // 初始化选中第一个下拉

    this.getTableList()
  },
  beforeDestroy() {
  },
  methods: {

    // 点击第一个下拉
    changeSelect(val) {
      this.depositId = val
    },

    // 定存金额范围
    FixedDepositFun(row) {
      axios.post(`${this.LBSGateway}/interest-service/amount/range/findMany`, { start: 0, end: 1000 }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          // this.options =
          const options = JSON.parse(res.data.data)
          this.options = options

          // this.$nextTick(()=>{
          //   this.system_one=true    ///是否展示系统参数配置
          // })

          // 如果有参数，是编辑的，特殊处理
          if (row) {
            const depositrange = row.depositrange
            let id = ''
            this.options.forEach(item => {
              const str = item.amountrangemin + '.00000-' + item.amountrangemax + '.00000'
              if (str === depositrange) {
                id = item.id
              }
            })
            this.FixedFormUser.depositrange = id
          }
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },

    // 去员工申请页面查看详情
    Individual() {
      if (this.role === 'System Operator') {
        this.$router.push({ path: `/system/apply` })
      }
      if (this.role === 'System Manager') {
        this.$router.push({ path: `/system/approve` })
      }
    },
    // ------------- ---------------------------------------------------------------------------------------

    // 系统参数表
    SystemParameter() {
      this.loading = true
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/sysconfig/findMany`, { start: 0, end: 10000 }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.tableDataList_one = res.data.data.data
          this.$nextTick(() => {
            this.system_one = true // /是否展示系统参数配置
          })
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 系统参数删除每一条数据 /sysadmin-process/sysadmin/sysconfig/delete
    getSysDelet(row) {
      this.$confirm(this.$t('lbs.common.delMsg'), this.$t('lbs.common.tip'), {
        confirmButtonText: this.$t('lbs.common.confirm'),
        cancelButtonText: this.$t('lbs.common.cancel'),
        type: 'warning'
      }).then(() => {
        const str = {
          id: row.id,
          start: 0,
          end: 10000
        }
        axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/sysconfig/delete`, str, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.dialogVisibleone.show = false // 关闭弹框
            this.SystemParameter() // 刷新当前系统参数列表
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        })
      }).catch(() => {})
    },

    // 系统参数的添加弹框
    goclickplusOne() {
      this.dialogVisibleone.title = 'add'
      this.dialogVisibleone.show = true
      this.ruleFormList = {}
    },
    // 系统参数的添加弹框 确认添加按钮
    sysAddParameter() {
      const str = {
        depositrange: this.depositId,
        id: this.ruleFormList.id,
        item: this.ruleFormList.item,
        params: this.ruleFormList.params,
        remark: this.ruleFormList.remark,
        value: this.ruleFormList.value,
        sort: +this.ruleFormList.sort,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/sysconfig/insert`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.innerVisible = true
          this.dialogVisibleone.show = false // 关闭弹框
          this.SystemParameter() // 刷新当前系统参数列表
          this.$message({
            showClose: true,
            message: this.$t('Operate.AddedSuccess'), // 添加成功
            type: 'success'
          })
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },

    // 系统参数的編輯弹框  /sysadmin-process/sysadmin/sysconfig/update
    getEditFunOne(row) {
      this.dialogVisibleone.show = true
      this.dialogVisibleone.title = 'edit'
      this.ruleFormList = JSON.parse(JSON.stringify(row))
    },

    // 系统参数的編輯弹框确认按钮
    sysParameter() {
      const str = {
        depositrange: this.depositId,
        id: this.ruleFormList.id,
        item: this.ruleFormList.item,
        params: this.ruleFormList.params,
        remark: this.ruleFormList.remark,
        value: this.ruleFormList.value,
        sort: +this.ruleFormList.sort,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/sysconfig/update`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.innerVisible = true
          this.dialogVisibleone.show = false // 关闭弹框
          this.$message({
            showClose: true,
            message: this.$t('Operate.EditSuccess'), // 編輯成功
            type: 'success'
          })
          this.SystemParameter() // 刷新当前系统参数列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // ------------------------------------------------------------------------------------------

    // 节假日列表
    getHolidayFun() {
      this.loading = true
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/holiday/findMany`, { start: 0, end: 10000 }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.tableDataList_four = res.data.data.data
          this.$nextTick(() => {
            this.HolidayTable = true
          })
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
        .finally(() => {
          this.loading = false
        })
    },
    // 节假日的添加弹框
    goHolidayplus() {
      this.dialogVisiblefour.title = 'add'
      this.dialogVisiblefour.show = true
      this.holidayFormUser = {}
    },
    // 节假日的編輯弹框
    getHolidayEditFun(row) {
      this.dialogVisiblefour.show = true
      this.dialogVisiblefour.title = 'edit'
      this.holidayFormUser = JSON.parse(JSON.stringify(row))
      this.holidayFormUser.day = moment(this.holidayFormUser.day).format('YYYY-MM-DD')
    },
    // 节假日确认增加 /sysadmin-process/sysadmin/holiday/insert
    getHolidayAdd() {
      const str = {
        countrycode: this.holidayFormUser.countrycode,
        day: moment(this.holidayFormUser.day).format('YYYYMMDD'),
        id: this.holidayFormUser.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/holiday/insert`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisiblefour.show = false // 关闭弹框
          this.getHolidayFun() // 刷新当前节假日参数列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 节假日确认编辑 /sysadmin-process/sysadmin/holiday/update
    getHolidayEdit() {
      const str = {
        countrycode: this.holidayFormUser.countrycode,
        day: moment(this.holidayFormUser.day).format('YYYYMMDD'),
        id: this.holidayFormUser.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/holiday/update`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisiblefour.show = false // 关闭弹框
          this.getHolidayFun() // 刷新当前节假日参数列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },

    // 节假日删除弹框/sysadmin-process/sysadmin/holiday/delete
    getHolidayDelFun(row) {
      this.$confirm(this.$t('lbs.common.delMsg'), this.$t('lbs.common.tip'), {
        confirmButtonText: this.$t('lbs.common.confirm'),
        cancelButtonText: this.$t('lbs.common.cancel'),
        type: 'warning'
      }).then(() => {
        const str = {
          id: row.id,
          start: 0,
          end: 10000
        }
        axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/holiday/delete`, str, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.dialogVisiblefour.show = false // 关闭弹框
            this.getHolidayFun() // 刷新当前节假日参数列表
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        })
      }).catch(() => {})
    },

    // ---------------------------定存利率表22222222222222222222 ------------------------------
    getAmountRangeMain() {
      this.loading = true
      axios.post(`${this.LBSGateway}/interest-service/rate/rateFindMany`, { start: 0, end: 1000 }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          const datas = res.data.data
          const arr = JSON.parse(datas)
          this.tableDataList_two = arr
          this.$nextTick(() => {
            this.Fixeddeposit = true // 是否显示定存利率表
          })
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
        .finally(() => {
          this.loading = false
        })
    },
    // 定存利率添加goFixedplus
    goFixedplus() {
      this.dialogVisibleTwo.title = 'add'
      this.dialogVisibleTwo.show = true
      this.FixedFormUser = {}
      this.FixedDepositFun()
    },

    // 定存利率编辑
    getFixEditFun(row) {
      this.dialogVisibleTwo.show = true
      this.dialogVisibleTwo.title = 'edit'
      this.FixedFormUser = JSON.parse(JSON.stringify(row))

      // 编辑的下拉内容
      this.FixedDepositFun(row)
      // 第二个下拉的内容FixedFormUser.depositrange
      // let str = {
      //   id:this.FixedFormUser[0].
      // }
      // debugger
      this.changeSelect()
    },
    // 定存利率编辑确定按钮 /interest-service/rate/update
    getFixDepositEdit() {
      const str = {
        depositrange: this.FixedFormUser.depositrange,
        tdinterestrate: this.FixedFormUser.tdinterestrate,
        tdperiod: this.FixedFormUser.tdperiod,
        id: this.FixedFormUser.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/interest-service/rate/update`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisibleTwo.show = false // 关闭弹框
          this.getAmountRangeMain() // 刷新当前节假日参数列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 定存利率新增 /interest-service/rate/insert
    getFixDepositAdd() {
      const str = {
        depositrange: this.FixedFormUser.depositrange,
        tdinterestrate: this.FixedFormUser.tdinterestrate,
        tdperiod: this.FixedFormUser.tdperiod,
        id: this.FixedFormUser.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/interest-service/rate/insert`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisibleTwo.show = false // 关闭弹框
          this.getAmountRangeMain() // 刷新当前节假日参数列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 定存利率删除/interest-service/rate/delete
    getFixDelFun(row) {
      this.$confirm(this.$t('lbs.common.delMsg'), this.$t('lbs.common.tip'), {
        confirmButtonText: this.$t('lbs.common.confirm'),
        cancelButtonText: this.$t('lbs.common.cancel'),
        type: 'warning'
      }).then(() => {
        const str = {
          id: row.id,
          start: 0,
          end: 10000
        }
        axios.post(`${this.LBSGateway}/interest-service/rate/delete`, str, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.getAmountRangeMain() // 刷新当前节假日参数列表
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        }).catch(() => {
        })
      })
    },

    selectNode(value) {
      // 首先把所有的列表都处理
      this.system_one = false // 系统参数表
      this.HolidayTable = false // 节假日表
      this.Foreigninterest_show = false// 是否展示外汇利率表
      this.Fixeddeposit = false // 定存汇率表
      this.Personalloanamount_show = false // 个人贷款金额配置表
      this.Paycollection_show = false // 支付收款人信息表
      this.Regular_show = false // 定时跑批表
      if (value === '1') {
        this.SystemParameter() // 系统参数表
      } else if (value === '2') {
        this.getAmountRangeMain()
      } else if (value === '3') {
        this.Foreigninterest_show = true
        this.$nextTick(() => {
          this.$refs.Foreignexchange_ref.getForeignFun() // 外汇利率表
        })
      } else if (value === '4') {
        this.getHolidayFun() // 节假日参数表
      } else if (value === '5') {
        this.Personalloanamount_show = true
        this.$nextTick(() => {
          this.$refs.Personalloan_ref.getPersonalFun() // 个人贷款金额配置表
        })
      } else if (value === '6') {
        this.Paycollection_show = true
        this.$nextTick(() => {
          this.$refs.Paycollection_ref.getPaymentFun() // 个人贷款金额配置表
        })
      } else if (value === '7') {
        this.Regular_show = true
        this.$nextTick(() => {
          this.$refs.Regular_ref.getRegularFun() // 定时跑批表
        })
      }
    },

    // 获取表头，表格数据
    getTableList() {
      // const str = {
      //   'end': 0,
      //   'id': 0,
      //   'item': 'string',
      //   'params': [
      //     'string'
      //   ],
      //   'remark': 'string',
      //   'sort': 0,
      //   'start': 0,
      //   'value': 'string'
      // }
      const _this = this
      axios.post(`${_this.LBSGateway}/sysadmin-process/sysadmin/sysconfig/findMany`, { start: 0, end: 10000 }, { headers: { token: _this.token }}).then(res => {
        if (res.data.code === '200') {
          // const datas = res.data.data
          // -----------------------------------------------------
          // const params = {
          //   platform: 'Virtual banking system',
          //   userId: window.sessionStorage.getItem('role'),
          //   operationType: '虚拟银行运营角色系统参数配置'
          // }
          // this.$store.dispatch('app/VisitsFun', params).then((res) => {
          //   this.loading = false
          //   if (res.data.code !== 20000) {
          //     console.log(res.data.message)
          //   }
          // }).catch(error => {
          //   this.loading = false
          //   console.log(error.message)
          // })
          // -----------------------------------------
        } else {
          this.$message({
            showClose: true,
            message: res.data.desc,
            type: 'error'
          })
        }
      })
    },

    // /permission/findOne
    // /permission/insert
    // /permission/update

    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    }

  }

}
</script>

<style lang='scss' scoped>
  .workflow {
    min-height: 840px;

    .data-action-button-group {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }

    .list-group1 {
      display: inline-block;
      width: 100%;
      height: 66px;
    }

    .list-group2 {
      display: inline-block;
      width: 100%;
      height: 61px;
    }

    .w-group-item {
      width: 146px;
      margin: 0 5px 5px 0;
      text-align: center;
      cursor: move;
    }
  }
  .content{
    width: 100%;
    position: relative;
    .box{
      border: 3px solid white;
      background: #ffffff;
      width: 60%;
      margin: 30px auto;
      padding-top: 30px;

    }
    .box_one1{
      position: absolute;
      left: 0px;
      top:0px;
    }
  }
  .content_one{
    width: 100%;
    text-align: center;
    font-size: 22px;

  }
  .content_1{
    width: 80%;
    padding: 15px;
    margin: 10px auto;
    display: flex;
    justify-content: center;
    .Yuangong{
      line-height: 38px;
      font-size: 16px;
      margin-right: 20px;
    }
  }
  .content_2{
    width: 80%;
    padding: 15px;
    display: flex;
    justify-content: center;
    margin-left: 58px;
    .Yuangong{
      line-height: 38px;
      font-size: 13px;
    }
  }
  .box_two{
    position: relative;
    height: 60px;
    line-height: 60px;
    .box_two01{
      position: absolute;
      top:15px;
      left: -52px;
    }
  }
  .content01{
    width: 60px;
    height: 30px;
    font-size: 14px;
    border: 2px solid seagreen;
    text-align: center;
    line-height: 26px;
    border-radius: 5px;
    cursor: pointer;
    color: white;
    background:seagreen ;
    margin-left: 30px;
  }
  .plus{
    font-size: 30px;
    font-weight: bold;
  }
  .modalTitle{
    width: 100%;
    height: 43px;
    padding-left: 10px;
    font-weight: 600;

    h4{
      line-height: 43px;
      font-size: 15px;
    }
  }
  .applicationSuccess{
    line-height: 1.5;
    margin-bottom: 20px;
  }
</style>
