<template>
  <div>
    <el-card v-if="!dialogCaptureDemographic" v-loading.fullscreen.lock="loading" shadow="never" class="workflow">
      <div slot="header" class="clearfix">
        <span>{{ $t("Workflow.permissionconfig") }}</span>
      </div>

      <div class="content">
        <div class="box_one1">
          <!--staffNumberOpear:'',//员工编号-->
          <!--departmentOpear:'',//员工部门-->
          <!--员工编号 部门 角色:-->
          <p class="Opear_p">{{ $t("Workflow.Emplonumber") }}:<span style="color: #00B1B1">{{ staffNumberOpear }}</span></p>
          <p class="Opear_p">{{ $t("Workflow.departments") }}<span style="color: #00B1B1">{{ departmentOpear }}</span></p>
          <p class="Opear_p">{{ $t("Workflow.role") }}<span style="color: #00B1B1">{{ loginRole }}</span></p>
        </div>
        <el-table
          class="params-table"
          :data="tableDataList.slice((currentPage-1)*pagesize,currentPage*pagesize)"
        >
          <!--员工编号-->
          <el-table-column
            type="index"
            :label="$t('lbs.common.number')"
            align="center"
          />
          <!--操作对象-->
          <el-table-column
            prop="roles"
            :label="$t('Workflow.opearObject')"
            align="center"
          />
          <!--权限描述-->
          <el-table-column
            prop="name"
            :label="$t('Workflow.description')"
            align="center"
          />
          <!--权限接口-->
          <el-table-column
            prop="url"
            :label="$t('Workflow.interface')"
            align="center"
          />
          <!--接口额度审核字段-->
          <el-table-column
            prop="checkLimitAmtField"
            :label="$t('Workflow.approvalfield')"
            align="center"
          />

          <!--操作-->
          <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope"
                size="mini"
                type="text"
                @click="getEditFun(scope.row)"
              >
                {{ $t("Workflow.Edit") }}
              </el-button>
              <el-button
                v-if="scope"
                size="mini"
                type="text"
                @click="getDelFun(scope.row)"
              >
                {{ $t("Workflow.Delete") }}
              </el-button>

            </template>
          </el-table-column>
        </el-table>
        <div class="block">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 40]"
            :page-size="pagesize"
            :total="tableDataList.length"
            layout="total, prev, pager, next"
            style="margin-top: 20px; text-align: right;"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <!--el-icon-plus-->
        <div class="plus" @click="goclickplus()"><i class="el-icon-plus" /></div>
        <!--增加页面-->
        <el-dialog
          :title="$t('lbs.common.add')"
          :visible.sync="dialogVisiblePlus"
          width="50%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >

          <el-form :model="ruleFormList" label-width="200px" class="demo-ruleForm" style="margin-left: 10%">
            <!--操作对象-->
            <el-form-item :label="$t('Workflow.opearObject')" prop="roles">
              <el-input v-model="ruleFormList.roles" style="width:50%" />
            </el-form-item>
            <!--权限描述-->
            <el-form-item :label="$t('Workflow.description')" prop="name">
              <el-input v-model="ruleFormList.name" style="width: 50%" />
            </el-form-item>
            <!--权限接口-->
            <el-form-item :label="$t('Workflow.interface')" prop="url">
              <el-input v-model="ruleFormList.url" style="width: 50%" />
            </el-form-item>
            <!--接口额度审核字段-->
            <el-form-item :label="$t('Workflow.approvalfield')" prop="checkLimitAmtField">
              <el-input v-model="ruleFormList.checkLimitAmtField" style="width:50%" />
            </el-form-item>

          </el-form>

          <span slot="footer" class="dialog-footer">
            <!--取消-->
            <el-button @click="dialogVisiblePlus = false">{{ $t('Workflow.Cancel') }}</el-button>
            <!--确定-->
            <el-button type="primary" @click="PlusSure()">{{ $t('Workflow.Confirm') }}</el-button>
          </span>
        </el-dialog>
        <!--编辑-->
        <el-dialog
          :title="$t('lbs.common.edit')"
          :visible.sync="dialogVisibleEdit"
          width="50%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
        >

          <el-form :model="editform" label-width="200px" class="demo-ruleForm" style="margin-left: 10%">
            <!--操作对象-->
            <el-form-item :label="$t('Workflow.opearObject')">
              <el-input v-model="editform.roles" style="width:50%" />
            </el-form-item>
            <!--权限描述-->
            <el-form-item :label="$t('Workflow.description')">
              <el-input v-model="editform.name" style="width: 50%" />
            </el-form-item>
            <!--权限接口-->
            <el-form-item :label="$t('Workflow.interface')">
              <el-input v-model="editform.url" style="width: 50%" />
            </el-form-item>
            <!--接口额度审核字段-->
            <el-form-item :label="$t('Workflow.approvalfield')">
              <el-input v-model="editform.checkLimitAmtField" style="width:50%" />
            </el-form-item>

          </el-form>

          <span slot="footer" class="dialog-footer">
            <!--取消-->
            <el-button @click="dialogVisibleEdit = false">{{ $t('Workflow.Cancel') }}</el-button>
            <!--确定-->
            <el-button type="primary" @click="EditSure('editform')">{{ $t('Workflow.Confirm') }}</el-button>
          </span>
        </el-dialog>

      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  components: {},
  props: [],
  data() {
    return {
      token: null,
      name: '',
      dialogCaptureDemographic: false,
      loading: false,

      currentPage: 1,
      pagesize: 10,
      totalPages: 0,
      tableDataList: [], // 列表
      dialogVisiblePlus: false, // 增加弹框
      dialogVisibleEdit: false, // 编辑弹框

      ruleFormList: {
        start: 0,
        end: 10000,
        id: '',
        roles: '', // 操作对象
        name: '', // 操作描述
        url: '', // 权限接口
        checkLimitAmtField: ''// 接口额度审核字段
      },
      editform: {
        start: 0,
        end: 10000,
        id: '',
        name: '',
        roles: '',
        url: '',
        checkLimitAmtField: ''
      },
      loginRole: '', // 员工角色
      loginNameOpear: '', // 员工名称
      staffNumberOpear: '', // 员工编号
      departmentOpear: ''// 员工部门
      // rules: {
      //    roles: [
      //        // 请输入操作对象
      //        { required: true, message:this.$t('Workflow.PleaseObj'), trigger: 'blur' },
      //      ],
      //      //请输入权限描述
      //      name: [
      //        { required: true, message: this.$t('Workflow.Pleasedesc'), trigger: 'blur'},
      //        ],
      //      url: [
      //        // 请输入权限接口
      //        { required: true, message:this.$t('Workflow.Pleasepermiss'),trigger: 'blur' },
      //      ],
      //      checkLimitAmtField:[
      //        // 请填写接口额度审核字段
      //        { required: true, message: this.$t('Workflow.Pleaseinterface'),trigger: 'blur' },
      //      ]
      //    }
    }
  },
  watch: {},
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.loginNameOpear = window.sessionStorage.getItem('LoginName_Opear') // 员工姓名
    this.loginRole = window.sessionStorage.getItem('role') // 角色
    this.staffNumberOpear = window.sessionStorage.getItem('staffNumber_Opear') // 员工编号
    this.departmentOpear = window.sessionStorage.getItem('department') // 部门
  },
  mounted() {
    this.getjurisdicyionFun()
  },
  beforeDestroy() {
  },
  methods: {
    // 查
    getjurisdicyionFun() {
      const _this = this
      axios.post(`${_this.LBSGateway}/sysadmin-service/permission/findMany`, { start: 0, end: 10000 }, { headers: { token: _this.token }}).then(res => {
        // axios.post(`http://121.36.44.239:8105/permission/findMany`, {start: 0,end:10000} ,{headers: {token: _this.token}}) .then(res => {
        // 点击登录提交的时候，给本地储存资料
        // this.$utils.store.set("studyUrl", data.body.data);
        if (res.data.code === '200') {
          const datas = res.data.data
          const arr = JSON.parse(datas)
          arr.forEach(item => {
            if (item.roles) {
              const res_roles = this.isJSON(item.roles)
              if (res_roles) {
                item.roles = JSON.parse(item.roles)
              } else {
                const arr = []
                arr[0] = item.roles
                item.roles = arr
              }
            } else {
              item.roles = []
            }
          })
          this.tableDataList = arr
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    isJSON(str) {
      if (typeof str === 'string') {
        try {
          JSON.parse(str)
          return true
        } catch (e) {
          console.log(e)
          return false
        }
      }
    },
    // 编辑
    getEditFun(row) {
      this.dialogVisibleEdit = true
      this.editform = row
    },
    // 编辑确定 roles name url checkLimitAmtField
    EditSure() {
      const _this = this
      const requestData = {
        checkLimitAmtField: this.editform.checkLimitAmtField,
        name: this.editform.name,
        roles: this.editform.roles.toString(),
        url: this.editform.url,
        id: this.editform.id,
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/permission/update`, requestData, { headers: { token: _this.token }}).then(response => {
        if (response.data.code === '200') {
          this.dialogVisibleEdit = false
          this.getjurisdicyionFun()
          this.$message({
            showClose: true,
            message: response.data.msg,
            type: 'success'
          })
        } else {
          this.dialogVisibleEdit = false
          this.$message({
            showClose: true,
            message: response.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 删除
    getDelFun(row) {
      this.$confirm(this.$t('lbs.common.delMsg'), this.$t('lbs.common.tip'), {
        confirmButtonText: this.$t('lbs.common.confirm'),
        cancelButtonText: this.$t('lbs.common.cancel'),
        type: 'warning'
      }).then(() => {
        axios.post(`${this.LBSGateway}/sysadmin-service/permission/delete`, { id: row.id }, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.getjurisdicyionFun()
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        })
      }).catch(() => {})
    },

    // 增加
    goclickplus() {
      this.dialogVisiblePlus = true
    },
    // 确定增加
    PlusSure() {
      const _this = this
      const requestData = {
        name: this.ruleFormList.name,
        roles: this.ruleFormList.roles,
        url: this.ruleFormList.url,
        checkLimitAmtField: this.ruleFormList.checkLimitAmtField,
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/permission/insert`, requestData, { headers: { token: _this.token }}).then(response => {
        if (response.data.code === '200') {
          this.dialogVisiblePlus = false
          this.$message({
            showClose: true,
            message: response.data.msg,
            type: 'success'
          })
          this.getjurisdicyionFun()
        } else {
          this.$message({
            showClose: true,
            message: response.data.msg,
            type: 'error'
          })
        }
      })
    },

    // /permission/findOne
    // /permission/insert
    // /permission/update

    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    }

  }

}
</script>

<style lang='scss' scoped>
  .workflow {
    min-height: 840px;
    .list-group1 {
      display: inline-block;
      width: 100%;
      height: 66px;
    }

    .list-group2 {
      display: inline-block;
      width: 100%;
      height: 61px;
    }

    .w-group-item {
      width: 146px;
      margin: 0 5px 5px 0;
      text-align: center;
      cursor: move;
    }
  }
  .content{
    width: 100%;
    position: relative;
    .box{
      border: 3px solid white;
      background: #ffffff;
      width: 60%;
      margin: 0px auto;
      padding-top: 30px;

    }
    .box_one1{
      position: absolute;
      left: 0px;
      top:0px;
    }
  }
  .content_one{
    width: 100%;
    text-align: center;

  }
  .content_1{
    width: 80%;
    padding: 15px;
    margin: 10px auto;
    display: flex;
    justify-content: center;
    .Yuangong{
      line-height: 38px;
      font-size: 16px;
      margin-right: 20px;
    }
  }
  .content_2{
    width: 80%;
    padding: 15px;
    display: flex;
    justify-content: center;
    margin-left: 58px;
    .Yuangong{
      line-height: 38px;
      font-size: 13px;
    }
  }
  .box_two{
    position: relative;
    height: 60px;
    line-height: 60px;
    .box_two01{
      position: absolute;
      top:15px;
      left: -52px;
    }
  }
  .content01{
    width: 60px;
    height: 30px;
    font-size: 14px;
    border: 2px solid seagreen;
    text-align: center;
    line-height: 26px;
    border-radius: 5px;
    cursor: pointer;
    color: white;
    background:seagreen ;
    margin-left: 30px;
  }
.plus{
  font-size: 30px;
  font-weight: bold;
  cursor: pointer;
}
  .Opear_p{
    font-weight: bold;
    span{
      color:#00B1B1 ;
    }
  }
</style>
