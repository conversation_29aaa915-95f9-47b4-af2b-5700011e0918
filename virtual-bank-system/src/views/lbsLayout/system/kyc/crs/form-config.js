import { formConfig as customerFormConfig } from '@/views/lbsLayout/system/kyc/customer/form-config'
import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { booleanOptions, statusOptions } from '@/views/lbsLayout/system/kyc/options'

export const formConfig = [
  ...customerFormConfig,
  {
    'label': 'kyc.crs.foreignCountryTaxNumber',
    'placeholder': 'kyc.crs.foreignCountryTaxNumberPlaceholder',
    'prop': 'foreignCountryTaxNumber',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 1,
    'step-strictly': true,
    'controls': false,
    'precision': 0,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.crs.foreignCountryTaxNumberPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.validityPeriod',
    'placeholder': 'kyc.crs.validityPeriodPlaceholder',
    'prop': 'validityPeriod',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'kyc.crs.validityPeriodPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.report',
    'placeholder': 'kyc.crs.reportPlaceholder',
    'prop': 'report',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': booleanOptions,
    'rules': [
      { required: true, message: 'kyc.crs.reportPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.crsStatus',
    'placeholder': 'kyc.crs.crsStatusPlaceholder',
    'prop': 'crsStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'options': statusOptions,
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.crs.crsStatusPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.remark',
    'placeholder': 'kyc.crs.remarkPlaceholder',
    'prop': 'remark',
    'component': 'el-input',
    'span': 24,
    'width': '100%',
    'type': 'textarea',
    'clearable': true,
    'maxlength': 255
  },
  {
    'label': 'kyc.creationDate',
    'placeholder': 'kyc.creationDatePlaceholder',
    'prop': 'creationDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.creationDatePlaceholder' }
    ]
  },
  {
    'label': 'kyc.updateDate',
    'placeholder': 'kyc.updateDatePlaceholder',
    'prop': 'updateDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.updateDatePlaceholder' }
    ]
  }
]
