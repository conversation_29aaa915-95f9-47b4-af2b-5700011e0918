export const customerIdTypeOptions = [
  { label: 'kyc.options.idCard', value: 'I' },
  { label: 'kyc.options.passport', value: 'P' },
  { label: 'kyc.options.others', value: 'X' }
]

export const countryOptions = [
  { label: 'HK', value: 'HK' },
  { label: 'CN', value: 'CN' },
  { label: 'MY', value: 'MY' },
  { label: 'JP', value: 'JP' },
  { label: 'FR', value: 'FR' },
  { label: 'IN', value: 'IN' },
  { label: 'DE', value: 'DE' },
  { label: 'PH', value: 'PH' },
  { label: 'GB', value: 'GB' },
  { label: 'SG', value: 'SG' },
  { label: 'AU', value: 'AU' },
  { label: 'US', value: 'US' }
]

export const genderOptions = [
  { label: 'kyc.options.male', value: 'M' },
  { label: 'kyc.options.female', value: 'F' }
]

export const booleanOptions = [
  { label: 'kyc.options.yes', value: 'Y' },
  { label: 'kyc.options.no', value: 'N' }
]

export const statusOptions = [
  { label: 'kyc.options.active', value: 'Active' },
  { label: 'kyc.options.cancel', value: 'Cancel' }
]

export const sanctionTypeOptions = [
  { label: 'kyc.options.political', value: 'P' },
  { label: 'kyc.options.economic', value: 'E' },
  { label: 'kyc.options.military', value: 'M' },
  { label: 'kyc.options.cultural', value: 'C' }
]

export function getLabel(type, value) {
  let temp = []
  switch (type.toUpperCase()) {
    case 'CUSTOMER_ID_TYPE':
      temp = customerIdTypeOptions.filter(item => item.value === value)
      break
    case 'COUNTRY':
      temp = countryOptions.filter(item => item.value === value)
      break
    case 'GENDER':
      temp = genderOptions.filter(item => item.value === value)
      break
    case 'BOOLEAN':
      temp = booleanOptions.filter(item => item.value === value)
      break
    case 'STATUS':
      temp = statusOptions.filter(item => item.value === value)
      break
    case 'SANCTION_TYPE':
      temp = sanctionTypeOptions.filter(item => item.value === value)
      break
    default:
      temp = []
      break
  }
  if (temp && temp.length) {
    return temp[0].label
  }
  return ''
}

export function handlerCommonData(data) {
  const formData = JSON.parse(JSON.stringify(data))
  formData.customerMasterInfoEntity = {}
  const commonField = [
    'accountNumber',
    'customerId',
    'customerIdType',
    'customerNumber',
    'dateOfBirth',
    'department',
    'firstName',
    'gender',
    'industry',
    'issueCountry',
    'lastName',
    'nationality',
    'otherLanguageName',
    'position',
    'role'
  ]
  commonField.forEach(item => {
    formData.customerMasterInfoEntity[item] = formData[item]
    delete formData[item]
  })
  return formData
}
