<template>
  <div class="create-permission-container">
    <permission-form
      :permission-form.sync="permissionForm"
      :permission-options="permissionOptions"
      :role-options="roleOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="createPermission()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import PermissionForm from '@/views/lbsLayout/system/limit/LimitForm.vue'
import axios from 'axios'

export default {
  name: 'CreateLimit',
  components: { PermissionForm },
  props: {
    roleOptions: {
      type: Array,
      required: true
    },
    permissionOptions: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      token: null,
      confirmButtonLoading: false,
      permissionForm: {
        roleId: '',
        funPermissionId: '',
        limit: 0
      }
    }
  },
  async mounted() {
    this.token = window.sessionStorage.getItem('token')
  },
  methods: {
    // 调用API创建权限
    createPermission() {
      this.confirmButtonLoading = true
      const requestData = { ...this.permissionForm }
      axios.post(this.LBSGateway + '/sysadmin-process/sysadmin/role-limit/create', requestData, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg } = res.data
          if (code === '200') {
            this.$message.success(msg)
            this.$emit('success')
          } else {
            this.$message.error(msg)
          }
        })
        .catch((error) => {
          this.$message.error(error.msg)
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.create-permission-container {
  padding: 0 20px;
}
</style>
