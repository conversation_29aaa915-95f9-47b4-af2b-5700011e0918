<template>
  <div class="update-permission-container">
    <permission-form
      :permission-form.sync="roleLimit"
      :permission-options="permissionOptions"
      :role-options="roleOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="updateRoleLimit()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import PermissionForm from '@/views/lbsLayout/system/limit/LimitForm.vue'
import axios from 'axios'

export default {
  name: 'UpdateLimit',
  components: { PermissionForm },
  props: {
    roleLimitId: {
      type: Number,
      required: true
    },
    roleOptions: {
      type: Array,
      required: true
    },
    permissionOptions: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      token: null,
      confirmButtonLoading: false,
      roleLimit: {}
    }
  },
  async mounted() {
    this.token = window.sessionStorage.getItem('token')

    // 加载权限信息
    this.roleLimit = await this.getRoleLimitDetails()
  },
  methods: {
    async getRoleLimitDetails() {
      const { data } = await axios.get(this.LBSGateway + `/sysadmin-process/sysadmin/role-limit/${this.roleLimitId}`, { headers: { token: this.token }})
      const { data: permission } = data
      return permission
    },
    // 调用API更新权限
    updateRoleLimit() {
      this.confirmButtonLoading = true
      const requestData = { ...this.roleLimit }
      axios.post(this.LBSGateway + '/sysadmin-process/sysadmin/role-limit/update', requestData, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg } = res.data
          if (code === '200') {
            this.$message.success(msg)
            this.$emit('success')
          } else {
            this.$message.error(msg)
          }
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.update-permission-container {
  padding: 0 20px;
}
</style>
