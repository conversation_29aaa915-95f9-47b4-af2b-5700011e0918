<template>
  <div v-loading="loading" class="role-limit-management-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 角色 -->
        <el-form-item :label="$t('permission.role')" prop="roleId">
          <el-select v-model="searchForm.roleId" clearable :placeholder="$t('permission.role')">
            <el-option v-for="(item, index) in roleOptions" :key="index" :label="item.role" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- 权限功能 -->
        <el-form-item :label="$t('permission.function')" prop="funPermissionId">
          <el-select v-model="searchForm.funPermissionId" clearable :placeholder="$t('permission.function')">
            <el-option v-for="(item, index) in permissionOptions" :key="index" :label="item.permissionName" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getRoleLimitList()">{{ $t('permission.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('permission.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateRoleLimitDialog()">{{ $t('permission.create') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getRoleLimitList()" />
      </div>

      <!-- 权限表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="roleLimitList" row-key="id" stripe :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('permission.roleName')" prop="roleName" align="left" />
          <el-table-column :label="$t('permission.functionName')" prop="permissionName" align="left" />
          <el-table-column :label="$t('permission.limit')" prop="limit" align="left" />
          <el-table-column :label="$t('permission.operate')" align="center" width="193px">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdateRoleLimitDialog(scope.row.id)">{{ $t('permission.edit') }}</el-button>
              <el-button type="text" size="mini" text icon="el-icon-delete" @click="batchDeleteRoleLimit(scope.row.id)">{{ $t('permission.delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>

    <!-- 创建权限的对话框 -->
    <el-dialog
      :title="$t('permission.createLimit')"
      :visible.sync="createRoleLimitDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <create-limit
        :key="createKey"
        :role-options="roleOptions"
        :permission-options="permissionOptions"
        @success="handlerCreateRoleLimitSuccess()"
        @cancel="closeCreateRoleLimitDialog()"
      />
    </el-dialog>

    <!-- 更新权限的对话框 -->
    <el-dialog
      :title="$t('permission.editLimit')"
      :visible.sync="updateRoleLimitDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <!-- key的作用: 重新刷新子组件 -->
      <update-limit
        :key="updateKey"
        :role-options="roleOptions"
        :permission-options="permissionOptions"
        :role-limit-id="updateRoleLimitId"
        @success="handlerUpdateRoleLimitSuccess()"
        @cancel="closeUpdateRoleLimitDialog()"
      />
    </el-dialog>
  </div>
</template>

<script>
import CreateLimit from '@/views/lbsLayout/system/limit/CreateLimit.vue'
import UpdateLimit from '@/views/lbsLayout/system/limit/UpdateLimit.vue'
import axios from 'axios'

export default {
  name: 'LimitManagement',
  components: { CreateLimit, UpdateLimit },
  data() {
    return {
      // 页面loading
      loading: false,
      token: null,
      // 表格loading
      tableLoading: false,
      createKey: '',
      updateKey: '',
      // 分页查询请求参数
      searchForm: {
        roleId: '',
        funPermissionId: ''
      },
      // 角色权限额度配置列表
      roleLimitList: [],
      // 权限列表
      permissionOptions: [],
      // 创建权限的对话框标记：true 显示，false 隐藏
      createRoleLimitDialogVisible: false,
      // 更新权限的对话框标记：true 显示，false 隐藏
      updateRoleLimitDialogVisible: false,
      confirmButtonLoading: false,
      updateRoleLimitId: '',
      roleOptions: []
    }
  },
  async mounted() {
    this.token = window.sessionStorage.getItem('token')

    this.getPermissionList()
    this.getRoleList()
    this.getRoleLimitList()
  },
  methods: {
    getRoleLimitList() {
      this.loading = true

      const requestData = { ...this.searchForm }

      axios.get(this.LBSGateway + '/sysadmin-process/sysadmin/role-limit', { params: requestData, headers: { token: this.token }})
        .then((res) => {
          const { code, msg, data: roleLimitList } = res.data
          if (code === '200') {
            this.roleLimitList = roleLimitList
          } else {
            this.roleLimitList = []
            this.$message.error(msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getRoleList() {
      const requestData = {
        start: 0,
        end: 10000
      }
      this.loading = true
      axios.post(`${this.LBSGateway}/sysadmin-service/role/findMany`, requestData, { headers: { token: this.token }})
        .then(res => {
          if (res.data.code === '200') {
            const datas = res.data.data
            if (datas) {
              this.roleOptions = JSON.parse(res.data.data)
            } else {
              this.roleOptions = []
            }
          } else {
            this.$message.error(res.data.msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 删除权限
    batchDeleteRoleLimit(roleLimitId) {
      this.$confirm(this.$t('permission.deletePermissionPrompt'), this.$t('permission.tip'), {
        confirmButtonText: this.$t('permission.confirm'),
        cancelButtonText: this.$t('permission.cancel'),
        type: 'warning'
      }).then(() => {
        this.loading = true
        axios.post(this.LBSGateway + `/sysadmin-process/sysadmin/role-limit/delete`, { id: roleLimitId }, { headers: { token: this.token }})
          .then((res) => {
            const { code, msg } = res.data
            if (code === '200') {
              this.$message.success(msg)
            } else {
              this.$message.error(msg)
            }
            // 重新加载权限列表
            this.getRoleLimitList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 更新权限成功
    handlerUpdateRoleLimitSuccess() {
      this.updateRoleLimitDialogVisible = false
      this.getRoleLimitList()
    },
    // 打开更新权限对话框
    openUpdateRoleLimitDialog(roleLimitId) {
      this.updateRoleLimitId = roleLimitId
      this.updateKey = String(new Date().getTime())
      this.updateRoleLimitDialogVisible = true
    },
    // 关闭更新权限对话框
    closeUpdateRoleLimitDialog() {
      this.updateRoleLimitDialogVisible = false
    },
    // 创建权限成功
    handlerCreateRoleLimitSuccess() {
      this.createRoleLimitDialogVisible = false
      this.getRoleLimitList()
    },
    // 调用API分页查询权限列表
    getPermissionList() {
      this.loading = true

      const requestData = { isLimit: 1, type: 'list' }

      axios.get(this.LBSGateway + '/sysadmin-process/sysadmin/fun-permission', { params: requestData, headers: { token: this.token }})
        .then((res) => {
          const { code, msg, data: permissionList } = res.data
          if (code === '200') {
            this.permissionOptions = permissionList
          } else {
            this.permissionOptions = []
            this.$message.error(msg)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 打开创建权限对话框
    openCreateRoleLimitDialog() {
      this.createKey = String(new Date().getTime())
      this.createRoleLimitDialogVisible = true
    },
    // 关闭创建权限对话框
    closeCreateRoleLimitDialog() {
      this.createRoleLimitDialogVisible = false
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.role-limit-management-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
  .bottom-actions-button-group {
    text-align: right;
  }
}
</style>
