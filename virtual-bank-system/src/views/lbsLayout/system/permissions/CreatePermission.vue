<template>
  <div class="create-permission-container">
    <permission-form
      :permission-form.sync="permissionForm"
      :permission-options="permissionOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="createPermission()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import PermissionForm from '@/views/lbsLayout/system/permissions/PermissionForm.vue'
import axios from 'axios'

export default {
  name: 'CreatePermission',
  components: { PermissionForm },
  data() {
    return {
      token: null,
      confirmButtonLoading: false,
      permissionForm: {
        parentId: '',
        permissionName: '',
        accredit: '',
        sortNumber: 1,
        isLimit: 0,
        isApprove: 0,
        url: ''
      },
      permissionOptions: []
    }
  },
  async mounted() {
    this.token = window.sessionStorage.getItem('token')

    // 加载权限列表
    this.permissionOptions = await this.getPermissionOptions()
  },
  methods: {
    async getPermissionOptions() {
      const { data } = await axios.get(this.LBSGateway + '/sysadmin-process/sysadmin/fun-permission', { headers: { token: this.token }})
      const { data: permissionOptions } = data

      console.log(permissionOptions)

      function clearEmptyChildren(nodes) {
        nodes.forEach(node => {
          if (!node.children.length) {
            delete node.children
          } else {
            clearEmptyChildren(node.children)
          }
        })
      }

      clearEmptyChildren(permissionOptions)

      return [
        {
          accredit: 'root',
          children: permissionOptions,
          id: 0,
          parentId: 0,
          permissionName: 'Root',
          sortNumber: 1,
          label: 'Root'
        }
      ]
    },
    // 调用API创建权限
    createPermission() {
      this.confirmButtonLoading = true
      const requestData = { ...this.permissionForm }
      axios.post(this.LBSGateway + '/sysadmin-process/sysadmin/fun-permission/create', requestData, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg } = res.data
          if (code === '200') {
            this.$message.success(msg)
            this.$emit('success')
          } else {
            this.$message.error(msg)
          }
        })
        .catch((error) => {
          this.$message.error(error.msg)
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.create-permission-container {
  padding: 0 20px;
}
</style>
