<template>
  <div v-loading.fullscreen.lock="loading">
    <!--定时跑批表 Fixeddeposit-->
    <template>
      <el-table
        :data="tableDataList_seven.slice((currentPage-1)*pagesize,currentPage*pagesize)"
        stripe
        style="width: 100%"
      >
        <!--序号:label="$t('lbs.common.number')"-->
        <el-table-column
          type="index"
          :label="$t('lbs.common.number')"
          align="center"
        />
        <el-table-column
          prop="name"
          label="name"
          align="center"
        />

        <el-table-column
          prop="groupName"
          label="groupName"
          align="center"
        />
        <el-table-column
          prop="cron"
          label="cron"
          align="center"
        />
        <el-table-column
          prop="url"
          label="url"
          align="center"
        />
        <el-table-column
          prop="parameter"
          label="parameter"
          align="center"
        />
        <el-table-column
          prop="description"
          label="description"
          align="center"
        />
        <el-table-column
          prop="vmParam"
          label="vmParam"
          align="center"
        />
        <el-table-column
          prop="jarPath"
          label="jarPath"
          align="center"
        />
        <el-table-column
          prop="status"
          label="status"
          align="center"
        />
        <!--定时跑批表操作-->
        <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope"
              size="mini"
              type="text"
              @click="RegularEditFun(scope.row)"
            >
              {{ $t("Workflow.Edit") }}
            </el-button>
            <el-button
              v-if="scope"
              size="mini"
              type="text"
              @click="RegularDelFun(scope.row)"
            >
              {{ $t("Workflow.Delete") }}
            </el-button>

          </template>
        </el-table-column>
      </el-table>
    </template>
    <div class="block">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[5, 10, 20, 40]"
        :page-size="pagesize"
        :total="tableDataList_seven.length"
        layout="total, prev, pager, next"
        style="margin-top: 20px; text-align: right;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!--定时跑批表表弹框start-->
    <el-dialog
      :title="dialogVisibleseven.title === 'add' ? $t('Operate.RegularAdd') : $t('Operate.RegularEdit')"
      :visible.sync="dialogVisibleseven.show"
      width="40%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >

      <el-form ref="ruleFormList" :model="Regularbatch" label-width="130px" label-position="left" size="small">
        <el-form-item label="Name">
          <el-input v-model="Regularbatch.name" />
        </el-form-item>
        <el-form-item label="Group Name">
          <el-input v-model="Regularbatch.groupName" />
        </el-form-item>
        <el-form-item label="Cron">
          <el-input v-model="Regularbatch.cron" />
        </el-form-item>
        <el-form-item label="Url">
          <el-input v-model="Regularbatch.url" />
        </el-form-item>
        <el-form-item label="Parameter">
          <el-input v-model="Regularbatch.parameter" />
        </el-form-item>
        <el-form-item label="Description">
          <el-input v-model="Regularbatch.description" />
        </el-form-item>
        <el-form-item label="Vm Param">
          <el-input v-model="Regularbatch.vmParam" />
        </el-form-item>
        <el-form-item label="Jar Path">
          <el-input v-model="Regularbatch.jarPath" />
        </el-form-item>
        <el-form-item label="Status">
          <el-input v-model="Regularbatch.status" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <!--取消-->
        <el-button size="small" @click="dialogVisibleseven.show = false">{{ $t('Workflow.Cancel') }}</el-button>
        <!--定时跑批表增加确定-->
        <el-button v-if="dialogVisibleseven.title === 'add'" size="small" type="primary" @click="PayAdd()">{{ $t('Operate.Determine') }}</el-button>
        <!--定时跑批表编辑确定-->
        <el-button v-if="dialogVisibleseven.title === 'edit'" size="small" type="primary" @click="PayEdit()">{{ $t('Operate.Okedit') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import axios from 'axios'

export default {
  components: {},
  props: {},
  data() {
    return {
      loading: false,
      name: '',
      tableDataList_seven: [], // 外汇利率标的数据
      Regularbatch: {}, // 弹框表单
      currentPage: 1,
      pagesize: 10,
      totalPages: 0,
      token: null,
      dialogVisibleseven: {
        show: false,
        title: 'add'
      }

    }
  },
  watch: {},
  created() {
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
  },
  beforeDestroy() {
  },
  methods: {
    getRegularFun() {
      this.loading = true
      // 定时跑批表
      axios.post(`${this.LBSGateway}/schedulejob/job/findMany`, { start: 0, end: 10000 }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          const datas = res.data.data
          const arr = JSON.parse(datas)
          this.tableDataList_seven = arr
          this.tableDataList_seven.forEach(item => {
            this.tableDataList_seven.createdate = moment(item.createdate).format('YYYY-MM-DD')
            this.tableDataList_seven.lastupdatedate = moment(item.lastupdatedate).format('YYYY-MM-DD')
          })
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
        .finally(() => {
          this.loading = false
        })
    },
    // 点击加号出现弹框
    Regularplus() {
      this.dialogVisibleseven.title === 'add'
      this.dialogVisibleseven.show = true
      this.Regularbatch = {}
    },
    // 确认添加 /payment-service/payment/category/insert
    // name group cron url parameter description vmParam jarPath status
    PayAdd() {
      const str = {
        name: this.Regularbatch.name,
        groupName: this.Regularbatch.groupName,
        cron: this.Regularbatch.cron,
        url: this.Regularbatch.url,
        parameter: this.Regularbatch.parameter,
        description: this.Regularbatch.description,
        vmParam: this.Regularbatch.vmParam,
        jarPath: this.Regularbatch.jarPath,
        status: this.Regularbatch.status,
        id: this.Regularbatch.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/schedulejob/job/insert`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisibleseven.show = false // 关闭弹框
          this.getRegularFun() // 刷新当前外汇利率列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 点击编辑出现弹框
    RegularEditFun(row) {
      this.dialogVisibleseven.show = true
      this.dialogVisibleseven.title = 'edit'
      this.Regularbatch = row
    },
    // 确认编辑 /payment-service/payment/category/update
    PayEdit() {
      const str = {
        name: this.Regularbatch.name,
        groupName: this.Regularbatch.groupName,
        cron: this.Regularbatch.cron,
        url: this.Regularbatch.url,
        parameter: this.Regularbatch.parameter,
        description: this.Regularbatch.description,
        vmParam: this.Regularbatch.vmParam,
        jarPath: this.Regularbatch.jarPath,
        status: this.Regularbatch.status,
        id: this.Regularbatch.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/schedulejob/job/update`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisibleseven.show = false // 关闭弹框
          this.getRegularFun() // 刷新当前外汇利率列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },

    // 定时跑批删除/payment-service/payment/category/delete
    RegularDelFun(row) {
      this.$confirm(this.$t('lbs.common.delMsg'), this.$t('lbs.common.tip'), {
        confirmButtonText: this.$t('lbs.common.confirm'),
        cancelButtonText: this.$t('lbs.common.cancel'),
        type: 'warning'
      }).then(() => {
        const str = {
          id: row.id,
          start: 0,
          end: 10000
        }
        axios.post(`${this.LBSGateway}/schedulejob/job/delete`, str, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.dialogVisibleseven.show = false // 关闭弹框
            this.getRegularFun() // 刷新参数列表
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        })
      }).catch(() => {})
    },

    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    }
  }

}
</script>

<style lang='scss' scoped>
  .modalTitle{
    width: 100%;
    height: 43px;
    padding-left: 10px;
    font-weight: 600;

    h4{
      line-height: 43px;
      font-size: 15px;
    }
  }
  .plus{
    font-size: 30px;
    font-weight: bold;
  }
</style>
