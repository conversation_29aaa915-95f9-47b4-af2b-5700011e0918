<template>
  <div class="userinformation">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.userInformation') }}</p>
        </div>
      </el-col>
    </el-row>
    <div class="main">
      <el-row>
        <el-col :span="8">
          <p class="title2">{{ $t('lbs.personalInformation') }}</p>
          <div class="image">
            <img alt="" src="./header.png">
          </div>
        </el-col>
        <el-col :span="16" class="userinfo">
          <el-row>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.name') }}</p>
              <p class="value">{{ userInfo.customerName }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.chineseName') }}</p>
              <p class="value">{{ userInfo.chinesename }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">ID</p>
              <p class="value">{{ userInfo.customerid }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.gender') }}</p>
              <p class="value">{{ userInfo.gender }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.nationality') }}</p>
              <p class="value">{{ userInfo.issuecountry }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.birthDay') }}</p>
              <p class="value">{{ $moment.parseZone(Number(userInfo.dateofbirth)).local().format("YYYY-MM-DD") }}</p>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="main">
      <el-row>
        <el-col :span="8">
          <div style="height: 190px">
            <p class="title2" style="margin: 110px 0 10px">{{ $t('lbs.contactInformation') }}</p>
            <div style="width: 100px;margin: 0 auto">
              <el-button style="width: 100px" type="info" icon="el-icon-edit" @click="editUserInfo()">{{ $t('lbs.edit') }}</el-button>
            </div>

          </div>
        </el-col>
        <el-col :span="16" class="userinfo" style="padding-top: 0">
          <el-row>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.mobilePhoneNumber') }}</p>
              <p class="value">{{ userInfo.mobilephonenumber }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.residentialAddress') }}</p>
              <p class="value">{{ userInfo.residentialaddress }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.mailingAddress') }}</p>
              <p class="value">{{ userInfo.mailingaddress }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.residencePhoneNumber') }}</p>
              <p class="value">{{ userInfo.residencephonenumber }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.companyEmail') }}</p>
              <p class="value">{{ userInfo.emailaddress }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.weChatId') }}</p>
              <p class="value">{{ userInfo.wechatid }}</p>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="main" style="border-bottom: none">
      <el-row>
        <el-col :span="8">
          <div style="height: 220px">
            <p class="title2" style="margin: 160px 0 10px">{{ $t('lbs.workingInformation') }}</p>
          </div>
        </el-col>
        <el-col :span="16" class="userinfo" style="padding-top: 0">
          <el-row>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.occupation') }}</p>
              <p class="value">{{ userInfo.occupation }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.employerCompanyName') }}</p>
              <p class="value">{{ userInfo.employercompanyname }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.position') }}</p>
              <p class="value">{{ userInfo.position }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.companyAddress') }}</p>
              <p class="value">{{ userInfo.companyaddress }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.companyPhoneNumber') }}</p>
              <p class="value">{{ userInfo.companyphonenumber }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.yearsOfServices') }}</p>
              <p class="value">{{ userInfo.yearsofservices }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.monthlySalary') }}</p>
              <p class="value">{{ userInfo.monthlysalary }}</p>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Userinformation',
  data() {
    return {
      userInfo: {}
    }
  },
  mounted() {
    this.userInfo = JSON.parse(window.sessionStorage.getItem('userInfo'))
  },
  methods: {
    editUserInfo() {
      this.$router.push({ path: '/lbsusercenter/edituserinfo' })
    }
  }
}
</script>

<style scoped>
  .userinformation .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .userinformation p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .userinformation .max-title {
    color: #22C1E6;
    font-size: 50px;
    margin-bottom: 40px;
    line-height: 80px;
    font-weight: 500;
  }

  .userinformation .main {
    margin: 40px 100px;
    border-bottom: 1px #cccccc solid;
    padding: 10px 0 40px
  }

  .userinformation .title2 {
    text-align: center;
    color: #707070;
    font-size: 40px;
    margin-bottom: 40px;
  }

  .userinformation .image {
    width: 308px;
    margin: 0 auto;
  }

  .userinformation .userinfo p{
    font-size: 25px;
    line-height: 53px;
    display: inline-block;
  }

  .userinformation .userinfo .value{
    font-size: 22px;
    color: #000000;
  }
  .userinformation .userinfo {
    padding-top: 40px;
  }
  .userinformation .key{
    width: 450px;
  }
</style>
