<template>
  <el-dialog
    append-to-body
    :visible.sync="dialogFormVisible"
    :before-close="hide"
    :title="group_name"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="form" :model="form" :rules="formrules" label-width="120px">
      <el-form-item :label="$t(&quot;Workflow.accountType&quot;)" prop="accountType">
        <el-select v-model="form.accountType" style="width:100%" @change="refreshccy">
          <el-option label="Saving Account" value="001" />
          <el-option label="Current Account" value="002" />
          <el-option label="Foreign Currency Account" value="003" />
          <el-option label="Term Deposit Account" value="100" />
          <el-option label="Stock Trading Account" value="300" />
          <el-option label="Precious Metal Account" value="400" />
          <el-option label="Mutual Fund Account" value="500" />
          <el-option label="Mortgage Loan" value="600" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.currencyCode&quot;)" prop="currencyCode">
        <el-select v-model="form.currencyCode" style="width:100%">
          <el-option v-for="item in ccyOptions" :key="item" :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.customerNumber&quot;)" prop="customerNumber">
        <el-input v-model="form.customerNumber" :maxlength="25" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.relaccountnumber&quot;)" prop="relaccountnumber">
        <el-input v-model="form.relaccountnumber" :maxlength="34" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="hide">{{ $t("lbs.common.cancel") }}</el-button>
      <el-button type="primary" @click="submit">{{ $t("lbs.common.confirm") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

import axios from 'axios'

export default {
  name: 'AccountOpening',
  props: {
    processInstanceId: {
      type: String,
      default: ''
    },
    group_name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      dialogFormVisible: true,
      userId: window.localStorage.getItem('username'),
      requestData: {
        'processInstanceId': this.processInstanceId,
        'group_name': 'eKYC',
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form: {
        accountType: '001',
        currencyCode: 'HKD',
        customerNumber: '',
        relaccountnumber: ''
      },
      ccyOptions: [],
      formrules: {
        customerNumber: [{ required: true, message: this.$t('Workflow.customerNumbererror'), trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    refreshccy() {
      const _this = this
      _this.form.currencyCode = 'HKD'
      if (_this.form.accountType === '003') {
        _this.ccyOptions = ['HKD', 'CNY', 'USD', 'AUD', 'EUR', 'CHF', 'CAD', 'GBP', 'JPY', 'NZD', 'SGD']
      } else {
        _this.ccyOptions = ['HKD']
      }
    },
    submit: function() {
      const _this = this
      _this.$refs['form'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.requestData.api_json = _this.form
          axios.post(`${this.workflowHost}/workflow/executeFlow`, _this.requestData,
            { headers: { token: window.localStorage.getItem('accesstoken') }})
            .then(response => {
              _this.loading = false
              if (response.data.code === 20000) {
                if (_this.form.accountType === '001' || _this.form.accountType === '002') {
                  _this.$alert('By default, the system will top up 200,000 - 500,000 HKD randomly as initial balance for the newly created saving and current account.', 'Message', {
                    confirmButtonText: _this.$t('lbs.common.confirm'),
                    callback: action => {
                      _this.$emit('refreshStatus')
                    }
                  })
                } else {
                  _this.$emit('refreshStatus')
                }
              } else {
                _this.$message.error(response.data.message)
                _this.$alert(response.data.data.response)
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$message.error(error.message)
            })
        }
      })
    },
    hide() {
      this.$emit('close')
    }
  }
}
</script>
