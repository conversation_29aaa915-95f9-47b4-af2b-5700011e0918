<template>
  <el-dialog
    append-to-body
    :visible.sync="dialogFormVisible"
    :before-close="hide"
    :title="group_name"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="form" :model="form" :rules="formrules" label-width="120px">
      <el-form-item :label="$t(&quot;Workflow.customerID&quot;)" prop="customerId">
        <el-input v-model="form.customerId" :maxlength="35" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.file&quot;)" prop="file">
        <el-upload
          ref="upload"
          class="upload-demo"
          action="/test"
          accept=".bmp,.jpg,.jpeg,.png"
          :auto-upload="false"
          :show-file-list="true"
          :drag="true"
          :limit="1"
          :on-change="exportData"
          :file-list="fileList"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            <em>{{ $t("lbs.ClickUpload") }}</em>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="hide">{{ $t("lbs.common.cancel") }}</el-button>
      <el-button type="primary" @click="submit">{{ $t("lbs.common.confirm") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

import axios from 'axios'

export default {
  name: 'CaptureSignature',
  props: {
    processInstanceId: {
      type: String,
      default: ''
    },
    group_name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      dialogFormVisible: true,
      userId: window.localStorage.getItem('username'),
      requestData: {
        'processInstanceId': this.processInstanceId,
        'group_name': 'eKYC',
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form: {
        customerId: 'U735535(9)',
        file: null
      },
      formrules: {
        customerId: [{ required: true, message: this.$t('Workflow.customerIDerror'), trigger: 'blur' }],
        file: [{ required: true, message: this.$t('Workflow.residentialaddresserror'), trigger: 'change' }]
      },
      fileList: []
    }
  },
  created() {},
  methods: {
    exportData(file, fileList) {
      const vue = this
      if (!file) {
        return
      }

      vue.form.file = file.raw
      vue.fileList = fileList.slice(-1) // 取最后一个元素
    },
    submit: function() {
      const _this = this
      _this.$refs['form'].validate(valid => {
        if (valid) {
          _this.loading = true
          const request = new FormData()
          request.append('file', _this.form.file)
          request.append('api_formdata', JSON.stringify({ customerId: _this.form.customerId }))
          request.append('processInstanceId', _this.processInstanceId)
          request.append('group_name', 'eKYC')
          axios.post(`${this.workflowHost}/workflow/executeUploadFlow`, request,
            { headers: { token: window.localStorage.getItem('accesstoken') }})
            .then(response => {
              _this.loading = false
              if (response.data.code === 20000) {
                _this.$emit('refreshStatus')
              } else {
                _this.$message.error(response.data.message)
                _this.$alert(response.data.data.response)
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$message.error(error.message)
            })
        }
      })
    },
    hide() {
      this.$emit('close')
    }
  }
}
</script>
