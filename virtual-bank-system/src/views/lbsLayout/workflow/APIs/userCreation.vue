<template>
  <el-dialog
    append-to-body
    :visible.sync="dialogFormVisible"
    :before-close="hide"
    :title="group_name"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="form" :model="form" :rules="formrules" label-width="120px">
      <el-form-item :label="$t(&quot;Workflow.developerID&quot;)" prop="developerID">
        <el-input v-model="form.developerID" :maxlength="50" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.customerNumber&quot;)" prop="customerNumber">
        <el-input v-model="form.customerNumber" :maxlength="25" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.loginName&quot;)" prop="loginName">
        <el-input v-model="form.loginName" :maxlength="50" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.loginPwd&quot;)" prop="loginPwd">
        <el-input v-model="form.loginPwd" :maxlength="50" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="hide">{{ $t("lbs.common.cancel") }}</el-button>
      <el-button type="primary" @click="submit">{{ $t("lbs.common.confirm") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

import axios from 'axios'

export default {
  name: 'SetPin',
  props: {
    processInstanceId: {
      type: String,
      default: ''
    },
    group_name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      dialogFormVisible: true,
      userId: window.localStorage.getItem('username'),
      requestData: {
        'processInstanceId': this.processInstanceId,
        'group_name': 'eKYC',
        'api_header': [{ token: '1' }],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form: {
        developerID: '001',
        loginName: '',
        loginPwd: '',
        customerNumber: ''
      },
      formrules: {
        developerID: [{ required: true, message: this.$t('Workflow.developerIDerror'), trigger: 'blur' }],
        loginName: [{ required: true, message: this.$t('Workflow.loginNameerror'), trigger: 'blur' }],
        loginPwd: [{ required: true, message: this.$t('Workflow.loginPwderror'), trigger: 'blur' }],
        customerNumber: [{ required: true, message: this.$t('Workflow.customerNumbererror'), trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    submit: function() {
      const _this = this
      _this.$refs['form'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.requestData.api_json = _this.form
          axios.post(`${this.workflowHost}/workflow/executeFlow`, _this.requestData,
            { headers: { token: window.localStorage.getItem('accesstoken') }})
            .then(response => {
              _this.loading = false
              if (response.data.code === 20000) {
                _this.$emit('refreshStatus')
              } else {
                _this.$message.error(response.data.message)
                _this.$alert(response.data.data.response)
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$message.error(error.message)
            })
        }
      })
    },
    hide() {
      this.$emit('close')
    }
  }
}
</script>
