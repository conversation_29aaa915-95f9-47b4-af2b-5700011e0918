{"name": "virtual-credit-card-system", "version": "4.4.0", "description": "virtual credit card system ui.", "author": "", "scripts": {"dev": "vue-cli-service serve", "build:simnectz": "vue-cli-service build --mode simnectz", "build:cuhk": "vue-cli-service build --mode cuhk", "build:hsu": "vue-cli-service build --mode hsu", "preview": "node build/index.js --preview", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src --fix", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"axios": "1.6.5", "core-js": "^3.6.5", "element-ui": "2.15.14", "js-cookie": "2.2.0", "moment": "^2.24.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qs": "^6.11.2", "vue": "2.6.10", "vue-i18n": "7.3.2", "vue-router": "3.0.6", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-service": "^3.3.1", "autoprefixer": "^8.0.0", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "runjs": "4.1.3", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}