import { post } from '@/api/utils/http'

const baseUrl = '/creditcard-experience/credit-card'

export function creditCardOpenApplication(data) {
  return post(`${baseUrl}/account/open-application`, data)
}

export function getCreditCardMasterList(data) {
  return post(`${baseUrl}/account/list`, data)
}

export function accountOpenActivation(data) {
  return post(`${baseUrl}/account/open-activation`, data)
}

export function getCreditCardMasterDetails(data) {
  return post(`${baseUrl}/account/details`, data)
}

export function getCreditCardTransactionDetailsPage(data) {
  return post(`${baseUrl}/transaction/list`, data)
}

export function repayment(data) {
  return post(`${baseUrl}/transaction/repayment`, data)
}

export function lossReporting(data) {
  return post(`${baseUrl}/maintenance/loss-reporting`, data)
}

export function cancellation(data) {
  return post(`${baseUrl}/maintenance/cancellation`, data)
}

export function limitAdjustment(data) {
  return post(`${baseUrl}/limit/adjustment`, data)
}

export function getCreditCardStatementPage(data) {
  return post(`${baseUrl}/statement/page`, data)
}

export function getOutstandingAmount(data) {
  return post(`${baseUrl}/statement/outstanding-amount`, data)
}

export function enquiryPendingStatement(data) {
  return post(`${baseUrl}/statement/pending-details`, data)
}
