import request from '@/utils/request'
import { post } from '@/api/utils/http'

const baseUrl = '/creditcard-experience/credit-card/customer'

export function reference(data) {
  return request({
    url: `${baseUrl}/reference`,
    method: 'post',
    data
  })
}

export function getCreditCardDetails(data) {
  return post(`${baseUrl}/credit-card-details`, data)
}

export function getStatementDetails(data) {
  return post(`${baseUrl}/statement-details`, data)
}

export function getDemographicsDetails(data) {
  return post(`${baseUrl}/demographics-details`, data)
}

export function getCreditDetails(data) {
  return post(`${baseUrl}/credit-details`, data)
}

export function getPortfolioDetails(data) {
  return post(`${baseUrl}/portfolio-details`, data)
}
