import request from '@/utils/request'

const baseUrl = '/creditcard-experience/credit-card'

export function getCreditCardLimitAdjustmentList() {
  return request({
    url: `${baseUrl}/creditCardLimitAdjustment/list`,
    method: 'post'
  })
}

export function getCreditCardLimitAdjustmentPage(data) {
  return request({
    url: `${baseUrl}/limit/adjustment-page`,
    method: 'post',
    data
  })
}

export function deleteCreditCardLimitAdjustment(id) {
  return request({
    url: `${baseUrl}/creditCardLimitAdjustment/delete`,
    method: 'post',
    data: { id }
  })
}

export function createCreditCardLimitAdjustment(data) {
  return request({
    url: `${baseUrl}/creditCardLimitAdjustment/create`,
    method: 'post',
    data
  })
}

export function updateCreditCardLimitAdjustment(data) {
  return request({
    url: `${baseUrl}/creditCardLimitAdjustment/update`,
    method: 'post',
    data
  })
}

export function reviewCreditCardLimitAdjustment(data) {
  return request({
    url: `${baseUrl}/limit/approval`,
    method: 'post',
    data
  })
}

