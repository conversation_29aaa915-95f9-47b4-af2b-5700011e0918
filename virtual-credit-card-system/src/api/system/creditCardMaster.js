import request from '@/utils/request'

const baseUrl = '/creditcard-experience/credit-card'

export function getCreditCardMasterPage(data) {
  return request({
    url: `${baseUrl}/creditCardMaster/page`,
    method: 'post',
    data
  })
}

export function deleteCreditCardMaster(id) {
  return request({
    url: `${baseUrl}/creditCardMaster/delete`,
    method: 'post',
    data: { id }
  })
}

export function createCreditCardMaster(data) {
  return request({
    url: `${baseUrl}/creditCardMaster/create`,
    method: 'post',
    data
  })
}

export function updateCreditCardMaster(data) {
  return request({
    url: `${baseUrl}/creditCardMaster/update`,
    method: 'post',
    data
  })
}

export function accountLossReporting(data) {
  return request({
    url: `${baseUrl}/account/open-activation`,
    method: 'post',
    data
  })
}
