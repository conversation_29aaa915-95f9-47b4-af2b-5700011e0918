import request from '@/utils/request'

const baseUrl = '/creditcard-experience/credit-card/statement'

export function getCreditCardStatementList() {
  return request({
    url: `${baseUrl}/list`,
    method: 'post'
  })
}

export function deleteCreditCardStatement(id) {
  return request({
    url: `${baseUrl}/creditCardStatement/delete`,
    method: 'post',
    data: { id }
  })
}

export function createCreditCardStatement(data) {
  return request({
    url: `${baseUrl}/creditCardStatement/create`,
    method: 'post',
    data
  })
}

export function updateCreditCardStatement(data) {
  return request({
    url: `${baseUrl}/creditCardStatement/update`,
    method: 'post',
    data
  })
}

