{"addRole": "添加角色", "editRole": "編輯角色", "staffNumberIsRequired": "員工編號不能為空白", "departmentIsRequired": "部門不能為空", "roleIsRequired": "角色不能為空", "usernameIsRequired": "用戶名不能為空", "emailIsRequired": "電子郵件不能為空", "pwdIsRequired": "密碼不能為空", "emailAddress": "電子郵箱", "username": "用戶名", "CustomerNumber": "客戶編號", "industryNum": "收款人產業編號", "Payeeindustry": "收款人行業", "UpdateTime": "更新時間", "Cteatime": "建立時間", "proCode": "產品代碼", "Currencytype": "貨幣類型", "Minimum": "貸款最低金額", "Maximum": "貸款最大金額", "Lowinterest": "低利率", "Highinteres": "高利率", "Currencyname": "貨幣名稱", "Currencyabbreviation": "貨幣簡稱", "Decimalplaces": "小數位", "BnakBuy": "銀行買入價", "BankSell": "銀行賣出價", "FixedAmount": "定存金額範圍", "Deposit": "存款期間", "depositrate": "存款利率", "selectDataType": "選擇流程類型", "eKYC": "創建eKYC", "insurance": "創造保險理賠", "country": "國家", "Sensitive": "敏感權限", "depositLimit": "存款限額", "withdrawalLimit": "提款限額", "transferLimit": "轉帳限額", "paymentLimit": "支付限額", "fxLimit": "外匯限額", "termDepositLimit": "定存限額", "stockLimit": "股票限額", "bondLimit": "債券限額", "fundLimit": "基金限額", "describe": "描述", "applicant": "申請人", "date": "日期", "OperationType": "操作類型", "type": "型別", "ApplicationNo": "申請號碼", "submitTime": "提交時間", "Status": "狀態", "Application": "申請表", "SourcedataBase": "來源資料庫", "parameterT": "系統參數表", "Fixeddeposit": "存款利率表", "Foreign": "外匯利率表", "Holiday": "假日配置表", "loanamount": "個人貸款金額配置表", "Payee": "支付收款人資料表", "batch": "定時跑批表", "parameterN": "參數", "value": "值", "remarks": "備註", "PleaseObj": "請輸入操作物件", "Pleasedesc": "請輸入權限描述", "Pleasepermiss": "請輸入權限介面", "Pleaseinterface": "請填寫介面額度審核欄位", "opearObject": "操作物件", "description": "權限描述", "interface": "權限介面", "approvalfield": "介面額度審核欄位", "parameter": "系統參數配置", "permissionconfig": "員工權限配置", "pleaseSelect": "請選擇", "Retailquota": "零售額度", "Industrialcial": "工商額度", "Employeeconfig": "員工額度配置", "departmentC": "員工部門與角色配置", "query": "查詢", "Emplonumber": "員工編號", "EmplonumberMsg": "請選擇員工編號", "department": "部門", "departments": "部門", "rolequota": "員工角色與額度配置", "role": "角色", "Retail": "零售額度", "Industria": "工商額度", "UpdateT": "更新時間", "Configuration": "設定時間", "operation": "操作", "operate": "系統運維", "jurisdiction": "權限", "Employee": "員工列表", "Workflow": "工作流程", "WorkflowNodeManage": "節點管理", "Add": "新增", "Edit": "編輯", "Delete": "刪除", "Cancel": "取消", "Confirm": "確認", "AddTip": "你確定要新增嗎？", "DeleteTip": "你確定要刪除嗎？", "UpdateTip": "你確定要更新嗎？", "CanNotEmptyTip": "該欄位不能為空白", "OperatingSuccess": "操作成功", "Tip": "提示", "Operate": "操作", "Name": "名稱", "Group": "分組（中文）", "GroupEn": "分組（英文）", "ApiUrl": "API位址", "ApiMethod": "API方法", "ApiHeader": "api_header", "ApiParams": "api_params", "CreateDate": "建立日期", "EnterNameTip": "請輸入節點名稱", "EnterGroupTip": "請輸入節點分組", "EnterApiUrlTip": "請輸入 api_url", "EnterApiMethodTip": "請輸入 api_method", "EnterApiHeaderTip": "請輸入 api_header", "EnterApiParamsTip": "請輸入 api_params", "AddFlow": "新增節點", "EditFlow": "編輯節點", "WorkflowManage": "規則管理", "Json": "Json", "LastUpdateDate": "更新時間", "Save": "儲存", "Update": "更新", "AddWorkflow": "新增規則", "UpdateWorkflow": "更新規則", "createworkflow": "建立工作流程", "PublicWorkflow": "公共工作流程", "CustomWorkflow": "自訂工作流程", "desc": "分組", "kyc": "KYC - 開戶流程", "ProcessGroup": "流程群組", "Process": "步驟", "nodelist": "開戶流程節點清單", "nodeInsurancelist": "保險理賠流程節點清單", "rules": "開戶流程節點規則", "tip1": "提示：使用公共節點建立您的Workflow。", "tip2": "<p> <span>請依照以下說明開始建立Workflow：</span> </p> <p> <span>1.Workflow建立工具</span> </p> <p> < span>&nbsp; &nbsp; 點選<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow建立工具</b></a>跳到線上編輯工具。工作流程中點選下載按鈕，得到公用工作流程範本點選下載。 &nbsp; 在開啟的工具頁面中點選open，選擇上一個步驟下載得到的EKYC.bpmn檔。 > <span>&nbsp; &nbsp; 在workflow範本的基礎上開始建立您的workflow。 <span>&nbsp; &nbsp; 在工具頁面左下角點擊「下載」圖標，儲存您建立的工作流程文件，然後點擊右側的「上傳文件」上傳您儲存的工作流程檔案。 > <p> <br/> </p> <p> <span>Workflow建立工具的詳細操作手冊，請<a target=\"_blank\" href=\"https://simnectzplatform.com/platform/ api/portal/developers/wk/download\"><b>點選下載</b></a>。</span> </p> <p> <span>使用公用Node建立workflow，需要遵守Node之間的順序規則，請參考「<a target=\"_blank\" href=\"/tooling/nodeRules?type=KYC\"})\"><b>公共Node規則</b></a> 」。 </span> </p> <p> <br/> </p>", "insurancetip1": "提示：使用公共節點建立您的Workflow。", "insurancetip2": "<p> <span>請依照以下說明開始建立Workflow：</span> </p> <p> <span>1.Workflow建立工具</span> </p> <p> < span>&nbsp; &nbsp; 點選<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow建立工具</b></a>跳到線上編輯工具。工作流程中點選下載按鈕，得到公用工作流程範本點選下載。 &nbsp; 在開啟的工具頁面中點選open，選擇上一個步驟下載得到的insurance_claim.bpmn檔。 > <span>&nbsp; &nbsp; 在workflow範本的基礎上開始建立您的workflow。 <span>&nbsp; &nbsp; 在工具頁面左下角點擊「下載」圖標，儲存您建立的工作流程文件，然後點擊右側的「上傳文件」上傳您儲存的工作流程檔案。 > <p> <br/> </p> <p> <span>Workflow建立工具的詳細操作手冊，請<a target=\"_blank\" href=\"https://simnectzplatform.com/platform/ api/portal/developers/wk/download\"><b>點選下載</b></a>。</span> </p> <p> <span>使用公用Node建立workflow，需要遵守Node之間的順序規則，請參考「<a target=\"_blank\" href=\"/tooling/nodeRules?type=insurance\"})\"><b>公共Node規則</b></a> 」。 </span> </p> <p> <br/> </p>", "class": "型別", "file": "檔案", "selectworkflow": "選擇工作流程", "workflowStatus": "工作流程運作狀態", "accommodation": "住宅狀況", "branchcode": "分行代碼", "chinesename": "中文名", "clearingcode": "銀行代碼", "companyaddress": "公司地址", "companyphonenumber": "公司電話", "countrycode": "國家代碼", "customerID": "個人ID", "customerIDType": "證件類型", "dateOfBirth": "出生日期", "education": "教育程度", "emailaddress": "電子郵件", "employercompanyname": "公司名稱", "firstname": "名", "gender": "性別", "issueCountry": "簽發國家", "proofOfAddress": "地址證明", "proofOfAddressError": "請上傳地址證明", "lastname": "姓", "mailingAddress": "郵寄地址", "maritalstatus": "婚姻狀況", "mobilePhoneNumber": "行動電話", "monthlysalary": "每月收入", "nationality": "國籍", "occupation": "職業", "permanentresidencestatus": "永久居留身分", "position": "職位", "residencephonenumber": "住宅電話", "residentialaddress": "住宅地址", "wechatid": "微訊號", "yearsofresidence": "居住年限", "yearsofservices": "工作年限", "accountType": "帳戶類型", "currencyCode": "貨幣代號", "customerNumber": "用戶編號", "relaccountnumber": "關聯帳戶", "acknowledge": "確認條款", "accountNumber": "用戶其他銀行帳戶", "accountNumbererror": "請填寫您的其他銀行帳戶", "fileerror": "請上傳檔案,檔案格式：bmp,jpg,jpeg,png,大小在1MB以內", "customerIDerror": "客戶在其國家/地區的唯一識別。最大長度：35", "dateOfBirtherror": "客戶的生日", "firstnameerror": "最大長度：70", "lastnameerror": "最大長度：70", "gendererror": "性別不能為空", "issueCountryerror": "客戶ID的核發國家。最大長度：20", "mailingAddresserror": "您的郵件可以寄到的地址。最大長度：1000", "mobilePhoneNumbererror": "客戶的手機號碼。最大長度：34", "residentialaddresserror": "客戶居住地址。最大長度：1000", "accountTypeerror": "帳戶類型指帳戶是否為儲蓄帳戶、活期帳戶、股票交易帳戶等。可能值：001（儲蓄帳戶）；002（活期帳戶）；003（外幣帳戶）；100（定期存款帳戶）； 300（股票交易帳戶）；400（貴金屬帳戶）；500（共同基金帳戶）；600（抵押貸款）最大長度：3", "currencyCodeerror": "為帳戶設定的貨幣。請根據帳戶類型設定正確的幣種類型。目前，儲蓄/活期/定期存款/貸款/基金/股票/貴金屬帳戶目前只支援港幣；外匯帳戶支援以下11種貨幣：港幣、人民幣、美元、澳幣、歐元、瑞士法郎、加幣、英鎊、日圓、紐元、新加坡元。", "customerNumbererror": "在銀行系統中建立新客戶記錄時產生的唯一ID。最大長度：25", "relAccountNumberError": "請輸入關聯帳戶", "developerIDerror": "在SIMNECTZ API平台中註冊的開發者ID。最大長度：50", "loginNameerror": "為登入帳號設定的登入名稱。最大長度：50", "loginPwderror": "為登入帳號設定的登入密碼。最大長度：50", "developernameerrorerror": "開發者姓名。最大長度：140", "nationalityError": "客戶的國籍。最大長度：20", "monthlySalaryError": "客戶的月收入。最小值：0", "countryCodeError": "國家代碼不能為空", "clearingCodeError": "銀行代碼不能為空", "definition_key": "編號", "status": "狀態", "createcustomer": "建立客戶", "view": "查看結果", "resume": "恢復", "viewReponse": "檢視資料", "run": "運行", "uploadtip": "只上傳*.bpmn文件，檔案大小不能超過2MB。", "flowNameerror": "請填入工作流程名稱", "deleteTip": "確定刪除嗎？", "developerID": "用戶ID", "developername": "用戶姓名", "email": "電子郵件", "loginName": "登入帳號", "loginPwd": "登入密碼", "CustomerProfile": "填寫用戶資料", "finishTip": "工作流程已結束：", "createSuccess": "開戶成功!", "complete": "工作流程結束", "hasbeenlatestnode": "已經是最後一個節點了", "Biometrics": "人臉辨識", "Signature": "簽名", "CheckingBlacklist": "黑名單偵測", "CheckingBlacklistTip": "正在偵測用戶是否被列入黑名單，請等待...", "CheckingSanction": "制裁名單偵測", "CheckingSanctionTip": "正在偵測用戶是否被列入製裁名單，請等待...", "PEPTip": "正在偵測用戶是否被列入政治公共人物名單，请等待...", "VerifyAddress": "住址偵測", "VerifyAddressTip": "正在偵測用戶住址是否真實有效，請等待...", "VerifyBiometrics": "驗證生物特徵", "AccountOpening": "開戶", "AcknowledgeTC": "確認用戶條款", "CaptureSignature": "提交簽名", "CRS": "共同申報準則偵測", "PEP": "", "FATCA": "海外帳戶稅務合規檢測", "userCreation": "設定帳號", "SetPin": "登記帳號資料", "AccountOpeningMsg": "根據指定的帳戶類型建立新帳戶。", "AcknowledgeTCMsg": "當用戶做一些操作時，首先需要同意協議。", "CaptureDemographicMsg": "所有銀行服務都以客戶為基礎。在開戶或使用其他服務之前，您需要建立新客戶記錄，請提交您的客戶資訊。", "CaptureSignatureMsg": "透過這個介面，可以上傳簽名照片並綁定到對應的客戶。", "CheckBlacklistMsg": "如果用戶資訊在黑名單上，則無法執行後續操作。", "CheckSanctionMsg": "檢查客戶是否被列入製裁名單。", "PEPMsg": "檢查客戶是否被列入政治公共人物名單。", "CRSMsg": "檢查用戶資料是否提交至CRS。填寫客戶的任何其他銀行卡帳戶進行檢測。", "FATCAMsg": "檢查用戶資料是否提交給FATCA。填寫客戶的任何其他銀行卡帳戶進行偵測。", "SetPinMsg": "在客戶進行交易之前建立銀行系統的登入帳戶。", "userCreationMsg": "登記網銀帳戶的暱稱。", "VerifyAddressMsg": "驗證用戶住址是否真實有效。", "VerifyBiometricsMsg": "人臉辨識。提交包含客戶臉部資訊的照片。", "acknowledgeerror": "請勾選同意最終用戶隱私權政策。", "currentStatus": "工作流程狀態：", "addressReview": "地址審核", "addressReviewError": "請選擇地址審核", "invalidAddress": "地址審核不通過", "preview": "預覽", "download": "下載", "CreditLimitAllocation": "信用額度分配", "CreditLimitAllocationMsg": "為客戶帳戶分配信用額度", "privacyPolicyContent": "<p>親愛的客戶：</p><p><br></p><p>感謝您選擇本銀行為您的金融服務提供者。為了保障您的資金安全、維護雙方權益，並遵循相關法律法規要求，特此向您明確個人銀行開戶的相關聲明條款。請您在仔細閱讀並充分理解以下內容後，請進行開戶操作或簽署相關文件。</p><p><br></p><p>一、客戶身分確認與資訊真實性</p><p><br></p><p>本人聲明：本人自願向本銀行申請開立個人銀行帳戶，並承諾所提供的所有個人資料（包括但不限於姓名、性別、出生日期、國籍、聯絡方式、職業、住址等）均真實、準確、完整、有效，無虛假、誤導或遺漏之處。</p><p>驗證：本人同意並授權本行透過合法途徑（包括但不限於公安部門、徵信機構等）核實本人身分資訊的真實性，並接受銀行為完成開戶流程所進行的必要身分驗證措施。</p><p><br></p><p>二、帳戶使用與管理</p><p><br></p><p>合法合規使用：本人承諾將嚴格按照國家法律法規、監管要求及本銀行的相關規定使用帳戶，不進行任何違法、違規或損害銀行及其他客戶利益的活動，包括但不限於洗錢、恐怖融資、逃稅、欺詐等行為。</p><p><br></p><p>帳戶安全：本人將妥善保管帳戶密碼、電子銀行登入資訊、交易驗證碼等敏感訊息，不洩露給任何第三方，並承擔因個人保管不善導致的帳戶資金損失風險。</p><p><br></p><p>及時通知：如本人聯絡方式、住址等個人資料發生變更，或發現帳戶有異常交易、被竊用等風險狀況，將立即通知本銀行，並配合銀行採取相應措施。</p><p><br></p><p>三、費用與利率</p><p><br></p><p>費用說明：本人已了解並同意依本銀行公佈的收費標準支付帳戶管理費、交易手續費等相關費用。具體費用標準以銀行最新公告為準。</p><p><br></p><p>利率政策：本銀行帳戶的存款利率及計息方式遵循中國人民銀行及本銀行的相關規定執行，具體以銀行公告為準。</p><p><br></p><p>四、隱私保護</p><p><br></p><p>本銀行承諾將嚴格遵守《中華人民共和國個人資訊保護法》等法律法規，對客戶的個人資訊採取嚴格的安全保密措施，未經客戶同意，不向任何第三方披露客戶的個人資訊，但法律法規另有規定或經客戶授權的情形除外。</p><p><br></p><p>五、協議變更與終止</p><p><br></p><p>協議變更：本銀行有權依法規、監理要求及業務需要調整本聲明條款。如有變更，銀行將透過官方網站、營業據點等管道公告，客戶應定期查閱並遵守最新條款。</p><p><br></p><p>帳戶終止：如客戶需註銷帳戶，應事先向本銀行提出書面申請，並依銀行規定辦理相關手續。帳戶註銷後，本銀行將停止提供相應服務，但保留依法法規要求處理帳戶資訊的權利。</p><p><br></p><p>六、爭議解決</p><p><br></p><p>雙方因執行本聲明條款發生的爭議，應先透過友好協商解決；協商不成時，可提交至本銀行所在地人民法院訴訟解決。</p><p><br></p><p>七、其他</p><p><br></p><p>本聲明條款自客戶簽署確認之日起生效，並與本銀行其他相關規定共同構成雙方之間的完整協議。</p>", "endUserPrivacyPolicy": "同意最終用戶隱私政策"}