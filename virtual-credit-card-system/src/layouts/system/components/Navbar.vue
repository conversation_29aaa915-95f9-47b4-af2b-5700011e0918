<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-container">
      <div class="login-details">
        <span>
          {{ $t('vcs.common.username') }}: {{ userDetails.loginName }}
        </span>
        <span>
          {{ $t('vcs.common.role') }}: {{ roles[0] }}
        </span>
      </div>

      <div class="language-select">
        <language />
      </div>

      <div class="right-menu">

        <el-dropdown class="avatar-container" trigger="click">
          <div class="avatar-wrapper">
            <img :src="defaultAvatar" width="32px" class="user-avatar">
            <i class="el-icon-caret-bottom" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <el-dropdown-item @click.native="logout">
              <span style="display:block;">{{ $t('vcs.authorize.logout') }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import Hamburger from '@/components/Hamburger/index.vue'
import Language from '@/components/Language/index.vue'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    Language
  },
  data() {
    return {
      defaultAvatar: require(`@/assets/images/avatar.png`)
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'userDetails',
      'roles'
    ])
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    logout() {
      this.$store.dispatch('user/logout')
      this.$router.push('/authorize/login')
    }
  }
}
</script>
<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, .025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .language-select {
    height: 50px;
    display: flex;
    align-items: center;
    float: right;
    margin-right: 15px;
  }

  .right-container {
    float: right;
    display: flex;
    align-items: center;
    height: 100%;

    .login-details {
      display: flex;
      align-items: center;
      margin-right: 15px;
      font-size: 14px;
      color: #333;

      span {
        margin-right: 10px;
        font-size: 16px;
      }
    }

    .right-menu {
      height: 100%;
      line-height: 50px;

      &:focus {
        outline: none;
      }

      .avatar-container {
        margin-right: 30px;

        .avatar-wrapper {
          margin-top: 5px;
          position: relative;

          .user-avatar {
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 10px;
          }

          .el-icon-caret-bottom {
            cursor: pointer;
            position: absolute;
            right: -20px;
            top: 25px;
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
