import Layout from '@/layouts/system/index.vue'

export default [
  {
    path: '/credit-card',
    component: Layout,
    name: 'credit-card',
    redirect: '/credit-card/overview',
    meta: { title: 'creditCard', icon: 'credit-card', role: 'customer' },
    children: [
      {
        path: 'overview',
        name: 'overview',
        component: () => import('@/views/credit-card/overview/index.vue'),
        meta: { title: 'creditCardOverview', icon: 'credit-card', role: 'customer' }
      },
      {
        path: 'details/:creditCardNumber',
        name: 'details',
        hidden: true,
        component: () => import('@/views/credit-card/details/Details.vue'),
        meta: { title: 'creditCardDetails', icon: 'application', role: 'customer' }
      },
      {
        path: 'activation/:creditCardNumber/:customerId',
        name: 'activation',
        hidden: true,
        component: () => import('@/views/credit-card/activation/Activation.vue'),
        meta: { title: 'creditCardActivation', icon: 'application', role: 'customer' }
      },
      {
        path: 'cancellation/:creditCardNumber',
        name: 'cancellation',
        hidden: true,
        component: () => import('@/views/credit-card/cancellation/Cancellation.vue'),
        meta: { title: 'creditCancellation', icon: 'application', role: 'customer' }
      },
      {
        path: 'limit-adjustment/:creditCardNumber',
        name: 'limit-adjustment',
        hidden: true,
        component: () => import('@/views/credit-card/limit-adjustment/LimitAdjustment.vue'),
        meta: { title: 'creditCardLimitAdjustment', icon: 'application', role: 'customer' }
      },
      {
        path: 'loss-reporting/:creditCardNumber',
        name: 'loss-reporting',
        hidden: true,
        component: () => import('@/views/credit-card/loss-reporting/LossReporting.vue'),
        meta: { title: 'creditCardLossReporting', icon: 'application', role: 'customer' }
      },
      {
        path: 'repayment/:creditCardNumber',
        name: 'repayment',
        hidden: true,
        component: () => import('@/views/credit-card/repayment/Repayment.vue'),
        meta: { title: 'creditCardRepayment', icon: 'application', role: 'customer' }
      },
      {
        path: 'statement/:creditCardNumber',
        name: 'statement',
        hidden: true,
        component: () => import('@/views/credit-card/statement/Statement.vue'),
        meta: { title: 'creditCardStatement', icon: 'application', role: 'customer' }
      }
    ]
  }
]
