import { getInfo } from '@/api/system/user'
import { login } from '@/api/system/authorize'
import { getLoginPk, removeLoginPk, setLoginPk } from '@/utils/auth'
import { refreshRouter, resetRouter } from '@/router'
import { enquiryAllAccount } from '@/api/system/accounts'

const getDefaultState = () => {
  return {
    accessToken: '',
    loginPk: getLoginPk(),
    avatar: '',
    roles: [],
    permissions: [],
    userDetails: null,
    debitAccounts: null
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_ACCESS_TOKEN: (state, accessToken) => {
    state.accessToken = accessToken
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSION: (state, permissions) => {
    state.permissions = permissions
  },
  SET_USER_DETAILS: (state, userDetails) => {
    state.userDetails = userDetails
  },
  SET_LOGIN_PK: (state, loginPk) => {
    state.loginPk = loginPk
  },
  SET_DEBIT_ACCOUNTS: (state, debitAccounts) => {
    state.debitAccounts = debitAccounts
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { /* customerNumber, */username, password } = userInfo

    return new Promise((resolve, reject) => {
      login({ loginname: username.trim(), loginpwd: password/*, customernumber: customerNumber.trim()*/ }).then(response => {
        const { data } = response
        setLoginPk(data.loginPk)
        commit('SET_LOGIN_PK', data.loginPk)
        refreshRouter()
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit }, loginPk) {
    return new Promise((resolve, reject) => {
      getInfo(loginPk).then(response => {
        const { data: userDetails } = response

        if (!userDetails) {
          return reject('Verification failed, please Login again.')
        }

        const { role, permissions, token } = userDetails
        commit('SET_ACCESS_TOKEN', token)
        commit('SET_ROLES', (role ? [role] : []).map(item => item.toLowerCase()))
        commit('SET_PERMISSION', permissions || [])
        delete userDetails.token
        commit('SET_USER_DETAILS', userDetails)
        resolve(userDetails)
      }).catch(error => {
        reject(error)
      })
    })
  },

  getAccounts({ commit }, customerNumber) {
    return new Promise((resolve, reject) => {
      enquiryAllAccount(customerNumber).then(res => {
        const { data: accounts } = res

        function getAccountOptions(accounts) {
          const needAccountType = Object.keys(accounts).filter(item => ['saving', 'current'].includes(item))

          const options = []
          needAccountType.forEach(accountType => {
            const o = accounts[accountType].map(account => {
              return {
                label: account.accountNumber,
                value: account.accountNumber
              }
            })
            if (o && o.length > 0) {
              options.push({
                label: getLabel(accountType),
                options: o
              })
            }
          })
          return options
        }

        function getLabel(accountType) {
          if (accountType === 'saving') {
            return `vcs.common.savingAccount`
          }
          if (accountType === 'current') {
            return `vcs.common.currentAccount`
          }
        }

        commit('SET_DEBIT_ACCOUNTS', getAccountOptions(accounts))
        resolve(accounts)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    removeLoginPk() // must remove token first
    resetRouter()
    commit('RESET_STATE')
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeLoginPk() // must remove token first
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

