<template>
  <div class="dashboard-container">
    <div class="dashboard-text">Username: {{ userDetails.loginName }}</div>
    <div class="dashboard-text">Roles: {{ roles }}</div>
    <div class="dashboard-text">Permissions: {{ permissions }}</div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Dashboard',
  computed: {
    ...mapGetters([
      'userDetails',
      'roles',
      'permissions'
    ])
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}
</style>
