<template>
  <div class="login-container">

    <div class="header">
      <img class="logo" :src="logo" alt="logo" style="" @click="officialWebsite()">
      <h1>{{ $t('vcs.common.platformName') }}</h1>
    </div>

    <el-row class="content">
      <el-col :span="10" :xs="24" class="content-left">
        <img class="authorize-image" src="../../../assets/images/authorize.jpg" alt="">
        <div class="by" style="cursor:pointer;float: right;margin-top: 10px" @click="officialWebsite()">
          {{ $t('vcs.common.copyright') }}
        </div>
      </el-col>
      <el-col :span="14" :xs="24" class="content-right">
        <div class="content-container">
          <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
            <div class="title-container">
              <h3 class="title">{{ $t('vcs.router.login') }}</h3>
            </div>

            <div class="language-select">
              <Language />
            </div>

            <!--            <el-form-item prop="customerNumber" class="form-item">-->
            <!--              <span class="svg-container">-->
            <!--                <svg-icon icon-class="user" />-->
            <!--              </span>-->
            <!--              <el-input-->
            <!--                ref="customerNumber"-->
            <!--                v-model="loginForm.customerNumber"-->
            <!--                :placeholder="$t('vcs.authorize.customerNumberPlaceholder')"-->
            <!--                name="customerNumber"-->
            <!--                type="text"-->
            <!--                tabindex="1"-->
            <!--                auto-complete="off"-->
            <!--              />-->
            <!--            </el-form-item>-->

            <el-form-item prop="username" class="form-item">
              <span class="svg-container">
                <svg-icon icon-class="user" />
              </span>
              <el-input
                ref="username"
                v-model="loginForm.username"
                :placeholder="$t('vcs.authorize.usernamePlaceholder')"
                name="username"
                type="text"
                tabindex="2"
                auto-complete="off"
              />
            </el-form-item>

            <el-form-item prop="password" class="form-item">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
              <el-input
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                :placeholder="$t('vcs.authorize.passwordPlaceholder')"
                name="password"
                tabindex="2"
                auto-complete="off"
                @keyup.enter.native="handleLogin"
              />
              <span class="show-pwd" @click="showPwd">
                <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
              </span>
            </el-form-item>

            <el-button :loading="loading" type="primary" class="login-button" @click.native.prevent="handleLogin">
              {{ $t('vcs.authorize.login') }}
            </el-button>

            <el-row>
              <el-col :span="16">
                <el-link :underline="false" type="primary" class="authorize-apply-link" @click="goLink('/authorize/apply')">{{ $t('vcs.router.creditCardApply') }}</el-link>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-col>

    </el-row>
    <div class="footer">
      <el-link :href="getOfficialWebsiteHome()" target="_blank" class="footer-link">@2024 SIMNECTZ Inc.</el-link>
      <el-link href="https://beian.miit.gov.cn/" target="_blank" class="footer-link">陕ICP备20000279号-2</el-link>
    </div>

  </div>
</template>

<script>
import Language from '@/components/Language/index.vue'

export default {
  name: 'Login',
  components: { Language },
  data() {
    const validateCustomerNumber = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('vcs.authorize.customerNumberIsRequired')))
      } else {
        callback()
      }
    }
    const validateCustomerId = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('vcs.authorize.usernameIsRequired')))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('vcs.authorize.passwordIsRequired')))
      } else {
        callback()
      }
    }
    return {
      logo: require(`@/assets/images/logo-${process.env.VUE_APP_TEMPORARY_PERMISSION}.png`),
      loginForm: {
        // customerNumber: '',
        username: '',
        password: ''
      },
      platform: undefined,
      loginRules: {
        // customerNumber: [{ required: true, trigger: 'blur', validator: validateCustomerNumber }],
        username: [{ required: true, trigger: 'blur', validator: validateCustomerId }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.loginForm = {
      username: this.$route.query.loginName,
      password: this.$route.query.loginPassword
      // customerNumber: this.$route.query.customerNumber
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = 'text'
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    goLink(path) {
      this.$router.push({ path: path })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('user/login', this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || '/' })
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    officialWebsite() {
    },
    getOfficialWebsiteHome() {
    }
  }
}
</script>

<style lang="scss">
$bg: #fff;
$light_gray: #707070;
$cursor: #707070;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0;
      -webkit-appearance: none;
      border-radius: 0;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(0, 0, 0, 0.3);
    background: $bg;
    border-radius: 5px;
    color: #707070;
  }
}
</style>

<style lang="scss" scoped>
$bg: #fff;
$dark_gray: #889aa4;
$light_gray: #333;

.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 0 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .form-item {
    margin-bottom: 30px;
  }

  .login-button {
    width: 100%;
    margin-bottom: 15px;
    height: 40px;
    margin-top: 10px;
  }

  .captcha-image {
    width: 100%;
    height: 47px;
    cursor: pointer;
  }

  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0 auto 10px auto;
      text-align: center;
      font-weight: bold;

      &::before,
      &::after {
        position: absolute;
        top: 46%;
        content: "";
        display: block;
        width: 100px;
        height: 1px;
        background: #333;
      }
      &::before {
        left: 0;
      }
      &::after {
        right: 0;
      }
    }
  }

  .language-select {
    margin-bottom: 10px;
    text-align: right;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 3px 5px 6px 15px;
    color: #555;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .forgot-password {
    text-align: right;
  }
  .authorize-apply-link {
    font-size: 16px;
  }
}
</style>

<style lang="scss" scoped>
$bg: #fff;
$width: 1100px;

.login-container {
  width: 100%;
  height: 100vh;
  background: $bg url("../../../assets/images/background.jpg") no-repeat center;
  background-size: cover;

  .header {
    padding-top:50px;
    width: $width;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;

    .logo {
      width: 200px;
      height: auto;
    }

    img {
      cursor:pointer;
    }

    h1 {
      display: inline-block;
      font-size: 32px;
      font-weight: 400;
    }
  }

  .content {
    width: $width;
    margin: 0 auto;
    padding-bottom: 50px;

    .content-left {
      font-size: 16px;
      font-family: "Open Sans", sans-serif;
      text-align: center;
      margin-bottom: 40px;

      .authorize-image {
        text-align: center;
        width: 95%;
        border: 1px solid #999;
        border-radius: 10px;
        box-shadow: 10px 10px 10px #999;
        margin-bottom: 10px;
      }
    }
  }

  .footer {
    margin-top: 100px;
    text-align: center;
  }

  .footer-link {
    font-size: 16px;
  }
}
</style>
