<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.creditCardActivation')">
      <customize-form
        ref="customizeForm"
        form-ref="dataForm"
        size="small"
        :model="formData"
        :form-item-config="formConfig"
        label-width="240px"
        label-position="left"
        class="apply-form"
      />
      <div style="text-align: right; padding: 10px">
        <el-button size="small" @click="handlerCancel"> {{ $t('vcs.common.cancel') }}</el-button>
        <el-button type="primary" size="small" :loading="confirmButtonLoading" @click="handlerConfirm">
          {{ $t('vcs.common.confirm') }}
        </el-button>
      </div>
    </customize-card>
  </div>
</template>

<script>

import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { formConfig } from '@/views/credit-card/activation/form-config'
import { accountOpenActivation } from '@/api/business/credit-card'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'CreditCardActivation',
  components: { CustomizeCard, CustomizeForm },
  data() {
    return {
      loading: false,
      formData: {
        creditCardNumber: this.$route.params.creditCardNumber,
        customerId: this.$route.params.customerId,
        password: '',
        retypePassword: ''
      },
      formConfig: formConfig,
      confirmButtonLoading: false
    }
  },
  methods: {
    handlerCancel() {
      this.$router.go(-1)
    },
    handlerConfirm() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          this.confirmButtonLoading = true
          accountOpenActivation(data)
            .then(res => {
              const { msg } = res
              this.$message.success(msg)
              this.$router.go(-1)
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    }
  }
}
</script>
