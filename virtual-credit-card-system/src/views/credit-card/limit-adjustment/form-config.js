import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { nextLimitChangeReviewOptions } from '@/data'

export const formConfig = [
  {
    'label': 'vcs.creditCardMaintenance.creditCardNumber',
    'placeholder': 'vcs.creditCardMaintenance.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.creditCardNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.customerId',
    'placeholder': 'vcs.creditCardApplication.customerIdPlaceholder',
    'prop': 'customerId',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 35,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.customerIdPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.currentCreditLimit',
    'placeholder': 'vcs.creditCardLimitAdjustment.currentCreditLimitPlaceholder',
    'prop': 'currentCreditLimit',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'min': 0,
    'controls': false,
    'step': 1,
    'step-strictly': true,
    'precision': 0,
    'clearable': true,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.currentCreditLimitPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.availableLimit',
    'placeholder': 'vcs.creditCardLimitAdjustment.availableLimitPlaceholder',
    'prop': 'availableLimit',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'min': 0,
    'controls': false,
    'step': 1,
    'step-strictly': true,
    'precision': 0,
    'clearable': true,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.availableLimitPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.currentLimitExpiry',
    'placeholder': 'vcs.creditCardLimitAdjustment.currentLimitExpiryPlaceholder',
    'prop': 'currentLimitExpiry',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'format': 'yyyy-MM-DD',
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.currentLimitExpiryPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.newLimit',
    'placeholder': 'vcs.creditCardLimitAdjustment.newLimitPlaceholder',
    'prop': 'newLimit',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'min': 0,
    'controls': false,
    'step': 1000,
    'step-strictly': true,
    'precision': 0,
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.newLimitPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.nextLimitChangeReview',
    'placeholder': 'vcs.creditCardLimitAdjustment.nextLimitChangeReviewPlaceholder',
    'prop': 'nextLimitChangeReview',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': nextLimitChangeReviewOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.nextLimitChangeReviewPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.remarks',
    'placeholder': 'vcs.creditCardMaintenance.remarksPlaceholder',
    'prop': 'remarks',
    'component': 'el-input',
    'type': 'textarea',
    'span': 24,
    'width': '100%',
    'clearable': true,
    'maxlength': 70
  }
]
