<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.creditCardLossReporting')">
      <customize-form
        ref="customizeForm"
        form-ref="dataForm"
        size="small"
        :model="formData"
        :form-item-config="formConfig"
        label-width="240px"
        label-position="left"
        class="apply-form"
        @update="reportLossToPoliceChange($event)"
      />
      <div style="text-align: right; padding: 10px">
        <el-button size="small" @click="handlerCancel"> {{ $t('vcs.common.cancel') }}</el-button>
        <el-button type="primary" size="small" :loading="confirmButtonLoading" @click="handlerConfirm">
          {{ $t('vcs.common.confirm') }}
        </el-button>
      </div>
    </customize-card>
  </div>
</template>

<script>

import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { formConfig } from '@/views/credit-card/loss-reporting/form-config'
import { getCreditCardMasterDetails, lossReporting } from '@/api/business/credit-card'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'CreditCardLossReporting',
  components: { CustomizeForm, CustomizeCard },
  data() {
    return {
      loading: false,
      formData: {
        creditCardNumber: '',
        customerId: '',
        customerIdType: '',
        issueCountry: '',
        lossReportingReason: '',
        reportLossToPolice: '',
        policeFileNo: '',
        reportLossDate: null,
        remarks: ''
      },
      formConfig: formConfig,
      confirmButtonLoading: false
    }
  },
  mounted() {
    this.getCreditCardMaster()
  },
  methods: {
    getCreditCardMaster() {
      getCreditCardMasterDetails({ creditCardNumber: this.$route.params.creditCardNumber })
        .then((res) => {
          this.formData.creditCardNumber = res.data.creditCardNumber
          this.formData.customerId = res.data.customerId
          this.formData.customerIdType = res.data.customerIdType
          this.formData.issueCountry = res.data.issueCountry
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerCancel() {
      this.$router.go(-1)
    },
    handlerConfirm() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          this.confirmButtonLoading = true
          lossReporting(data)
            .then(res => {
              const { msg } = res
              this.$message.success(msg)
              this.$router.push('/credit-card/overview')
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    reportLossToPoliceChange({ prop, value }) {
      if (prop === 'reportLossToPolice') {
        const index = this.formConfig.findIndex(item => item.prop === 'policeFileNo')
        if (index > -1) {
          this.formConfig[index].hidden = value !== 'Y'
        }
      }
    }
  }
}
</script>
