<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.redemption')" :show-back-btn="false">
      <el-form
        ref="creditCardForm"
        :model="creditCardForm"
        size="small"
        label-width="220px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('vcs.creditCardMaster.creditCardNumber')" prop="creditCardNumber">
              <el-select
                v-model="creditCardForm.creditCardNumber"
                class="search-form-item"
                @change="selectCreditCard"
              >
                <el-option
                  v-for="(item, index) in creditCardList"
                  :key="index"
                  :label="item.creditCardNumber"
                  :value="item.rewardPoint"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('vcs.creditCardMaster.rewardPoint')" prop="rewardPoint">
              <el-input
                v-model="creditCardForm.rewardPoint"
                disabled
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-divider />

      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="220px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardProductDetails.catalogueName')" prop="catalogueCode">
              <el-select
                v-model="searchForm.catalogueCode"
                :placeholder="$t('vcs.creditCardProductDetails.catalogueNameSelectPlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option
                  v-for="(item, index) in catalogueOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardProductDetails.pointsRequiredFrom')" prop="pointsRequiredFrom">
              <el-input-number
                v-model="searchForm.pointsRequiredFrom"
                :placeholder="$t('vcs.creditCardProductDetails.pointsRequiredFromPlaceholder')"
                clearable
                :controls="false"
                style="line-height: 34px"
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardProductDetails.pointsRequiredTo')" prop="pointsRequiredTo">
              <el-input-number
                v-model="searchForm.pointsRequiredTo"
                :placeholder="$t('vcs.creditCardProductDetails.pointsRequiredToPlaceholder')"
                clearable
                :controls="false"
                style="line-height: 34px"
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('vcs.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardProductDetails.productName')"
            prop="productName"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardProductDetails.rewardPointsRequiredForEachUnit')"
            prop="rewardPointsRequiredForEachUnit"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardProductDetails.additionalCashRequired')"
            prop="additionalCashRequired"
            align="center"
            width="180"
          />
          <el-table-column
            :label="$t('vcs.creditCardProductDetails.catalogueName')"
            prop="catalogueCode"
            align="center"
            width="150"
          >
            <template slot-scope="{ row }">
              {{ getCatalogueNameByCode(row.catalogueCode) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardProductDetails.productInventory')"
            prop="productInventory"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardProductDetails.createDate')"
            prop="createDate"
            align="center"
            width="150"
          >
            <template slot-scope="{ row }">
              {{ moment(row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardProductDetails.productDescription')"
            prop="productDescription"
            align="center"
          />
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-input-number
                v-model="row.count"
                style="width: 140px;text-align: center"
                :min="0"
                :max="row.productInventory"
                @change="selectProduct(row)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>

      <div class="table-redemption">
        <span>{{ $t('vcs.pointsRedemption.additionalCashRequired') }}:</span>
        <span class="table-redemption-total">{{ additionalCashRequired }}</span>
        <span>{{ $t('vcs.pointsRedemption.totalPoints') }}:</span>
        <span class="table-redemption-total">{{ totalPoints }}</span>
        <el-button :disabled="creditCardList.length === 0" type="primary" :loading="redemptionButtonLoading" @click="openSubmitDialog">{{ $t('vcs.common.submit') }}</el-button>
      </div>
    </customize-card>

    <el-dialog
      :title="$t('vcs.pointsRedemption.redemptionTitle')"
      :visible.sync="submitDialog.visible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="10vh"
      destroy-on-close
    >
      <el-form
        ref="rewardPointRedemptionForm"
        :model="rewardPointRedemption"
        size="small"
        label-width="170px"
        label-position="left"
      >
        <el-form-item :label="$t('vcs.pointsRedemption.debitAccountNumber')" prop="accountNumber">
          <el-select
            v-model="rewardPointRedemption.accountNumber"
            :placeholder="$t('vcs.pointsRedemption.debitAccountNumberPlaceholder')"
            class="search-form-item"
            clearable
          >
            <el-option-group
              v-for="group in store.getters.debitAccounts"
              :key="group.label"
              :label="$t(group.label)"
            >
              <el-option
                v-for="(option, index) in group.options"
                :key="index"
                :label="option.label"
                :value="option.value"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="submitDialog.visible = false"> {{ $t('vcs.common.cancel') }}</el-button>
        <el-button
          type="primary"
          size="small"
          :loading="submitDialog.confirmButtonLoading"
          @click="submitRedemption()"
        >{{ $t('vcs.common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import store from '@/store'
import { getCreditCardProductDetailsPage } from '@/api/system/creditCardProductDetails'
import { getCreditCardProductCatalogueList } from '@/api/system/creditCardProductCatalogue'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getCreditCardMasterList } from '@/api/business/credit-card'
import { redemption } from '@/api/system/creditCardRewardPoint'

export default {
  name: 'PointsRedemption',
  components: { CustomizeCard },
  data() {
    return {
      moment,
      store,
      loading: false,
      tableLoading: false,
      redemptionButtonLoading: false,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        catalogueCode: '',
        productCode: '',
        pointsRequiredFrom: undefined,
        pointsRequiredTo: undefined
      },
      items: [],
      total: 0,
      creditCardForm: {
        creditCardNumber: '',
        rewardPoint: 0
      },
      creditCardList: [],
      catalogueOptions: [],
      selectedProducts: {},
      totalPrice: 0,
      additionalCashRequired: 0,
      totalPoints: 0,
      submitDialog: {
        visible: false,
        confirmButtonLoading: false
      },
      rewardPointRedemption: {
        creditCardNumber: '',
        accountNumber: '',
        productDetails: []
      }
    }
  },
  mounted() {
    this.getItems()
    this.getCatalogueList()
    this.getCreditCardList()
  },
  methods: {
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      getCreditCardProductDetailsPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          for (const item of items) {
            const product = this.selectedProducts[item.id]
            if (product) {
              item.count = product.count
            } else {
              item.count = 0
            }
          }

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    getCreditCardList() {
      getCreditCardMasterList({
        creditCardStatusList: ['A']
      })
        .then((res) => {
          this.creditCardList = res.data
        })
        .then(() => {
          if (this.creditCardList.length > 0) {
            this.creditCardForm = {
              creditCardNumber: this.creditCardList[0].creditCardNumber,
              rewardPoint: this.creditCardList[0].rewardPoint
            }
          }
        })
    },
    getCatalogueList() {
      getCreditCardProductCatalogueList({})
        .then(res => {
          this.catalogueOptions = res.data.map(item => {
            return {
              label: item.catalogueName,
              value: item.catalogueCode
            }
          })
        })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    selectCreditCard(item) {
      this.creditCardForm.rewardPoint = item
    },
    selectProduct(row) {
      if (row.count > 0) {
        this.selectedProducts[row.id] = {
          ...row
        }
      } else {
        delete this.selectedProducts[row.id]
      }
      this.calculateTotalPrice()
    },
    calculateTotalPrice() {
      const selectProductValues = Array.from(Object.values(this.selectedProducts))
      const filterSelectProductValues = selectProductValues.filter(item => item.count !== 0)
      this.totalPrice = filterSelectProductValues.reduce((acc, item) => acc + item.productPrice * item.count, 0)
      this.additionalCashRequired = filterSelectProductValues.reduce((acc, item) => acc + item.additionalCashRequired * item.count, 0)
      this.totalPoints = filterSelectProductValues.reduce((acc, item) => acc + item.rewardPointsRequiredForEachUnit * item.count, 0)
    },
    openSubmitDialog() {
      if (Object.keys(this.selectedProducts).length === 0) {
        this.$message.error(this.$t('vcs.pointsRedemption.pleaseSelectProductMessage'))
        return
      }
      const selectProductValues = Array.from(Object.values(this.selectedProducts))
      const filterSelectProductValues = selectProductValues.filter(item => item.count !== 0)
      this.rewardPointRedemption.productDetails = filterSelectProductValues.map(item => {
        return {
          unit: item.count,
          productCode: item.productCode
        }
      })
      this.rewardPointRedemption.creditCardNumber = this.creditCardForm.creditCardNumber
      if (this.additionalCashRequired === 0) {
        this.redemptionButtonLoading = true
        redemption(this.rewardPointRedemption)
          .then(res => {
            const { msg } = res
            this.resetSelectedProducts()
            this.getCreditCardList()
            this.getItems()
            this.$message.success(msg)
          })
          .finally(() => {
            this.redemptionButtonLoading = false
          })
      } else {
        this.submitDialog.visible = true
      }
    },
    submitRedemption() {
      this.$refs['rewardPointRedemptionForm'].validate((valid) => {
        if (valid) {
          this.submitDialog.confirmButtonLoading = true
          redemption(this.rewardPointRedemption)
            .then(res => {
              const { msg } = res
              this.resetSelectedProducts()
              this.getCreditCardList()
              this.getItems()
              this.$message.success(msg)
            })
            .finally(() => {
              this.submitDialog.confirmButtonLoading = false
              this.submitDialog.visible = false
            })
        }
      })
    },
    resetSelectedProducts() {
      this.selectedProducts = {}
      this.totalPrice = 0
      this.totalPoints = 0
    },
    getCatalogueNameByCode(catalogueCode) {
      const index = this.catalogueOptions.findIndex(item => item.value === catalogueCode)
      if (index > -1) {
        return this.catalogueOptions[index].label
      }
      return ''
    }
  }
}
</script>

<style scoped lang="scss">
.business-container {
  .table-redemption {
    margin-top: 10px;
    text-align: right;

    span {
      display: inline-block;
    }

    .table-redemption-total {
      margin: 0 10px;
      color: red;
      font-size: 20px
    }

  }
}
</style>
<style lang="scss">
.business-container {
  .el-table {
    .el-input-number .el-input__inner {
      text-align: center !important;
    }
  }
}
</style>
