import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { billingCycleOptions } from '@/data'

export const formConfig = [
  {
    'label': 'vcs.creditCardStatement.simulator.statementDate',
    'placeholder': 'vcs.creditCardStatement.simulator.statementDatePlaceholder',
    'prop': 'statementDate',
    'span': 12,
    'component': 'el-date-picker',
    'type': 'date',
    'format': 'yyyy-MM-dd',
    'valueFormat': 'timestamp',
    'width': '100%',
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.statementDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.billingCycle',
    'placeholder': 'vcs.creditCardStatement.simulator.billingCyclePlaceholder',
    'prop': 'billingCycle',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'disabled': true,
    'options': billingCycleOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.billingCyclePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.dueDate',
    'placeholder': 'vcs.creditCardStatement.simulator.dueDatePlaceholder',
    'prop': 'dueDate',
    'span': 12,
    'component': 'el-date-picker',
    'type': 'date',
    'format': 'yyyy-MM-dd',
    'valueFormat': 'timestamp',
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.dueDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.statementAmount',
    'placeholder': 'vcs.creditCardStatement.simulator.statementAmountPlaceholder',
    'prop': 'statementAmount',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'max': 9999999999999.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.statementAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.minimumRepaymentPct',
    'placeholder': 'vcs.creditCardStatement.simulator.minimumRepaymentPctPlaceholder',
    'prop': 'minimumPaymentPct',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'max': 100,
    'controls': false,
    'step': 1,
    'step-strictly': true,
    'precision': 0,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.minimumRepaymentPctPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.minimumRepaymentAPR',
    'placeholder': 'vcs.creditCardStatement.simulator.minimumRepaymentAPRPlaceholder',
    'prop': 'minimumPaymentAPR',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'max': 100,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 5,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.minimumRepaymentAPRPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.lateCharge',
    'placeholder': 'vcs.creditCardStatement.simulator.lateChargePlaceholder',
    'prop': 'lateCharge',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'max': 9999999999999.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'width': '100%',
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.lateChargePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.nullRepaymentAPR',
    'placeholder': 'vcs.creditCardStatement.simulator.nullRepaymentAPRPlaceholder',
    'prop': 'nullPaymentAPR',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'max': 100,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 5,
    'width': '100%',
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.nullRepaymentAPRPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.transaction',
    'prop': 'creditCardTransactionRecords',
    'span': 24,
    'width': '100%',
    'slot-name': 'transaction',
    'rules': [
      { required: true }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.today',
    'placeholder': 'vcs.creditCardStatement.simulator.todayPlaceholder',
    'prop': 'today',
    'span': 12,
    'component': 'el-date-picker',
    'type': 'datetime',
    'valueFormat': 'timestamp',
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.simulator.todayPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.simulator.totalOverdueInterest',
    'placeholder': 'vcs.creditCardStatement.simulator.totalOverdueInterestPlaceholder',
    'prop': 'totalOverdueInterest',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'max': 9999999999999.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'width': '100%',
    'disabled': true
  }
]
