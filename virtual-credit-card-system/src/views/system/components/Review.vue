<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="50%"
    @close="resetForm('reviewForm')"
  >
    <el-form
      ref="reviewForm"
      :model="reviewRow"
      label-position="left"
      :label-width="labelWidth"
      size="small"
      class="review-form"
    >
      <el-form-item :label="reviewResultLabel" prop="reviewResult" :rules="[{required: true, message: reviewResultPlaceholder }]">
        <el-select v-model="reviewRow.reviewResult" class="form-item" clearable :placeholder="reviewResultPlaceholder">
          <el-option v-for="item in options" :key="item.value" :label="$t(item.label)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('vcs.creditCardMaintenance.comment')" prop="comment">
        <el-input v-model="reviewRow.comment" type="textarea" :placeholder="$t('vcs.creditCardMaintenance.commentPlaceholder')" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:visible', false)"> {{ $t('vcs.common.cancel') }}</el-button>
      <el-button
        type="primary"
        size="small"
        :loading="confirmReviewButtonLoading"
        @click="handlerConfirm('reviewForm')"
      >{{ $t('vcs.common.confirm') }}</el-button>
    </span>
  </el-dialog>
</template>
<script>

export default {
  name: 'Review',
  props: {
    visible: {
      type: Boolean,
      default: () => false
    },
    title: {
      type: String,
      required: true
    },
    reviewResultLabel: {
      type: String,
      required: true
    },
    reviewResultPlaceholder: {
      type: String,
      required: true
    },
    options: {
      type: Array,
      required: true
    },
    labelWidth: {
      type: String,
      required: false,
      default: ''
    },
    confirmReviewButtonLoading: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      reviewRow: {
        reviewResult: '',
        comment: ''
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  methods: {
    handlerConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('update:confirmReviewButtonLoading', true)
          this.$emit('handler-validated', this.reviewRow)
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.review-form {
  .form-item {
    width: 100%;
  }
}
</style>
