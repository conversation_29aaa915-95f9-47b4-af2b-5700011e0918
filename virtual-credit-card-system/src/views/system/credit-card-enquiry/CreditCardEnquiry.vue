<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.creditCardEnquiry')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="160px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('vcs.creditCardMaintenance.creditCardNumber')" prop="creditCardNumber">
              <el-input
                v-model="searchForm.creditCardNumber"
                :placeholder="$t('vcs.creditCardMaintenance.creditCardNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getCreditCardMaster">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">
              {{ $t('vcs.common.reset') }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <el-descriptions v-if="creditCardDetails" class="descriptions" :column="2" border>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.creditCardNumber') }}</template>
          {{ creditCardDetails.creditCardNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.associatedCusNum') }}</template>
          {{ creditCardDetails.associatedCusNum }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.countryCode') }}</template>
          {{ creditCardDetails.countryCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.clearingCode') }}</template>
          {{ creditCardDetails.clearingCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.branchCode') }}</template>
          {{ creditCardDetails.branchCode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.holderName') }}</template>
          {{ creditCardDetails.holderName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.common.currency') }}</template>
          {{ creditCardDetails.currency }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.creditCardType') }}</template>
          {{ $t(getLabel("CREDIT_CARD_TYPE", creditCardDetails.creditCardType)) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.approvedLimit') }}</template>
          {{ creditCardDetails.approvedLimit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.cashAdvanceLimit') }}</template>
          {{ creditCardDetails.cashAdvanceLimit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.issuanceDate') }}</template>
          {{ creditCardDetails.issuanceDate ? moment(Number(creditCardDetails.issuanceDate)).format('YYYY-MM-DD') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.repaymentCycle') }}</template>
          {{ $t(getLabel("BILLING_CYCLE", creditCardDetails.repaymentCycle)) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.usedLimit') }}</template>
          {{ creditCardDetails.usedLimit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.expiryDate') }}</template>
          {{ creditCardDetails.expiryDate ? moment(Number(creditCardDetails.expiryDate)).format('YYYY-MM-DD') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.availableLimit') }}</template>
          {{ creditCardDetails.availableLimit }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.creditCardStatus') }}</template>
          {{ $t(getLabel("CREDIT_CARD_STATUS", creditCardDetails.creditCardStatus)) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.rewardPoint') }}</template>
          {{ creditCardDetails.rewardPoint }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.nextLimitReviewDate') }}</template>
          {{ creditCardDetails.nextLimitReviewDate ? moment(Number(creditCardDetails.nextLimitReviewDate)).format('YYYY-MM-DD') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.reportLossDate') }}</template>
          {{ creditCardDetails.reportLossDate ? moment(Number(creditCardDetails.reportLossDate)).format('YYYY-MM-DD HH:mm:ss') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.reportCancelDate') }}</template>
          {{ creditCardDetails.reportCancelDate ? moment(Number(creditCardDetails.reportCancelDate)).format('YYYY-MM-DD HH:mm:ss') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.creditCardLimitApprovedDate') }}</template>
          {{ creditCardDetails.creditCardLimitApprovedDate ? moment(Number(creditCardDetails.creditCardLimitApprovedDate)).format('YYYY-MM-DD HH:mm:ss') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.limitApprovalStatus') }}</template>
          {{ $t(getLabel("LIMIT_ADJUSTMENT_STATUS", creditCardDetails.limitApprovalStatus)) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.createDate') }}</template>
          {{ creditCardDetails.createDate ? moment(Number(creditCardDetails.createDate)).format('YYYY-MM-DD HH:mm:ss') : '' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardMaster.remarks') }}</template>
          {{ creditCardDetails.remarks }}
        </el-descriptions-item>
      </el-descriptions>
    </customize-card>
  </div>
</template>

<script>
import { getCreditCardDetails } from '@/api/system/creditCardCustomer'
import { getLabel } from '@/data'
import moment from 'moment'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'CreditCardDetails',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      getLabel,
      moment,
      creditCardDetails: null,
      searchForm: {
        creditCardNumber: ''
      }
    }
  },
  methods: {
    getCreditCardMaster() {
      getCreditCardDetails(this.searchForm)
        .then((res) => {
          this.creditCardDetails = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
