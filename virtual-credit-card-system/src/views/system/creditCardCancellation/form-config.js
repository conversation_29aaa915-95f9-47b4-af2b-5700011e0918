import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { applicationStatusOptions, countryOptions, customerIdTypeOptions } from '@/data'

export const formConfig = [
  {
    'label': 'vcs.creditCardMaintenance.creditCardNumber',
    'placeholder': 'vcs.creditCardMaintenance.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.creditCardNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.customerIdType',
    'placeholder': 'vcs.creditCardApplication.customerIdTypePlaceholder',
    'prop': 'customerIdType',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': customerIdTypeOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.customerIdTypePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.customerId',
    'placeholder': 'vcs.creditCardApplication.customerIdPlaceholder',
    'prop': 'customerId',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 35,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.customerIdPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.issueCountry',
    'placeholder': 'vcs.creditCardApplication.issueCountryPlaceholder',
    'prop': 'issueCountry',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': countryOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.issueCountryPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.cancellationReason',
    'placeholder': 'vcs.creditCardMaintenance.cancellationReasonPlaceholder',
    'prop': 'reason',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.cancellationReasonPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.remarks',
    'placeholder': 'vcs.creditCardMaintenance.remarksPlaceholder',
    'prop': 'remarks',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.remarksPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.createDate',
    'placeholder': 'vcs.creditCardMaintenance.createDatePlaceholder',
    'prop': 'createDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-DD HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.createDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.applicationStatus',
    'placeholder': 'vcs.creditCardMaintenance.applicationStatusPlaceholder',
    'prop': 'applicationStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'options': applicationStatusOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.applicationStatusPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.approvalStaffNumber',
    'placeholder': 'vcs.creditCardMaintenance.approvalStaffNumberPlaceholder',
    'prop': 'approvalStaffNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.approvalStaffNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.approvalDate',
    'placeholder': 'vcs.creditCardMaintenance.approvalDatePlaceholder',
    'prop': 'approvalDate',
    'component': 'el-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-DD HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.approvalDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.approvalComment',
    'placeholder': 'vcs.creditCardMaintenance.approvalCommentPlaceholder',
    'prop': 'approvalComment',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.approvalCommentPlaceholder' }
    ]
  }
]
