import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { adjustmentTypeOptions, countryOptions, customerIdTypeOptions, limitAdjustmentStatusOptions } from '@/data'

export const formConfig = [
  {
    'label': 'vcs.creditCardLimitAdjustment.creditCardNumber',
    'placeholder': 'vcs.creditCardLimitAdjustment.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.creditCardNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.customerIdType',
    'placeholder': 'vcs.creditCardApplication.customerIdTypePlaceholder',
    'prop': 'customerIdType',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': customerIdTypeOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.customerIdTypePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.customerId',
    'placeholder': 'vcs.creditCardApplication.customerIdPlaceholder',
    'prop': 'customerId',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 35,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.customerIdPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.issueCountry',
    'placeholder': 'vcs.creditCardApplication.issueCountryPlaceholder',
    'prop': 'issueCountry',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': countryOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.issueCountryPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.availableLimit',
    'placeholder': 'vcs.creditCardLimitAdjustment.availableLimitPlaceholder',
    'prop': 'availableLimit',
    'component': 'el-input-number',
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 35,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.availableLimitPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.expiryDate',
    'placeholder': 'vcs.creditCardLimitAdjustment.expiryDatePlaceholder',
    'prop': 'expiryDate',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'format': 'yyyy-MM-DD',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.expiryDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.previousLimit',
    'placeholder': 'vcs.creditCardLimitAdjustment.previousLimitPlaceholder',
    'prop': 'previousLimit',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.previousLimitPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.newLimit',
    'placeholder': 'vcs.creditCardLimitAdjustment.newLimitPlaceholder',
    'prop': 'newLimit',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.newLimitPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.nextLimitReviewDate',
    'placeholder': 'vcs.creditCardLimitAdjustment.nextLimitReviewDatePlaceholder',
    'prop': 'nextLimitReviewDate',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'format': 'yyyy-MM-DD',
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.nextLimitReviewDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.adjustmentType',
    'placeholder': 'vcs.creditCardLimitAdjustment.adjustmentTypePlaceholder',
    'prop': 'adjustmentType',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': adjustmentTypeOptions,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.adjustmentTypePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.remarks',
    'placeholder': 'vcs.creditCardLimitAdjustment.remarksPlaceholder',
    'prop': 'remarks',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.remarksPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.createDate',
    'placeholder': 'vcs.creditCardLimitAdjustment.createDatePlaceholder',
    'prop': 'createDate',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'format': 'yyyy-MM-DD HH:mm:ss',
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.createDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.limitAdjustmentStatus',
    'placeholder': 'vcs.creditCardLimitAdjustment.limitAdjustmentStatusPlaceholder',
    'prop': 'limitAdjustmentStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': limitAdjustmentStatusOptions,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.limitAdjustmentStatusPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.approvalStaffNumber',
    'placeholder': 'vcs.creditCardMaintenance.approvalStaffNumberPlaceholder',
    'prop': 'approvalStaffNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.approvalStaffNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.approvalDate',
    'placeholder': 'vcs.creditCardLimitAdjustment.approvalDatePlaceholder',
    'prop': 'approvalDate',
    'component': 'el-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-DD HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.approvalDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardLimitAdjustment.approvalComment',
    'placeholder': 'vcs.creditCardLimitAdjustment.approvalCommentPlaceholder',
    'prop': 'approvalComment',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardLimitAdjustment.approvalCommentPlaceholder' }
    ]
  }
]
