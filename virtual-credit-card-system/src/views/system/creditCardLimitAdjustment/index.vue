<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.limitAdjustmentManagement')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="180px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item
              :label="$t('vcs.creditCardLimitAdjustment.limitAdjustmentStatus')"
              prop="limitAdjustmentStatus"
            >
              <el-select
                v-model="searchForm.limitAdjustmentStatus"
                :placeholder="$t('vcs.creditCardLimitAdjustment.limitAdjustmentStatusPlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option
                  v-for="item in limitAdjustmentStatusOptions"
                  :key="item.value"
                  :label="$t(item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardLimitAdjustment.creditCardNumber')" prop="creditCardNumber">
              <el-input
                v-model="searchForm.creditCardNumber"
                :placeholder="$t('vcs.creditCardLimitAdjustment.creditCardNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardLimitAdjustment.customerId')" prop="customerId">
              <el-input
                v-model="searchForm.customerId"
                :placeholder="$t('vcs.creditCardLimitAdjustment.customerIdPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('vcs.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.creditCardNumber')"
            prop="creditCardNumber"
            align="center"
            width="160"
          />
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.customerId')"
            prop="customerId"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.customerIdType')"
            prop="customerIdType"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("CUSTOMER_ID_TYPE", scope.row.customerIdType)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.issueCountry')"
            prop="issueCountry"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("COUNTRY", scope.row.issueCountry)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.previousLimit')"
            prop="previousLimit"
            align="center"
            width="180"
          />
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.availableLimit')"
            prop="availableLimit"
            align="center"
            width="160"
          />
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.expiryDate')"
            prop="expiryDate"
            align="center"
            width="200"
          >
            <template slot-scope="scope">
              {{ moment(Number(scope.row.expiryDate)).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.newLimit')"
            prop="newLimit"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.limitAdjustmentStatus')"
            prop="limitAdjustmentStatus"
            align="center"
            width="190"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("LIMIT_ADJUSTMENT_STATUS", scope.row.limitAdjustmentStatus)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardLimitAdjustment.createDate')"
            prop="createDate"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ moment(Number(scope.row.createDate)).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewDialog(row)"
              >
                {{ $t('vcs.common.view') }}
              </el-button>
              <el-button
                :disabled="row.limitAdjustmentStatus !=='P'"
                type="text"
                size="mini"
                text
                @click="reviewDialog(row)"
              >
                {{ $t('vcs.common.review') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>
      <!-- 查看的对话框 -->
      <el-dialog
        :title="$t('vcs.creditCardLimitAdjustment.viewDetails')"
        :visible.sync="viewDialogVisible"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        width="60%"
        top="50px"
      >
        <descriptions :view-row="viewRow" />
      </el-dialog>

      <el-dialog
        :title="isUpdate ? $t('vcs.creditCardLimitAdjustment.editDetails') : $t('vcs.creditCardLimitAdjustment.createDetails')"
        :visible.sync="dataFormDialogVisible"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        width="60%"
        top="10vh"
        destroy-on-close
      >
        <customize-form
          ref="customizeForm"
          form-ref="dataForm"
          size="small"
          :model="formData"
          :form-item-config="formConfig"
          label-width="220px"
          label-position="left"
        />

        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="dataFormDialogVisible = false"> {{ $t('vcs.common.cancel') }}</el-button>
          <el-button
            v-if="!isUpdate"
            type="primary"
            size="small"
            :loading="confirmButtonLoading"
            @click="handlerCreateData()"
          >{{ $t('vcs.common.confirm') }}</el-button>
          <el-button
            v-if="isUpdate"
            type="primary"
            size="small"
            :loading="confirmButtonLoading"
            @click="handlerUpdateData()"
          >{{ $t('vcs.common.update') }}</el-button>
        </span>
      </el-dialog>

      <review
        :confirm-review-button-loading.sync="confirmReviewButtonLoading"
        :visible.sync="reviewDialogVisible"
        :options="limitAdjustmentStatusOptions.filter(item=>item.value!=='P')"
        label-width="190px"
        :title="$t('vcs.creditCardLimitAdjustment.limitAdjustmentReview')"
        :review-result-label="$t('vcs.creditCardLimitAdjustment.limitAdjustmentStatus')"
        :review-result-placeholder="$t('vcs.creditCardLimitAdjustment.limitAdjustmentStatusPlaceholder')"
        @handler-validated="handlerReview($event)"
      />
    </customize-card>
  </div>
</template>

<script>
import moment from 'moment'
import Descriptions from '@/components/Descriptions/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import Review from '@/views/system/components/Review.vue'
import {
  createCreditCardLimitAdjustment,
  deleteCreditCardLimitAdjustment,
  getCreditCardLimitAdjustmentPage,
  reviewCreditCardLimitAdjustment,
  updateCreditCardLimitAdjustment
} from '@/api/system/creditCardLimitAdjustment'
import { formConfig } from '@/views/system/creditCardLimitAdjustment/form-config'
import { getLabel, limitAdjustmentStatusOptions } from '@/data'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'CreditCardLimitAdjustment',
  components: { CustomizeCard, CustomizeForm, Descriptions, Review },
  data() {
    const hiddenFields = ['maintenanceType']
    const showConfig = formConfig.filter(item => !hiddenFields.includes(item.prop))
    return {
      moment,
      getLabel,
      limitAdjustmentStatusOptions,
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      confirmButtonLoading: false,
      dataFormDialogVisible: false,
      reviewCreditCardNumber: '',
      reviewDialogVisible: false,
      confirmReviewButtonLoading: false,
      isUpdate: false,
      formConfig: showConfig,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        limitAdjustmentStatus: '',
        creditCardNumber: '',
        customerId: ''
      },
      viewRow: [],
      formData: {},
      items: [],
      total: 0
    }
  },
  mounted() {
    this.getItems()
  },
  methods: {
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      }
      delete requestData.dateRange
      getCreditCardLimitAdjustmentPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handlerCreateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          createCreditCardLimitAdjustment(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          updateCreditCardLimitAdjustment(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    viewDialog(row) {
      const description = { ...row }
      this.viewRow = this.formConfig
        .filter(item => {
          if (row.limitAdjustmentStatus !== 'P') {
            return true
          }
          return item.prop !== 'approvalStaffNumber' && item.prop !== 'approvalDate' && item.prop !== 'approvalComment'
        })
        .map(item => {
          let value = description[item.prop]
          if (item.format) {
            return { key: this.$t(item.label), value: moment(Number(value)).format(item.format.replace('dd', 'DD').replace('yyyy', 'YYYY')) }
          } else {
            if (item.options) {
              const option = item.options.filter(option => option.value === value)
              if (option && option.length) {
                value = this.$t(option[0].label)
              }
            }
            return { key: this.$t(item.label), value: value }
          }
        })
      this.viewDialogVisible = true
    },
    openUpdateDialog(row) {
      this.formData = { ...row }
      this.isUpdate = true
      this.dataFormDialogVisible = true
    },
    openCreateDialog() {
      this.isUpdate = false
      this.formData = {}
      if (this.$refs.customizeForm) {
        this.$refs.customizeForm.resetForm()
      }
      this.dataFormDialogVisible = true
    },
    deleteItem(id) {
      this.$confirm(this.$t('vcs.common.deleteTipMessage'), this.$t('vcs.common.tip'), {
        confirmButtonText: this.$t('vcs.common.confirm'),
        cancelButtonText: this.$t('vcs.common.cancel'),
        type: 'warning'
      }).then(() => {
        this.tableLoading = true
        deleteCreditCardLimitAdjustment(id)
          .then((res) => {
            const { msg } = res
            this.searchForm.currentPage = 1
            this.$message.success(msg)
            this.getItems()
            this.dataFormDialogVisible = false
          })
          .finally(() => {
            this.tableLoading = false
          })
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    reviewDialog(row) {
      this.reviewCreditCardNumber = row.creditCardNumber
      this.reviewDialogVisible = true
    },
    handlerReview(row) {
      const limitApproval = {
        creditCardNumber: this.reviewCreditCardNumber,
        limitAdjustmentStatus: row.reviewResult,
        remarks: row.comment
      }
      reviewCreditCardLimitAdjustment(limitApproval)
        .then(res => {
          this.reviewDialogVisible = false
          const { msg } = res
          this.$message.success(msg)
          this.getItems()
        })
        .finally(() => {
          this.confirmReviewButtonLoading = false
        })
    }
  }
}
</script>
