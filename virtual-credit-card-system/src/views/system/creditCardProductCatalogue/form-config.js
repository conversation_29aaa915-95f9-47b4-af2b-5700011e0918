export const formConfig = [
  {
    'label': 'vcs.creditCardProductCatalogue.catalogueCode',
    'placeholder': 'vcs.creditCardProductCatalogue.catalogueCodePlaceholder',
    'prop': 'catalogueCode',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardProductCatalogue.catalogueCodePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductCatalogue.catalogueName',
    'placeholder': 'vcs.creditCardProductCatalogue.catalogueNamePlaceholder',
    'prop': 'catalogueName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardProductCatalogue.catalogueNamePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductCatalogue.createDate',
    'placeholder': 'vcs.creditCardProductCatalogue.createDatePlaceholder',
    'prop': 'createDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'hidden': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp'
  },
  {
    'label': 'vcs.creditCardProductCatalogue.remarks',
    'placeholder': 'vcs.creditCardProductCatalogue.remarksPlaceholder',
    'prop': 'remarks',
    'component': 'el-input',
    'type': 'textarea',
    'span': 24,
    'width': '100%',
    'clearable': true,
    'maxlength': 70
  }
]
