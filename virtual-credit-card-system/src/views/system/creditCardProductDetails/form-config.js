import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'

export const formConfig = [
  {
    'label': 'vcs.creditCardProductDetails.catalogueName',
    'placeholder': 'vcs.creditCardProductDetails.catalogueNameSelectPlaceholder',
    'prop': 'catalogueCode',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': [],
    'rules': [
      { required: true, message: 'vcs.creditCardProductDetails.catalogueNameSelectPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductDetails.productCode',
    'placeholder': 'vcs.creditCardProductDetails.productCodePlaceholder',
    'prop': 'productCode',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardProductDetails.productCodePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductDetails.productName',
    'placeholder': 'vcs.creditCardProductDetails.productNamePlaceholder',
    'prop': 'productName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardProductDetails.productNamePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductDetails.rewardPointsRequiredForEachUnit',
    'placeholder': 'vcs.creditCardProductDetails.rewardPointsRequiredForEachUnitPlaceholder',
    'prop': 'rewardPointsRequiredForEachUnit',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'min': 0,
    'max': 99999,
    'controls': false,
    'step': 1,
    'step-strictly': true,
    'precision': 0,
    'rules': [
      { required: true, message: 'vcs.creditCardProductDetails.rewardPointsRequiredForEachUnitPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductDetails.additionalCashRequired',
    'placeholder': 'vcs.creditCardProductDetails.additionalCashRequiredPlaceholder',
    'prop': 'additionalCashRequired',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'min': 0,
    'max': 99999,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'rules': [
      { required: true, message: 'vcs.creditCardProductDetails.additionalCashRequiredPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductDetails.productPrice',
    'placeholder': 'vcs.creditCardProductDetails.productPricePlaceholder',
    'prop': 'productPrice',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'min': 0,
    'max': 99999,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'hidden': true,
    'rules': [
      { required: true, message: 'vcs.creditCardProductDetails.productPricePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductDetails.createDate',
    'placeholder': 'vcs.creditCardProductDetails.createDatePlaceholder',
    'prop': 'createDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'hidden': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp'
  },
  {
    'label': 'vcs.creditCardProductDetails.productInventory',
    'placeholder': 'vcs.creditCardProductDetails.productInventoryPlaceholder',
    'prop': 'productInventory',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'min': 0,
    'max': 99999,
    'controls': false,
    'step': 1,
    'step-strictly': true,
    'precision': 0,
    'rules': [
      { required: true, message: 'vcs.creditCardProductDetails.productInventoryPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardProductDetails.productDescription',
    'placeholder': 'vcs.creditCardProductDetails.productDescriptionPlaceholder',
    'prop': 'productDescription',
    'component': 'el-input',
    'type': 'textarea',
    'span': 24,
    'width': '100%',
    'clearable': true,
    'maxlength': 70
  }
]
