import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'

export const formConfig = [
  {
    'label': 'vcs.creditCardStatement.creditCardNumber',
    'placeholder': 'vcs.creditCardStatement.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.creditCardNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.statementDate',
    'placeholder': 'vcs.creditCardStatement.statementDatePlaceholder',
    'prop': 'statementDate',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.statementDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.statementBalance',
    'placeholder': 'vcs.creditCardStatement.statementBalancePlaceholder',
    'prop': 'statementBalance',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.statementBalancePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.repaymentDueDate',
    'placeholder': 'vcs.creditCardStatement.repaymentDueDatePlaceholder',
    'prop': 'repaymentDueDate',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.repaymentDueDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.minimumPayment',
    'placeholder': 'vcs.creditCardStatement.minimumPaymentPlaceholder',
    'prop': 'minimumPayment',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.minimumPaymentPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.repaymentAmount',
    'placeholder': 'vcs.creditCardStatement.repaymentAmountPlaceholder',
    'prop': 'repaymentAmount',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.repaymentAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.overdueInterest',
    'placeholder': 'vcs.creditCardStatement.overdueInterestPlaceholder',
    'prop': 'overdueInterest',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.overdueInterestPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.statementStorePath',
    'placeholder': 'vcs.creditCardStatement.statementStorePathPlaceholder',
    'prop': 'statementStorePath',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.statementStorePathPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.points',
    'placeholder': 'vcs.creditCardStatement.pointsPlaceholder',
    'prop': 'points',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.pointsPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.pointExpireDate',
    'placeholder': 'vcs.creditCardStatement.pointExpireDatePlaceholder',
    'prop': 'pointExpireDate',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.pointExpireDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.createDate',
    'placeholder': 'vcs.creditCardStatement.createDatePlaceholder',
    'prop': 'createDate',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.createDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.statementStatus',
    'placeholder': 'vcs.creditCardStatement.statementStatusPlaceholder',
    'prop': 'statementStatus',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.statementStatusPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.interestRate',
    'placeholder': 'vcs.creditCardStatement.interestRatePlaceholder',
    'prop': 'interestRate',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.interestRatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.overdueSurcharge',
    'placeholder': 'vcs.creditCardStatement.overdueSurchargePlaceholder',
    'prop': 'overdueSurcharge',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.overdueSurchargePlaceholder' }
    ]
  }
]
