<template>
  <div v-loading="loading" class="business-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="220px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.creditCardNumber')" prop="creditCardNumber">
              <el-input
                v-model="searchForm.creditCardNumber"
                :placeholder="$t('vcs.creditCardStatement.creditCardNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.statementDate')" prop="statementDate">
              <el-input
                v-model="searchForm.statementDate"
                :placeholder="$t('vcs.creditCardStatement.statementDatePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.statementBalance')" prop="statementBalance">
              <el-input
                v-model="searchForm.statementBalance"
                :placeholder="$t('vcs.creditCardStatement.statementBalancePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.repaymentDueDate')" prop="repaymentDueDate">
              <el-input
                v-model="searchForm.repaymentDueDate"
                :placeholder="$t('vcs.creditCardStatement.repaymentDueDatePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.minimumPayment')" prop="minimumPayment">
              <el-input
                v-model="searchForm.minimumPayment"
                :placeholder="$t('vcs.creditCardStatement.minimumPaymentPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.repaymentAmount')" prop="repaymentAmount">
              <el-input
                v-model="searchForm.repaymentAmount"
                :placeholder="$t('vcs.creditCardStatement.repaymentAmountPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.overdueInterest')" prop="overdueInterest">
              <el-input
                v-model="searchForm.overdueInterest"
                :placeholder="$t('vcs.creditCardStatement.overdueInterestPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.statementStorePath')" prop="statementStorePath">
              <el-input
                v-model="searchForm.statementStorePath"
                :placeholder="$t('vcs.creditCardStatement.statementStorePathPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.points')" prop="points">
              <el-input
                v-model="searchForm.points"
                :placeholder="$t('vcs.creditCardStatement.pointsPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.pointExpireDate')" prop="pointExpireDate">
              <el-input
                v-model="searchForm.pointExpireDate"
                :placeholder="$t('vcs.creditCardStatement.pointExpireDatePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.createDate')" prop="createDate">
              <el-input
                v-model="searchForm.createDate"
                :placeholder="$t('vcs.creditCardStatement.createDatePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.statementStatus')" prop="statementStatus">
              <el-input
                v-model="searchForm.statementStatus"
                :placeholder="$t('vcs.creditCardStatement.statementStatusPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.interestRate')" prop="interestRate">
              <el-input
                v-model="searchForm.interestRate"
                :placeholder="$t('vcs.creditCardStatement.interestRatePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardStatement.overdueSurcharge')" prop="overdueSurcharge">
              <el-input
                v-model="searchForm.overdueSurcharge"
                :placeholder="$t('vcs.creditCardStatement.overdueSurchargePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.common.createDate')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('vcs.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('vcs.common.startDate')"
                :end-placeholder="$t('vcs.common.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('vcs.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateDialog">
            {{ $t('vcs.common.create') }}
          </el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardStatement.creditCardNumber')"
            prop="creditCardNumber"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.statementDate')"
            prop="statementDate"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.statementBalance')"
            prop="statementBalance"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.repaymentDueDate')"
            prop="repaymentDueDate"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.minimumPayment')"
            prop="minimumPayment"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.repaymentAmount')"
            prop="repaymentAmount"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.overdueInterest')"
            prop="overdueInterest"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.statementStorePath')"
            prop="statementStorePath"
            align="center"
            width="150"
          />
          <el-table-column :label="$t('vcs.creditCardStatement.points')" prop="points" align="center" width="150" />
          <el-table-column
            :label="$t('vcs.creditCardStatement.pointExpireDate')"
            prop="pointExpireDate"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.createDate')"
            prop="createDate"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.statementStatus')"
            prop="statementStatus"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.interestRate')"
            prop="interestRate"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardStatement.overdueSurcharge')"
            prop="overdueSurcharge"
            align="center"
            width="150"
          />
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewDialog(row)"
              >
                {{ $t('vcs.common.view') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-edit"
                @click="openUpdateDialog(row)"
              >
                {{ $t('vcs.common.edit') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-delete"
                @click="deleteItem(row.id)"
              >
                {{ $t('vcs.common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>
    </el-card>
    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('vcs.creditCardStatement.viewDetails')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>

    <el-dialog
      :title="isUpdate ? $t('vcs.creditCardStatement.editDetails') : $t('vcs.creditCardStatement.createDetails')"
      :visible.sync="dataFormDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="10vh"
      destroy-on-close
    >
      <customize-form
        ref="customizeForm"
        form-ref="dataForm"
        size="small"
        :model="formData"
        :form-item-config="formConfig"
        label-width="220px"
        label-position="left"
      />

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dataFormDialogVisible = false"> {{ $t('vcs.common.cancel') }}</el-button>
        <el-button
          v-if="!isUpdate"
          type="primary"
          size="small"
          :loading="confirmButtonLoading"
          @click="handlerCreateData()"
        >{{ $t('vcs.common.confirm') }}</el-button>
        <el-button
          v-if="isUpdate"
          type="primary"
          size="small"
          :loading="confirmButtonLoading"
          @click="handlerUpdateData()"
        >{{ $t('vcs.common.update') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Descriptions from '@/components/Descriptions/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import {
  createCreditCardStatement,
  deleteCreditCardStatement,
  getCreditCardStatementPage,
  updateCreditCardStatement
} from '@/api/system/creditCardStatement'
import { formConfig } from '@/views/system/creditCardStatement/form-config'

export default {
  name: 'CreditCardStatement',
  components: { CustomizeForm, Descriptions },
  data() {
    return {
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      confirmButtonLoading: false,
      dataFormDialogVisible: false,
      isUpdate: false,
      formConfig: formConfig,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        dateRange: [],
        creditCardNumber: '',
        statementDate: '',
        statementBalance: '',
        repaymentDueDate: '',
        minimumPayment: '',
        repaymentAmount: '',
        overdueInterest: '',
        statementStorePath: '',
        points: '',
        pointExpireDate: '',
        createDate: '',
        statementStatus: '',
        interestRate: '',
        overdueSurcharge: ''
      },
      viewRow: [],
      formData: {},
      items: [],
      total: 0
    }
  },
  mounted() {
    this.getItems()
  },
  methods: {
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      } delete requestData.dateRange
      getCreditCardStatementPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handlerCreateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          createCreditCardStatement(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          updateCreditCardStatement(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    viewDialog(row) {
      const description = { ...row }
      this.viewRow = this.formConfig.map(item => {
        return { key: this.$t(item.label), value: description[item.prop] }
      })
      this.viewDialogVisible = true
    },
    openUpdateDialog(row) {
      this.formData = { ...row }
      this.isUpdate = true
      this.dataFormDialogVisible = true
    },
    openCreateDialog() {
      this.isUpdate = false
      this.formData = {}
      if (this.$refs.customizeForm) {
        this.$refs.customizeForm.resetForm()
      }
      this.dataFormDialogVisible = true
    },
    deleteItem(id) {
      this.$confirm(this.$t('vcs.common.deleteTipMessage'), this.$t('vcs.common.tip'), {
        confirmButtonText: this.$t('vcs.common.confirm'),
        cancelButtonText: this.$t('vcs.common.cancel'),
        type: 'warning'
      }).then(() => {
        this.tableLoading = true
        deleteCreditCardStatement(id)
          .then((res) => {
            const { msg } = res
            this.searchForm.currentPage = 1
            this.$message.success(msg)
            this.getItems()
            this.dataFormDialogVisible = false
          })
          .finally(() => {
            this.tableLoading = false
          })
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
