import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { transactionTypeOptions } from '@/data'

export const formConfig = [
  {
    'label': 'vcs.creditCardTransactionDetails.creditCardNumber',
    'placeholder': 'vcs.creditCardTransactionDetails.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.creditCardNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.dealNumber',
    'placeholder': 'vcs.creditCardTransactionDetails.dealNumberPlaceholder',
    'prop': 'dealNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.dealNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.transactionDate',
    'placeholder': 'vcs.creditCardTransactionDetails.transactionDatePlaceholder',
    'prop': 'transactionDate',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'format': 'yyyy-MM-DD HH:mm:ss',
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.transactionDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.merchantNumber',
    'placeholder': 'vcs.creditCardTransactionDetails.merchantNumberPlaceholder',
    'prop': 'merchantNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.merchantNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.transactionCcy',
    'placeholder': 'vcs.creditCardTransactionDetails.transactionCcyPlaceholder',
    'prop': 'transactionCcy',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.transactionCcyPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.transactionAmount',
    'placeholder': 'vcs.creditCardTransactionDetails.transactionAmountPlaceholder',
    'prop': 'transactionAmount',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.transactionAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.bookingCcy',
    'placeholder': 'vcs.creditCardTransactionDetails.bookingCcyPlaceholder',
    'prop': 'bookingCcy',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.bookingCcyPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.bookingAmount',
    'placeholder': 'vcs.creditCardTransactionDetails.bookingAmountPlaceholder',
    'prop': 'bookingAmount',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.bookingAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.authorizationNumber',
    'placeholder': 'vcs.creditCardTransactionDetails.authorizationNumberPlaceholder',
    'prop': 'authorizationNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.authorizationNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardTransactionDetails.transactionType',
    'placeholder': 'vcs.creditCardTransactionDetails.transactionTypePlaceholder',
    'prop': 'transactionType',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': transactionTypeOptions,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardTransactionDetails.transactionTypePlaceholder' }
    ]
  }
]
