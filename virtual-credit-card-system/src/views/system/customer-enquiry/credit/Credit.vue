<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.creditEnquiry')" :show-back-btn="false">
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="160px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('vcs.creditCardMaster.customerNumber')" prop="customerNumber">
              <el-input
                v-model="searchForm.customerNumber"
                :placeholder="$t('vcs.creditCardMaster.customerNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getCreditDetails">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">
              {{ $t('vcs.common.reset') }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <el-descriptions v-if="creditDetails" class="descriptions" :column="1" border style="margin-bottom: 10px">
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.customerNumber') }}</template>{{ creditDetails.customerNumber }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.aggregateCreditLimitCurrency') }}</template>{{ creditDetails.aggregateCreditLimitCurrency }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.aggregateCreditLimit') }}</template>{{ creditDetails.aggregateCreditLimit ? Number(creditDetails.aggregateCreditLimit).toFixed(2) : '0.00' }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.aggregateCreditLimitUtilization') }}</template>{{ creditDetails.aggregateCreditLimitUtilization ? Number(creditDetails.aggregateCreditLimitUtilization).toFixed(2) : '0.00' }}</el-descriptions-item>
      </el-descriptions>

      <el-table v-if="creditDetails" :data="creditDetails.limitStructureList" border>
        <el-table-column :label="$t('vcs.creditCardMaster.accountNumber')" prop="accountNumber" align="center" />
        <el-table-column :label="$t('vcs.creditCardMaster.creditCurrency')" prop="creditCurrency" align="center" />
        <el-table-column :label="$t('vcs.creditCardMaster.creditLimit')" prop="creditLimit" align="center">
          <template slot-scope="{ row }">
            {{ row.creditLimit ? Number(row.creditLimit).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('vcs.creditCardMaster.creditLimitUtilization')" prop="creditLimitUtilization" align="center">
          <template slot-scope="{ row }">
            {{ row.creditLimitUtilization ? Number(row.creditLimitUtilization).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('vcs.creditCardMaster.creditLimitApprovalDate')" prop="creditLimitApprovalDate" align="center">
          <template slot-scope="{ row }">
            {{ row.creditLimitApprovalDate ? moment(row.creditLimitApprovalDate).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('vcs.creditCardMaster.creditProductType')" prop="creditProductType" align="center">
          <template slot-scope="{ row }">
            {{ $t(getLabel('CREDIT_PRODUCT_TYPE', row.creditProductType)) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('vcs.creditCardMaster.creditLimitSecuredStatus')" prop="creditLimitSecuredStatus" align="center">
          <template slot-scope="{ row }">
            {{ $t(getLabel('CREDIT_LIMIT_SECURED_STATUS', row.creditLimitSecuredStatus)) }}
          </template>
        </el-table-column>
      </el-table>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import moment from 'moment'
import { getCreditDetails } from '@/api/system/creditCardCustomer'
import { getLabel } from '@/data'

export default {
  name: 'CreditInformationCredit',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      moment,
      getLabel,
      creditDetails: null,
      searchForm: {
        customerNumber: ''
      }
    }
  },
  methods: {
    getCreditDetails() {
      this.loading = true
      getCreditDetails(this.searchForm)
        .then((res) => {
          this.creditDetails = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
