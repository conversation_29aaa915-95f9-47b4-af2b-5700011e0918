<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.portfolioEnquiry')" :show-back-btn="false">
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="160px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="$t('vcs.creditCardMaster.customerNumber')" prop="customerNumber">
              <el-input
                v-model="searchForm.customerNumber"
                :placeholder="$t('vcs.creditCardMaster.customerNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getPortfolioDetails">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">
              {{ $t('vcs.common.reset') }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <el-descriptions v-if="portfolioDetails" class="descriptions" :column="2" border>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.depositAsset') }}</template>{{ portfolioDetails.depositAsset }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.termDepositAsset') }}</template>{{ portfolioDetails.termDepositAsset }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.stockAsset') }}</template>{{ portfolioDetails.stockAsset }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.fundAsset') }}</template>{{ portfolioDetails.fundAsset }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.fexAsset') }}</template>{{ portfolioDetails.fexAsset }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.bondAsset') }}</template>{{ portfolioDetails.bondAsset }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.creditCardLiability') }}</template>{{ portfolioDetails.creditCardLiability }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.loanLiability') }}</template>{{ portfolioDetails.loanLiability }}</el-descriptions-item>
        <el-descriptions-item><template slot="label">{{ $t('vcs.creditCardMaster.wealthPortfolioCurrency') }}</template>{{ portfolioDetails.localCurrency }}</el-descriptions-item>
      </el-descriptions>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getPortfolioDetails } from '@/api/system/creditCardCustomer'

export default {
  name: 'CreditInformationPortfolio',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      portfolioDetails: null,
      searchForm: {
        customerNumber: ''
      }
    }
  },
  methods: {
    getPortfolioDetails() {
      this.loading = true
      getPortfolioDetails(this.searchForm)
        .then((res) => {
          this.portfolioDetails = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
