import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { booleanOptions, countryOptions, customerIdTypeOptions, genderOptions, statusOptions } from '@/data'

export const commonFormConfig = [
  {
    'label': 'vcs.kyc.customer.firstName',
    'placeholder': 'vcs.kyc.customer.firstNamePlaceholder',
    'prop': 'firstName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'updateDisabled': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.firstNamePlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.lastName',
    'placeholder': 'vcs.kyc.customer.lastNamePlaceholder',
    'prop': 'lastName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.lastNamePlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.customerId',
    'placeholder': 'vcs.kyc.customer.customerIdPlaceholder',
    'prop': 'customerId',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'maxlength': 35,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.customerIdPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.customerIdType',
    'placeholder': 'vcs.kyc.customer.customerIdTypePlaceholder',
    'prop': 'customerIdType',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': customerIdTypeOptions,
    'updateDisabled': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.customerIdTypePlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.issueCountry',
    'placeholder': 'vcs.kyc.customer.issueCountryPlaceholder',
    'prop': 'issueCountry',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': countryOptions,
    'updateDisabled': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.issueCountryPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.dateOfBirth',
    'placeholder': 'vcs.kyc.customer.dateOfBirthPlaceholder',
    'prop': 'dateOfBirth',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'date',
    'format': 'yyyy-MM-dd',
    'valueFormat': 'timestamp',
    'updateDisabled': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.dateOfBirthPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.otherLanguageName',
    'placeholder': 'vcs.kyc.customer.otherLanguageNamePlaceholder',
    'prop': 'otherLanguageName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70
    // 'rules': [
    //   { required: true, message: 'vcs.kyc.customer.otherLanguageNamePlaceholder' }
    // ]
  },
  {
    'label': 'vcs.kyc.customer.gender',
    'placeholder': 'vcs.kyc.customer.genderPlaceholder',
    'prop': 'gender',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'options': genderOptions,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.genderPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.nationality',
    'placeholder': 'vcs.kyc.customer.nationalityPlaceholder',
    'prop': 'nationality',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'options': countryOptions,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.nationalityPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.customer.department',
    'placeholder': 'vcs.kyc.customer.departmentPlaceholder',
    'prop': 'department',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.departmentPlaceholder' }
    ],
    'maxlength': 30
  },
  {
    'label': 'vcs.kyc.customer.role',
    'placeholder': 'vcs.kyc.customer.rolePlaceholder',
    'prop': 'role',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.rolePlaceholder' }
    ],
    'maxlength': 70
  },
  {
    'label': 'vcs.kyc.customer.industry',
    'placeholder': 'vcs.kyc.customer.industryPlaceholder',
    'prop': 'industry',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.industryPlaceholder' }
    ],
    'maxlength': 50
  },
  {
    'label': 'vcs.kyc.customer.position',
    'placeholder': 'vcs.kyc.customer.positionPlaceholder',
    'prop': 'position',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.positionPlaceholder' }
    ],
    'maxlength': 70
  },
  {
    'label': 'vcs.kyc.customer.customerNumber',
    'placeholder': 'vcs.kyc.customer.customerNumberPlaceholder',
    'prop': 'customerNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.customerNumberPlaceholder' }
    ],
    'maxlength': 25,
    'hidden': true
  },
  {
    'label': 'vcs.kyc.customer.accountNumber',
    'placeholder': 'vcs.kyc.customer.accountNumberPlaceholder',
    'prop': 'accountNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.customer.accountNumberPlaceholder' }
    ],
    'maxlength': 25,
    'hidden': true
  }
]

export const crsFormConfig = [
  ...commonFormConfig,
  {
    'label': 'vcs.kyc.crs.foreignCountryTaxNumber',
    'placeholder': 'vcs.kyc.crs.foreignCountryTaxNumberPlaceholder',
    'prop': 'foreignCountryTaxNumber',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 1,
    'step-strictly': true,
    'controls': false,
    'precision': 0,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.crs.foreignCountryTaxNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.crs.validityPeriod',
    'placeholder': 'vcs.kyc.crs.validityPeriodPlaceholder',
    'prop': 'validityPeriod',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'vcs.kyc.crs.validityPeriodPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.crs.report',
    'placeholder': 'vcs.kyc.crs.reportPlaceholder',
    'prop': 'report',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': booleanOptions,
    'rules': [
      { required: true, message: 'vcs.kyc.crs.reportPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.crs.crsStatus',
    'placeholder': 'vcs.kyc.crs.crsStatusPlaceholder',
    'prop': 'crsStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'options': statusOptions,
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.crs.crsStatusPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.crs.remark',
    'placeholder': 'vcs.kyc.crs.remarkPlaceholder',
    'prop': 'remark',
    'component': 'el-input',
    'span': 24,
    'width': '100%',
    'type': 'textarea',
    'clearable': true,
    'maxlength': 255
  },
  {
    'label': 'vcs.common.creationDate',
    'placeholder': 'kyc.creationDatePlaceholder',
    'prop': 'creationDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'updateDisabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.creationDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.common.updateDate',
    'placeholder': 'kyc.updateDatePlaceholder',
    'prop': 'updateDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'updateDisabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.updateDatePlaceholder' }
    ]
  }
]

export const fatcaFormConfig = [
  ...commonFormConfig,
  {
    'label': 'vcs.kyc.facta.taxpayerIdentificationNumber',
    'placeholder': 'vcs.kyc.facta.taxpayerIdentificationNumberPlaceholder',
    'prop': 'taxpayerIdentificationNumber',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 1,
    'step-strictly': true,
    'controls': false,
    'precision': 0,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.kyc.facta.taxpayerIdentificationNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.facta.validityPeriod',
    'placeholder': 'vcs.kyc.facta.validityPeriodPlaceholder',
    'prop': 'validityPeriod',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'vcs.kyc.facta.validityPeriodPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.facta.report',
    'placeholder': 'vcs.kyc.facta.reportPlaceholder',
    'prop': 'report',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': booleanOptions,
    'rules': [
      { required: true, message: 'vcs.kyc.facta.reportPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.facta.factaStatus',
    'placeholder': 'vcs.kyc.facta.factaStatusPlaceholder',
    'prop': 'factaStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': statusOptions,
    'hidden': true,
    'rules': [
      { required: true, message: 'vcs.kyc.facta.factaStatusPlaceholder' }
    ]
  },
  {
    'label': 'vcs.kyc.facta.remark',
    'placeholder': 'vcs.kyc.facta.remarkPlaceholder',
    'prop': 'remark',
    'component': 'el-input',
    'span': 24,
    'type': 'textarea',
    'width': '100%',
    'clearable': true,
    'maxlength': 255
  },
  {
    'label': 'vcs.common.creationDate',
    'placeholder': 'kyc.creationDatePlaceholder',
    'prop': 'creationDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.creationDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.common.updateDate',
    'placeholder': 'kyc.updateDatePlaceholder',
    'prop': 'updateDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'updateDisabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.updateDatePlaceholder' }
    ]
  }
]
